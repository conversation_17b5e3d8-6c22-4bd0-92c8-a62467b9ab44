import React from 'react'

interface SkeletonProps {
  className?: string
  width?: string | number
  height?: string | number
  borderRadius?: string
  animate?: boolean
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  width = '100%',
  height = '1rem',
  borderRadius = '0.25rem',
  animate = true
}) => {
  return (
    <div
      className={`bg-stone-700/50 ${animate ? 'animate-pulse' : ''} ${className}`}
      style={{
        width,
        height,
        borderRadius
      }}
    />
  )
}

interface SkeletonCardProps {
  className?: string
  rows?: number
  rowHeight?: string | number
  headerHeight?: string | number
  showHeader?: boolean
}

export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  className = '',
  rows = 3,
  rowHeight = '1rem',
  headerHeight = '1.5rem',
  showHeader = true
}) => {
  return (
    <div className={`bg-stone-800/80 p-4 rounded-lg shadow-md ${className}`}>
      {showHeader && (
        <div className="flex items-center justify-between mb-4">
          <Skeleton width="60%" height={headerHeight} borderRadius="0.375rem" />
          <Skeleton width="20%" height="2rem" borderRadius="0.375rem" />
        </div>
      )}
      <div className="space-y-2">
        {Array.from({ length: rows }).map((_, index) => (
          <Skeleton 
            key={index} 
            height={rowHeight} 
            className={index === 0 ? 'w-full' : index === rows - 1 ? 'w-1/3' : 'w-2/3'} 
          />
        ))}
      </div>
    </div>
  )
}

interface SkeletonListProps {
  count?: number
  className?: string
  itemClassName?: string
}

export const SkeletonList: React.FC<SkeletonListProps> = ({
  count = 3,
  className = '',
  itemClassName = ''
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard key={index} className={itemClassName} />
      ))}
    </div>
  )
}

interface SkeletonTableProps {
  rows?: number
  columns?: number
  className?: string
  showHeader?: boolean
}

export const SkeletonTable: React.FC<SkeletonTableProps> = ({
  rows = 5,
  columns = 4,
  className = '',
  showHeader = true
}) => {
  return (
    <div className={`w-full ${className}`}>
      {showHeader && (
        <div className="grid gap-4 mb-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton key={index} height="2rem" />
          ))}
        </div>
      )}
      <div className="space-y-4">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div
            key={rowIndex}
            className="grid gap-4"
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton key={colIndex} height="1.5rem" />
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

interface SkeletonDashboardProps {
  cardCount?: number
  className?: string
}

export const SkeletonDashboard: React.FC<SkeletonDashboardProps> = ({
  cardCount = 5,
  className = ''
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: cardCount }).map((_, index) => (
        <div key={index} className="bg-stone-800/80 p-4 rounded-lg shadow-md">
          <div className="flex items-center justify-between mb-4">
            <Skeleton width="40%" height="1.5rem" borderRadius="0.375rem" />
            <div className="flex space-x-2">
              <Skeleton width="2rem" height="2rem" borderRadius="0.375rem" />
              <Skeleton width="2rem" height="2rem" borderRadius="0.375rem" />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="space-y-2">
              <Skeleton width="80%" height="1rem" />
              <Skeleton width="60%" height="1rem" />
            </div>
            <div className="space-y-2">
              <Skeleton width="70%" height="1rem" />
              <Skeleton width="50%" height="1rem" />
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-stone-700">
            <div className="grid grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton width="90%" height="0.75rem" />
                  <Skeleton width="60%" height="0.75rem" />
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

interface SkeletonLoansProps {
  count?: number
  className?: string
}

export const SkeletonLoans: React.FC<SkeletonLoansProps> = ({
  count = 3,
  className = ''
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-stone-800/80 p-4 rounded-lg shadow-md">
          <div className="flex items-center justify-between mb-4">
            <Skeleton width="30%" height="1.5rem" borderRadius="0.375rem" />
            <Skeleton width="20%" height="1.5rem" borderRadius="0.375rem" />
          </div>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="space-y-2">
              <Skeleton width="80%" height="0.75rem" />
              <Skeleton width="60%" height="0.75rem" />
            </div>
            <div className="space-y-2">
              <Skeleton width="70%" height="0.75rem" />
              <Skeleton width="50%" height="0.75rem" />
            </div>
            <div className="space-y-2">
              <Skeleton width="90%" height="0.75rem" />
              <Skeleton width="40%" height="0.75rem" />
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-stone-700">
            <div className="flex justify-between">
              <Skeleton width="40%" height="1rem" />
              <Skeleton width="20%" height="1rem" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

interface SkeletonLicensesProps {
  count?: number
  className?: string
}

export const SkeletonLicenses: React.FC<SkeletonLicensesProps> = ({
  count = 6,
  className = ''
}) => {
  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-stone-800/80 p-4 rounded-lg shadow-md">
          <div className="flex items-center justify-between mb-4">
            <Skeleton width="60%" height="1.25rem" borderRadius="0.375rem" />
            <Skeleton width="2rem" height="2rem" borderRadius="9999px" />
          </div>
          <div className="space-y-3 mb-4">
            <div className="flex justify-between">
              <Skeleton width="40%" height="0.75rem" />
              <Skeleton width="30%" height="0.75rem" />
            </div>
            <div className="flex justify-between">
              <Skeleton width="35%" height="0.75rem" />
              <Skeleton width="25%" height="0.75rem" />
            </div>
            <div className="flex justify-between">
              <Skeleton width="45%" height="0.75rem" />
              <Skeleton width="20%" height="0.75rem" />
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-stone-700 flex justify-end space-x-2">
            <Skeleton width="5rem" height="2rem" borderRadius="0.375rem" />
            <Skeleton width="5rem" height="2rem" borderRadius="0.375rem" />
          </div>
        </div>
      ))}
    </div>
  )
}
