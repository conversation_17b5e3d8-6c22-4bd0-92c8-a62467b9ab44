import React from 'react'
import { FormSection, CheckboxGroup, FormField } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Firearm Type section component
 *
 * This component handles the Firearm Type section of the New License form.
 * It collects the type of firearm for the license application and
 * maps the form fields to the XML placeholders for document generation.
 *
 * XML Placeholders for Firearm Type:
 *
 * // TYPE OF FIREARM
 * - {Rifle} - Rifle (Mark with X)
 * - {Shotgun} - Shotgun (Mark with X)
 * - {Pistol} - Pistol (Mark with X)
 * - {Comb} - Combination (Mark with X)
 * - {OtherDesign} - Other Design of Firearm (Mark with X)
 * - {OtherDesignE} - Explain the other Design (Fill in)
 */
const FirearmType: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Firearm Type</h3>
      <FormSection title="Type of Firearm" subtitle="Select the type of firearm">
        <div className="space-y-3">
          <div className="mb-4">
            <CheckboxGroup
              options={[
                { name: 'rifle', label: 'Rifle', checked: formData.rifle || false },
                { name: 'shotgun', label: 'Shotgun', checked: formData.shotgun || false },
                { name: 'pistol', label: 'Pistol', checked: formData.pistol || false },
                { name: 'comb', label: 'Combination', checked: formData.comb },
                { name: 'otherDesign', label: 'Other Design', checked: formData.otherDesign }
              ]}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const { name, checked } = e.target
                updateFormData({
                  [name]: checked
                })
              }}
              columns={4}
            />
          </div>

          {formData.otherDesign && (
            <FormField
              label="Other Design Information"
              name="otherDesignE"
              value={formData.otherDesignE}
              onChange={handleChange}
              placeholder="Please specify the other design"
              type="textarea"
              rows={2}
            />
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default FirearmType
