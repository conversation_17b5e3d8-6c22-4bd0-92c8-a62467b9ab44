import { app, session } from 'electron'
import log from 'electron-log'
import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'

/**
 * NetworkOptimizer class to improve GitHub download speeds
 * This class configures network settings to optimize GitHub API and download performance
 */
export class NetworkOptimizer {
  private readonly cachePath: string
  private readonly maxCacheSize: number = 500 * 1024 * 1024 // 500MB cache limit

  constructor() {
    // Set up a dedicated cache directory for GitHub downloads
    this.cachePath = path.join(app.getPath('userData'), 'github-cache')

    // Create cache directory if it doesn't exist
    if (!fs.existsSync(this.cachePath)) {
      try {
        fs.mkdirSync(this.cachePath, { recursive: true })
        log.info(`Created GitHub cache directory at: ${this.cachePath}`)
      } catch (error) {
        log.error('Failed to create GitHub cache directory:', error)
      }
    }
  }

  /**
   * Initialize network optimizations for GitHub downloads
   */
  public initialize(): void {
    log.info('Initializing network optimizations for GitHub downloads')

    try {
      // Configure the default session
      const defaultSession = session.defaultSession

      // Set custom user agent to help with GitHub API rate limiting
      const customUserAgent = `FirearmStudio/${app.getVersion()} (${os.platform()}; ${os.arch()}) ElectronUpdater`
      defaultSession.setUserAgent(customUserAgent)
      log.info(`Set custom user agent: ${customUserAgent}`)

      // Configure cache settings
      this.configureCacheSettings(defaultSession)

      // Configure proxy settings if needed
      this.configureProxySettings(defaultSession)

      // Configure DNS settings
      this.configureDNSSettings()

      // Configure TCP settings for Windows
      this.configureTCPSettings()

      log.info('Network optimizations applied successfully')
    } catch (error) {
      log.error('Failed to apply network optimizations:', error)
    }
  }

  /**
   * Configure cache settings for the session
   */
  private configureCacheSettings(defaultSession: Electron.Session): void {
    try {
      // Clear existing cache to start fresh
      defaultSession.clearCache().then(() => {
        log.info('Cleared existing network cache')

        // Configure cache settings
        // Note: Electron doesn't have a direct method to set cache size limit
        // We'll use the available APIs to manage cache
        log.info(`Cache will be managed with a target size of ${this.maxCacheSize / (1024 * 1024)}MB`)

        // Schedule periodic cache cleanup to maintain size
        this.scheduleCacheCleanup(defaultSession)
      }).catch(err => {
        log.error('Failed to clear cache:', err)
      })
    } catch (error) {
      log.error('Error configuring cache settings:', error)
    }
  }

  /**
   * Configure proxy settings if needed
   */
  private configureProxySettings(defaultSession: Electron.Session): void {
    try {
      // Check if we need to bypass proxy for GitHub
      const proxyBypassRules = [
        'github.com',
        'api.github.com',
        'objects.githubusercontent.com',
        'github-releases.githubusercontent.com',
        'github-production-release-asset-*.s3.amazonaws.com'
      ]

      defaultSession.setProxy({
        proxyBypassRules: proxyBypassRules.join(','),
        pacScript: '',
        proxyRules: ''
      }).then(() => {
        log.info('Proxy bypass rules configured for GitHub domains')
      }).catch(err => {
        log.error('Failed to configure proxy settings:', err)
      })
    } catch (error) {
      log.error('Error configuring proxy settings:', error)
    }
  }

  /**
   * Configure DNS settings for faster lookups
   */
  private configureDNSSettings(): void {
    try {
      // On Windows, we can use PowerShell to configure DNS settings
      if (process.platform === 'win32') {
        const { execSync } = require('child_process')

        try {
          // Set DNS cache TTL to 1 hour (3600 seconds)
          execSync('netsh interface ipv4 set global defaultcurhoplimit=64', { windowsHide: true })
          log.info('Configured IPv4 hop limit for better network performance')
        } catch (execError) {
          log.error('Failed to configure DNS settings:', execError)
        }
      }
    } catch (error) {
      log.error('Error configuring DNS settings:', error)
    }
  }

  /**
   * Configure TCP settings for Windows
   */
  private configureTCPSettings(): void {
    try {
      // On Windows, we can use PowerShell to configure TCP settings
      if (process.platform === 'win32') {
        const { execSync } = require('child_process')

        try {
          // Enable TCP window scaling
          execSync('netsh interface tcp set global autotuninglevel=normal', { windowsHide: true })
          log.info('Configured TCP auto-tuning for better download performance')
        } catch (execError) {
          log.error('Failed to configure TCP settings:', execError)
        }
      }
    } catch (error) {
      log.error('Error configuring TCP settings:', error)
    }
  }

  /**
   * Schedule periodic cache cleanup to maintain size
   */
  private scheduleCacheCleanup(session: Electron.Session): void {
    // Schedule cache cleanup every 30 minutes
    const CLEANUP_INTERVAL = 30 * 60 * 1000; // 30 minutes in milliseconds

    // Initial cleanup
    this.cleanupCache();

    // Set up periodic cleanup
    setInterval(() => {
      this.cleanupCache();

      // Also check the current cache size and clear if needed
      session.getCacheSize().then(size => {
        log.info(`Current cache size: ${size / (1024 * 1024)}MB`);
        if (size > this.maxCacheSize) {
          log.info('Cache size exceeds limit, clearing cache');
          session.clearCache().then(() => {
            log.info('Cache cleared due to size limit');
          }).catch(err => {
            log.error('Failed to clear cache:', err);
          });
        }
      }).catch(err => {
        log.error('Failed to get cache size:', err);
      });
    }, CLEANUP_INTERVAL);
  }

  /**
   * Clean up old cache files to prevent excessive disk usage
   */
  public cleanupCache(): void {
    try {
      log.info('Cleaning up GitHub cache directory')

      // Read cache directory
      fs.readdir(this.cachePath, (err, files) => {
        if (err) {
          log.error('Failed to read cache directory:', err)
          return
        }

        // Get file stats and sort by access time
        const fileStats = files
          .map(file => {
            const filePath = path.join(this.cachePath, file)
            try {
              const stats = fs.statSync(filePath)
              return { file: filePath, stats }
            } catch (statErr) {
              log.error(`Failed to get stats for ${filePath}:`, statErr)
              return null
            }
          })
          .filter(item => item !== null)
          .sort((a, b) => a!.stats.atime.getTime() - b!.stats.atime.getTime())

        // Calculate total size
        let totalSize = fileStats.reduce((sum, item) => sum + item!.stats.size, 0)

        // Remove oldest files if total size exceeds limit
        if (totalSize > this.maxCacheSize) {
          log.info(`Cache size (${totalSize / (1024 * 1024)}MB) exceeds limit, cleaning up oldest files`)

          for (const item of fileStats) {
            if (totalSize <= this.maxCacheSize) break

            try {
              fs.unlinkSync(item!.file)
              totalSize -= item!.stats.size
              log.info(`Removed cache file: ${item!.file}`)
            } catch (unlinkErr) {
              log.error(`Failed to remove cache file ${item!.file}:`, unlinkErr)
            }
          }
        }
      })
    } catch (error) {
      log.error('Error cleaning up cache:', error)
    }
  }
}
