import React from 'react';
import { FormField } from '../../../FormComponents';
import { DashboardIcons } from '../../../icons/DashboardIcons';
import { Firearm } from '../../../../types/firearm';

interface IdentificationSectionProps {
  formData: Partial<Firearm>;
  loanId?: string;
  onStorageTypeChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

export const IdentificationSection: React.FC<IdentificationSectionProps> = ({
  formData,
  loanId,
  onStorageTypeChange,
  onChange
}) => {
  return (
    <div>
      <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
        <DashboardIcons.Firearm className="w-3.5 h-3.5 text-orange-400" />
        Identification & Storage
      </h2>
      <div className={`grid ${formData.storage_type === 'Owner' ? 'grid-cols-2' : 'grid-cols-3'} gap-2`}>
        <FormField
          label="Stock #"
          name="stock_number"
          value={formData.stock_number || ''}
          onChange={onChange}
          required
          inputClassName="premium-field"
        />
        {formData.storage_type !== 'Owner' && (
          <FormField
            label="Dealer Number or ID"
            name="dealer_id_number"
            value={formData.dealer_id_number || ''}
            onChange={onChange}
            required
            inputClassName="premium-field"
          />
        )}
        <div>
          <label className="block text-xs font-medium text-stone-300 mb-0.5">Storage Type</label>
          <select
            name="storage_type"
            value={formData.storage_type || 'Owner'}
            onChange={onStorageTypeChange}
            disabled={!!loanId}
            className={`w-full bg-stone-700 border border-orange-400/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500/60 focus:border-orange-400 transition-all duration-150 text-xs shadow-sm premium-field ${loanId ? 'opacity-80 cursor-not-allowed' : ''}`}
          >
            <option value="Owner">Owner</option>
            <option value="Private">Private</option>
            <option value="Dealer">Dealer</option>
          </select>
          {formData.storage_type === 'Dealer' && (
            <p className="text-xs text-amber-400">12mo free storage</p>
          )}
          {loanId && (
            <p className="text-xs text-stone-400">Fixed for loan firearms</p>
          )}
        </div>
      </div>
    </div>
  );
};
