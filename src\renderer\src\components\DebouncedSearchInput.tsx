import { useRef } from 'react'
import { useSearchDebounce } from '../hooks/useSearchDebounce'
import { DashboardIcons } from './icons/DashboardIcons'

export interface DebouncedSearchInputProps {
  onSearch: (query: string) => void
  placeholder?: string
  isLoading?: boolean
  initialValue?: string
  autoFocus?: boolean
  className?: string
  debounceTime?: number
  onClear?: () => void
}

function DebouncedSearchInput({
  onSearch,
  placeholder = 'Search...',
  isLoading = false,
  initialValue = '',
  autoFocus = false,
  className = '',
  debounceTime = 300,
  onClear
}: DebouncedSearchInputProps): React.JSX.Element {
  const inputRef = useRef<HTMLInputElement>(null)
  
  // Use our custom hook for debounced search
  const {
    searchTerm,
    handleSearchChange,
    clearSearch
  } = useSearchDebounce(onSearch, debounceTime, initialValue)
  
  // Handle clear button click
  const handleClear = () => {
    clearSearch()
    
    if (onClear) {
      onClear()
    }
    
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Ignore empty searches
    if (!searchTerm.trim()) return
    
    // Perform immediate search
    onSearch(searchTerm)
  }
  
  return (
    <div className={`relative w-full ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder={placeholder}
            autoFocus={autoFocus}
            className="w-full bg-stone-700/50 text-white pl-10 pr-10 py-3 rounded-lg 
              focus:ring-2 focus:ring-orange-500 focus:bg-stone-700 outline-none transition-all"
            aria-label="Search"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-stone-400">
            <DashboardIcons.Search className="w-5 h-5" />
          </div>
          
          {/* Clear button */}
          {searchTerm && (
            <button
              type="button"
              onClick={handleClear}
              className="absolute right-10 top-1/2 transform -translate-y-1/2 text-stone-400 
                hover:text-white transition-colors"
              aria-label="Clear search"
            >
              <DashboardIcons.Close className="w-5 h-5" />
            </button>
          )}
          
          {/* Submit button */}
          <button
            type="submit"
            disabled={isLoading}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-stone-400 
              hover:text-white transition-colors disabled:opacity-50"
            aria-label="Submit search"
          >
            {isLoading ? (
              <DashboardIcons.Spinner className="w-5 h-5 animate-spin" />
            ) : (
              <DashboardIcons.Enter className="w-5 h-5" />
            )}
          </button>
        </div>
      </form>
    </div>
  )
}

export default DebouncedSearchInput
