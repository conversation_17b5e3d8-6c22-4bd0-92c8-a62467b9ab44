import React from 'react'
import { FormSection, FormField, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Criminal History section component
 */
const CriminalHistory: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  const yesNoOptions = [
    { value: 'yes', label: 'Yes' },
    { value: 'no', label: 'No' }
  ]

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Criminal History</h3>

      {/* Criminal Conviction */}
      <FormSection
        title="Criminal Conviction"
        subtitle="Have you ever been convicted of an offense in RSA?"
      >
        <RadioGroup
          name="offense"
          value={formData.offenseYes ? 'yes' : formData.offenseNo ? 'no' : ''}
          onChange={(value) =>
            updateFormData({
              offenseYes: value === 'yes',
              offenseNo: value === 'no'
            })
          }
          options={yesNoOptions}
          required={false}
        />

        {formData.offenseYes && (
          <div className="mt-2 space-y-3 p-3 bg-stone-800 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <FormField
                label="Police Station"
                name="policeStation"
                value={formData.policeStation || ''}
                onChange={handleChange}
                required={false}
              />

              <FormField
                label="Case Number"
                name="caseNumber"
                value={formData.caseNumber || ''}
                onChange={handleChange}
                required={false}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <FormField
                label="Charge"
                name="charge"
                value={formData.charge || ''}
                onChange={handleChange}
                required={false}
              />

              <FormField
                label="Outcome/Verdict"
                name="outcomeVerdict"
                value={formData.outcomeVerdict || ''}
                onChange={handleChange}
                required={false}
              />
            </div>
          </div>
        )}
      </FormSection>

      {/* Pending Cases */}
      <FormSection
        title="Pending Cases"
        subtitle="Are there any pending cases against you?"
      >
        <RadioGroup
          name="pendingCase"
          value={formData.pendingCaseYes ? 'yes' : formData.pendingCaseNo ? 'no' : ''}
          onChange={(value) =>
            updateFormData({
              pendingCaseYes: value === 'yes',
              pendingCaseNo: value === 'no'
            })
          }
          options={yesNoOptions}
          required={false}
        />

        {formData.pendingCaseYes && (
          <div className="mt-2 space-y-3 p-3 bg-stone-800 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <FormField
                label="Police Station"
                name="pendingCasePoliceStation"
                value={formData.pendingCasePoliceStation || ''}
                onChange={handleChange}
                required={false}
              />

              <FormField
                label="Case Number"
                name="pendingCaseCaseNumber"
                value={formData.pendingCaseCaseNumber || ''}
                onChange={handleChange}
                required={false}
              />

              <FormField
                label="Offence"
                name="pendingCaseOffence"
                value={formData.pendingCaseOffence || ''}
                onChange={handleChange}
                required={false}
              />
            </div>
          </div>
        )}
      </FormSection>

      {/* Lost/Stolen Firearms */}
      <FormSection
        title="Lost/Stolen Firearms"
        subtitle="Have any of your firearms been lost or stolen?"
      >
        <RadioGroup
          name="lostStolen"
          value={formData.lostStolenYes ? 'yes' : formData.lostStolenNo ? 'no' : ''}
          onChange={(value) =>
            updateFormData({
              lostStolenYes: value === 'yes',
              lostStolenNo: value === 'no'
            })
          }
          options={yesNoOptions}
          required={false}
        />

        {formData.lostStolenYes && (
          <div className="mt-2 space-y-3 p-3 bg-stone-800 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <FormField
                label="Police Station"
                name="lostStolenPoliceStation"
                value={formData.lostStolenPoliceStation || ''}
                onChange={handleChange}
                required={false}
              />

              <FormField
                label="Case Number"
                name="lostStolenCaseNumber"
                value={formData.lostStolenCaseNumber || ''}
                onChange={handleChange}
                required={false}
              />
            </div>

            <FormField
              label="Details"
              name="lostStolenCircumstances"
              value={formData.lostStolenCircumstances || ''}
              onChange={handleChange}
              type="textarea"
              rows={3}
              required={false}
            />
          </div>
        )}
      </FormSection>

      {/* Negligence Investigation */}
      <FormSection
        title="Negligence Investigation"
        subtitle="Was a case of negligence opened and investigated regarding the stolen/lost firearm?"
      >
        <RadioGroup
          name="investigation"
          value={formData.investigationYes ? 'yes' : formData.investigationNo ? 'no' : ''}
          onChange={(value) =>
            updateFormData({
              investigationYes: value === 'yes',
              investigationNo: value === 'no'
            })
          }
          options={yesNoOptions}
          required={false}
        />

        {formData.investigationYes && (
          <div className="space-y-3 p-3 bg-stone-800 rounded-lg">
            <FormField
              label="Police Station"
              name="investigationPoliceStation"
              value={formData.investigationPoliceStation || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Case Number"
              name="investigationCaseNumber"
              value={formData.investigationCaseNumber || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Charge"
              name="investigationCharge"
              value={formData.investigationCharge || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Outcome/Verdict"
              name="investigationOutcome"
              value={formData.investigationOutcome || ''}
              onChange={handleChange}
              required={false}
            />
          </div>
        )}
      </FormSection>

      {/* Declared Unfit */}
      <FormSection
        title="Declared Unfit"
        subtitle="Have you ever been declared unfit to possess a firearm?"
      >
        <RadioGroup
          name="declaredUnfit"
          value={formData.declaredUnfitYes ? 'yes' : formData.declaredUnfitNo ? 'no' : ''}
          onChange={(value) =>
            updateFormData({
              declaredUnfitYes: value === 'yes',
              declaredUnfitNo: value === 'no'
            })
          }
          options={yesNoOptions}
          required={false}
        />

        {formData.declaredUnfitYes && (
          <div className="space-y-3 p-3 bg-stone-800 rounded-lg">
            <FormField
              label="Police Station"
              name="declaredUnfitPoliceStation"
              value={formData.declaredUnfitPoliceStation || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Case Number"
              name="declaredUnfitCaseNumber"
              value={formData.declaredUnfitCaseNumber || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Charge"
              name="declaredUnfitCharge"
              value={formData.declaredUnfitCharge || ''}
              onChange={handleChange}
              required={false}
            />

            <div>
              <FormField
                label="Date from which unfit"
                name="declaredUnfitDate"
                value={formData.declaredUnfitDate || ''}
                onChange={handleChange}
                type="date"
                required={false}
              />
            </div>

            <div>
              <FormField
                label="Period of Unfitness"
                name="declaredUnfitPeriod"
                value={formData.declaredUnfitPeriod || ''}
                onChange={handleChange}
                required={false}
              />
            </div>
          </div>
        )}
      </FormSection>

      {/* Confiscated */}
      <FormSection
        title="Confiscated"
        subtitle="Has a firearm ever been confiscated?"
      >
        <RadioGroup
          name="confiscated"
          value={formData.confiscatedYes ? 'yes' : formData.confiscatedNo ? 'no' : ''}
          onChange={(value) =>
            updateFormData({
              confiscatedYes: value === 'yes',
              confiscatedNo: value === 'no'
            })
          }
          options={yesNoOptions}
          required={false}
        />

        {formData.confiscatedYes && (
          <div className="space-y-3 p-3 bg-stone-800 rounded-lg">
            <FormField
              label="Police Station"
              name="confiscatedPoliceStation"
              value={formData.confiscatedPoliceStation || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Case Number"
              name="confiscatedCaseNumber"
              value={formData.confiscatedCaseNumber || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Circumstances"
              name="confiscatedCircumstances"
              value={formData.confiscatedCircumstances || ''}
              onChange={handleChange}
              type="textarea"
              required={false}
            />

            <FormField
              label="Outcome/Verdict"
              name="confiscatedOutcome"
              value={formData.confiscatedOutcome || ''}
              onChange={handleChange}
              required={false}
            />
          </div>
        )}
      </FormSection>

      {/* Protection Orders/Allegations of Violence */}
      <FormSection
        title="Protection Orders/Allegations of Violence"
        subtitle="In the past 5 years have you been served with a Protection Order, or visited by a police official concerning allegations of violence or other conflict in your home or elsewhere?"
      >
        <RadioGroup
          name="protectionAllegations"
          value={
            formData.protectionAllegationsYes
              ? 'yes'
              : formData.protectionAllegationsNo
                ? 'no'
                : ''
          }
          onChange={(value) =>
            updateFormData({
              protectionAllegationsYes: value === 'yes',
              protectionAllegationsNo: value === 'no'
            })
          }
          options={yesNoOptions}
          required={false}
        />

        {formData.protectionAllegationsYes && (
          <div className="space-y-3 p-3 bg-stone-800 rounded-lg">
            <FormField
              label="Details of protection order or allegations"
              name="protectionAllegationsDetails"
              value={formData.protectionAllegationsDetails || ''}
              onChange={handleChange}
              type="textarea"
              rows={3}
              required={false}
            />
          </div>
        )}
      </FormSection>

      {/* Denied Firearm License/Permit */}
      <FormSection
        title="Denied Firearm License/Permit"
        subtitle="In the past 5 years have you ever been denied a Licence, Permit, or Authorization regarding a firearm?"
      >
        <RadioGroup
          name="deniedFirearm"
          value={formData.deniedFirearmYes ? 'yes' : formData.deniedFirearmNo ? 'no' : ''}
          onChange={(value) =>
            updateFormData({
              deniedFirearmYes: value === 'yes',
              deniedFirearmNo: value === 'no'
            })
          }
          options={yesNoOptions}
          required={false}
        />

        {formData.deniedFirearmYes && (
          <div className="space-y-3 p-3 bg-stone-800 rounded-lg">
            <FormField
              label="Police Station"
              name="deniedFirearmPoliceStation"
              value={formData.deniedFirearmPoliceStation || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Case Number"
              name="deniedFirearmCaseNumber"
              value={formData.deniedFirearmCaseNumber || ''}
              onChange={handleChange}
              required={false}
            />

            <FormField
              label="Denied Firearm give reason"
              name="deniedFirearmReason"
              value={formData.deniedFirearmReason || ''}
              onChange={handleChange}
              type="textarea"
              rows={3}
              required={false}
            />
          </div>
        )}
      </FormSection>
    </div>
  )
}

export default CriminalHistory
