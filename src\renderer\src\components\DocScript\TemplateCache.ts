/**
 * Template caching system for document templates
 * Provides functionality to cache templates locally and retrieve them when needed
 */

// Define the structure of a cached template
interface CachedTemplate {
  url: string;
  fileName: string;
  blob: Blob;
  timestamp: number;
  hash?: string; // Optional hash for version checking
}

// Cache expiration time (24 hours in milliseconds)
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

// IndexedDB database name and store name
const DB_NAME = 'TemplateCache';
const STORE_NAME = 'templates';
const DB_VERSION = 1;

/**
 * Initialize the template cache database
 * @returns A promise that resolves when the database is ready
 */
export const initTemplateCache = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('Error opening template cache database:', event);
      reject(new Error('Failed to open template cache database'));
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      // Create the templates object store if it doesn't exist
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        const store = db.createObjectStore(STORE_NAME, { keyPath: 'url' });
        store.createIndex('fileName', 'fileName', { unique: false });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
};

/**
 * Save a template to the cache
 * @param url The URL of the template
 * @param fileName The name of the template file
 * @param blob The template blob
 * @param hash Optional hash for version checking
 * @returns A promise that resolves when the template is cached
 */
export const cacheTemplate = async (
  url: string,
  fileName: string,
  blob: Blob,
  hash?: string
): Promise<void> => {
  try {
    const db = await initTemplateCache();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      
      const cachedTemplate: CachedTemplate = {
        url,
        fileName,
        blob,
        timestamp: Date.now(),
        hash
      };
      
      const request = store.put(cachedTemplate);
      
      request.onsuccess = () => {
        console.log(`Template cached successfully: ${fileName}`);
        resolve();
      };
      
      request.onerror = (event) => {
        console.error('Error caching template:', event);
        reject(new Error('Failed to cache template'));
      };
      
      transaction.oncomplete = () => {
        db.close();
      };
    });
  } catch (error) {
    console.error('Error in cacheTemplate:', error);
    throw error;
  }
};

/**
 * Get a template from the cache
 * @param url The URL of the template
 * @returns A promise that resolves with the cached template or null if not found
 */
export const getCachedTemplate = async (url: string): Promise<CachedTemplate | null> => {
  try {
    const db = await initTemplateCache();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readonly');
      const store = transaction.objectStore(STORE_NAME);
      
      const request = store.get(url);
      
      request.onsuccess = () => {
        const cachedTemplate = request.result as CachedTemplate;
        
        if (cachedTemplate) {
          // Check if the cache has expired
          const now = Date.now();
          if (now - cachedTemplate.timestamp > CACHE_EXPIRATION) {
            console.log(`Cached template expired: ${cachedTemplate.fileName}`);
            resolve(null);
          } else {
            console.log(`Template found in cache: ${cachedTemplate.fileName}`);
            resolve(cachedTemplate);
          }
        } else {
          console.log(`Template not found in cache: ${url}`);
          resolve(null);
        }
      };
      
      request.onerror = (event) => {
        console.error('Error retrieving cached template:', event);
        reject(new Error('Failed to retrieve cached template'));
      };
      
      transaction.oncomplete = () => {
        db.close();
      };
    });
  } catch (error) {
    console.error('Error in getCachedTemplate:', error);
    return null;
  }
};

/**
 * Clear expired templates from the cache
 * @returns A promise that resolves when expired templates are cleared
 */
export const clearExpiredTemplates = async (): Promise<void> => {
  try {
    const db = await initTemplateCache();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      
      const index = store.index('timestamp');
      const now = Date.now();
      const range = IDBKeyRange.upperBound(now - CACHE_EXPIRATION);
      
      const request = index.openCursor(range);
      
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result as IDBCursorWithValue;
        
        if (cursor) {
          console.log(`Removing expired template: ${cursor.value.fileName}`);
          cursor.delete();
          cursor.continue();
        }
      };
      
      request.onerror = (event) => {
        console.error('Error clearing expired templates:', event);
        reject(new Error('Failed to clear expired templates'));
      };
      
      transaction.oncomplete = () => {
        console.log('Expired templates cleared');
        db.close();
        resolve();
      };
    });
  } catch (error) {
    console.error('Error in clearExpiredTemplates:', error);
  }
};

/**
 * Create a File object from a cached template
 * @param cachedTemplate The cached template
 * @returns A File object
 */
export const createFileFromCachedTemplate = (cachedTemplate: CachedTemplate): File => {
  return new File([cachedTemplate.blob], cachedTemplate.fileName, {
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  });
};
