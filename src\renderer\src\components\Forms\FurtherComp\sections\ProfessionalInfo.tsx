import React from 'react'
import { FormSection } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionProps } from '../utils/types'

const ProfessionalInfo: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  // Handle address field changes
  const handleAddressChange = (address: string, postalCode?: string) => {
    updateFormData({
      workAddress: address,
      workPostalCode: postalCode || formData.workPostalCode
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Professional Information</h3>

      <FormSection title="Company Information" subtitle="Your work details">
        <div className="space-y-3">
          {/* Company Name and Trade/Profession side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Company Name</label>
                <input
                  type="text"
                  name="companyName"
                  value={formData.companyName || ''}
                  onChange={handleChange}
                  placeholder="Enter your company name"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Trade/Profession</label>
                <input
                  type="text"
                  name="tradeProfession"
                  value={formData.tradeProfession || ''}
                  onChange={handleChange}
                  placeholder="e.g. Engineer, Accountant, etc."
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </FormSection>

      <FormSection title="Work Address" subtitle="Your work location">
        <AddressInput
          label="Work Address"
          value={formData.workAddress || ''}
          postalCode={formData.workPostalCode || ''}
          onChange={(address, postalCode) =>
            handleAddressChange(address, postalCode)
          }
          placeholder="Enter your work address"
          isTextarea={false}
          required={true}
          postalCodeRequired={true}
        />
        <p className="text-xs text-stone-400 mt-1 mb-3">
          Enter your work address and postal code.
        </p>

        {/* Work Contact */}
        <div className="mt-3">
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Work Contact</label>
                <input
                  type="tel"
                  name="workNumber"
                  value={formData.workNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. ************"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default ProfessionalInfo
