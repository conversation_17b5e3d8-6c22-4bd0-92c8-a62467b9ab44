import React from 'react'
import { Loan } from '../../../types'

interface LoanCardClientInfoProps {
  loan: Loan
  highlightMatch: (text: string) => JSX.Element | string
}

const LoanCardClientInfo: React.FC<LoanCardClientInfoProps> = ({ loan, highlightMatch }) => {
  if (!loan.clients) {
    return null
  }

  return (
    <div className="overflow-hidden">
      <div className="px-3 py-3 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
        <p className="flex items-center gap-2 group">
          <span className="text-orange-400 font-medium">Name:</span>
          <span className="text-white truncate">
            {highlightMatch(
              `${loan.clients.first_name} ${loan.clients.last_name}`
            )}
          </span>
        </p>

        <p className="flex items-center gap-2 group">
          <span className="text-orange-400 font-medium">Email:</span>
          <span className="text-white truncate">
            {highlightMatch(loan.clients?.email || 'N/A')}
          </span>
        </p>

        <p className="flex items-center gap-2 group">
          <span className="text-orange-400 font-medium">Phone:</span>
          <span className="text-white truncate">
            {loan.clients?.phone
              ? highlightMatch(
                  loan.clients?.phone.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3')
                )
              : 'N/A'}
          </span>
        </p>

        <p className="flex items-center gap-2 group">
          <span className="text-orange-400 font-medium">ID:</span>
          <span className="text-white truncate">
            {loan.clients?.id_number
              ? highlightMatch(
                  loan.clients?.id_number.replace(
                    /(\d{6})(\d{4})(\d{3})/,
                    '$1 $2 $3'
                  )
                )
              : 'N/A'}
          </span>
        </p>
      </div>
    </div>
  )
}

export default React.memo(LoanCardClientInfo)
