import <PERSON>z<PERSON><PERSON> from 'pizzip';
import Docxtemplater from 'docxtemplater';
import { NewLicenceData } from '../../types/NewLicenceData';
import { FormData } from '../../types/FormData';
import { getCachedTemplate, cacheTemplate, createFileFromCachedTemplate, clearExpiredTemplates } from './TemplateCache';
import { DocumentProcessingError } from './DocumentProcessingError';
import FixInitiTagModule from "./FixInitiTagModule";

// Interface for the template data mapping
interface TemplateData {
  [key: string]: string | number | boolean | null | undefined;
}

/**
 * Creates a mapping of form data to template placeholders
 * @param formData The form data to map to template placeholders
 * @returns A mapping of template placeholders to form data values
 */
export const createTemplateDataMapping = (formData: NewLicenceData): TemplateData => {
  // Create a base template data object with only properties that exist in NewLicenceData
  let templateData: TemplateData = {
    // Application Type
    MainHF: formData.mainHF || false,
    AddHF: formData.addHF || false,

    // SAP 350 (A) DETAILS - Firearm received from details
    SAP350A_NAME: formData.sap350aName || '',
    SAP350A_ID_FAR: formData.sap350aIdFar || '',
    SAP350A_ADDRESS: formData.sap350aAddress || '',
    SAP350A_POSTAL: formData.sap350aPostal || '',
    SAP350A_DATE: formData.sap350aDate || '',

    // License Types
    S13: formData.s13 || false,
    S15: formData.s15 || false,
    S16: formData.s16 || false,
    S20: formData.s20 || false,
    S20A: formData.s20a || false,
    S20B: formData.s20b || false,
    S20C: formData.s20c || false,

    // Firearm Types
    Rifle: formData.rifle || false,
    Shotgun: formData.shotgun || false,
    Pistol: formData.pistol || false,
    Comb: formData.comb || false,
    OtherDesign: formData.otherDesign || false,
    OtherDesignE: formData.otherDesignE || '',

    // Firearm Action Types
    Semi: formData.semi || false,
    Auto: formData.auto || false,
    Man: formData.man || false,
    OtherF: formData.otherF || '',

    // Personal Information
    FULLNAME: formData.fullName || `${formData.firstName || ''} ${formData.lastName || ''}`,
    FIRSTNAME: formData.firstName || '',
    LASTNAME: formData.lastName || '',
    IDNUMBER: formData.idNumber || '',
    ID: formData.idNumber || '',  // Add explicit mapping for ID placeholder
    PHONENUMBER: formData.phoneNumber || '',
    EMAIL: formData.email || '',
    PHYSICALADDRESS: formData.physicalAddress || '',
    POSTALCODE: formData.postalCode || '',

    // Professional Information
    COMPANYNAME: formData.companyName || '',
    TRADEPROFESSION: formData.tradeProfession || '',
    WORKADDRESS: formData.workAddress || '',
    WORKNUMBER: formData.workNumber || '',
    WORKPOSTALCODE: formData.workPostalCode || '',

    // Firearm Details
    Make: formData.make || '',
    Model: formData.model || '',
    Caliber: formData.caliber || '',
    ENGG: formData.engg || '',
    PAFK: formData.pafk || '', // Physical address where firearm(s) is kept
    PAFKPOSTAL: formData.pafkPostal || '', // Postal Code for firearm address
    FLAP: formData.flap || '', // Name and surname of current owner/authorized person
    FLID: formData.flid || '', // Identification number of current owner/authorized person
    DESIGNATION: formData.designation || '', // Designation
    APDATE: formData.apDate || '', // Date
    APPLACE: formData.apPlace || '', // Place

    // Firearm component type
    BSN: formData.bsn || '',
    FSN: formData.fsn || '',
    RSN: formData.rsn || '',

    // Firearm component make
    BSNM: formData.bsnm || '',
    FSNM: formData.fsnm || '',
    RSNM: formData.rsnm || '',

    // Template specific fields
    TEMPLATENAME: formData.templateName || '',

    // Remove duplicate health declaration form fields - these are properly handled in the criminal history section below

    // Natural person fields
    NPEMAIL: formData.npemail || '',
    NPADDRESS: formData.npaddress || '',
    NPPOSTAL: formData.nppostal || '',
    NPID: formData.npid || '',
    NPSAID: formData.npsaid || '',
    NPIDNO: formData.npidno || '',
    NPDATEB: formData.npdateb || '',
    NPAGE: formData.npage || '',
    NPSEXMALE: formData.npsexmale || '',
    NPSEXFEMALE: formData.npsexfemale || '',
    NPTYPEOFRES: formData.nptypeofres || '',
    NPPROF: formData.npprof || '',
    NPECNAME: formData.npecname || '',
    NPECADD: formData.npecadd || '',
    NPECPOSTAL: formData.npecpostal || '',

    // Standard placeholders for Personal Information - Natural Person's Details
    // Personal Info
    FirstName: formData.npname || formData.firstName || '',
    LastName: formData.nplname || formData.lastName || '',
    Cell: formData.npcell || formData.phoneNumber || '',
    Email: formData.npemail || formData.email || '',
    Address: formData.npaddress || formData.physicalAddress || '',
    // Use NP_POSTALCODE to avoid duplicate with POSTALCODE
    NP_POSTALCODE: formData.nppostal || formData.postalCode || '',
    // Use NP_ID to avoid duplicate with ID
    NP_ID: formData.npid || formData.idNumber || '',
    PASSPORT: formData.passport || '',

    // Professional Info
    Company: formData.npecname || formData.companyName || '',
    Trade: formData.npprof || formData.tradeProfession || '',
    WorkA: formData.npecadd || formData.workAddress || '',
    WorkP: formData.npecpostal || formData.workPostalCode || '',
    WorkC: formData.npworkc || formData.workNumber || '',

    // Marital Status
    SINGLES: formData.maritalStatus === 'single',
    MARRIED: formData.maritalStatus === 'married',
    DIVORCED: formData.maritalStatus === 'divorced',
    WIDOWER: formData.maritalStatus === 'widower',
    WIDOW: formData.maritalStatus === 'widow',

    // Spouse Particulars
    SPOUSEID: formData.spouseIdType === 'spouseId',
    SPOUSEPORT: formData.spouseIdType === 'spousePort',
    SPOUSEIDNO: formData.spouseIdNo || '',
    SPOUSEPASSN: formData.spousePassN || '',
    SPOUSEFULLNAME: formData.spouseFullName || '',

    // Personal Information Applicant Particulars
    SAID: formData.npsaid || formData.citizenType === 'saId',
    FID: formData.npidno || formData.citizenType === 'fId',
    PermRes: formData.permRes || false,
    Initials: formData.npinitials || formData.initials || '',
    AGE: formData.npage || formData.age || '',
    SEXM: formData.npsexmale || formData.sexM || false,
    SEXF: formData.npsexfemale || formData.sexF || false,
    BirthDate: formData.npdateb || formData.birthDate || '',
    HOUSETYPE: formData.nptypeofres || formData.houseType || '',

    // Current Owner Types
    PCOA: formData.pcoa || false,
    PCOB: formData.pcob || false,
    PCOC: formData.pcoc || false,
    PCOE: formData.pcoe || false,

    // Mappings with PO prefix for Private Owner section
    POFirstName: formData.firstName || '',
    POLastName: formData.lastName || '',
    POInitials: formData.initials || '',
    POCell: formData.cell || '',
    POWorkC: formData.workC || '',
    POEmail: formData.email || '',
    POAddress: formData.address || '',
    POPOSTALCODE: formData.postalCode || '',
    POID: formData.idNumber || '',
    POPASSPORT: formData.passport || '',

    // Firearm dealer/broker fields
    FDBCALL: formData.fdbcall || '',
    FDBMAIL: formData.fdbmail || '',
    FDRPNS: formData.fdrpns || '',
    FDRPIDSA: formData.fdrpidsa || '',
    FDRPIDNO: formData.fdrpidno || '',
    FDRPID: formData.fdrpid || '',
    FDRPCALL: formData.fdrpcall || '',
    FDRPADD: formData.fdrpadd || '',
    FDRPOSTAL: formData.fdrpostal || '',

    // Personal Info - Using different names to avoid duplicates
    PersonName: formData.fullName || `${formData.firstName || ''} ${formData.lastName || ''}`,
    // AGE, BirthDate, HOUSETYPE, SEXM, SEXF, SAID, FID, and marital status fields are already defined above
    // Removing duplicates to fix the errors

  // Handle boolean properties that need to be mapped to Yes/No values
    // Add executor information
    EXECUTORNAME: formData.executorFullName || '',
    EXECUTORNUMBER: formData.executorNumber || '',
    ExecutorAddress: formData.executorPhysicalAddress || '',

    // Competency Form
    // Use the correct placeholders for the Competency Certificate section
    TradeFirearm: formData.tradeFirearm || false,
    PossessFirearm: formData.possessFirearm || false,
    // New competency type checkboxes
    CompHandgun: formData.compHandgun || false,
    CompRifle: formData.compRifle || false,
    CompShotgun: formData.compShotgun || false,
    CompSelfLoading: formData.compSelfLoading || false,
    F2AB: formData.f2ab || '',
    F3: formData.f3 || '',
    F4: formData.f4 || '',
    // Keep other competency form fields
    // Pistol, Rifle, and Shotgun are already defined above
    Revolver: formData.revolver,
    SelfLoading: formData.selfLoading ? 'X' : '',
    SelfLoadingT: formData.selfLoading ? 'Self-Loading' : '',

    // Applicant Particulars - PermRes is already defined above

    // Spouse Particulars are already mapped above

    // Training Institution
    NameInst: formData.nameInst,
    SerialCert: formData.serialCert,
    CertIssueDate: formData.certIssueDate,
    CertExpiryDate: formData.certExpiryDate,

    // Offenses - Support both NewComp and NewLicence field names
    H5A: formData.offenseYes || formData.h5a || false,
    H5B: formData.offenseNo || formData.h5b || false,
    'H5.1': formData.policeStation || formData.h51 || '',
    'H5.2': formData.caseNumber || formData.h52 || '',
    'H5.3': formData.charge || formData.h53 || '',
    'H5.4': formData.outcomeVerdict || formData.h54 || '',

    // Pending Cases - Support both NewComp and NewLicence field names
    H6A: formData.pendingCaseYes || formData.h6a || false,
    H6B: formData.pendingCaseNo || formData.h6b || false,
    'H6.1': formData.pendingCasePoliceStation || formData.h61 || '',
    'H6.2': formData.pendingCaseCaseNumber || formData.h62 || '',
    'H6.A3': formData.pendingCaseOffence || formData.h6a3 || '',

    // Lost/Stolen Firearms - Support both NewComp and NewLicence field names
    H7A: formData.lostStolenYes || formData.h7a || false,
    H7B: formData.lostStolenNo || formData.h7b || false,
    'H7.1': formData.lostStolenPoliceStation || formData.h71 || '',
    'H7.2': formData.lostStolenCaseNumber || formData.h72 || '',
    'H7.3': formData.lostStolenCircumstances || formData.h73 || '',
    'H7.4': formData.lostStolenFirearmDetails || formData.h74 || '',

    // Negligence Investigation - Support both NewComp and NewLicence field names
    H8A: formData.investigationYes || formData.h8a || false,
    H8B: formData.investigationNo || formData.h8b || false,
    'H8.1': formData.investigationPoliceStation || formData.h81 || '',
    'H8.2': formData.investigationCaseNumber || formData.h82 || '',
    'H8.3': formData.investigationCharge || formData.h83 || '',
    'H8.4': formData.investigationOutcome || formData.h84 || '',

    // Declared Unfit - Support both NewComp and NewLicence field names
    H9A: formData.declaredUnfitYes || formData.h9a || false,
    H9B: formData.declaredUnfitNo || formData.h9b || false,
    'H9.1': formData.declaredUnfitPoliceStation || formData.h91 || '',
    'H9.2': formData.declaredUnfitCaseNumber || formData.h92 || '',
    'H9.3': formData.declaredUnfitCharge || formData.h93 || '',
    'H9.4': formData.declaredUnfitDate || formData.h94 || '',
    'H9.5': formData.declaredUnfitPeriod || formData.h95 || '',

    // Confiscated - Support both NewComp and NewLicence field names
    // Only one of these should be marked with an X in the document
    // These placeholders are handled specially later to ensure only one gets an X
    // Using the correct placeholders as specified:
    // - {H10A} - Confiscated Yes (Mark with X)
    // - {H10B} - Confiscated No (Mark with X)
    // - {H10.1} - Confiscated Police Station
    // - {H10.2} - Confiscated Case Number
    // - {H10.3} - Confiscated Circumstances
    // - {H10.4} - Confiscated Outcome/Verdict
    H10A: (formData.confiscatedYes || formData.h10a) ? true : false,
    H10B: (formData.confiscatedNo || formData.h10b) ? true : false,
    'H10.1': formData.confiscatedPoliceStation || formData.h101 || '',
    'H10.2': formData.confiscatedCaseNumber || formData.h102 || '',
    'H10.3': formData.confiscatedCircumstances || formData.h103 || '',
    'H10.4': formData.confiscatedOutcome || formData.h104 || '',

    // Protection Allegations - Support both NewComp and NewLicence field names
    H11A: formData.protectionAllegationsYes || (formData as any).h11a || false,
    H11B: formData.protectionAllegationsNo || (formData as any).h11b || false,
    'H11.1': formData.protectionAllegationsDetails || (formData as any).h111 || '',

    // Denied Firearm License/Permit - Support both NewComp and NewLicence field names
    H12A: formData.deniedFirearmYes || (formData as any).h12a || false,
    H12B: formData.deniedFirearmNo || (formData as any).h12b || false,
    H12A1: formData.deniedFirearmReason || (formData as any).h12a1 || '',
    'H12.1': formData.deniedFirearmPoliceStation || (formData as any).h121 || '',
    'H12.2': formData.deniedFirearmCaseNumber || (formData as any).h122 || '',

    // Under Age of 21
    'H17.1A': formData.conductBusiness,
    'H17.1B': formData.gainfullyEmployed,
    'H17.1C': formData.dedicatedHunter,
    'H17.1D': formData.dedicatedSportPersonal,
    'H17.1E': formData.privateCollector,
    'H17.1F': formData.publicCollector,
    'H17.1G': formData.otherUnderAge21,
    'H17.1H': formData.otherDetails,

    // Certificate details
    'D1.6B1': formData.certNumber,
    'D1.6C1': formData.handgunCertIssueDate,
    'D1.6D1': formData.handgunCertExpiryDate,

    // Competency Certificate Types
    'D1.6A1': formData.handgunType,

    'D1.6A2': formData.handgunRifleType,
    'D1.6B2': formData.handgunRifleCertNumber,
    'D1.6C2': formData.handgunRifleCertIssueDate,
    'D1.6D2': formData.handgunRifleCertExpiryDate,

    'D1.6A3': formData.rifleType,
    'D1.6B3': formData.rifleCertNumber,
    'D1.6C3': formData.rifleCertIssueDate,
    'D1.6D3': formData.rifleCertExpiryDate,

    'D1.6A4': formData.shotgunType,
    'D1.6B4': formData.shotgunCertNumber,
    'D1.6C4': formData.shotgunCertIssueDate,
    'D1.6D4': formData.shotgunCertExpiryDate,

    'D1.6A5': formData.shotgunHandgunType,
    'D1.6B5': formData.shotgunHandgunCertNumber,
    'D1.6C5': formData.shotgunHandgunCertIssueDate,
    'D1.6D5': formData.shotgunHandgunCertExpiryDate,

    'D1.6A6': formData.rifleShotgunType,
    'D1.6B6': formData.rifleShotgunCertNumber,
    'D1.6C6': formData.rifleShotgunCertIssueDate,
    'D1.6D6': formData.rifleShotgunCertExpiryDate,

    'D1.6A7': formData.handgunRifleShotgunType,
    'D1.6B7': formData.handgunRifleShotgunCertNumber,
    'D1.6C7': formData.handgunRifleShotgunCertIssueDate,
    'D1.6D7': formData.handgunRifleShotgunCertExpiryDate,

    'D1.6A8': formData.handMachineCarbineType,
    'D1.6B8': formData.handMachineCarbineCertNumber,
    'D1.6C8': formData.handMachineCarbineCertIssueDate,
    'D1.6D8': formData.handMachineCarbineCertExpiryDate,

    'D1.6A9': formData.handgunHandMachineType,
    'D1.6B9': formData.handgunHandMachineCertNumber,
    'D1.6C9': formData.handgunHandMachineCertIssueDate,
    'D1.6D9': formData.handgunHandMachineCertExpiryDate,

    'D1.6A10': formData.handgunRifleHandMachineType,
    'D1.6B10': formData.handgunRifleHandMachineCertNumber,
    'D1.6C10': formData.handgunRifleHandMachineCertIssueDate,
    'D1.6D10': formData.handgunRifleHandMachineCertExpiryDate,

    'D1.6A11': formData.handgunRifleShotgunHandMachineType,
    'D1.6B11': formData.handgunRifleShotgunHandMachineCertNumber,
    'D1.6C11': formData.handgunRifleShotgunHandMachineCertIssueDate,
    'D1.6D11': formData.handgunRifleShotgunHandMachineCertExpiryDate,

    'D1.6A12': formData.rifleHandMachineType,
    'D1.6B12': formData.rifleHandMachineCertNumber,
    'D1.6C12': formData.rifleHandMachineCertIssueDate,
    'D1.6D12': formData.rifleHandMachineCertExpiryDate,

    'D1.6A13': formData.rifleShotgunHandMachineType,
    'D1.6B13': formData.rifleShotgunHandMachineCertNumber,
    'D1.6C13': formData.rifleShotgunHandMachineCertIssueDate,
    'D1.6D13': formData.rifleShotgunHandMachineCertExpiryDate,

    'D1.6A14': formData.shotgunHandMachineType,
    'D1.6B14': formData.shotgunHandMachineCertNumber,
    'D1.6C14': formData.shotgunHandMachineCertIssueDate,
    'D1.6D14': formData.shotgunHandMachineCertExpiryDate,

    // Competency Expiry Status
    '15A': formData.competencyExpiry90DaysBeforeYes,
    '15B': formData.competencyExpiry90DaysBeforeNo,
    '15C': formData.competencyExpiry90DaysBeforeReason,

    '16A': formData.afterExpiryYes,
    '16B': formData.afterExpiryNo,
    '16C': formData.afterExpiryReason,

    // Safe Storage
    SAFEYES: formData.safeYes || false,
    SAFENO: formData.safeNo || false,
    SAFEH: formData.safeH || false,
    SAFER: formData.safeR || false,
    SAFES: formData.safeS || false,
    SAFED: formData.safeD || false,
    SAFESE: formData.safeSe || '',
    SAFEDINFO: formData.safeDInfo || '',
    SAFEMOUNTYES: formData.safeMountYes || false,
    SAFEMOUNTNO: formData.safeMountNo || false,
    SAFEWALL: formData.safeWall || false,
    SAFEFLOOR: formData.safeFloor || false
  };

  // Handle boolean properties
  const booleanFields = [
    // NewComp form field names
    'pendingCaseYes', 'pendingCaseNo', 'lostStolenYes', 'lostStolenNo',
    'protectionAllegationsYes', 'protectionAllegationsNo',
    'deniedFirearmYes', 'deniedFirearmNo',
    'afterExpiryYes', 'afterExpiryNo',
    // NewLicence form field names
    'h5a', 'h5b', 'h6a', 'h6b', 'h7a', 'h7b', 'h8a', 'h8b', 'h9a', 'h9b',
    // Exclude h10a and h10b since they're handled separately with H10A and H10B
    'h11a', 'h11b', 'h12a', 'h12b'
  ];

  booleanFields.forEach(field => {
    if (field in formData) {
      const value = formData[field as keyof NewLicenceData];
      if (typeof value === 'boolean') {
        // Convert boolean to 'Yes' or 'No' string format for template
        templateData[field] = value ? 'Yes' : 'No';

        // Also create uppercase version
        const upperField = field.toUpperCase();
        templateData[upperField] = value ? 'YES' : 'NO';
      }
    }
  });

  // Special case for self-loading type
  if (formData.selfLoading) {
    templateData.SelfLoadingT = 'Self-Loading';
  }

  // Special handling for Firearm Dealer in Current Owner section
  if (formData.pcob) {
    // If Firearm Dealer is selected, only use these specific placeholders
    // and clear any other current owner related placeholders
    const firearmDealerPlaceholders = {
      // Keep only these specific placeholders for Firearm Dealer
      FDRCN: formData.fdrcn || '',
      FDTAS: formData.fdtas || '',
      FDFARN: formData.fdfarn || '',
      FDBADRE: formData.fdbadre || '',
      FDPOSTAL: formData.fdpostal || '',
      FDBCALL: formData.fdbcall || '',
      FDBMAIL: formData.fdbmail || '',
      FDRPNS: formData.fdrpns || '',
      FDRPIDSA: formData.fdrpidsa || false,
      FDRPIDNO: formData.fdrpidno || false,
      FDRPID: formData.fdrpid || '',
      FDRPCALL: formData.fdrpcall || '',
      FDRPADD: formData.fdrpadd || '',
      FDRPOSTAL: formData.fdrpostal || '',
      // Keep the current owner type selection
      PCOB: true,
      PCOA: false,
      PCOC: false,
      PCOE: false
    };

    // Clear any other current owner related placeholders
    // by creating a new templateData object with only the firearm dealer placeholders
    // for the current owner section
    templateData = {
      ...templateData,
      ...firearmDealerPlaceholders
    };
  }

  // Special handling for Company in Current Owner section
  if (formData.pcoc) {
    // If Company is selected, only use these specific placeholders
    // and clear any other current owner related placeholders
    const companyPlaceholders = {
      // Keep only these specific placeholders for Company
      CRCN: formData.crcn || '',
      CTN: formData.ctn || '',
      CFARN: formData.cfarn || '',
      CPADD: formData.cpadd || '',
      CPOSTAL: formData.cpostal || '',
      CBTN: formData.cbtn || '',
      CCEM: formData.ccem || '',
      CRPNS: formData.crpns || '',
      CRPIDSA: formData.crpidsa || false,
      CRPIDNO: formData.crpidno || false,
      CRPID: formData.crpid || '',
      CRPCALL: formData.crpcall || '',
      CRPADD: formData.crpadd || '',
      CRPOSTAL: formData.crpostal || '',
      // Keep the current owner type selection
      PCOC: true,
      PCOA: false,
      PCOB: false,
      PCOE: false
    };

    // Clear any other current owner related placeholders
    // by creating a new templateData object with only the company placeholders
    // for the current owner section
    templateData = {
      ...templateData,
      ...companyPlaceholders
    };
  }

  // Special handling for Estate in Current Owner section
  if (formData.pcoe) {
    // If Estate is selected, only use these specific placeholders
    // and clear any other current owner related placeholders
    const estatePlaceholders = {
      // Keep only these specific placeholders for Estate
      EXECUTOR: formData.executor || false,
      ADMINISTRATOR: formData.administrator || false,
      CURATORSHIP: formData.curatorship || false,
      TRUST: formData.trust || false,
      DELAST: formData.deLast || '',
      DEFULLNAME: formData.deFullName || '',
      DEINITIALS: formData.deInitials || '',
      IDOF: formData.idOf || '',
      DEENAME: formData.deEName || '',
      DEEIDNO: formData.deEIdNo || '',
      DEEIDSA: formData.deEIdSa || false,
      DEEID: formData.deEId || '',
      DEEADD: formData.deEAdd || '',
      DEEPOSTAL: formData.deEPostal || '',
      DEECELL: formData.deECell || '',
      DEEEMAIL: formData.deEEmail || '',
      // Keep the current owner type selection
      PCOE: true,
      PCOA: false,
      PCOB: false,
      PCOC: false
    };

    // Clear any other current owner related placeholders
    // by creating a new templateData object with only the estate placeholders
    // for the current owner section
    templateData = {
      ...templateData,
      ...estatePlaceholders
    };
  }

  // Special handling for Juristic Person's Details section
  // This ensures all Juristic Person's Details placeholders get replaced in the document
  if (formData.pcoc) {
    // Map all Juristic Person's Details placeholders
    const juristicPersonPlaceholders = {
      // Juristic Person's Details placeholders
      JPNAME: formData.jpname || '', // Registered company name
      JPTN: formData.jptn || '', // Trading as name
      JPFARN: formData.jpfarn || '', // FAR number
      JPADD: formData.jpadd || '', // Postal address & Business address
      JPPOSTAL: formData.jppostal || '', // Postal Code
      JPBTN: formData.jpbtn || '', // Business telephone number
      JPEM: formData.jpem || '', // E-mail address
      JPFRB: formData.jpfrb || '', // Firearm registered TO business
      JPFRBNO: formData.jpfrbno || '', // Number of persons employed by the business to handle firearms
      JPRPNS: formData.jprpns || '', // Responsible person (Name and surname)
      JPRPIDSA: formData.jprpidsa || false, // Type of identification Responsible person (Mark with an X) for South African
      JPRPIDNO: formData.jprpidno || false, // Type of identification Responsible person (Mark with an X) for NONE South African
      JPRPID: formData.jprpid || '', // Identity number of responsible person
      JPRPCALL: formData.jprpcall || '', // Cellphone number of responsible person
      JPRPADD: formData.jprpadd || '', // Physical address of responsible person
      JPRPOSTAL: formData.jprpostal || '' // Postal Code of responsible person
    };

    // Add the Juristic Person's Details placeholders to the template data
    templateData = {
      ...templateData,
      ...juristicPersonPlaceholders
    };
  }

  // Special handling for Association Membership section
  // This ensures all Association Membership placeholders get replaced in the document
  // Map all Association Membership placeholders
  const associationMembershipPlaceholders = {
    // Association Membership placeholders - don't set F5A and F5B here
    // as they will be handled specially below
    F6: formData.f6 || '', // Association Name (Fill in)
    ASSFARN: formData.assFarn || '', // Association FAR number (Fill in)
    F7: formData.f7 || '', // Membership Number (Fill in)
    F8: formData.f8 || '', // Date Joined Association (Fill in)
    ASSEXPIRE: formData.assExpire || '' // Date of Expiry of Membership (Fill in)
  };

  // Add the Association Membership placeholders to the template data
  templateData = {
    ...templateData,
    ...associationMembershipPlaceholders
  };

  // Special handling for confiscated firearm fields
  // Ensure that only one option gets an X in the document
  // This is separate from the booleanFields handling to ensure we use 'X' instead of 'Yes'/'No'
  if (formData.h10a || formData.confiscatedYes) {
    // If Yes is selected, set Yes to X and No to empty
    templateData.H10A = 'X';
    templateData.H10B = '';
  } else if (formData.h10b || formData.confiscatedNo) {
    // If No is selected, set No to X and Yes to empty
    templateData.H10A = '';
    templateData.H10B = 'X';
  }

  // Special handling for Association Membership fields
  // Ensure that only one option gets an X in the document
  if (formData.f5a) {
    // If Yes is selected, set Yes to X and No to empty
    templateData.F5A = 'X';
    templateData.F5B = '';
  } else if (formData.f5b) {
    // If No is selected, set No to X and Yes to empty
    templateData.F5A = '';
    templateData.F5B = 'X';
  }

  // Special handling for Additional Firearm Licence Holders fields
  // Ensure that only one option gets an X in the document
  if (formData.afhy) {
    // If Yes is selected, set Yes to X and No to empty
    templateData.POAFHY = 'X';
    templateData.POAFHN = '';
  } else if (formData.afhn) {
    // If No is selected, set No to X and Yes to empty
    templateData.POAFHY = '';
    templateData.POAFHN = 'X';
  }

  // Handle Firearms in Possession table
  if (formData.firearmsInPossession && Array.isArray(formData.firearmsInPossession)) {
    // Process each row in the firearms in possession table
    formData.firearmsInPossession.forEach((firearm, index) => {
      const rowNum = index + 1;
      if (rowNum <= 10) { // We only support 10 rows
        templateData[`FIP_TYPE_${rowNum}`] = firearm.type || '';
        templateData[`FIP_CALIBRE_${rowNum}`] = firearm.calibre || '';
        templateData[`FIP_MAKE_${rowNum}`] = firearm.make || '';
        templateData[`FIP_BARREL_${rowNum}`] = firearm.barrelSerialNo || '';
        templateData[`FIP_FRAME_${rowNum}`] = firearm.frameSerialNo || '';
        templateData[`FIP_LICENCE_${rowNum}`] = firearm.licenceNo || '';
      }
    });

    // Initialize empty placeholders for unused rows
    for (let i = (formData.firearmsInPossession.length + 1); i <= 10; i++) {
      templateData[`FIP_TYPE_${i}`] = '';
      templateData[`FIP_CALIBRE_${i}`] = '';
      templateData[`FIP_MAKE_${i}`] = '';
      templateData[`FIP_BARREL_${i}`] = '';
      templateData[`FIP_FRAME_${i}`] = '';
      templateData[`FIP_LICENCE_${i}`] = '';
    }
  } else {
    // Initialize all placeholders as empty if no firearms data
    for (let i = 1; i <= 10; i++) {
      templateData[`FIP_TYPE_${i}`] = '';
      templateData[`FIP_CALIBRE_${i}`] = '';
      templateData[`FIP_MAKE_${i}`] = '';
      templateData[`FIP_BARREL_${i}`] = '';
      templateData[`FIP_FRAME_${i}`] = '';
      templateData[`FIP_LICENCE_${i}`] = '';
    }
  }

  return templateData;
};

/**
 * Creates a mapping of SAPS Inspection Report form data to template placeholders
 * @param formData The form data to map to template placeholders
 * @returns A mapping of template placeholders to form data values
 */
export const createInspectionReportMapping = (formData: FormData): TemplateData => {
  // Create a template data object with the SAPS Inspection Report placeholders
  let templateData: TemplateData = {
    // Representative Information
    INS_NAME: formData.representativeName || '',
    INS_ID: formData.representativeId || '',
    INS_ClientN: formData.clientName || '',
    INS_ClientID: formData.clientId || '',

    // Firearm Type
    INS_Pistol: formData.pistol || false,
    INS_Rifle: formData.rifle || false,
    INS_Shotgun: formData.shotgun || false,
    INS_Revolver: formData.revolver || false,
    INS_Comb: formData.combination || false,
    INS_hmc: formData.carbine || false,
    INS_FOther: formData.otherFirearmType || false,
    INS_Details: formData.otherFirearmTypeDetails || '',

    // Firearm Details
    INS_BSERIAL: formData.barrelSerialNumber || '',
    INS_BMAKE: formData.barrelMake || '',
    INS_FSERIAL: formData.frameSerialNumber || '',
    INS_FSMAKE: formData.frameMake || '',
    INS_RSERIAL: formData.receiverSerialNumber || '',
    INS_RMAKE: formData.receiverMake || '',

    // Action Type
    INS_Manual: formData.manual || false,
    INS_Semi: formData.semiAutomatic || false,
    INS_Auto: formData.automatic || false,
    INS_Bolt: formData.bolt || false,
    INS_Bneck: formData.breakneck || false,
    INS_Pump: formData.pump || false,
    INS_Lever: formData.lever || false,
    INS_Cylinder: formData.cylinder || false,
    INS_FBlock: formData.fallingBlock || false,
    INS_CBL: formData.cappingBreechLoader || false,
    INS_AOther: formData.otherActionTypeChecked || false,
    INS_ADetails: formData.otherActionTypeDetails || '',
    INS_ENGMake: formData.engravedMake || '',
    INS_COMMENTS: formData.comments || '',

    // Additional Information
    INS_COUNTRY: formData.countryOfOrigin || '',
    INS_DATE: formData.reportDate || ''
  };

  return templateData;
};

/**
 * Creates a mapping of E350 Information form data to template placeholders
 * @param formData The form data to map to template placeholders
 * @returns A mapping of template placeholders to form data values
 */
export const createE350InformationMapping = (formData: FormData): TemplateData => {
  // Create a template data object with the E350 Information placeholders
  let templateData: TemplateData = {
    // Period Information
    '350_DATE': formData.periodDateFrom || '',

    // Firearm Type
    '350_Pistol': formData.pistol || false,
    '350_Rifle': formData.rifle || false,
    '350_Shotgun': formData.shotgun || false,
    '350_Revolver': formData.revolver || false,
    '350_Comb': formData.combination || false,
    '350_hmc': formData.carbine || false,

    // Firearm Details
    '350_Make': formData.make || '',
    '350_Model': formData.model || '',
    '350_Serial': formData.serialNumber || '',
    '350_Cal': formData.caliber || '',

    // Received From
    '350_RName': formData.receiverName || '',
    '350_ID': formData.receiverId || '',
    '350_DREC': formData.dateReceivedFrom || ''
  };

  return templateData;
};

/**
 * Creates a mapping of Annexure A form data to template placeholders
 * @param formData The form data to map to template placeholders
 * @returns A mapping of template placeholders to form data values
 */
export const createAnnexureAMapping = (formData: FormData): TemplateData => {
  // Create a template data object with the Annexure A placeholders
  let templateData: TemplateData = {
    // PERSONAL INFORMATION
    AN_FirstName: formData.firstName || '',
    AN_LastName: formData.lastName || '',
    AN_Initials: formData.initials || '',
    AN_ID: formData.idNumber || '',
    AN_PASSPORT: formData.passport || '',
    AN_Email: formData.email || '',
    AN_Cell: formData.phoneNumber || '',
    AN_WorkC: formData.workNumber || '',
    AN_Address: formData.physicalAddress || '',
    AN_POSTALCODE: formData.postalCode || '',

    // CITIZENSHIP STATUS
    AN_SAIDX: formData.saId || false,
    AN_PASSPORTX: formData.fId || false,

    // FIREARM APPLICATION TYPES
    AN_MainHF: formData.mainHF || false,
    AN_AddHF: formData.addHF || false,
    AN_S13: formData.s13 || false,
    AN_S15: formData.s15 || false,
    AN_S16: formData.s16 || false,
    AN_S20: formData.s20 || false,
    AN_S20A: formData.s20a || false,
    AN_S20B: formData.s20b || false,
    AN_S20C: formData.s20c || false,

    // FIREARM DETAILS
    AN_Make: formData.make || '',
    AN_Model: formData.model || '',
    AN_SerialNumber: formData.serialNumber || '',
    AN_Caliber: formData.caliber || '',
    AN_ENGG: formData.engravedDetails || '',

    // FIREARM TYPE
    AN_Pistol: formData.pistol || false,
    AN_Rifle: formData.rifle || false,
    AN_Shotgun: formData.shotgun || false,
    AN_Revolver: formData.revolver || false,
    AN_Comb: formData.combination || false,
    AN_hmc: formData.carbine || false,

    // FIREARM ACTION TYPE
    AN_Semi: formData.semiAutomatic || false,
    AN_Auto: formData.automatic || false,
    AN_Man: formData.manual || false,
    AN_OtherF: formData.otherActionType || '',

    // FIREARM COMPONENTS
    AN_BSN: formData.barrelSerialNumber || '',
    AN_FSN: formData.frameSerialNumber || '',
    AN_RSN: formData.receiverSerialNumber || '',
    AN_BSNM: formData.barrelMake || '',
    AN_FSNM: formData.frameMake || '',
    AN_RSNM: formData.receiverMake || '',

    // COMPETENCY CERTIFICATE TYPES
    'AN_D1.6A1': formData.handgunType || false,
    'AN_D1.6B1': formData.certNumber || '',
    'AN_D1.6C1': formData.certIssueDate || '',
    'AN_D1.6D1': formData.certExpiryDate || ''
  };

  return templateData;
};

/**
 * Processes the template data to format values correctly
 * @param templateData The raw template data mapping
 * @returns Processed template data with formatted values
 */
export const processTemplateData = (templateData: TemplateData): TemplateData => {
  // Ensure all Mark with X placeholders are handled
  const markWithXFields = [
    // Firearm Dealer ID Type
    'FDRPIDSA', 'FDRPIDNO',
    // Company ID Type
    'CRPIDSA', 'CRPIDNO',
    // Juristic Person ID Type
    'JPRPIDSA', 'JPRPIDNO',
    // Estate Type and ID Type
    'EXECUTOR', 'ADMINISTRATOR', 'CURATORSHIP', 'TRUST', 'DEEIDSA',
    // Association Membership
    'F5A', 'F5B',
    // Additional Firearm Licence Holders
    'POAFHY', 'POAFHN',
    // Application Type
    'MainHF', 'AddHF',
    // License Types
    'S13', 'S15', 'S16', 'S20', 'S20A', 'S20B', 'S20C',
    // Firearm Types
    'Rifle', 'Shotgun', 'Pistol', 'Comb', 'OtherDesign',
    // Firearm Action Types
    'Semi', 'Auto', 'Man',
    // Current Owner Types
    'PCOA', 'PCOB', 'PCOC', 'PCOE',
    // Safe Storage
    'SAFEYES', 'SAFENO', 'SAFEH', 'SAFER', 'SAFES', 'SAFED', 'SAFEMOUNTYES', 'SAFEMOUNTNO', 'SAFEWALL', 'SAFEFLOOR',
    // Marital Status
    'SINGLES', 'MARRIED', 'DIVORCED', 'WIDOWER', 'WIDOW',
    // Spouse Particulars
    'SPOUSEID', 'SPOUSEPORT',
    // Personal Information Applicant Particulars
    'SAID', 'FID', 'PermRes', 'SEXM', 'SEXF',
    // Competency Certificate
    'TradeFirearm', 'PossessFirearm', 'CompHandgun', 'CompRifle', 'CompShotgun', 'CompSelfLoading',
    // Criminal History Yes/No Fields
    'H5A', 'H5B', 'H6A', 'H6B', 'H7A', 'H7B', 'H8A', 'H8B', 'H9A', 'H9B', 'H10A', 'H10B', 'H11A', 'H11B', 'H12A', 'H12B',
    // SAPS Inspection Report Fields
    'INS_Pistol', 'INS_Rifle', 'INS_Shotgun', 'INS_Revolver', 'INS_Comb', 'INS_hmc', 'INS_FOther',
    'INS_Manual', 'INS_Semi', 'INS_Auto', 'INS_Bolt', 'INS_Bneck', 'INS_Pump', 'INS_Lever', 'INS_Cylinder', 'INS_FBlock', 'INS_CBL', 'INS_AOther',
    // E350 Information Fields
    '350_Pistol', '350_Rifle', '350_Shotgun', '350_Revolver', '350_Comb', '350_hmc',
    // Annexure A Fields
    'AN_SAIDX', 'AN_PASSPORTX', 'AN_MainHF', 'AN_AddHF', 'AN_S13', 'AN_S15', 'AN_S16', 'AN_S20', 'AN_S20A', 'AN_S20B', 'AN_S20C',
    'AN_Pistol', 'AN_Rifle', 'AN_Shotgun', 'AN_Revolver', 'AN_Comb', 'AN_hmc', 'AN_Semi', 'AN_Auto', 'AN_Man', 'AN_D1.6A1'
  ];

  markWithXFields.forEach((key) => {
    if (templateData[key] === true || templateData[key] === 'true' || templateData[key] === 'X' || templateData[key] === 'YES') {
      templateData[key] = 'X';
    } else {
      templateData[key] = '';
    }
  });
  // Capitalize all string values and process boolean values
  const processedData = Object.fromEntries(
    Object.entries(templateData).map(([key, value]) => {
      if (typeof value === 'boolean') {
        return [key, value ? 'X' : ''] // Keep the X for checkboxes
      }
      if (typeof value === 'string') {
        // Special handling for ID fields to ensure they're properly processed
        if (key === 'ID' || key === 'IDNUMBER' || key === 'POID' || key === 'FDRPID' || key === 'CRPID' || key === 'DEEID' || key === 'IDOF' || key === 'JPRPID') {
          // Make sure ID values are preserved exactly as entered
          return [key, value];
        }

        // Remove any placeholder text wrapped in {}
        let processedValue = value.toUpperCase(); // Convert strings to uppercase
        // Remove any placeholder text that might be in the value itself
        processedValue = processedValue.replace(/\{[^\}]+\}/g, '');
        return [key, processedValue];
      }
      if (value === undefined || value === null) {
        return [key, ''] as [string, string];
      }
      // Convert other types to uppercase strings and remove placeholders
      let processedValue = String(value).toUpperCase();
      processedValue = processedValue.replace(/\{[^\}]+\}/g, '');
      return [key, processedValue] as [string, string];
    })
  )

  // Add SelfLoadingT directly to processed data
  if (templateData.SelfLoadingT) {
    processedData.SelfLoadingT = 'Self-Loading';
  }

  // Special handling for confiscated firearm fields
  // Ensure that only one option gets an X in the document
  // This is a second check to make sure the confiscated firearm fields are properly handled
  // and that only one of H10A or H10B gets an X
  if (templateData.H10A === 'X') {
    processedData.H10A = 'X';
    processedData.H10B = '';
  } else if (templateData.H10B === 'X') {
    processedData.H10A = '';
    processedData.H10B = 'X';
  }

  // Special handling for Association Membership fields
  // Ensure that only one option gets an X in the document
  // This is a second check to make sure the Association Membership fields are properly handled
  // and that only one of F5A or F5B gets an X
  if (templateData.F5A === 'X') {
    processedData.F5A = 'X';
    processedData.F5B = '';
  } else if (templateData.F5B === 'X') {
    processedData.F5A = '';
    processedData.F5B = 'X';
  }

  // Special handling for Additional Firearm Licence Holders fields
  // Ensure that only one option gets an X in the document
  // This is a second check to make sure the Additional Firearm Licence Holders fields are properly handled
  // and that only one of POAFHY or POAFHN gets an X
  if (templateData.POAFHY === 'X') {
    processedData.POAFHY = 'X';
    processedData.POAFHN = '';
  } else if (templateData.POAFHN === 'X') {
    processedData.POAFHY = '';
    processedData.POAFHN = 'X';
  }

  return processedData;
}


/**
 * Validates a template before processing
 * @param zip The PizZip instance containing the template
 * @returns True if the template is valid, false otherwise
 */
const validateTemplate = (zip: PizZip): boolean => {
  try {
    // Check if the template has the expected structure
    if (!zip.files['word/document.xml']) {
      throw new DocumentProcessingError(
        'Invalid template: missing document.xml',
        'INVALID_TEMPLATE_STRUCTURE'
      );
    }

    // Check for template corruption
    const contentTypes = zip.files['[Content_Types].xml'];
    if (!contentTypes) {
      throw new DocumentProcessingError(
        'Invalid template: missing content types',
        'INVALID_TEMPLATE_STRUCTURE'
      );
    }

    return true;
  } catch (error) {
    if (error instanceof DocumentProcessingError) {
      throw error;
    }
    console.error('Template validation error:', error);
    throw new DocumentProcessingError(
      'Failed to validate template',
      'TEMPLATE_VALIDATION_FAILED',
      error
    );
  }
};

/**
 * Processes a document template with form data
 * @param file The template file to process
 * @param formData The form data to fill the template with
 * @returns A Promise that resolves with the processed document blob and suggested filename
 */
export const processDocument = async (
  file: File,
  formData: any // Use any type to accept both FormData and NewLicenceData
): Promise<{ blob: Blob; filename: string }> => {
  // Log the template information
  console.log('Processing document with template:', file.name);
  console.log('Form type:', formData.templateName);
  console.log('Template URL:', formData.templateUrl);
  return new Promise<{ blob: Blob; filename: string }>((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        if (!e.target?.result) {
          throw new DocumentProcessingError(
            'Failed to read template file',
            'FILE_READ_ERROR'
          );
        }

        const arrayBuffer = e.target.result as ArrayBuffer;
        const zip = new PizZip(arrayBuffer);

        // Validate the template before processing
        validateTemplate(zip);

        // Create template data mapping
        let templateData: TemplateData = {};
        let processedData: TemplateData = {};

        // Check which form type we're processing
        if (formData.templateName && formData.templateName.includes('New Licence')) {
          // Use the createTemplateDataMapping function for NewLicence forms
          console.log('Processing New Licence form with 271_SAPS_Form.docx template');
          templateData = createTemplateDataMapping(formData);
          processedData = processTemplateData(templateData);
        } else if (formData.templateName && formData.templateName.includes('SAPS_Inspection_Report')) {
          // Use the createInspectionReportMapping function for SAPS Inspection Report forms
          templateData = createInspectionReportMapping(formData);
          processedData = processTemplateData(templateData);
        } else if (formData.templateName && formData.templateName.includes('E350A_Infomation')) {
          // Use the createE350InformationMapping function for E350 Information forms
          templateData = createE350InformationMapping(formData);
          processedData = processTemplateData(templateData);
        } else if (formData.templateName && formData.templateName.includes('Annexure_A_381A')) {
          // Use the createAnnexureAMapping function for Annexure A forms
          templateData = createAnnexureAMapping(formData);
          processedData = processTemplateData(templateData);
        } else {
          // For RenewLicence and other forms, use the form data directly
          // and process it to ensure proper formatting
          processedData = {};

          // Process all properties in the form data
          for (const key in formData) {
            const value = formData[key];

            // Convert boolean values to 'X' for checkboxes
            if (typeof value === 'boolean') {
              processedData[key] = value ? 'X' : '';
            } else if (value === null || value === undefined) {
              // Convert null/undefined to empty string
              processedData[key] = '';
            } else {
              // Keep other values as is
              processedData[key] = value;
            }
          }

          // Ensure all required placeholders for Renew Licence are present
          // Type of Licence placeholders
          if ('s13' in formData) processedData.S13 = formData.s13 ? 'X' : '';
          if ('s15' in formData) processedData.S15 = formData.s15 ? 'X' : '';
          if ('s16' in formData) processedData.S16 = formData.s16 ? 'X' : '';
          if ('s20' in formData) processedData.S20 = formData.s20 ? 'X' : '';
          if ('s20a' in formData) processedData.S20A = formData.s20a ? 'X' : '';
          if ('s20b' in formData) processedData.S20B = formData.s20b ? 'X' : '';

          // Details of Original Licence placeholders
          if ('originalLicenceNumber' in formData) processedData.OrigLicNum = formData.originalLicenceNumber || '';
          if ('originalLicenceIssueDate' in formData) processedData.OrigDateIssued = formData.originalLicenceIssueDate || '';
          if ('originalLicenceExpiryDate' in formData) processedData.OrigExpiryDate = formData.originalLicenceExpiryDate || '';

          // Additional original licences
          if (formData.additionalOriginalLicences && formData.additionalOriginalLicences.length > 0) {
            if (formData.additionalOriginalLicences[0]) {
              processedData.OrigLicNum2 = formData.additionalOriginalLicences[0].licenceNumber || '';
              processedData.OrigDateIssued2 = formData.additionalOriginalLicences[0].issueDate || '';
              processedData.OrigExpiryDate2 = formData.additionalOriginalLicences[0].expiryDate || '';
            }
            if (formData.additionalOriginalLicences[1]) {
              processedData.OrigLicNum3 = formData.additionalOriginalLicences[1].licenceNumber || '';
              processedData.OrigDateIssued3 = formData.additionalOriginalLicences[1].issueDate || '';
              processedData.OrigExpiryDate3 = formData.additionalOriginalLicences[1].expiryDate || '';
            }
            if (formData.additionalOriginalLicences[2]) {
              processedData.OrigLicNum4 = formData.additionalOriginalLicences[2].licenceNumber || '';
              processedData.OrigDateIssued4 = formData.additionalOriginalLicences[2].issueDate || '';
              processedData.OrigExpiryDate4 = formData.additionalOriginalLicences[2].expiryDate || '';
            }
          }

          // Personal Information placeholders
          if ('lastName' in formData) processedData.LastName = formData.lastName || '';
          if ('firstName' in formData) processedData.FirstNames = formData.firstName || '';
          if ('initials' in formData) {
            processedData.Initials = formData.initials || '';
            processedData.Initial = formData.initials || '';
            processedData.Initi = formData.initials || '';
          }
          if ('physicalAddress' in formData) processedData.Address = formData.physicalAddress || '';
          if ('postalCode' in formData) processedData.PostalCode = formData.postalCode || '';
          if ('workNumber' in formData) processedData.WorkNumber = formData.workNumber || '';
          if ('phoneNumber' in formData) processedData.PhoneNumber = formData.phoneNumber || '';
          if ('email' in formData) processedData.Email = formData.email || '';

          // Identification Type placeholders
          if ('saId' in formData) processedData.SAID = formData.saId ? 'X' : '';
          if ('fId' in formData) processedData.Passport = formData.fId ? 'X' : '';
          if ('permRes' in formData) processedData.PermRes = formData.permRes ? 'X' : '';
          if ('idNumber' in formData) {
            processedData.IDNumber = formData.idNumber || '';
            processedData.ID = formData.idNumber || '';
            processedData.IDNUMBER = formData.idNumber || '';
          }
          if ('passport' in formData) processedData.PassportNumber = formData.passport || '';
          if ('permResNumber' in formData) processedData.PermResNumber = formData.permResNumber || '';

          // Juristic Person's Details placeholders (only for business licence types)
          if (formData.s20 || formData.s20a || formData.s20b) {
            if ('companyName' in formData) processedData.CompanyName = formData.companyName || '';
            if ('tradingAsName' in formData) processedData.TradingAs = formData.tradingAsName || '';
            if ('farNumber' in formData) processedData.FARNumber = formData.farNumber || '';
            if ('postalAddress' in formData) processedData.PostalAddress = formData.postalAddress || '';
            if ('workPostalCode' in formData) processedData.JuristicPostalCode = formData.workPostalCode || '';
            if ('businessTelNumber' in formData) processedData.BusinessTelNumber = formData.businessTelNumber || '';
            if ('workNumber' in formData) processedData.JuristicWorkNumber = formData.workNumber || '';
            if ('companyEmail' in formData) processedData.JuristicEmail = formData.companyEmail || '';

            // Responsible Person's Details placeholders
            if ('responsiblePersonName' in formData) processedData.ResponsiblePersonName = formData.responsiblePersonName || '';
            if ('responsiblePersonSaId' in formData) processedData.ResponsiblePersonSAID = formData.responsiblePersonSaId ? 'X' : '';
            if ('responsiblePersonPassport' in formData) processedData.ResponsiblePersonPassport = formData.responsiblePersonPassport ? 'X' : '';
            if ('responsiblePersonIdNumber' in formData) processedData.ResponsiblePersonIDNumber = formData.responsiblePersonIdNumber || '';
            if ('responsiblePersonPassportNumber' in formData) processedData.ResponsiblePersonPassportNumber = formData.responsiblePersonPassportNumber || '';
            if ('responsiblePersonCellNumber' in formData) processedData.ResponsiblePersonCellNumber = formData.responsiblePersonCellNumber || '';
            if ('responsiblePersonAddress' in formData) processedData.ResponsiblePersonAddress = formData.responsiblePersonAddress || '';
            if ('responsiblePersonPostalCode' in formData) processedData.ResponsiblePersonPostalCode = formData.responsiblePersonPostalCode || '';
          }

          // Other Information Section placeholders
          if ('before90DaysYes' in formData) processedData['15A'] = formData.before90DaysYes ? 'X' : '';
          if ('before90DaysNo' in formData) processedData['15B'] = formData.before90DaysNo ? 'X' : '';
          if ('before90DaysReason' in formData) processedData['15C'] = formData.before90DaysReason || '';
          if ('afterExpiryYes' in formData) processedData['16A'] = formData.afterExpiryYes ? 'X' : '';
          if ('afterExpiryNo' in formData) processedData['16B'] = formData.afterExpiryNo ? 'X' : '';
          if ('afterExpiryReason' in formData) processedData['16C'] = formData.afterExpiryReason || '';
          if ('afterDueBeforeExpiryYes' in formData) processedData['17A'] = formData.afterDueBeforeExpiryYes ? 'X' : '';
          if ('afterDueBeforeExpiryNo' in formData) processedData['17B'] = formData.afterDueBeforeExpiryNo ? 'X' : '';
          if ('afterDueBeforeExpiryReason' in formData) processedData['17C'] = formData.afterDueBeforeExpiryReason || '';

          // Apply processTemplateData to ensure consistent formatting
          processedData = processTemplateData(processedData);

          // Set templateData to the processed data for logging purposes
          templateData = processedData;
        }

        // Debug log to check ID mapping
        console.log('ID Number from form:', formData.idNumber);
        console.log('ID placeholder value:', templateData.ID);
        console.log('IDNUMBER placeholder value:', templateData.IDNUMBER);
        console.log('Processed ID value:', processedData.ID);
        console.log('Processed IDNUMBER value:', processedData.IDNUMBER);

        // Debug log for criminal history fields
        console.log('Criminal History Fields:');
        console.log('Form data h5a (Yes):', formData.h5a, 'h5b (No):', formData.h5b);
        console.log('H5A (Offense Yes):', processedData.H5A);
        console.log('H5B (Offense No):', processedData.H5B);

        console.log('Form data h6a (Yes):', formData.h6a, 'h6b (No):', formData.h6b);
        console.log('H6A (Pending Case Yes):', processedData.H6A);
        console.log('H6B (Pending Case No):', processedData.H6B);

        console.log('Form data h7a (Yes):', formData.h7a, 'h7b (No):', formData.h7b);
        console.log('H7A (Lost/Stolen Yes):', processedData.H7A);
        console.log('H7B (Lost/Stolen No):', processedData.H7B);

        console.log('Form data h8a (Yes):', formData.h8a, 'h8b (No):', formData.h8b);
        console.log('H8A (Investigation Yes):', processedData.H8A);
        console.log('H8B (Investigation No):', processedData.H8B);

        console.log('Form data h9a (Yes):', formData.h9a, 'h9b (No):', formData.h9b);
        console.log('H9A (Declared Unfit Yes):', processedData.H9A);
        console.log('H9B (Declared Unfit No):', processedData.H9B);

        console.log('Form data h10a (Yes):', formData.h10a, 'h10b (No):', formData.h10b);
        console.log('H10A (Confiscated Yes):', processedData.H10A);
        console.log('H10B (Confiscated No):', processedData.H10B);
        console.log('H10.1 (Police Station):', processedData['H10.1']);
        console.log('H10.2 (Case Number):', processedData['H10.2']);
        console.log('H10.3 (Circumstances):', processedData['H10.3']);
        console.log('H10.4 (Outcome/Verdict):', processedData['H10.4']);

        // Debug log for Juristic Person's Details fields
        if (formData.pcoc) {
          console.log('Juristic Person\'s Details Fields:');
          console.log('JPNAME (Registered company name):', processedData.JPNAME);
          console.log('JPTN (Trading as name):', processedData.JPTN);
          console.log('JPFARN (FAR number):', processedData.JPFARN);
          console.log('JPADD (Postal address & Business address):', processedData.JPADD);
          console.log('JPPOSTAL (Postal Code):', processedData.JPPOSTAL);
          console.log('JPBTN (Business telephone number):', processedData.JPBTN);
          console.log('JPEM (E-mail address):', processedData.JPEM);
          console.log('JPFRB (Firearm registered TO business):', processedData.JPFRB);
          console.log('JPFRBNO (Number of persons employed):', processedData.JPFRBNO);
          console.log('JPRPNS (Responsible person):', processedData.JPRPNS);
          console.log('JPRPIDSA (South African):', processedData.JPRPIDSA);
          console.log('JPRPIDNO (Non-South African):', processedData.JPRPIDNO);
          console.log('JPRPID (Identity number):', processedData.JPRPID);
          console.log('JPRPCALL (Cellphone number):', processedData.JPRPCALL);
          console.log('JPRPADD (Physical address):', processedData.JPRPADD);
          console.log('JPRPOSTAL (Postal Code):', processedData.JPRPOSTAL);
        }

        // Debug log for Association Membership fields
        console.log('Association Membership Fields:');
        console.log('F5A (Member Yes):', processedData.F5A);
        console.log('F5B (Member No):', processedData.F5B);
        console.log('F6 (Association Name):', processedData.F6);
        console.log('ASSFARN (Association FAR number):', processedData.ASSFARN);
        console.log('F7 (Membership Number):', processedData.F7);
        console.log('F8 (Date Joined):', processedData.F8);
        console.log('ASSEXPIRE (Expiry Date):', processedData.ASSEXPIRE);

        // Debug log for Additional Firearm Licence Holders fields
        console.log('Additional Firearm Licence Holders Fields:');
        console.log('Form data afhy (Yes):', formData.afhy, 'afhn (No):', formData.afhn);
        console.log('POAFHY (Additional Holders Yes):', processedData.POAFHY);
        console.log('POAFHN (Additional Holders No):', processedData.POAFHN);

        try {
          // Create an instance of our custom module to fix unclosed tags
          const fixInitiTagModule = new FixInitiTagModule();

          // Initialize Docxtemplater with our custom module
          const doc = new Docxtemplater(zip, {
            paragraphLoop: true,
            linebreaks: true,
            nullGetter: function(): string {
              // Return empty string for null/undefined values
              return "";
            },
            modules: [fixInitiTagModule]
          });

          // Log that we're using the custom module
          console.log('Using FixInitiTagModule to handle unclosed tags');

          // Use the renderAsync method instead of resolveData
          await doc.renderAsync(processedData);

          const output = doc.getZip().generate({
            type: 'blob',
            mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });

          // Generate filename with fallbacks
          let filename = 'document.docx';

          // Special case for SAPS Inspection Report - use client name and date
          if (formData.templateName && formData.templateName.includes('SAPS_Inspection_Report') && formData.clientName) {
            // Format current date as YYYY-MM-DD
            const today = new Date();
            const dateStr = today.toISOString().split('T')[0];
            filename = `SAPS_Inspection_Report_${formData.clientName.replace(/\s+/g, '_')}_${dateStr}.docx`;
          }
          // Special case for E350 Information - use receiver name and date
          else if (formData.templateName && formData.templateName.includes('E350A_Infomation') && formData.receiverName) {
            // Format current date as YYYY-MM-DD
            const today = new Date();
            const dateStr = today.toISOString().split('T')[0];
            filename = `E350_Information_${formData.receiverName.replace(/\s+/g, '_')}_${dateStr}.docx`;
          }
          // Special case for Annexure A - use applicant's name and date
          else if (formData.templateName && formData.templateName.includes('Annexure_A_381A') && formData.firstName && formData.lastName) {
            // Format current date as YYYY-MM-DD
            const today = new Date();
            const dateStr = today.toISOString().split('T')[0];
            filename = `Annexure_A_${formData.firstName}_${formData.lastName}_${dateStr}.docx`;
          }
          // For other forms, try different fields for the filename with fallbacks
          else if (formData.fullName) {
            filename = `${formData.fullName.replace(/\s+/g, '_')}_${formData.templateName ?? 'document.docx'}`;
          } else if (formData.firstName && formData.lastName) {
            filename = `${formData.firstName}_${formData.lastName}_${formData.templateName ?? 'document.docx'}`;
          } else if (formData.companyName) {
            filename = `${formData.companyName.replace(/\s+/g, '_')}_${formData.templateName ?? 'document.docx'}`;
          } else if (formData.crcn) { // Company registration number
            filename = `Company_${formData.crcn}_${formData.templateName ?? 'document.docx'}`;
          } else if (formData.fdrcn) { // Firearm dealer registration number
            filename = `Dealer_${formData.fdrcn}_${formData.templateName ?? 'document.docx'}`;
          } else {
            filename = `filled_${formData.templateName ?? 'document.docx'}`;
          }

          // Sanitize filename to remove invalid characters
          filename = filename.replace(/[\/:*?"<>|]/g, '_');

          // Return the processed document blob and filename
          resolve({ blob: output, filename });
        } catch (error) {
          const docxError = error as any;
          console.error('Docxtemplater error:', docxError);

          // Provide more specific error messages based on the error type
          if (docxError && docxError.properties && docxError.properties.errors) {
            const errors = docxError.properties.errors;

            // Check for missing tags/placeholders
            const missingTags = errors.filter((e: any) => e.properties && e.properties.tag);
            if (missingTags.length > 0) {
              const tags = missingTags.map((e: any) => e.properties.tag).join(', ');
              throw new DocumentProcessingError(
                `Template contains placeholders that couldn't be filled: ${tags}`,
                'MISSING_PLACEHOLDERS',
                { tags }
              );
            }
          }

          throw new DocumentProcessingError(
            'Error generating document',
            'DOCXTEMPLATER_ERROR',
            docxError
          );
        }
      } catch (error) {
        console.error('Error processing template:', error);

        // Convert errors to user-friendly messages
        if (error instanceof DocumentProcessingError) {
          reject(error);
        } else {
          reject(new DocumentProcessingError(
            'An unexpected error occurred while processing the document',
            'UNKNOWN_ERROR',
            error
          ));
        }
      }
    };

    reader.onerror = (error) => {
      reject(new DocumentProcessingError(
        'Failed to read the template file',
        'FILE_READ_ERROR',
        error
      ));
    };

    // Start reading the file
    reader.readAsArrayBuffer(file);
  });
}

/**
 * Saves a processed document
 * @param blob The document blob to save
 * @param suggestedFilename The suggested filename
 * @param showSaveDialog Whether to show a save dialog (true) or auto-download (false)
 * @returns A Promise that resolves when the document is saved
 */
export const saveProcessedDocument = async (
  blob: Blob,
  suggestedFilename: string,
  showSaveDialog: boolean = false
): Promise<void> => {
  try {
    // Check if we're in an Electron environment (either via electronAPI or electron.ipcRenderer)
    const isElectronEnvironment = !!(window.electronAPI || window.electron?.ipcRenderer);

    if (!isElectronEnvironment) {
      console.error('Electron environment not detected');
      throw new DocumentProcessingError(
        'Electron environment not detected',
        'ELECTRON_API_UNAVAILABLE'
      );
    }



    // Check for required APIs (showSaveDialog in either electronAPI or electron.ipcRenderer)
    const showSaveDialogAPI = window.electronAPI?.showSaveDialog || window.electron?.ipcRenderer?.showSaveDialog;

    if (!showSaveDialogAPI) {
      console.error('showSaveDialog API not available in any API surface');
      throw new DocumentProcessingError(
        'showSaveDialog API not available',
        'ELECTRON_API_UNAVAILABLE'
      );
    }

    // Always use the Electron save dialog when showSaveDialog is true
    if (showSaveDialog) {
      // Get the appropriate showSaveDialog API
      // Use type assertion to handle TypeScript error
      const showSaveDialogAPI = (window.electronAPI as any)?.showSaveDialog || (window.electron?.ipcRenderer as any)?.showSaveDialog;

      // Check if showSaveDialogAPI is defined before calling it
      if (!showSaveDialogAPI) {
        throw new DocumentProcessingError(
          'Save dialog API not available',
          'API_NOT_AVAILABLE'
        );
      }

      // Show save dialog to let user choose where to save the file
      const { canceled, filePath } = await showSaveDialogAPI({
        title: 'Save Document',
        defaultPath: suggestedFilename,
        filters: [
          { name: 'Word Documents', extensions: ['docx'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (canceled || !filePath) {

        return;
      }

      // Check if saveFile API is available in electronAPI or electron.ipcRenderer
      // Use type assertion to handle TypeScript error
      const saveFileAPI = (window.electronAPI as any)?.saveFile || (window.electron?.ipcRenderer as any)?.saveFile;

      if (saveFileAPI) {
        // Use the Electron API to save the file directly
        const buffer = await blob.arrayBuffer();

        // Call the appropriate API
        const result = await saveFileAPI(filePath, buffer);

        if (result.success) {
        } else {
          throw new DocumentProcessingError(
            `Failed to save file: ${result.error || 'Unknown error'}`,
            'SAVE_ERROR'
          );
        }
      } else {
        // Fallback to browser download if saveFile API is not available
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Extract just the filename from the full path
        const pathParts = filePath.split(/[\\/]/);
        const filename = pathParts[pathParts.length - 1];
        link.download = filename;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);


      }
    }
    // If showSaveDialog is false, use auto-save with default filename
    else {
      // Check if saveFile API is available in electronAPI or electron.ipcRenderer
      // Use type assertion to handle TypeScript error
      const saveFileAPI = (window.electronAPI as any)?.saveFile || (window.electron?.ipcRenderer as any)?.saveFile;

      if (saveFileAPI) {
        // Use the Electron API to save the file directly with default filename
        const buffer = await blob.arrayBuffer();

        // Call the appropriate API
        const result = await saveFileAPI(suggestedFilename, buffer);

        if (result.success) {
        } else {
          throw new DocumentProcessingError(
            `Failed to save file: ${result.error || 'Unknown error'}`,
            'SAVE_ERROR'
          );
        }
      } else {
        // Fallback to browser download if saveFile API is not available
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = suggestedFilename;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);


      }
    }
  } catch (error) {


    if (error instanceof DocumentProcessingError) {
      throw error;
    } else {
      throw new DocumentProcessingError(
        'Failed to save the document',
        'SAVE_ERROR',
        error
      );
    }
  }
}



/**
 * Fetches a template file from a URL with caching support
 * @param url The URL to fetch the template from
 * @param fileName The name to give the fetched file
 * @param bypassCache Whether to bypass the cache and fetch directly (default: true)
 * @returns A Promise that resolves with the fetched file or null if there was an error
 */
export const fetchTemplateFile = async (url: string, fileName: string, bypassCache: boolean = true): Promise<File | null> => {
  try {
    // Clear expired templates on fetch (this is an async operation that won't block)
    clearExpiredTemplates().catch(() => {});

    // Check cache first if not bypassing
    if (!bypassCache) {
      const cachedTemplate = await getCachedTemplate(url);
      if (cachedTemplate) {

        return createFileFromCachedTemplate(cachedTemplate);
      }
    }

    // If not in cache or bypassing cache, fetch from URL

    // Add cache-busting parameter to URL
    const cacheBustUrl = bypassCache ? `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}` : url;

    // Implement retry logic with exponential backoff
    const maxRetries = 3;
    let retryCount = 0;
    let lastError: Error | null = null;

    while (retryCount < maxRetries) {
      try {
        const response = await fetch(cacheBustUrl, {
          // Add cache control headers to prevent browser caching issues
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch template: ${response.status} ${response.statusText}`);
        }

        const blob = await response.blob();

        // Create a File object from the blob
        const file = new File([blob], fileName, {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });

        // Only cache the template if bypassCache is false
        if (!bypassCache) {
          await cacheTemplate(url, fileName, blob);
        }

        return file;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        retryCount++;

        if (retryCount < maxRetries) {
          // Exponential backoff: wait longer between each retry
          const waitTime = Math.pow(2, retryCount) * 1000;
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    // If we've exhausted all retries, throw the last error
    if (lastError) {
      throw lastError;
    }

    return null;
  } catch (error) {
    throw error; // Re-throw to allow proper error handling upstream
  }
}
