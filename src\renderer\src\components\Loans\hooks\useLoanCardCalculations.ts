import { useMemo } from 'react'
import { Loan, LoanPayment } from '../../../types'
import { getMonthlyPaymentStatuses } from '../../../utils/paymentAllocation'

/**
 * Custom hook for loan card calculations
 * Centralizes all calculation logic for the loan card component
 */
export const useLoanCardCalculations = (loan: Loan, payments: LoanPayment[]) => {
  // Calculate progress percentage
  const calculateProgress = useMemo(() => {
    if (loan.weapon_cost <= 0) return 100
    const paid = loan.weapon_cost - loan.remaining_balance
    return Math.round(Math.min(100, Math.max(0, (paid / loan.weapon_cost) * 100)))
  }, [loan.weapon_cost, loan.remaining_balance])

  // Get loan term information
  const loanTermInfo = useMemo(() => {
    const totalMonths = loan.loan_term || 12 // Default to 12 if not specified

    if (!loan.start_date) {
      return {
        totalMonths,
        elapsedMonths: 0,
        remainingMonths: totalMonths,
        formattedTerm: `0/${totalMonths} months`
      }
    }

    const startDate = new Date(loan.start_date)
    const currentDate = new Date()

    // Calculate months elapsed since loan started
    const elapsedMonths =
      (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
      (currentDate.getMonth() - startDate.getMonth())

    // Calculate remaining months (ensure it's not negative)
    const remainingMonths = Math.max(0, totalMonths - elapsedMonths)

    // Format the term string
    const formattedTerm = `${Math.min(elapsedMonths, totalMonths)}/${totalMonths} months`

    return {
      totalMonths,
      elapsedMonths,
      remainingMonths,
      formattedTerm
    }
  }, [loan.loan_term, loan.start_date])

  // Calculate remaining payments
  const remainingPayments = useMemo(() => {
    if (loan.status === 'paid' || loan.remaining_balance <= 0) {
      return 0
    }
    return loanTermInfo.remainingMonths
  }, [loan.status, loan.remaining_balance, loanTermInfo.remainingMonths])

  // Get monthly payment statuses
  const monthlyPaymentStatuses = useMemo(() => {
    return getMonthlyPaymentStatuses(loan, payments)
  }, [loan, payments])

  // Check if we're in the creation month (grace period)
  const isCreationMonth = useMemo(() => {
    if (!loan.start_date) return false

    const startDate = new Date(loan.start_date)
    const now = new Date()

    return startDate.getMonth() === now.getMonth() && startDate.getFullYear() === now.getFullYear()
  }, [loan.start_date])

  // Calculate days active since loan start date
  const daysActive = useMemo(() => {
    if (!loan.start_date) return 0

    const startDate = new Date(loan.start_date)

    // Reset time part to get accurate day calculation
    startDate.setHours(0, 0, 0, 0)
    const nowDateOnly = new Date()
    nowDateOnly.setHours(0, 0, 0, 0)

    // Calculate difference in milliseconds and convert to days
    const differenceMs = nowDateOnly.getTime() - startDate.getTime()
    return Math.floor(differenceMs / (1000 * 60 * 60 * 24))
  }, [loan.start_date])

  // Calculate monthly payment amount (base amount without penalties)
  const monthlyPaymentAmount = useMemo(() => {
    if (loan.status === 'paid' || loan.remaining_balance <= 0) {
      return 0
    }

    // Handle edge case: if no remaining months but balance exists
    if (remainingPayments <= 0 && loan.remaining_balance > 0) {
      // Loan is past due date but still has balance - full amount is due
      return loan.remaining_balance
    }

    // Calculate monthly payment by dividing remaining balance by remaining months
    // Use Math.ceil to ensure the loan will be fully paid by slightly overpaying each month
    // rather than having a remainder at the end
    return Math.ceil(loan.remaining_balance / Math.max(1, remainingPayments))
  }, [loan.status, loan.remaining_balance, remainingPayments])

  // Get current month's payments total
  const currentMonthPayments = useMemo(() => {
    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()

    // Filter payments made in the current month
    const currentMonthPayments = payments.filter((payment) => {
      const paymentDate = new Date(payment.payment_date)
      return paymentDate.getMonth() === currentMonth && paymentDate.getFullYear() === currentYear
    })

    // Sum up all payments made this month
    return currentMonthPayments.reduce((total, payment) => total + payment.amount, 0)
  }, [payments])

  // Calculate current month payment status and remaining amount
  const currentMonthPaymentStatus = useMemo(() => {
    const paidThisMonth = currentMonthPayments
    const remaining = monthlyPaymentAmount - paidThisMonth
    const isOverpaid = remaining < 0
    const progress =
      monthlyPaymentAmount > 0 ? Math.min(100, (paidThisMonth / monthlyPaymentAmount) * 100) : 100
    const isPaidInFull = monthlyPaymentAmount > 0 ? paidThisMonth >= monthlyPaymentAmount : true

    return {
      paid: paidThisMonth,
      required: monthlyPaymentAmount,
      remaining: Math.abs(remaining),
      isOverpaid,
      progress,
      isPaidInFull
    }
  }, [monthlyPaymentAmount, currentMonthPayments])

  // Check if this loan has insufficient payments in past months
  const hasInsufficientPastPayments = useMemo(() => {
    if (!loan.start_date || isCreationMonth) return false // No insufficient payments during grace period

    const startDate = new Date(loan.start_date)
    const currentDate = new Date()

    // Check if payment is past due (more than 7 days after due date)
    // First use payment_due_date, fallback to next_payment_date if not available
    if (loan.payment_due_date) {
      const dueDate = new Date(loan.payment_due_date)
      const sevenDaysAfterDue = new Date(dueDate)
      sevenDaysAfterDue.setDate(dueDate.getDate() + 7)

      // If current date is more than 7 days after due date, payment is late
      if (currentDate > sevenDaysAfterDue) {
        return true
      }
    } else if (loan.next_payment_date) {
      const dueDate = new Date(loan.next_payment_date)
      const sevenDaysAfterDue = new Date(dueDate)
      sevenDaysAfterDue.setDate(dueDate.getDate() + 7)

      // If current date is more than 7 days after due date, payment is late
      if (currentDate > sevenDaysAfterDue) {
        return true
      }
    }

    // Calculate expected payments based on months since start
    const monthsPassed =
      (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
      (currentDate.getMonth() - startDate.getMonth())

    // If still in creation month or the next month, no past payments required
    if (monthsPassed <= 1) return false

    // For 3rd month and beyond, we expect payments for all months except creation month
    // So we need (monthsPassed - 1) months of payments
    const requiredMonths = monthsPassed - 1

    // Calculate total amount that should have been paid
    const expectedPayment = requiredMonths * monthlyPaymentAmount

    // Calculate actual total paid
    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0)

    // Return true if paid amount is less than expected
    return totalPaid < expectedPayment
  }, [loan.start_date, loan.payment_due_date, loan.next_payment_date, isCreationMonth, monthlyPaymentAmount, payments])

  // Calculate missed payments
  const missedPayments = useMemo(() => {
    if (!loan.start_date || isCreationMonth) return 0 // No missed payments during grace period

    // Get monthly payment statuses
    // Filter for past months that are not fully paid and not in grace period
    const missedMonths = monthlyPaymentStatuses.filter(
      (month) => month.isPast && !month.isCurrentMonth && !month.isGracePeriod && month.remaining > 0
    )

    // Sum up the remaining amounts for all missed months
    return missedMonths.reduce((total, month) => total + month.remaining, 0)
  }, [loan.start_date, isCreationMonth, monthlyPaymentStatuses])

  // Calculate the next payment amount due
  const nextPaymentAmount = useMemo(() => {
    if (loan.status === 'paid' || loan.remaining_balance <= 0) {
      return { amount: 0, dueDate: null }
    }

    // Use payment_due_date if available, fallback to next_payment_date, or calculate it
    let dueDate: Date

    if (loan.payment_due_date) {
      // Prefer payment_due_date as it only updates when payments are caught up
      dueDate = new Date(loan.payment_due_date)
    } else if (loan.next_payment_date) {
      // Fallback to next_payment_date if payment_due_date is not available
      dueDate = new Date(loan.next_payment_date)
    } else {
      // Calculate next payment date (28th of next month)
      const currentDate = new Date()
      dueDate = new Date()

      // First payment is always due on the 28th of the month after the loan was created
      // This implements the grace period - no payment is due in the creation month
      if (isCreationMonth) {
        dueDate.setMonth(currentDate.getMonth() + 1)
        dueDate.setDate(28)
      } else {
        // For existing loans, check if we're past the 28th
        if (currentDate.getDate() > 28) {
          dueDate.setMonth(currentDate.getMonth() + 1)
        }
        dueDate.setDate(28)
      }
    }

    // Check if we're in the grace period (no payment due yet)
    if (isCreationMonth) {
      return { amount: 0, dueDate }
    }

    // 1. Get missed payments amount (past months not fully paid)
    const missedPaymentsAmount = missedPayments

    // 2. Get current month required payment
    const currentMonth = monthlyPaymentStatuses.find((month) => month.isCurrentMonth)
    let currentMonthAmount = 0

    if (currentMonth && !currentMonth.isGracePeriod) {
      currentMonthAmount = Math.max(0, currentMonth.remaining)
    }

    // 3. Add penalties
    const penalties = loan.penalties || 0

    // 4. Calculate total amount due
    let totalAmount = currentMonthAmount + missedPaymentsAmount + penalties

    // 5. Special case: if this is the final payment or loan is overdue
    if (loanTermInfo.remainingMonths <= 1 || loanTermInfo.remainingMonths <= 0) {
      // Make sure the last payment covers the entire remaining balance
      totalAmount = Math.max(totalAmount, loan.remaining_balance)
    }

    return {
      amount: totalAmount,
      dueDate: dueDate
    }
  }, [
    loan.status,
    loan.remaining_balance,
    loan.payment_due_date,
    loan.next_payment_date,
    loan.penalties,
    isCreationMonth,
    missedPayments,
    monthlyPaymentStatuses,
    loanTermInfo.remainingMonths
  ])

  // Get details about missed months
  const missedMonthsDetails = useMemo(() => {
    // Filter for past months that are not fully paid and not in grace period
    return monthlyPaymentStatuses
      .filter(
        (month) => month.isPast && !month.isCurrentMonth && !month.isGracePeriod && month.remaining > 0
      )
      .map((month) => ({
        description: month.description,
        amount: month.remaining
      }))
  }, [monthlyPaymentStatuses])

  // Generate calendar months from loan start date until now
  const paymentMonths = useMemo(() => {
    if (!loan.start_date) return [] as Date[]

    const startDate = new Date(loan.start_date)
    const currentDate = new Date()

    // Create array of months from start date to current date
    const months: Date[] = []
    let currentMonth = new Date(startDate)

    // Set to first day of month for consistent comparison
    currentMonth.setDate(1)

    // Create a date for the end of the current month
    const endOfCurrentMonth = new Date(currentDate)
    endOfCurrentMonth.setMonth(endOfCurrentMonth.getMonth() + 1)
    endOfCurrentMonth.setDate(0)

    while (currentMonth <= endOfCurrentMonth) {
      months.push(new Date(currentMonth))
      currentMonth.setMonth(currentMonth.getMonth() + 1)
    }

    return months
  }, [loan.start_date])

  return {
    calculateProgress,
    loanTermInfo,
    remainingPayments,
    monthlyPaymentStatuses,
    isCreationMonth,
    daysActive,
    monthlyPaymentAmount,
    currentMonthPayments,
    currentMonthPaymentStatus,
    hasInsufficientPastPayments,
    missedPayments,
    nextPaymentAmount,
    missedMonthsDetails,
    paymentMonths
  }
}
