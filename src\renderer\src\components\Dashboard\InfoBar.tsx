import React from 'react'
import { FilterState } from './types'

interface InfoBarProps {
  visibleCount: number
  totalCount: number
  searchQuery: string
  clientFilter: FilterState['clientFilter']
}

export const InfoBar: React.FC<InfoBarProps> = ({
  visibleCount,
  totalCount,
  searchQuery,
  clientFilter
}) => {
  return (
    <div className="text-stone-400">
      {visibleCount} of {totalCount} clients
      {searchQuery && (
        <span>
          {' '}
          for "<span className="text-orange-400 font-medium">{searchQuery}</span>"
        </span>
      )}
      {clientFilter !== 'all' && (
        <span>
          {' '}
          filtered by{' '}
          <span className="text-orange-400 font-medium">
            {clientFilter === 'with-licenses' ? 'With Licenses' : 'No Licenses'}
          </span>
        </span>
      )}
    </div>
  )
}
