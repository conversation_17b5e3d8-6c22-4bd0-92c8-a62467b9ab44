/**
 * South African ID number validation utility
 *
 * South African ID numbers follow the format: YYMMDD SSSS Z A C
 * - YYMMDD: Date of birth (YY=Year, MM=Month, DD=Day)
 * - SSSS: Gender (Females: 0000-4999, Males: 5000-9999)
 * - Z: Citizenship status (0=SA citizen, 1=Permanent resident)
 * - A: Usually 8 or 9 (part of the checksum calculation)
 * - C: Checksum digit calculated using the Luhn algorithm
 */

/**
 * Validates a South African ID number using the Luhn algorithm
 * @param idNumber - The ID number to validate
 * @returns Boolean indicating if the ID number is valid
 */
export const validateSouthAfricanID = (idNumber: string): boolean => {
  // Basic format check
  if (!/^\d{13}$/.test(idNumber)) {
    return false;
  }

  // Extract date of birth
  const year = parseInt(idNumber.substring(0, 2));
  const month = parseInt(idNumber.substring(2, 4));
  const day = parseInt(idNumber.substring(4, 6));

  // Check if date is valid
  if (month < 1 || month > 12) {
    return false;
  }

  // Check days in month
  const daysInMonth = new Date(
    year >= 0 && year < 100 ? 2000 + year : 1900 + year,
    month,
    0
  ).getDate();

  if (day < 1 || day > daysInMonth) {
    return false;
  }

  // Gender digits check (7-10th digits)
  const genderDigits = parseInt(idNumber.substring(6, 10));
  if (genderDigits < 0 || genderDigits > 9999) {
    return false;
  }

  // Citizenship status check (11th digit should be 0 or 1)
  const citizenshipStatus = parseInt(idNumber.charAt(10));
  if (citizenshipStatus !== 0 && citizenshipStatus !== 1) {
    return false;
  }

  // The 12th digit (index 11) is part of the checksum calculation
  // No specific validation needed for this digit

  // South African ID uses the Luhn algorithm for validation
  // Implementation based on the official South African ID validation rules
  let sum = 0;
  let alternate = false;

  // Process from right to left (starting with check digit)
  for (let i = idNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(idNumber.charAt(i));

    if (alternate) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    alternate = !alternate;
  }

  // The sum should be divisible by 10 for a valid ID
  return (sum % 10 === 0);
};

/**
 * Extracts information from a South African ID number
 * @param idNumber - The ID number to extract information from
 * @returns Object containing extracted information or null if invalid
 */
export const extractFromIdNumber = (idNumber: string) => {
  if (!validateSouthAfricanID(idNumber)) {
    return null;
  }

  // Extract date of birth
  const year = parseInt(idNumber.substring(0, 2));
  const month = parseInt(idNumber.substring(2, 4));
  const day = parseInt(idNumber.substring(4, 6));

  // Determine century (19xx or 20xx)
  const fullYear = year >= 0 && year < 50 ? 2000 + year : 1900 + year;

  // Create date object
  const birthDate = new Date(fullYear, month - 1, day);

  // Format birth date as YYYY-MM-DD
  const formattedBirthDate = birthDate.toISOString().split('T')[0];

  // Calculate age
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  // Determine gender (Females: 0000-4999, Males: 5000-9999)
  const genderDigits = parseInt(idNumber.substring(6, 10));
  const isMale = genderDigits >= 5000;

  // Determine citizenship
  const citizenshipStatus = parseInt(idNumber.charAt(10));
  const isCitizen = citizenshipStatus === 0;

  return {
    birthDate: formattedBirthDate,
    age: age.toString(),
    gender: isMale ? 'Male' : 'Female',
    isMale,
    isFemale: !isMale,
    npsexmale: isMale ? 'X' : '',
    npsexfemale: !isMale ? 'X' : '',
    sexM: isMale ? 'X' : '',
    sexF: !isMale ? 'X' : '',
    isCitizen,
    isPermanentResident: !isCitizen
  };
};
