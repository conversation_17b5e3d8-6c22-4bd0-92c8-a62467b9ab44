import React from 'react'
import { FormField, FormSection, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * CertificateDetails - Certificate Details Section Component
 *
 * Renders the certificate details form section for the Renew Competency form
 */
export default function CertificateDetails({
  formData,
  updateFormData,
  className = ''
}: SectionProps): JSX.Element {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target

    // Handle checkboxes
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement
      updateFormData({
        [name]: checkbox.checked
      })
      return
    }

    updateFormData({ [name]: value })
  }

  // Helper function to render certificate type fields
  const renderCertificateTypeFields = (typePrefix: string, typeName: string, typeNumber: number) => {
    const typeKey = `${typePrefix}Type` as keyof typeof formData
    const certNumberKey = `${typePrefix}CertNumber` as keyof typeof formData
    const issueDateKey = `${typePrefix}CertIssueDate` as keyof typeof formData
    const expiryDateKey = `${typePrefix}CertExpiryDate` as keyof typeof formData

    return (
      <div className="border-b border-gray-700 pb-4 mb-4">
        <div className="flex items-center mb-2">
          <input
            type="checkbox"
            name={typeKey as string}
            checked={formData[typeKey] as boolean || false}
            onChange={handleChange}
            className="mr-2 h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
          <label className="text-sm font-medium text-white">
            D1.6A{typeNumber} - {typeName} (Mark with X)
          </label>
        </div>

        {formData[typeKey] && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-2 pl-6">
            <FormField
              label="Certificate Number"
              name={certNumberKey as string}
              value={formData[certNumberKey] as string || ''}
              onChange={handleChange}
              placeholder="Enter certificate number"
            />

            <FormField
              label="Issue Date"
              name={issueDateKey as string}
              value={formData[issueDateKey] as string || ''}
              onChange={handleChange}
              placeholder="YYYY-MM-DD"
              type="date"
            />

            <FormField
              label="Expiry Date"
              name={expiryDateKey as string}
              value={formData[expiryDateKey] as string || ''}
              onChange={handleChange}
              placeholder="YYYY-MM-DD"
              type="date"
            />
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Competency Certificate Details</h3>

      <FormSection
        title="Competency Certificate Types"
        subtitle="Details of Competency Certificate Types of Competency Certificates"
      >
        <div className="grid grid-cols-2 gap-4">
          <div>
            {/* 1. Handgun */}
            {renderCertificateTypeFields('handgun', 'Handgun', 1)}

            {/* 3. Rifle */}
            {renderCertificateTypeFields('rifle', 'Rifle', 3)}

            {/* 5. Shotgun & Handgun */}
            {renderCertificateTypeFields('shotgunHandgun', 'Shotgun & Handgun', 5)}

            {/* 7. Handgun & Rifle & Shotgun */}
            {renderCertificateTypeFields('handgunRifleShotgun', 'Handgun & Rifle & Shotgun', 7)}

            {/* 9. Handgun & Hand Machine Carbine */}
            {renderCertificateTypeFields('handgunHandMachine', 'Handgun & Hand Machine Carbine', 9)}

            {/* 11. Handgun & Rifle & Shotgun & Hand Machine Carbine */}
            {renderCertificateTypeFields('handgunRifleShotgunHandMachine', 'Handgun & Rifle & Shotgun & Hand Machine Carbine', 11)}

            {/* 13. Rifle & Shotgun & Hand Machine Carbine */}
            {renderCertificateTypeFields('rifleShotgunHandMachine', 'Rifle & Shotgun & Hand Machine Carbine', 13)}
          </div>

          <div>
            {/* 2. Handgun & Rifle */}
            {renderCertificateTypeFields('handgunRifle', 'Handgun & Rifle', 2)}

            {/* 4. Shotgun */}
            {renderCertificateTypeFields('shotgun', 'Shotgun', 4)}

            {/* 6. Rifle & Shotgun */}
            {renderCertificateTypeFields('rifleShotgun', 'Rifle & Shotgun', 6)}

            {/* 8. Hand Machine Carbine */}
            {renderCertificateTypeFields('handMachineCarbine', 'Hand Machine Carbine', 8)}

            {/* 10. Handgun & Rifle & Hand Machine Carbine */}
            {renderCertificateTypeFields('handgunRifleHandMachine', 'Handgun & Rifle & Hand Machine Carbine', 10)}

            {/* 12. Rifle & Hand Machine Carbine */}
            {renderCertificateTypeFields('rifleHandMachine', 'Rifle & Hand Machine Carbine', 12)}

            {/* 14. Shotgun & Hand Machine Carbine */}
            {renderCertificateTypeFields('shotgunHandMachine', 'Shotgun & Hand Machine Carbine', 14)}
          </div>
        </div>
      </FormSection>

      <FormSection
        title="Competency Expiry Status"
        subtitle="Information regarding the competency certificate expiry"
      >
        <div className="grid grid-cols-1 gap-3">
          {/* Row 1: 90 Days Before Expiry Question */}
          <div className="grid grid-cols-2 gap-4">
            <RadioGroup
              label="Was the application handed in 90 days before the expiry of the competency certificate?"
              name="competencyExpiry90DaysBefore"
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              onChange={(value) => {
                const isYes = value === 'yes'
                updateFormData({
                  competencyExpiry90DaysBeforeYes: isYes,
                  competencyExpiry90DaysBeforeNo: !isYes
                })
              }}
              value={
                formData.competencyExpiry90DaysBeforeYes
                  ? 'yes'
                  : formData.competencyExpiry90DaysBeforeNo
                    ? 'no'
                    : ''
              }
              required
            />

            <RadioGroup
              label="Are you applying for a competency certificate after the expiry of your competency certificate?"
              name="afterExpiry"
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              onChange={(value) => {
                const isYes = value === 'yes'
                updateFormData({
                  afterExpiryYes: isYes,
                  afterExpiryNo: !isYes
                })
              }}
              value={formData.afterExpiryYes ? 'yes' : formData.afterExpiryNo ? 'no' : ''}
              required
            />
          </div>

          {/* Row 2: Reasons (if applicable) */}
          <div className="grid grid-cols-2 gap-4">
            {formData.competencyExpiry90DaysBeforeNo && (
              <FormField
                label="Provide a reason for not submitting 90 days before expiry"
                name="competencyExpiry90DaysBeforeReason"
                value={formData.competencyExpiry90DaysBeforeReason || ''}
                onChange={handleChange}
                placeholder="Enter reason"
                type="textarea"
                required
              />
            )}

            {formData.afterExpiryYes && (
              <FormField
                label="Provide a reason why you are applying after expiry"
                name="afterExpiryReason"
                value={formData.afterExpiryReason || ''}
                onChange={handleChange}
                placeholder="Enter reason"
                type="textarea"
              />
            )}
          </div>
        </div>
      </FormSection>
    </div>
  )
}
