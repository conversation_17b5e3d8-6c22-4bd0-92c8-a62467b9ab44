import React from 'react'
import { RenewLicenceData } from '../../../../types/RenewLicenceData'
import { FormField, RadioGroup, FormSection } from '../../../FormComponents'

interface OtherInformationProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  className?: string
}

const OtherInformation: React.FC<OtherInformationProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Other Information</h3>
      <FormSection
        title="Other Information"
        subtitle="Please provide additional information about your application"
      >
        <div className="space-y-6">
          {/* Question 1: 90 days before expiry */}
          <div className="p-4 bg-stone-800/50 rounded-lg">
            <h4 className="text-md font-semibold mb-3 text-white">WAS YOUR APPLICATION HANDED IN 90 DAYS BEFORE EXPIRY OF THE EXISTING LICENCE?</h4>
            <div className="mb-4">
              <RadioGroup
                name="before90Days"
                value={
                  formData.before90DaysYes ? 'yes' :
                  formData.before90DaysNo ? 'no' : ''
                }
                onChange={(value) => {
                  updateFormData({
                    before90DaysYes: value === 'yes',
                    before90DaysNo: value === 'no'
                  })
                }}
                options={[
                  { value: 'yes', label: 'YES' },
                  { value: 'no', label: 'NO' }
                ]}
                required
              />
            </div>

            {formData.before90DaysNo && (
              <FormField
                label="Reason"
                name="before90DaysReason"
                value={formData.before90DaysReason || ''}
                onChange={handleChange}
                placeholder="Enter reason"
                type="textarea"
                rows={2}
                required
              />
            )}
          </div>

          {/* Question 2: After due date but before expiry */}
          <div className="p-4 bg-stone-800/50 rounded-lg">
            <h4 className="text-md font-semibold mb-3 text-white">WAS YOUR APPLICATION HANDED IN AFTER THE DUE DATE, BUT BEFORE EXPIRY OF EXISTING LICENCE.</h4>
            <div className="mb-4">
              <RadioGroup
                name="afterDueBeforeExpiry"
                value={
                  formData.afterDueBeforeExpiryYes ? 'yes' :
                  formData.afterDueBeforeExpiryNo ? 'no' : ''
                }
                onChange={(value) => {
                  updateFormData({
                    afterDueBeforeExpiryYes: value === 'yes',
                    afterDueBeforeExpiryNo: value === 'no'
                  })
                }}
                options={[
                  { value: 'yes', label: 'YES' },
                  { value: 'no', label: 'NO' }
                ]}
                required
              />
            </div>

            {formData.afterDueBeforeExpiryYes && (
              <FormField
                label="Reason"
                name="afterDueBeforeExpiryReason"
                value={formData.afterDueBeforeExpiryReason || ''}
                onChange={handleChange}
                placeholder="Enter reason"
                type="textarea"
                rows={2}
                required
              />
            )}
          </div>

          {/* Question 3: After expiry */}
          <div className="p-4 bg-stone-800/50 rounded-lg">
            <h4 className="text-md font-semibold mb-3 text-white">WAS YOUR APPLICATION HANDED IN AFTER THE EXPIRY OF EXISTING LICENCE.</h4>
            <div className="mb-4">
              <RadioGroup
                name="afterExpiry"
                value={
                  formData.afterExpiryYes ? 'yes' :
                  formData.afterExpiryNo ? 'no' : ''
                }
                onChange={(value) => {
                  updateFormData({
                    afterExpiryYes: value === 'yes',
                    afterExpiryNo: value === 'no'
                  })
                }}
                options={[
                  { value: 'yes', label: 'YES' },
                  { value: 'no', label: 'NO' }
                ]}
                required
              />
            </div>

            {formData.afterExpiryYes && (
              <FormField
                label="Reason"
                name="afterExpiryReason"
                value={formData.afterExpiryReason || ''}
                onChange={handleChange}
                placeholder="Enter reason"
                type="textarea"
                rows={2}
                required
              />
            )}
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default OtherInformation
