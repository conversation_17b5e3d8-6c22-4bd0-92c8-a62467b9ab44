/**
 * Custom error class for document processing errors
 */
export class DocumentProcessingError extends Error {
  constructor(message: string, public readonly code: string, public readonly details?: any) {
    super(message);
    this.name = 'DocumentProcessingError';
  }
}

/**
 * Converts a technical error to a user-friendly error message
 * @param error The error to convert
 * @returns A user-friendly error message
 */
export const getUserFriendlyErrorMessage = (error: any): string => {
  if (error instanceof DocumentProcessingError) {
    switch (error.code) {
      case 'TEMPLATE_FETCH_ERROR':
        return 'Could not download the document template. Please check your internet connection and try again.';
      case 'INVALID_TEMPLATE_STRUCTURE':
        return 'The document template appears to be invalid or corrupted. Please try again with a different template.';
      case 'TEMPLATE_VALIDATION_FAILED':
        return 'The document template could not be validated. It may be corrupted or in an unsupported format.';
      case 'FILE_READ_ERROR':
        return 'Could not read the template file. The file may be corrupted or inaccessible.';
      case 'MISSING_PLACEHOLDERS':
        return 'Some required information is missing from the form. Please check that all required fields are filled in.';
      case 'DOCXTEMPLATER_ERROR':
        let errorMsg = 'An error occurred while generating the document. The template may contain unsupported formatting.';

        // Add more detailed information if available
        if (error.details && error.details.properties && error.details.properties.errors) {
          const errors = error.details.properties.errors;
          const missingTags = errors.filter((e: any) => e.properties && e.properties.tag);

          if (missingTags.length > 0) {
            const tags = missingTags.map((e: any) => e.properties.tag).join(', ');
            errorMsg += ` Missing placeholders: ${tags}`;
          }
        }

        return errorMsg;
      case 'SAVE_ERROR':
        return 'Could not save the document. Please check that you have permission to save files to the selected location.';
      default:
        return error.message;
    }
  }

  return error instanceof Error
    ? error.message
    : 'An unexpected error occurred while processing the document.';
};
