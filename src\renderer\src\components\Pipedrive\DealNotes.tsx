import React, { useState } from 'react';
import { parseNotes } from '../../utils/noteUtils';
import { DashboardIcons } from '../icons/DashboardIcons';

interface DealNotesProps {
  notes: string | null;
  compact?: boolean;
}

const DealNotes: React.FC<DealNotesProps> = ({ notes, compact = false }) => {
  const [showAllNotes, setShowAllNotes] = useState(false);
  const parsedNotes = parseNotes(notes);

  if (!parsedNotes.length) return null;

  // Modal for viewing all notes
  const NotesModal = () => (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[60]" onClick={() => setShowAllNotes(false)}>
      <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-white">Notes History</h3>
          <button
            type="button"
            onClick={() => setShowAllNotes(false)}
            className="text-stone-400 hover:text-stone-300"
          >
            <DashboardIcons.Close className="w-5 h-5" />
          </button>
        </div>

        <div className="mb-4 max-h-[60vh] overflow-y-auto custom-scrollbar">
          <div className="space-y-3">
            {parsedNotes.map((note, index) => (
              <div key={index} className="bg-stone-700/50 p-3 rounded-md">
                {note.date && (
                  <div className="text-orange-400 font-medium text-sm mb-1">[{note.date}]</div>
                )}
                <div className="text-white text-sm whitespace-pre-wrap">{note.content}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="button"
            onClick={() => setShowAllNotes(false)}
            className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );

  if (compact) {
    // Compact view for deal cards - show all notes with date stamps
    return (
      <div className="flex items-start">
        <span className="text-orange-400 font-medium text-sm w-20">Notes:</span>
        <div className="flex-1 text-white text-sm max-h-20 overflow-y-auto custom-scrollbar pr-1">
          <div className="space-y-1">
            {parsedNotes.map((note, index) => (
              <div key={index}>
                {note.date && <span className="text-orange-400 font-medium">[{note.date}]</span>} {note.content}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Only show a button to view all notes
  return (
    <>
      <div>
        {/* View all notes button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setShowAllNotes(true);
          }}
          className="text-orange-400 hover:text-orange-300 text-sm flex items-center bg-stone-700/30 hover:bg-stone-700/50 px-2 py-1 rounded transition-colors font-medium"
        >
          <DashboardIcons.Notes className="w-4 h-4 mr-1" />
          View all notes ({parsedNotes.length})
        </button>
      </div>

      {showAllNotes && <NotesModal />}
    </>
  );
};

export default DealNotes;
