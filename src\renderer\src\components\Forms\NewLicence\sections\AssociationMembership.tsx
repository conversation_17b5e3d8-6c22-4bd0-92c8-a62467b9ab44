import React from 'react'
import { FormSection, FormField, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Association Membership section component
 *
 * This component handles the Association Membership section of the New License form.
 * It collects information about the applicant's membership in an association.
 *
 * Placeholders used in this section:
 * - {F5A} - Member of Association Yes (Mark with X)
 * - {F5B} - Member of Association No (Mark with X)
 * - {F6} - Association Name (Fill in)
 * - {ASSFARN} - Association FAR number (Fill in)
 * - {F7} - Membership Number (Fill in)
 * - {F8} - Date Joined Association (Fill in)
 * - {ASSEXPIRE} - Date of Expiry of Membership (Fill in)
 */
const AssociationMembership: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Association Membership</h3>
      <FormSection
        title="Member of Association"
        subtitle="Please provide information about association membership"
      >
        <div className="space-y-3">
          <div className="mb-4">
            <RadioGroup
              name="memberOfAssociation"
              value={formData.f5a ? 'yes' : 'no'}
              onChange={(value) => {
                // Only update if the value is different from the current value
                const currentValue = formData.f5a ? 'yes' : 'no';
                if (value !== currentValue) {
                  updateFormData({
                    f5a: value === 'yes',
                    f5b: value === 'no'
                  })
                }
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Are you a member of an association?"
            />
          </div>

          {formData.f5a && (
            <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
              <FormField
                label="Association Name"
                name="f6"
                value={formData.f6 || ''}
                onChange={handleChange}
                placeholder="Enter association name"
              />

              <FormField
                label="Association FAR Number"
                name="assFarn"
                value={formData.assFarn || ''}
                onChange={handleChange}
                placeholder="Enter FAR number"
              />

              <FormField
                label="Membership Number"
                name="f7"
                value={formData.f7 || ''}
                onChange={handleChange}
                placeholder="Enter membership number"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  label="Date Joined"
                  name="f8"
                  value={formData.f8 || ''}
                  onChange={handleChange}
                  placeholder="YYYY-MM-DD"
                  type="date"
                />

                <FormField
                  label="Expiry Date"
                  name="assExpire"
                  value={formData.assExpire || ''}
                  onChange={handleChange}
                  placeholder="YYYY-MM-DD"
                  type="date"
                />
              </div>
            </div>
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default AssociationMembership
