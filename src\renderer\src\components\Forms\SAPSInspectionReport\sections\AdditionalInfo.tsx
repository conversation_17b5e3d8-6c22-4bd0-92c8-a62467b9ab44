import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'
import { isValidPastDate } from '../utils/helpers'

/**
 * Additional Information section component for SAPS Inspection Report form
 */
const AdditionalInfo: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Additional Information</h3>

      <FormSection title="Comments" subtitle="Add any additional comments or observations">
        <div className="space-y-3">
          <div className="flex flex-col">
            <label className="block text-sm font-medium text-stone-300 mb-1">Comments</label>
            <textarea
              name="comments"
              value={formData.comments || ''}
              onChange={handleChange}
              placeholder="Enter any additional comments"
              rows={4}
              className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
            />
          </div>
        </div>
      </FormSection>

      <FormSection title="Origin and Date" subtitle="Enter country of origin and report date">
        <div className="space-y-3">
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Country of Origin</label>
                <input
                  type="text"
                  name="countryOfOrigin"
                  value={formData.countryOfOrigin || ''}
                  onChange={handleChange}
                  placeholder="e.g. South Africa"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Report Date</label>
                <input
                  type="date"
                  name="reportDate"
                  value={formData.reportDate || ''}
                  onChange={handleChange}
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
                {formData.reportDate && !isValidPastDate(formData.reportDate) && (
                  <p className="text-xs text-red-400 mt-1">
                    Date cannot be in the future
                  </p>
                )}
              </div>
            </div>
          </div>
          
          <p className="text-xs text-stone-400 mt-1">
            This information is required for the SAPS Inspection Report
          </p>
        </div>
      </FormSection>
    </div>
  )
}

export default AdditionalInfo
