import React, { createContext } from 'react'
import { useWindowSize } from '../../contexts/WindowSizeContext'
import TestDataDropdown from './TestDataDropdown'

// Create a context for DocScript form state
export const DocScriptFormContext = createContext({
  isDocScriptForm: false
})

// Add a custom-scrollbar style
const customScrollbarStyle = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #4a4a4a;
    border-radius: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #636363;
  }
`

export interface FormSection {
  id: string
  title: string
}

interface TestDataOption {
  name: string
  value: string
}

interface FormLayoutProps {
  title: string
  sections: FormSection[]
  currentStep: number
  setCurrentStep: (step: number) => void
  onSubmit: (e: React.FormEvent) => void
  children: React.ReactNode
  submissionStatus?: 'idle' | 'processing' | 'success' | 'error'
  submissionMessage?: string | null
  onCancelSubmission?: () => void
  className?: string
  onNextStep?: () => void
  onFillTestData?: (dataType: string) => void // Updated to accept dataType parameter
  testDataOptions?: TestDataOption[] // Options for test data dropdown
  isDocScriptForm?: boolean // Flag to identify DocScript forms
}

const FormLayout = ({
  title,
  sections,
  currentStep,
  setCurrentStep,
  onSubmit,
  children,
  submissionStatus = 'idle',
  submissionMessage = null,
  onCancelSubmission = () => {},
  className = '',
  onNextStep,
  onFillTestData,
  testDataOptions = [],
  isDocScriptForm = false
}: FormLayoutProps) => {
  const totalSteps = sections.length

  const nextStep = () => {
    if (onNextStep) {
      onNextStep()
    } else if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Render section navigation
  const renderSectionNav = () => {
    // For DocScript forms, only show the section title and step counter, not the step indicators
    if (isDocScriptForm) {
      return (
        <div className="mb-4">
          {/* Section title and step counter */}
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <span className="inline-flex items-center justify-center w-6 h-6 mr-2 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs font-bold shadow-lg">
                {currentStep}
              </span>
              {sections[currentStep - 1].title}
            </h3>
            <span className="text-xs bg-stone-800 px-2 py-0.5 rounded-full text-stone-300 font-medium shadow-inner">
              Step {currentStep} of {totalSteps}
            </span>
          </div>

          {/* Progress bar */}
          <div className="w-full bg-stone-800 h-2 rounded-full overflow-hidden shadow-inner mb-2 relative">
            <div
              className="bg-gradient-to-r from-orange-500 to-orange-600 h-full transition-all duration-500 ease-out rounded-full shadow-lg"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            >
              <div className="absolute top-0 right-0 -mr-0.5 -mt-0.5 w-3 h-3 rounded-full bg-orange-600 border-2 border-stone-900 shadow-lg"></div>
            </div>
          </div>
        </div>
      )
    }

    // For non-DocScript forms, show the full navigation with step indicators
    return (
      <div className="mb-8">
        {/* Section title and step counter */}
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <span className="inline-flex items-center justify-center w-8 h-8 mr-3 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 text-white text-sm font-bold shadow-lg">
              {currentStep}
            </span>
            {sections[currentStep - 1].title}
          </h3>
          <span className="text-sm bg-stone-800 px-3 py-1 rounded-full text-stone-300 font-medium shadow-inner">
            Step {currentStep} of {totalSteps}
          </span>
        </div>

        {/* Progress bar */}
        <div className="w-full bg-stone-800 h-2.5 rounded-full overflow-hidden shadow-inner mb-4 relative">
          <div
            className="bg-gradient-to-r from-orange-500 to-orange-600 h-full transition-all duration-500 ease-out rounded-full shadow-lg"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          >
            <div className="absolute top-0 right-0 -mr-1 -mt-1 w-4 h-4 rounded-full bg-orange-600 border-2 border-stone-900 shadow-lg"></div>
          </div>
        </div>

        {/* Step indicators */}
        <div className="flex justify-between relative">
          {/* Connecting line behind the indicators */}
          <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-stone-700 -translate-y-1/2 z-0"></div>

          {sections.map((section, index) => (
            <div
              key={section.id}
              onClick={() => index < currentStep && setCurrentStep(index + 1)}
              className={`
                relative z-10 flex flex-col items-center transition-all duration-300
                ${index < currentStep ? 'cursor-pointer hover:scale-110' : 'cursor-default'}
              `}
            >
              {/* Step circle */}
              <div
                className={`
                  flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300
                  ${
                    index + 1 === currentStep
                      ? 'bg-gradient-to-r from-orange-500 to-orange-600 border-orange-300 shadow-lg shadow-orange-500/20 scale-110'
                      : index + 1 < currentStep
                        ? 'bg-gradient-to-r from-green-500 to-green-600 border-green-300 shadow-md'
                        : 'bg-stone-800 border-stone-700'
                  }
                `}
              >
                {index + 1 < currentStep ? (
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                ) : (
                  <span
                    className={`text-xs font-bold ${index + 1 === currentStep ? 'text-white' : 'text-stone-400'}`}
                  >
                    {index + 1}
                  </span>
                )}
              </div>

              {/* Step title */}
              <span
                className={`
                  mt-2 text-xs font-medium transition-all duration-300
                  ${
                    index + 1 === currentStep
                      ? 'text-orange-400'
                      : index + 1 < currentStep
                        ? 'text-green-400'
                        : 'text-stone-500'
                  }
                `}
              >
                {section.title}
              </span>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Render submission status overlay
  const renderSubmissionStatus = () => {
    if (submissionStatus === 'idle') return null

    return (
      <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 transition-opacity">
        <div className="bg-stone-800 rounded-xl p-8 max-w-md w-full shadow-2xl border border-stone-700">
          {submissionStatus === 'processing' && (
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 border-4 border-stone-600 border-t-orange-500 rounded-full animate-spin mb-4"></div>
              <h3 className="text-xl font-semibold text-white mb-2">Processing Document</h3>
              <p className="text-stone-400 text-center">{submissionMessage}</p>
            </div>
          )}

          {submissionStatus === 'success' && (
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="w-10 h-10 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Success!</h3>
              <p className="text-stone-400 text-center">{submissionMessage}</p>
            </div>
          )}

          {submissionStatus === 'error' && (
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="w-10 h-10 text-red-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Error</h3>
              <p className="text-stone-400 text-center">{submissionMessage}</p>
              <button
                onClick={onCancelSubmission}
                className="mt-4 px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors"
              >
                Try Again
              </button>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Use the window size context
  const { calculateResponsiveHeight } = useWindowSize()

  // Calculate responsive max height based on window height
  const formMaxHeight = calculateResponsiveHeight(85) // 85% of window height

  return (
    <DocScriptFormContext.Provider value={{ isDocScriptForm }}>
      <style>{customScrollbarStyle}</style>

      {/* Add the submission status overlay */}
      {renderSubmissionStatus()}

      <div
        className={`flex flex-col h-full ${className}`}
        style={{
          maxHeight: `${formMaxHeight}px`,
          ...(isDocScriptForm ? { position: 'relative', overflow: 'hidden' } : {})
        }}
      >
        {isDocScriptForm ? (
          <>
            {/* Fixed header for DocScript forms */}
            <div className="flex-shrink-0 bg-stone-800/95 backdrop-blur-sm border-b border-stone-700/50 pb-2 z-10">
              <div className="flex justify-between items-center mb-2 pt-1">
                <h2 className="text-xl font-bold text-white">{title}</h2>

                {/* Test Data Dropdown - Only show in development mode */}
                {onFillTestData && testDataOptions.length > 0 && process.env.NODE_ENV === 'development' && (
                  <TestDataDropdown
                    options={testDataOptions}
                    onSelect={(value) => onFillTestData(value)}
                  />
                )}
              </div>

              <div>{renderSectionNav()}</div>
            </div>

            {/* Scrollable content area that fits between header and footer */}
            <div className="overflow-y-auto pr-2 custom-scrollbar flex-1">
              <form id="formSubmit" onSubmit={onSubmit} className="space-y-3">
                {children}
              </form>
            </div>
          </>
        ) : (
          <>
            <div className={`flex justify-between items-center mb-4 flex-shrink-0`}>
              <h2 className="text-2xl font-bold text-white">{title}</h2>

              {/* Test Data Dropdown - Only show in development mode */}
              {onFillTestData && testDataOptions.length > 0 && process.env.NODE_ENV === 'development' && (
                <TestDataDropdown
                  options={testDataOptions}
                  onSelect={(value) => onFillTestData(value)}
                />
              )}
            </div>

            <div className="mb-4 flex-shrink-0">{renderSectionNav()}</div>

            <div className="flex-grow overflow-y-auto pr-4 mb-4 custom-scrollbar">
              <form id="formSubmit" onSubmit={onSubmit} className="space-y-6">
                {children}
              </form>
            </div>
          </>
        )}

        {/* Footer navigation */}
        {isDocScriptForm ? (
          <div className="flex-shrink-0 bg-stone-800/95 backdrop-blur-sm border-t border-stone-700 pt-2 pb-2 px-4 flex justify-between z-10 mt-auto">
            <button
              type="button"
              onClick={prevStep}
              className={`px-4 py-1.5 text-sm rounded-lg font-medium transition ${
                currentStep === 1
                  ? 'bg-stone-600 text-stone-400 cursor-not-allowed'
                  : 'bg-stone-700 text-white hover:bg-stone-600'
              }`}
              disabled={currentStep === 1}
            >
              Previous
            </button>

            <div className="flex items-center space-x-2">
              {sections.map((section, index) => (
                <div
                  key={section.id}
                  onClick={() => index < currentStep && setCurrentStep(index + 1)}
                  className={`
                    flex flex-col items-center justify-center
                    ${index < currentStep ? 'cursor-pointer' : 'cursor-default'}
                  `}
                >
                  {/* Step title above number */}
                  <span
                    className={`
                      text-[10px] font-medium mb-1
                      ${index + 1 === currentStep
                        ? 'text-orange-400'
                        : index + 1 < currentStep
                          ? 'text-green-400'
                          : 'text-stone-500'}
                    `}
                  >
                    {section.title}
                  </span>
                  {/* Step circle */}
                  <div
                    className={`
                      flex items-center justify-center w-6 h-6 rounded-full border transition-all
                      ${index + 1 === currentStep
                        ? 'bg-gradient-to-r from-orange-500 to-orange-600 border-orange-300'
                        : index + 1 < currentStep
                          ? 'bg-gradient-to-r from-green-500 to-green-600 border-green-300'
                          : 'bg-stone-800 border-stone-700'}
                    `}
                  >
                    {index + 1 < currentStep ? (
                      <svg
                        className="w-3 h-3 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    ) : (
                      <span className={`text-[10px] font-bold ${index + 1 === currentStep ? 'text-white' : 'text-stone-400'}`}>
                        {index + 1}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {currentStep < totalSteps ? (
              <button
                type="button"
                onClick={nextStep}
                className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-1.5 text-sm rounded-lg font-medium transition"
              >
                Next
              </button>
            ) : (
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  onSubmit(e);
                }}
                className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-1.5 text-sm rounded-lg font-medium shadow-xl shadow-orange-500/20 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                Submit
              </button>
            )}
          </div>
        ) : (
          <div className="flex justify-between mt-auto pt-3 border-t border-stone-700 flex-shrink-0">
            <button
              type="button"
              onClick={prevStep}
              className={`px-6 py-2 rounded-lg font-medium transition ${
                currentStep === 1
                  ? 'bg-stone-600 text-stone-400 cursor-not-allowed'
                  : 'bg-stone-700 text-white hover:bg-stone-600'
              }`}
              disabled={currentStep === 1}
            >
              Previous
            </button>

            {currentStep < totalSteps ? (
              <button
                type="button"
                onClick={nextStep}
                className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition"
              >
                Next
              </button>
            ) : (
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  onSubmit(e);
                }}
                className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-2 rounded-lg font-medium shadow-xl shadow-orange-500/20 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                Submit
              </button>
            )}
          </div>
        )}
      </div>
    </DocScriptFormContext.Provider>
  )
}

export default FormLayout
