import React, { useState, useEffect } from 'react';
import { Deal, Pipeline, PipelineStage } from '../../types/pipedrive';
import { DashboardIcons } from '../icons/DashboardIcons';

interface MoveDealModalProps {
  deal: Deal;
  onClose: () => void;
  onMoveDeal: (dealId: string, newStageId: string) => Promise<void>;
  getAllStages: () => Promise<any[]>;
}

interface StageWithPipeline extends PipelineStage {
  pipeline: {
    id: string;
    name: string;
  };
}

const MoveDealModal: React.FC<MoveDealModalProps> = ({ deal, onClose, onMoveDeal, getAllStages }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [allStages, setAllStages] = useState<StageWithPipeline[]>([]);
  const [selectedPipelineId, setSelectedPipelineId] = useState<string | null>(null);
  const [selectedStageId, setSelectedStageId] = useState<string | null>(null);
  const [movingDeal, setMovingDeal] = useState(false);
  const [currentStageName, setCurrentStageName] = useState<string>('Unknown');
  const [currentPipelineName, setCurrentPipelineName] = useState<string>('Unknown');

  // Fetch all stages and organize them by pipeline
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all stages with pipeline information
        const stagesData = await getAllStages();

        if (stagesData && stagesData.length > 0) {
          setAllStages(stagesData as StageWithPipeline[]);

          // Extract unique pipelines
          const uniquePipelines: Pipeline[] = [];
          const pipelineIds = new Set<string>();

          stagesData.forEach((stage: StageWithPipeline) => {
            if (stage.pipeline && !pipelineIds.has(stage.pipeline.id)) {
              pipelineIds.add(stage.pipeline.id);
              uniquePipelines.push({
                id: stage.pipeline.id,
                name: stage.pipeline.name,
                description: null,
                created_at: '',
                updated_at: ''
              });
            }

            // Find current stage and pipeline names
            if (stage.id === deal.stage_id) {
              setCurrentStageName(stage.name);
              if (stage.pipeline) {
                setCurrentPipelineName(stage.pipeline.name);
                // Pre-select the current pipeline
                setSelectedPipelineId(stage.pipeline.id);
              }
            }
          });

          setPipelines(uniquePipelines);
        }
      } catch (err) {
        console.error('Error fetching stages:', err);
        setError('Failed to load pipelines and stages');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [deal.stage_id, getAllStages]);

  // Get stages for the selected pipeline
  const filteredStages = allStages.filter(
    stage => stage.pipeline && stage.pipeline.id === selectedPipelineId
  );

  // Handle move deal
  const handleMoveDeal = async () => {
    if (!selectedStageId) return;

    try {
      setMovingDeal(true);
      await onMoveDeal(deal.id, selectedStageId);
      onClose();
    } catch (err) {
      console.error('Error moving deal:', err);
      setError('Failed to move deal');
    } finally {
      setMovingDeal(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 backdrop-blur-sm" onClick={onClose}>
      <div
        className="bg-gradient-to-br from-stone-800 to-stone-900 rounded-xl shadow-2xl w-full max-w-md p-6 border border-orange-500/30 animate-fadeIn"
        onClick={e => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-5 border-b border-stone-700/50 pb-3">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <DashboardIcons.Move className="w-5 h-5 mr-2 text-orange-400" />
            Move Deal to Another Pipeline
          </h3>
          <button
            onClick={onClose}
            className="text-stone-400 hover:text-orange-400 p-1 rounded-full transition-colors"
          >
            <DashboardIcons.Close className="w-5 h-5" />
          </button>
        </div>

        {/* Current location */}
        <div className="mb-5 bg-stone-700/30 p-3 rounded-md border border-stone-600/30">
          <div className="text-sm text-stone-400 mb-1">Current Location:</div>
          <div className="flex items-center text-white">
            <span className="font-medium">{currentPipelineName}</span>
            <DashboardIcons.NextPage className="w-4 h-4 mx-2 text-stone-500" />
            <span className="font-medium">{currentStageName}</span>
          </div>
        </div>

        {/* Deal title */}
        <div className="mb-5">
          <div className="text-sm text-stone-400 mb-1">Deal:</div>
          <div className="text-white font-medium">{deal.title}</div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <DashboardIcons.Spinner className="w-6 h-6 text-orange-400 animate-spin mr-3" />
            <span className="text-stone-300">Loading pipelines and stages...</span>
          </div>
        ) : error ? (
          <div className="bg-red-900/20 border border-red-500/30 rounded-md p-4 text-center mb-5">
            <p className="text-red-400">{error}</p>
          </div>
        ) : (
          <>
            {/* Pipeline selection */}
            <div className="mb-4">
              <label className="block text-stone-300 text-sm font-medium mb-2">
                Select Pipeline
              </label>
              <select
                value={selectedPipelineId || ''}
                onChange={e => {
                  setSelectedPipelineId(e.target.value);
                  setSelectedStageId(null); // Reset stage selection when pipeline changes
                }}
                className="w-full px-3 py-2 bg-stone-700 border border-stone-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="" disabled>Select a pipeline</option>
                {pipelines.map(pipeline => (
                  <option key={pipeline.id} value={pipeline.id}>
                    {pipeline.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Stage selection - only shown if a pipeline is selected */}
            {selectedPipelineId && (
              <div className="mb-5">
                <label className="block text-stone-300 text-sm font-medium mb-2">
                  Select Stage
                </label>
                <select
                  value={selectedStageId || ''}
                  onChange={e => setSelectedStageId(e.target.value)}
                  className="w-full px-3 py-2 bg-stone-700 border border-stone-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="" disabled>Select a stage</option>
                  {filteredStages.map(stage => (
                    <option
                      key={stage.id}
                      value={stage.id}
                      disabled={stage.id === deal.stage_id} // Disable current stage
                    >
                      {stage.name} {stage.id === deal.stage_id ? '(Current)' : ''}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </>
        )}

        {/* Action buttons */}
        <div className="flex justify-end gap-3 pt-3 border-t border-stone-700/50">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleMoveDeal}
            disabled={!selectedStageId || movingDeal || selectedStageId === deal.stage_id}
            className={`px-4 py-2 bg-orange-500 text-white rounded-md flex items-center ${
              !selectedStageId || movingDeal || selectedStageId === deal.stage_id
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-orange-600 transition-colors'
            }`}
          >
            {movingDeal && <DashboardIcons.Spinner className="w-4 h-4 mr-2 animate-spin" />}
            Move Deal
          </button>
        </div>
      </div>
    </div>
  );
};

export default MoveDealModal;
