import { memo, useState, useEffect } from 'react'
import { Client, License, ClientWallet } from '../../types'
import { Firearm } from '../../types/firearm'
import { DashboardIcons } from '../icons/DashboardIcons'
import { Dialog } from '../Dialog'
import { WalletTransactionForm } from '../Forms/WalletTransactionForm'
import { WalletTransactionLog } from '../WalletTransactionLog'
import { getSupabase } from '../../lib/supabase'

interface ClientCardProps {
  client: Client
  onEditClient: (client: Client) => void
  onAddLicense: (clientId: string) => void
  onEditLicense: (license: License, clientId: string) => void
  onDeleteLicense: (licenseId: string, clientId: string) => void
  onDeleteClient: (clientId: string) => void
  highlightTerms?: string[]
  isFocused?: boolean
  isOtherCardFocused?: boolean
  onFocusToggle?: (clientId: string | null) => void
}

function ClientCard({
  client,
  onEditClient,
  onAddLicense,
  onEditLicense,
  onDeleteLicense,
  onDeleteClient,
  highlightTerms = [],
  isFocused = false,
  isOtherCardFocused = false,
  onFocusToggle = () => {}
}: ClientCardProps): JSX.Element {
  const [dialogState, setDialogState] = useState<{
    isOpen: boolean
    licenseId: string
    clientId: string | null
  }>({
    isOpen: false,
    licenseId: '',
    clientId: null
  })

  const [wallet, setWallet] = useState<ClientWallet | null>(null)
  const [showAddFundsForm, setShowAddFundsForm] = useState(false)
  const [showDeductFundsForm, setShowDeductFundsForm] = useState(false)
  const [showTransactionLog, setShowTransactionLog] = useState(false)
  const [assignedFirearms, setAssignedFirearms] = useState<Firearm[]>([])
  const [loadingFirearms, setLoadingFirearms] = useState(false)
  const [licensePage, setLicensePage] = useState(1)
  const [firearmPage, setFirearmPage] = useState(1)
  const ITEMS_PER_PAGE = 3 // Number of items to show per page

  // Fetch client wallet on component mount
  useEffect(() => {
    const fetchWallet = async () => {
      try {
        const supabase = getSupabase()
        const { data, error } = await supabase
          .from('client_credit_wallets')
          .select('*')
          .eq('client_id', client.id)
          .single()

        if (error) {
          if (error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
            // Silently handle error
          }
          return
        }

        setWallet(data as ClientWallet)

        // Update client with wallet data if available
        if (data) {
          client.wallet_id = data.id
          client.wallet_balance = data.balance
        }
      } catch (err) {
        // Silently handle error
      }
    }

    fetchWallet()
  }, [client.id])

  // Fetch assigned firearms
  useEffect(() => {
    const fetchAssignedFirearms = async () => {
      setLoadingFirearms(true)
      try {
        const supabase = getSupabase()

        // Fetch active assignments for this client with firearm details
        const { data: assignments, error: assignmentError } = await supabase
          .from('firearm_assignments')
          .select(`
            id,
            firearm_id,
            assigned_date,
            free_storage_until,
            notes,
            firearms:firearm_id(*)
          `)
          .eq('client_id', client.id)
          .is('return_date', null)

        if (assignmentError) {
          console.error('Error fetching firearm assignments:', assignmentError)
          return
        }

        if (assignments && assignments.length > 0) {
          // Extract firearms from assignments and add assignment details
          const firearms = assignments.map(assignment => {
            // Cast to unknown first to avoid TypeScript error
            const firearm = assignment.firearms as unknown as Firearm
            if (firearm) {
              firearm.assignment_id = assignment.id
              firearm.assignment_notes = assignment.notes
              firearm.free_storage_until = assignment.free_storage_until
              firearm.is_assigned = true
              // Store assigned_date from the assignment in a custom property
              firearm.assigned_date = assignment.assigned_date
            }
            return firearm
          }).filter(Boolean) // Remove any null values

          setAssignedFirearms(firearms)
        }
      } catch (error) {
        console.error('Error in fetchAssignedFirearms:', error)
      } finally {
        setLoadingFirearms(false)
      }
    }

    fetchAssignedFirearms()
  }, [client.id])

  // Highlight matching text based on search terms
  const highlightText = (text: string) => {
    if (!text || !highlightTerms || highlightTerms.length === 0) {
      return text
    }

    // Escape special regex characters and filter out empty terms
    const terms = highlightTerms
      .filter((term) => term.trim().length > 0)
      .map((term) => term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))

    if (terms.length === 0) return text

    // Create regex for all terms with case-insensitive matching
    const regex = new RegExp(`(${terms.join('|')})`, 'gi')

    // Split the text by matches and create spans with highlighting
    const parts = text.split(regex)

    return parts.map((part, i) => {
      // Check if this part matches any of our search terms (case insensitive)
      const isMatch = terms.some((term) => new RegExp(`^${term}$`, 'i').test(part))

      return isMatch ? (
        <span key={i} className="bg-orange-500/30 text-white font-medium rounded px-0.5">
          {part}
        </span>
      ) : (
        part
      )
    })
  }

  const handleDelete = (licenseId: string): void => {
    setDialogState({ isOpen: true, licenseId, clientId: null })
  }

  const handleDeleteClient = (): void => {
    setDialogState({ isOpen: true, licenseId: '', clientId: client.id })
  }

  const handleConfirmDelete = (): void => {
    if (dialogState.clientId) {
      onDeleteClient(dialogState.clientId)
    } else {
      onDeleteLicense(dialogState.licenseId, client.id)
    }
    setDialogState({ isOpen: false, licenseId: '', clientId: null })
  }

  // Handle focus toggle
  const handleFocusToggle = () => {
    onFocusToggle(isFocused ? null : client.id)
  }

  return (
    <>
      <div
        className={`px-2 py-1 transition-all duration-300 ${
          isFocused && !isOtherCardFocused
            ? 'z-10'
            : isOtherCardFocused
              ? 'scale-98 opacity-40'
              : ''
        }`}
      >
        <div
          className={`bg-gradient-to-br from-stone-800/90 to-stone-900/90 backdrop-blur-md rounded-2xl p-6 shadow-[0_0_25px_rgba(0,0,0,0.3)] border transition-all duration-300 ease-in-out ${
            isFocused ? 'border-orange-500 shadow-lg shadow-orange-500/30' : 'border-orange-500/30'
          } relative overflow-hidden`}
        >
          {/* Decorative element */}
          <div className="absolute -top-24 -right-24 w-48 h-48 bg-orange-500/10 rounded-full blur-2xl" />
          <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-orange-500/5 rounded-full blur-2xl" />

          <div className="relative">
            <div className="flex justify-between items-start mb-6">
              <div className="flex items-center">
                <h2 className="text-2xl font-semibold text-white flex items-center">
                  <span className="inline-block mr-3 w-1.5 h-6 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                  Client Details
                </h2>

                {/* Focus button - only shown when not focused */}
                {!isFocused && (
                  <button
                    onClick={handleFocusToggle}
                    className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-stone-600/80 hover:text-white"
                    title="Focus on this client"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                    </svg>
                  </button>
                )}

                {/* Close button - shown only when focused */}
                {isFocused && (
                  <button
                    onClick={handleFocusToggle}
                    className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-red-500/80 hover:text-white"
                    title="Exit focus mode"
                  >
                    <DashboardIcons.Close className="w-4 h-4" />
                  </button>
                )}
              </div>
              <div className="flex gap-3">
                {client.role !== 'admin' && (
                  <button
                    onClick={() => onEditClient(client)}
                    className="flex items-center gap-2 px-4 py-2.5 bg-stone-700/80 hover:bg-stone-600 text-white rounded-lg transition-all duration-200 hover:shadow-lg shadow-md border border-white/5"
                  >
                    <DashboardIcons.Edit className="w-4 h-4" />
                    Edit Client
                  </button>
                )}
                <button
                  onClick={() => onAddLicense(client.id)}
                  className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white rounded-lg transition-all duration-200 hover:shadow-lg shadow-md"
                >
                  <DashboardIcons.Add className="w-4 h-4" />
                  Add License
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-3 text-stone-300 bg-stone-800/30 p-5 rounded-xl border border-stone-700/50">
                <p className="flex items-center gap-2 group">
                  <span className="text-orange-400 font-medium min-w-20">Name:</span>
                  <span className="text-white">
                    {highlightText(
                      `${client.first_name?.toUpperCase() || ''} ${client.last_name?.toUpperCase() || ''}`
                    )}
                  </span>
                  {client.role === 'admin' && (
                    <span className="ml-2 px-2 py-0.5 bg-orange-500 text-white text-xs font-semibold rounded-full">
                      Admin
                    </span>
                  )}
                  <button
                    onClick={() =>
                      navigator.clipboard.writeText(
                        `${client.first_name || ''} ${client.last_name || ''}`
                      )
                    }
                    className="opacity-0 group-hover:opacity-100 text-white hover:text-orange-400 transition-all duration-200"
                    title="Copy name"
                  >
                    <DashboardIcons.Copy className="w-4 h-4" />
                  </button>
                </p>
                <p className="flex items-center gap-2 group">
                  <span className="text-orange-400 font-medium min-w-20">Email:</span>
                  <span className="text-white">
                    {client.email ? highlightText(client.email) : 'N/A'}
                  </span>
                  {client.email && (
                    <button
                      onClick={() => navigator.clipboard.writeText(client.email)}
                      className="opacity-0 group-hover:opacity-100 text-white hover:text-orange-400 transition-all duration-200"
                      title="Copy email"
                    >
                      <DashboardIcons.Copy className="w-4 h-4" />
                    </button>
                  )}
                </p>
                <p className="flex items-center gap-2 group">
                  <span className="text-orange-400 font-medium min-w-20">Phone:</span>
                  <span className="text-white">
                    {client.phone
                      ? highlightText(client.phone.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3'))
                      : 'N/A'}
                  </span>
                  {client.phone && (
                    <button
                      onClick={() => navigator.clipboard.writeText(client.phone)}
                      className="opacity-0 group-hover:opacity-100 text-white hover:text-orange-400 transition-all duration-200"
                      title="Copy phone"
                    >
                      <DashboardIcons.Copy className="w-4 h-4" />
                    </button>
                  )}
                </p>
                <p className="flex items-center gap-2 group">
                  <span className="text-orange-400 font-medium min-w-20">ID Number:</span>
                  <span className="text-white">
                    {client.id_number
                      ? highlightText(client.id_number.replace(/(\d{6})(\d{4})(\d{3})/, '$1 $2 $3'))
                      : 'N/A'}
                  </span>
                  {client.id_number && (
                    <button
                      onClick={() => navigator.clipboard.writeText(client.id_number)}
                      className="opacity-0 group-hover:opacity-100 text-white hover:text-orange-400 transition-all duration-200"
                      title="Copy ID number"
                    >
                      <DashboardIcons.Copy className="w-4 h-4" />
                    </button>
                  )}
                </p>
                <p className="flex items-center gap-2 group">
                  <span className="text-orange-400 font-medium min-w-20">Address:</span>
                  <span className="text-white">
                    {client.address ? highlightText(client.address) : 'N/A'}
                  </span>
                  {client.address && (
                    <button
                      onClick={() => navigator.clipboard.writeText(client.address)}
                      className="opacity-0 group-hover:opacity-100 text-white hover:text-orange-400 transition-all duration-200"
                      title="Copy address"
                    >
                      <DashboardIcons.Copy className="w-4 h-4" />
                    </button>
                  )}
                </p>
                {client.club_provider && (
                  <p className="flex items-center gap-2 group">
                    <span className="text-orange-400 font-medium min-w-20">Club Provider:</span>
                    <span className="text-white">{client.club_provider}</span>
                  </p>
                )}
                {client.username && (
                  <p className="flex items-center gap-2 group">
                    <span className="text-orange-400 font-medium min-w-20">Username:</span>
                    <span className="text-white">{client.username.replace(/./g, '*')}</span>
                    <button
                      onClick={() => navigator.clipboard.writeText(client.username!)}
                      className="opacity-0 group-hover:opacity-100 text-white hover:text-orange-400 transition-all duration-200"
                      title="Copy username"
                    >
                      <DashboardIcons.Copy className="w-4 h-4" />
                    </button>
                  </p>
                )}
                {/* Password field removed as it's no longer stored in the database */}

                {/* Wallet Balance Section */}
                <div className="mt-4 pt-4 border-t border-stone-700/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-orange-400 font-medium">Wallet Balance:</span>
                      <span className={`text-lg font-semibold ${
                        wallet?.balance && wallet.balance > 0
                          ? 'text-green-500'
                          : wallet?.balance && wallet.balance < 0
                            ? 'text-red-500'
                            : 'text-white'
                      }`}>
                        R {wallet?.balance?.toFixed(2) || '0.00'}
                      </span>
                      <span className="text-xs text-stone-400 italic ml-1">(For Firearm Storage)</span>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setShowTransactionLog(true)}
                        className="px-2 py-1 text-xs bg-stone-700 hover:bg-stone-600 text-white rounded transition-colors"
                        title="View transaction history"
                      >
                        <span className="flex items-center gap-1">
                          <DashboardIcons.List className="w-3 h-3" />
                          History
                        </span>
                      </button>
                    </div>
                  </div>
                  <div className="flex gap-2 mt-2">
                    <button
                      onClick={() => setShowAddFundsForm(true)}
                      className="flex-1 px-3 py-1.5 text-sm bg-green-600 hover:bg-green-700 text-white rounded transition-colors flex items-center justify-center gap-1"
                    >
                      <DashboardIcons.Add className="w-3 h-3" />
                      Add Funds
                    </button>
                    <button
                      onClick={() => setShowDeductFundsForm(true)}
                      className="flex-1 px-3 py-1.5 text-sm bg-red-600 hover:bg-red-700 text-white rounded transition-colors flex items-center justify-center gap-1"
                      disabled={!wallet || wallet.balance <= 0}
                    >
                      <DashboardIcons.Remove className="w-3 h-3" />
                      Deduct Funds
                    </button>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-8">
                <div className="bg-stone-800/30 p-5 rounded-xl border border-stone-700/50">
                  <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <span className="inline-block mr-3 w-1 h-5 bg-orange-500 rounded-full" />
                    Licences ({client.gun_licences.length})
                  </h3>
                  <div className="space-y-3">
                    {client.gun_licences
                      .sort(
                        (a, b) =>
                          new Date(a.expiry_date).getTime() - new Date(b.expiry_date).getTime()
                      )
                      .slice((licensePage - 1) * ITEMS_PER_PAGE, licensePage * ITEMS_PER_PAGE)
                      .map((license, _index) => (
                      <div
                        key={license.id}
                        className="relative p-4 bg-stone-800/50 rounded-xl border border-stone-700/50 hover:border-orange-500/30 group transition-all duration-300 hover:shadow-lg"
                      >
                        <button
                          onClick={() => onEditLicense(license, client.id)}
                          className="absolute top-3 right-3 p-2 hover:bg-stone-700 rounded-lg transition-colors opacity-0 group-hover:opacity-100"
                        >
                          <DashboardIcons.Edit className="w-4 h-4 text-orange-400" />
                        </button>

                        <div className="space-y-2 pr-12">
                          <p className="text-white font-medium flex items-center">
                            <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full opacity-75" />
                            {license.make?.toUpperCase() || 'N/A'}{' '}
                            {license.issue_date && !isNaN(new Date(license.issue_date).getTime())
                              ? new Date(license.issue_date).toISOString().split('T')[0]
                              : 'N/A'}
                          </p>
                          <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-2">
                            <p className="text-sm text-stone-400">
                              <span className="text-orange-400/80">Type:</span>{' '}
                              {license.type?.toUpperCase() || 'N/A'}
                            </p>
                            <p className="text-sm text-stone-400">
                              <span className="text-orange-400/80">Caliber:</span>{' '}
                              {license.caliber?.toUpperCase() || 'N/A'}
                            </p>
                            <p className="text-sm text-stone-400">
                              <span className="text-orange-400/80">Serial Number:</span>{' '}
                              {license.serial_number?.toUpperCase() || 'N/A'}
                            </p>
                            <p className="text-sm text-stone-400">
                              <span className="text-orange-400/80">Section:</span>{' '}
                              {license.section?.toUpperCase() || 'N/A'}
                            </p>
                            <p className="text-sm text-stone-400">
                              <span className="text-orange-400/80">Stock Code:</span>{' '}
                              {license.stock_code?.toUpperCase() || 'N/A'}
                            </p>
                          </div>
                          {license.barrel_serial && (
                            <p className="text-sm text-stone-400">
                              <span className="text-orange-400/80">Barrel:</span>{' '}
                              {license.barrel_make?.toUpperCase() || 'N/A'} -{' '}
                              {license.barrel_serial.toUpperCase()}
                            </p>
                          )}
                          {license.receiver_serial && (
                            <p className="text-sm text-stone-400">
                              <span className="text-orange-400/80">Receiver:</span>{' '}
                              {license.receiver_make?.toUpperCase() || 'N/A'} -{' '}
                              {license.receiver_serial.toUpperCase()}
                            </p>
                          )}
                          {license.frame_serial && (
                            <p className="text-sm text-stone-400">
                              <span className="text-orange-400/80">Frame:</span>{' '}
                              {license.frame_make?.toUpperCase() || 'N/A'} -{' '}
                              {license.frame_serial.toUpperCase()}
                            </p>
                          )}
                          <p className="text-sm text-stone-300 flex items-center gap-2 mt-1 pt-1 border-t border-stone-700/50">
                            <span className="text-orange-400/80">Expiry:</span>
                            <span className="font-medium">
                              {new Date(license.expiry_date).toISOString().split('T')[0]}
                            </span>
                            {((): JSX.Element => {
                              const today = new Date()
                              const expiryDate = new Date(license.expiry_date)
                              const daysUntilExpiry = Math.ceil(
                                (expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
                              )

                              if (daysUntilExpiry < 0) {
                                return (
                                  <span className="flex items-center gap-1 ml-1">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-5 w-5 text-red-500"
                                      viewBox="0 0 20 20"
                                      fill="currentColor"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                    <span className="text-red-500 font-medium">
                                      Expired {Math.abs(daysUntilExpiry)} days ago
                                    </span>
                                  </span>
                                )
                              } else if (daysUntilExpiry <= 130) {
                                return (
                                  <span className="flex items-center gap-1 ml-1">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-5 w-5 text-yellow-500"
                                      viewBox="0 0 20 20"
                                      fill="currentColor"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                    <span className="text-yellow-500 font-medium">
                                      Expires in {daysUntilExpiry} days
                                    </span>
                                  </span>
                                )
                              } else {
                                return (
                                  <span className="flex items-center gap-1 ml-1">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-5 w-5 text-green-500"
                                      viewBox="0 0 20 20"
                                      fill="currentColor"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                    <span className="text-green-500 font-medium">
                                      Expires in {daysUntilExpiry} days
                                    </span>
                                  </span>
                                )
                              }
                            })()}
                          </p>
                        </div>

                        <button
                          onClick={() => handleDelete(license.id)}
                          className="absolute bottom-3 right-3 p-2 hover:bg-stone-700 rounded-lg transition-colors opacity-0 group-hover:opacity-100"
                        >
                          <DashboardIcons.Delete className="w-4 h-4 text-red-400" />
                        </button>
                      </div>
                    ))}
                  </div>

                  {/* Pagination for Licenses */}
                  {client.gun_licences.length > ITEMS_PER_PAGE && (
                    <div className="flex justify-center items-center mt-4 space-x-2">
                      <button
                        onClick={() => setLicensePage(prev => Math.max(prev - 1, 1))}
                        disabled={licensePage === 1}
                        className={`p-1 rounded ${
                          licensePage === 1
                            ? 'text-stone-500 cursor-not-allowed'
                            : 'text-orange-500 hover:bg-stone-700'
                        }`}
                        aria-label="Previous page"
                      >
                        <DashboardIcons.PrevPage className="w-5 h-5" />
                      </button>

                      <span className="text-sm text-stone-300">
                        Page {licensePage} of {Math.ceil(client.gun_licences.length / ITEMS_PER_PAGE)}
                      </span>

                      <button
                        onClick={() =>
                          setLicensePage(prev =>
                            Math.min(prev + 1, Math.ceil(client.gun_licences.length / ITEMS_PER_PAGE))
                          )
                        }
                        disabled={licensePage >= Math.ceil(client.gun_licences.length / ITEMS_PER_PAGE)}
                        className={`p-1 rounded ${
                          licensePage >= Math.ceil(client.gun_licences.length / ITEMS_PER_PAGE)
                            ? 'text-stone-500 cursor-not-allowed'
                            : 'text-orange-500 hover:bg-stone-700'
                        }`}
                        aria-label="Next page"
                      >
                        <DashboardIcons.NextPage className="w-5 h-5" />
                      </button>
                    </div>
                  )}
                </div>

                {/* Assigned Firearms Section */}
                <div className="bg-stone-800/30 p-5 rounded-xl border border-stone-700/50">
                  <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <span className="inline-block mr-3 w-1 h-5 bg-orange-500 rounded-full" />
                    Assigned Firearms ({assignedFirearms.length})
                  </h3>

                  {loadingFirearms ? (
                    <div className="flex justify-center items-center h-20">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-orange-500"></div>
                    </div>
                  ) : assignedFirearms.length === 0 ? (
                    <div className="p-4 bg-stone-800/50 rounded-xl border border-stone-700/50 text-stone-400 text-center">
                      No firearms assigned to this client
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {assignedFirearms
                        .slice((firearmPage - 1) * ITEMS_PER_PAGE, firearmPage * ITEMS_PER_PAGE)
                        .map((firearm) => (
                        <div
                          key={firearm.id}
                          className="relative p-4 bg-stone-800/50 rounded-xl border border-stone-700/50 hover:border-orange-500/30 group transition-all duration-300 hover:shadow-lg"
                        >
                          <div className="space-y-2 pr-12">
                            <p className="text-white font-medium flex items-center">
                              <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full opacity-75" />
                              {firearm.make?.toUpperCase() || 'N/A'} {firearm.model?.toUpperCase() || ''}
                              <span className="ml-2 px-2 py-0.5 bg-stone-700 text-stone-300 text-xs font-semibold rounded-full">
                                {firearm.storage_type}
                              </span>
                            </p>
                            <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-2">
                              <p className="text-sm text-stone-400">
                                <span className="text-orange-400/80">Serial:</span>{' '}
                                {firearm.serial?.toUpperCase() || 'N/A'}
                              </p>
                              <p className="text-sm text-stone-400">
                                <span className="text-orange-400/80">Stock #:</span>{' '}
                                {firearm.stock_number?.toUpperCase() || 'N/A'}
                              </p>
                              <p className="text-sm text-stone-400">
                                <span className="text-orange-400/80">Assigned:</span>{' '}
                                {firearm.assigned_date ? new Date(firearm.assigned_date).toLocaleDateString() : 'N/A'}
                              </p>
                              {firearm.free_storage_until && (
                                <p className="text-sm text-stone-400">
                                  <span className="text-orange-400/80">Free Until:</span>{' '}
                                  {new Date(firearm.free_storage_until).toLocaleDateString()}
                                </p>
                              )}
                            </div>
                            {firearm.assignment_notes && (
                              <p className="text-sm text-stone-400 mt-1 pt-1 border-t border-stone-700/50">
                                <span className="text-orange-400/80">Notes:</span>{' '}
                                {firearm.assignment_notes}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Pagination for Assigned Firearms */}
                  {assignedFirearms.length > ITEMS_PER_PAGE && (
                    <div className="flex justify-center items-center mt-4 space-x-2">
                      <button
                        onClick={() => setFirearmPage(prev => Math.max(prev - 1, 1))}
                        disabled={firearmPage === 1}
                        className={`p-1 rounded ${
                          firearmPage === 1
                            ? 'text-stone-500 cursor-not-allowed'
                            : 'text-orange-500 hover:bg-stone-700'
                        }`}
                        aria-label="Previous page"
                      >
                        <DashboardIcons.PrevPage className="w-5 h-5" />
                      </button>

                      <span className="text-sm text-stone-300">
                        Page {firearmPage} of {Math.ceil(assignedFirearms.length / ITEMS_PER_PAGE)}
                      </span>

                      <button
                        onClick={() =>
                          setFirearmPage(prev =>
                            Math.min(prev + 1, Math.ceil(assignedFirearms.length / ITEMS_PER_PAGE))
                          )
                        }
                        disabled={firearmPage >= Math.ceil(assignedFirearms.length / ITEMS_PER_PAGE)}
                        className={`p-1 rounded ${
                          firearmPage >= Math.ceil(assignedFirearms.length / ITEMS_PER_PAGE)
                            ? 'text-stone-500 cursor-not-allowed'
                            : 'text-orange-500 hover:bg-stone-700'
                        }`}
                        aria-label="Next page"
                      >
                        <DashboardIcons.NextPage className="w-5 h-5" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-8 flex justify-start">
              {client.role !== 'admin' && (
                <button
                  onClick={handleDeleteClient}
                  className="flex items-center gap-2 px-4 py-2.5 bg-red-600/90 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg border border-red-500/20"
                >
                  <DashboardIcons.Delete className="w-4 h-4" />
                  Delete Client
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      <Dialog
        isOpen={dialogState.isOpen}
        title={dialogState.clientId ? 'Delete Client' : 'Delete License'}
        message={
          dialogState.clientId
            ? 'Are you sure you want to delete this client and all associated licenses? This action cannot be undone.'
            : 'Are you sure you want to delete this license? This action cannot be undone.'
        }
        onConfirm={handleConfirmDelete}
        onCancel={() => setDialogState({ isOpen: false, licenseId: '', clientId: null })}
      />

      {/* Add Funds Form Modal */}
      {showAddFundsForm && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="w-full max-w-md">
            <WalletTransactionForm
              clientId={client.id}
              onClose={() => setShowAddFundsForm(false)}
              onSuccess={(updatedWallet) => {
                setWallet(updatedWallet)
                client.wallet_balance = updatedWallet.balance
                setShowAddFundsForm(false)
              }}
              transactionType="add"
            />
          </div>
        </div>
      )}

      {/* Deduct Funds Form Modal */}
      {showDeductFundsForm && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="w-full max-w-md">
            <WalletTransactionForm
              clientId={client.id}
              onClose={() => setShowDeductFundsForm(false)}
              onSuccess={(updatedWallet) => {
                setWallet(updatedWallet)
                client.wallet_balance = updatedWallet.balance
                setShowDeductFundsForm(false)
              }}
              transactionType="deduct"
            />
          </div>
        </div>
      )}

      {/* Transaction Log Modal */}
      {showTransactionLog && wallet && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 overflow-auto">
          <div className="w-full max-w-5xl">
            <WalletTransactionLog
              walletId={wallet.id}
              onClose={() => setShowTransactionLog(false)}
            />
          </div>
        </div>
      )}
    </>
  )
}

export default memo(ClientCard)
