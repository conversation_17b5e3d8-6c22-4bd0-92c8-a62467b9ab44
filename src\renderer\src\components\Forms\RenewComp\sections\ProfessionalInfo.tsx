import React from 'react'
import { FormField, FormSection, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * ProfessionalInfo - Professional Information Section Component
 *
 * Renders the professional information form section for the Renew Competency form
 */
export default function ProfessionalInfo({
  formData,
  updateFormData,
  className = ''
}: SectionProps): JSX.Element {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  // Handle competency type change
  const handleCompetencyTypeChange = (value: string) => {
    updateFormData({
      competencyType: value as 'possess' | 'trade' | ''
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Professional Information</h3>

      <div className="grid grid-cols-2 gap-4">
        <FormSection title="Competency Type" className="mb-4">
          <RadioGroup
            name="competencyType"
            value={formData.competencyType}
            onChange={handleCompetencyTypeChange}
            options={[
              { value: 'possess', label: 'Possess Firearm' },
              { value: 'trade', label: 'Trade in Firearms' }
            ]}
            required
          />
        </FormSection>

        <FormField
          label="Work Contact"
          name="workNumber"
          value={formData.workNumber}
          onChange={handleChange}
          placeholder="e.g. ************"
          required
          type="tel"
        />
      </div>
    </div>
  )
}
