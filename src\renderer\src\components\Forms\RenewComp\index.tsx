import React, { useState, useEffect, useMemo } from 'react'
import { FormData, initialFormData } from '../../../types/FormData'
import {
  FormLayout,
  TemplateStatus,
  FormSectionType
} from '../../FormComponents'

// Import section components
import PersonalInfo from './sections/PersonalInfo'
import ProfessionalInfo from './sections/ProfessionalInfo'
import CertificateDetails from './sections/CertificateDetails'

// Import types
import { ValidationStatus, TemplateStatus as TemplateStatusType } from './utils/types'

// Constants
const TEMPLATE_URL =
  'https://app.gunlicence.co.za/storage/v1/object/public/templates/Competency/517G_SAPS_Form.docx'
const TEMPLATE_NAME = '517G_SAPS_Form.docx'

// Types
interface RenewCompFormProps {
  formType: string
  onSubmit: (data: FormData) => void
}

/**
 * RenewComp - Competency Renewal Application Form Component
 *
 * A multi-step form for processing competency renewal applications
 * that passes data to DocScript for document generation.
 */
export default function RenewCompForm({ onSubmit }: RenewCompFormProps): JSX.Element {
  // Form state
  const [formData, setFormData] = useState<FormData>({
    ...initialFormData,
    competencyType: 'possess', // Default to possess for renewals
    // Set defaults for removed fields that might be required by the backend
    sexM: false,
    sexF: false,
    houseType: '',
    companyName: 'N/A',
    tradeProfession: 'N/A',
    workAddress: 'N/A',
    workPostalCode: '0000',
    workHouseUnitNumber: 'N/A',
    // Enable default basic competency type
    handgunType: true,
    // Set default citizenship type
    saId: true,
    fId: false,
    citizenType: 'saId'
  })
  const [currentStep, setCurrentStep] = useState<number>(1)

  // Template state
  const [templateStatus, setTemplateStatus] = useState<TemplateStatusType>('loading')
  const [templateError, setTemplateError] = useState<string | null>(null)

  // Submission state
  const [submissionStatus, setSubmissionStatus] = useState<ValidationStatus>('idle')
  const [submissionMessage, setSubmissionMessage] = useState<string | null>(null)

  // Form sections definition
  const sections: FormSectionType[] = useMemo(
    () => [
      { id: 'personal', title: 'Personal Information' },
      { id: 'professional', title: 'Professional Information' },
      { id: 'certificate', title: 'Certificate Details' }
    ],
    []
  )

  // Check template availability on component mount
  useEffect(() => {
    const abortController = new AbortController()

    const checkTemplate = async () => {
      setTemplateStatus('loading')
      setTemplateError(null)

      try {
        const response = await fetch(TEMPLATE_URL, {
          method: 'HEAD',
          signal: abortController.signal
        })

        if (!response.ok) {
          throw new Error(`Template not accessible: ${response.statusText}`)
        }

        setTemplateStatus('ready')
      } catch (err) {
        if (!abortController.signal.aborted) {
          console.error('Error checking template:', err)
          setTemplateStatus('error')
          setTemplateError('Could not access the template file. Please try again later.')
        }
      }
    }

    checkTemplate()
    return () => abortController.abort()
  }, [])

  // Helper function to update form data
  const updateFormData = (newData: Partial<FormData>) => {
    setFormData((prev) => ({ ...prev, ...newData }))
  }

  // Handle address field changes
  const handleAddressChange = (
    address: string,
    postalCode?: string,
    _houseNumber?: string, // Prefixed with _ to indicate it's intentionally unused
    isWorkAddress = false
  ) => {
    setFormData((prev) => ({
      ...prev,
      ...(isWorkAddress
        ? {
            workAddress: address,
            workPostalCode: postalCode || prev.workPostalCode
          }
        : {
            physicalAddress: address,
            postalCode: postalCode || prev.postalCode
          })
    }))
    // Note: _houseNumber parameter is included for compatibility but not used
  }

  // Deprecated function stub for backward compatibility
  // Will be removed in a future version
  const handleHouseNumberChange = () => {}

  // Validate form data (returns error message or null if valid)
  const validateFormData = (): string | null => {
    if (
      !formData.firstName ||
      !formData.lastName ||
      !formData.idNumber ||
      !formData.phoneNumber ||
      !formData.email ||
      !formData.physicalAddress ||
      !formData.postalCode
    ) {
      return 'Please fill in all required personal information fields.'
    }

    // House/Unit Number validation removed

    if (!formData.workNumber) {
      return 'Please fill in the required work contact information.'
    }

    // Check competency expiry status
    if (!(formData.competencyExpiry90DaysBeforeYes || formData.competencyExpiry90DaysBeforeNo)) {
      return 'Please indicate if your application was handed in 90 days before expiry.'
    }

    if (formData.competencyExpiry90DaysBeforeNo && !formData.competencyExpiry90DaysBeforeReason) {
      return 'Please provide a reason why your application was not handed in 90 days before expiry.'
    }

    if (!(formData.afterExpiryYes || formData.afterExpiryNo)) {
      return 'Please indicate if you are applying after the expiry of your competency certificate.'
    }

    if (formData.afterExpiryYes && !formData.afterExpiryReason) {
      return 'Please provide a reason why you are applying after the expiry of your competency certificate.'
    }

    return null
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    setSubmissionStatus('processing')
    setSubmissionMessage('Validating data...')

    try {
      // Validate form data
      const validationError = validateFormData()
      if (validationError) {
        setSubmissionStatus('error')
        setSubmissionMessage(validationError)
        return
      }

      // Create placeholders for competency certificate types
      const placeholders = {
        // Personal Information placeholders
        PossessFirearm: formData.competencyType === 'possess' ? 'X' : '',
        POSSESSFIREARM: formData.competencyType === 'possess' ? 'X' : '',
        possessFirearm: formData.competencyType === 'possess' ? 'X' : '',
        TradeFirearm: formData.competencyType === 'trade' ? 'X' : '',
        TRADEFIREARM: formData.competencyType === 'trade' ? 'X' : '',
        tradeFirearm: formData.competencyType === 'trade' ? 'X' : '',
        SAID: formData.saId ? 'X' : '',
        SAId: formData.saId ? 'X' : '',
        said: formData.saId ? 'X' : '',
        FID: formData.fId ? 'X' : '',
        FId: formData.fId ? 'X' : '',
        fid: formData.fId ? 'X' : '',
        ID: formData.idNumber || '',
        Id: formData.idNumber || '',
        id: formData.idNumber || '',
        PASSPORT: formData.passport || '',
        Passport: formData.passport || '',
        passport: formData.passport || '',
        LastName: formData.lastName || '',
        LASTNAME: formData.lastName || '',
        lastName: formData.lastName || '',
        lastname: formData.lastName || '',
        FirstName: formData.firstName || '',
        FIRSTNAME: formData.firstName || '',
        firstName: formData.firstName || '',
        firstname: formData.firstName || '',
        Initials: formData.initials || '',
        INITIALS: formData.initials || '',
        initials: formData.initials || '',
        Address: formData.physicalAddress || '',
        ADDRESS: formData.physicalAddress || '',
        address: formData.physicalAddress || '',
        POSTALCODE: formData.postalCode || '',
        PostalCode: formData.postalCode || '',
        postalCode: formData.postalCode || '',
        postalcode: formData.postalCode || '',
        WorkC: formData.workNumber || '',
        WORKC: formData.workNumber || '',
        workC: formData.workNumber || '',
        workc: formData.workNumber || '',
        Cell: formData.phoneNumber || '',
        CELL: formData.phoneNumber || '',
        cell: formData.phoneNumber || '',
        Email: formData.email || '',
        EMAIL: formData.email || '',
        email: formData.email || '',

        // Competency Expiry Status placeholders
        '15A': formData.competencyExpiry90DaysBeforeYes ? 'X' : '',
        '15a': formData.competencyExpiry90DaysBeforeYes ? 'X' : '',
        '15B': formData.competencyExpiry90DaysBeforeNo ? 'X' : '',
        '15b': formData.competencyExpiry90DaysBeforeNo ? 'X' : '',
        '15C': formData.competencyExpiry90DaysBeforeReason || '',
        '15c': formData.competencyExpiry90DaysBeforeReason || '',

        // After Expiry placeholders
        '16A': formData.afterExpiryYes ? 'X' : '',
        '16a': formData.afterExpiryYes ? 'X' : '',
        '16B': formData.afterExpiryNo ? 'X' : '',
        '16b': formData.afterExpiryNo ? 'X' : '',
        '16C': formData.afterExpiryReason || '',
        '16c': formData.afterExpiryReason || '',
        // 1. Handgun
        'D1.6A1': formData.handgunType ? 'X' : '',
        'd1.6a1': formData.handgunType ? 'X' : '',
        'D1.6B1': formData.handgunCertNumber || '',
        'd1.6b1': formData.handgunCertNumber || '',
        'D1.6C1': formData.handgunCertIssueDate || '',
        'd1.6c1': formData.handgunCertIssueDate || '',
        'D1.6D1': formData.handgunCertExpiryDate || '',
        'd1.6d1': formData.handgunCertExpiryDate || '',

        // 2. Handgun & Rifle
        'D1.6A2': formData.handgunRifleType ? 'X' : '',
        'd1.6a2': formData.handgunRifleType ? 'X' : '',
        'D1.6B2': formData.handgunRifleCertNumber || '',
        'd1.6b2': formData.handgunRifleCertNumber || '',
        'D1.6C2': formData.handgunRifleCertIssueDate || '',
        'd1.6c2': formData.handgunRifleCertIssueDate || '',
        'D1.6D2': formData.handgunRifleCertExpiryDate || '',
        'd1.6d2': formData.handgunRifleCertExpiryDate || '',

        // 3. Rifle
        'D1.6A3': formData.rifleType ? 'X' : '',
        'd1.6a3': formData.rifleType ? 'X' : '',
        'D1.6B3': formData.rifleCertNumber || '',
        'd1.6b3': formData.rifleCertNumber || '',
        'D1.6C3': formData.rifleCertIssueDate || '',
        'd1.6c3': formData.rifleCertIssueDate || '',
        'D1.6D3': formData.rifleCertExpiryDate || '',
        'd1.6d3': formData.rifleCertExpiryDate || '',

        // 4. Shotgun
        'D1.6A4': formData.shotgunType ? 'X' : '',
        'd1.6a4': formData.shotgunType ? 'X' : '',
        'D1.6B4': formData.shotgunCertNumber || '',
        'd1.6b4': formData.shotgunCertNumber || '',
        'D1.6C4': formData.shotgunCertIssueDate || '',
        'd1.6c4': formData.shotgunCertIssueDate || '',
        'D1.6D4': formData.shotgunCertExpiryDate || '',
        'd1.6d4': formData.shotgunCertExpiryDate || '',

        // 5. Shotgun & Handgun
        'D1.6A5': formData.shotgunHandgunType ? 'X' : '',
        'd1.6a5': formData.shotgunHandgunType ? 'X' : '',
        'D1.6B5': formData.shotgunHandgunCertNumber || '',
        'd1.6b5': formData.shotgunHandgunCertNumber || '',
        'D1.6C5': formData.shotgunHandgunCertIssueDate || '',
        'd1.6c5': formData.shotgunHandgunCertIssueDate || '',
        'D1.6D5': formData.shotgunHandgunCertExpiryDate || '',
        'd1.6d5': formData.shotgunHandgunCertExpiryDate || '',

        // 6. Rifle & Shotgun
        'D1.6A6': formData.rifleShotgunType ? 'X' : '',
        'd1.6a6': formData.rifleShotgunType ? 'X' : '',
        'D1.6B6': formData.rifleShotgunCertNumber || '',
        'd1.6b6': formData.rifleShotgunCertNumber || '',
        'D1.6C6': formData.rifleShotgunCertIssueDate || '',
        'd1.6c6': formData.rifleShotgunCertIssueDate || '',
        'D1.6D6': formData.rifleShotgunCertExpiryDate || '',
        'd1.6d6': formData.rifleShotgunCertExpiryDate || '',

        // 7. Handgun & Rifle & Shotgun
        'D1.6A7': formData.handgunRifleShotgunType ? 'X' : '',
        'd1.6a7': formData.handgunRifleShotgunType ? 'X' : '',
        'D1.6B7': formData.handgunRifleShotgunCertNumber || '',
        'd1.6b7': formData.handgunRifleShotgunCertNumber || '',
        'D1.6C7': formData.handgunRifleShotgunCertIssueDate || '',
        'd1.6c7': formData.handgunRifleShotgunCertIssueDate || '',
        'D1.6D7': formData.handgunRifleShotgunCertExpiryDate || '',
        'd1.6d7': formData.handgunRifleShotgunCertExpiryDate || '',

        // 8. Hand Machine Carbine
        'D1.6A8': formData.handMachineCarbineType ? 'X' : '',
        'd1.6a8': formData.handMachineCarbineType ? 'X' : '',
        'D1.6B8': formData.handMachineCarbineCertNumber || '',
        'd1.6b8': formData.handMachineCarbineCertNumber || '',
        'D1.6C8': formData.handMachineCarbineCertIssueDate || '',
        'd1.6c8': formData.handMachineCarbineCertIssueDate || '',
        'D1.6D8': formData.handMachineCarbineCertExpiryDate || '',
        'd1.6d8': formData.handMachineCarbineCertExpiryDate || '',

        // 9. Handgun & Hand Machine Carbine
        'D1.6A9': formData.handgunHandMachineType ? 'X' : '',
        'd1.6a9': formData.handgunHandMachineType ? 'X' : '',
        'D1.6B9': formData.handgunHandMachineCertNumber || '',
        'd1.6b9': formData.handgunHandMachineCertNumber || '',
        'D1.6C9': formData.handgunHandMachineCertIssueDate || '',
        'd1.6c9': formData.handgunHandMachineCertIssueDate || '',
        'D1.6D9': formData.handgunHandMachineCertExpiryDate || '',
        'd1.6d9': formData.handgunHandMachineCertExpiryDate || '',

        // 10. Handgun & Rifle & Hand Machine Carbine
        'D1.6A10': formData.handgunRifleHandMachineType ? 'X' : '',
        'd1.6a10': formData.handgunRifleHandMachineType ? 'X' : '',
        'D1.6B10': formData.handgunRifleHandMachineCertNumber || '',
        'd1.6b10': formData.handgunRifleHandMachineCertNumber || '',
        'D1.6C10': formData.handgunRifleHandMachineCertIssueDate || '',
        'd1.6c10': formData.handgunRifleHandMachineCertIssueDate || '',
        'D1.6D10': formData.handgunRifleHandMachineCertExpiryDate || '',
        'd1.6d10': formData.handgunRifleHandMachineCertExpiryDate || '',

        // 11. Handgun & Rifle & Shotgun & Hand Machine Carbine
        'D1.6A11': formData.handgunRifleShotgunHandMachineType ? 'X' : '',
        'd1.6a11': formData.handgunRifleShotgunHandMachineType ? 'X' : '',
        'D1.6B11': formData.handgunRifleShotgunHandMachineCertNumber || '',
        'd1.6b11': formData.handgunRifleShotgunHandMachineCertNumber || '',
        'D1.6C11': formData.handgunRifleShotgunHandMachineCertIssueDate || '',
        'd1.6c11': formData.handgunRifleShotgunHandMachineCertIssueDate || '',
        'D1.6D11': formData.handgunRifleShotgunHandMachineCertExpiryDate || '',
        'd1.6d11': formData.handgunRifleShotgunHandMachineCertExpiryDate || '',

        // 12. Rifle & Hand Machine Carbine
        'D1.6A12': formData.rifleHandMachineType ? 'X' : '',
        'd1.6a12': formData.rifleHandMachineType ? 'X' : '',
        'D1.6B12': formData.rifleHandMachineCertNumber || '',
        'd1.6b12': formData.rifleHandMachineCertNumber || '',
        'D1.6C12': formData.rifleHandMachineCertIssueDate || '',
        'd1.6c12': formData.rifleHandMachineCertIssueDate || '',
        'D1.6D12': formData.rifleHandMachineCertExpiryDate || '',
        'd1.6d12': formData.rifleHandMachineCertExpiryDate || '',

        // 13. Rifle & Shotgun & Hand Machine Carbine
        'D1.6A13': formData.rifleShotgunHandMachineType ? 'X' : '',
        'd1.6a13': formData.rifleShotgunHandMachineType ? 'X' : '',
        'D1.6B13': formData.rifleShotgunHandMachineCertNumber || '',
        'd1.6b13': formData.rifleShotgunHandMachineCertNumber || '',
        'D1.6C13': formData.rifleShotgunHandMachineCertIssueDate || '',
        'd1.6c13': formData.rifleShotgunHandMachineCertIssueDate || '',
        'D1.6D13': formData.rifleShotgunHandMachineCertExpiryDate || '',
        'd1.6d13': formData.rifleShotgunHandMachineCertExpiryDate || '',

        // 14. Shotgun & Hand Machine Carbine
        'D1.6A14': formData.shotgunHandMachineType ? 'X' : '',
        'd1.6a14': formData.shotgunHandMachineType ? 'X' : '',
        'D1.6B14': formData.shotgunHandMachineCertNumber || '',
        'd1.6b14': formData.shotgunHandMachineCertNumber || '',
        'D1.6C14': formData.shotgunHandMachineCertIssueDate || '',
        'd1.6c14': formData.shotgunHandMachineCertIssueDate || '',
        'D1.6D14': formData.shotgunHandMachineCertExpiryDate || '',
        'd1.6d14': formData.shotgunHandMachineCertExpiryDate || '',
      }

      // Create a copy of the form data with template info and placeholders
      const submissionData = {
        ...formData,
        ...placeholders,
        templateUrl: TEMPLATE_URL,
        templateName: TEMPLATE_NAME
      }

      // Update status and notify parent
      setSubmissionStatus('success')
      setSubmissionMessage('Form submitted successfully!')

      // Send data to DocScript component for processing
      onSubmit(submissionData)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setSubmissionStatus('error')
      setSubmissionMessage(`Error processing form: ${errorMessage}`)
    }
  }

  // Render the current step based on section ID
  const renderCurrentStep = () => {
    const sectionId = sections[currentStep - 1].id

    switch (sectionId) {
      case 'personal':
        return (
          <PersonalInfo
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
            handleHouseNumberChange={handleHouseNumberChange}
          />
        )
      case 'professional':
        return (
          <ProfessionalInfo
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'certificate':
        return (
          <CertificateDetails
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      default:
        return null
    }
  }

  // Render template status component
  const renderTemplateStatus = () => (
    <TemplateStatus status={templateStatus} templateName={TEMPLATE_NAME} error={templateError} />
  )

  // Handle cancel submission
  const onCancelSubmission = () => {
    setSubmissionStatus('idle')
    setSubmissionMessage(null)
  }

  // Main component render
  return (
    <FormLayout
      title="Renew Competency Application"
      sections={sections}
      currentStep={currentStep}
      setCurrentStep={setCurrentStep}
      onSubmit={handleSubmit}
      submissionStatus={submissionStatus}
      submissionMessage={submissionMessage}
      onCancelSubmission={onCancelSubmission}
      isDocScriptForm={true}
    >
      {templateStatus === 'error' && renderTemplateStatus()}
      {renderCurrentStep()}
    </FormLayout>
  )
}
