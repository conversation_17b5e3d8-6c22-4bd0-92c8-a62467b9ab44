import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

// Define representative options
const representativeOptions = [
  { name: '<PERSON>', id: '9303315179084' },
  { name: '<PERSON><PERSON><PERSON>', id: '9605095344088' },
  { name: '<PERSON>', id: '9306035115080' },
  { name: '<PERSON>', id: '9109195024088' },
  { name: '<PERSON>', id: '8912146156086' }
]

/**
 * Representative Information section component for SAPS Inspection Report form
 */
const RepresentativeInfo: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  // Custom handlers for name and ID fields

  // Handle selection from dropdown
  const handleNameSelection = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    updateFormData({ representativeName: value })

    // Only auto-fill ID if exact match is found
    const matchingOption = representativeOptions.find(option => option.name === value)
    if (matchingOption) {
      updateFormData({ representativeId: matchingOption.id })
    }
  }

  const handleIdSelection = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target
    updateFormData({ representativeId: value })

    // Only auto-fill name if exact match is found
    const matchingOption = representativeOptions.find(option => option.id === value)
    if (matchingOption) {
      updateFormData({ representativeName: matchingOption.name })
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Representative Information</h3>

      <FormSection title="Representative Details" subtitle="Enter the representative's information">
        <div className="space-y-3">
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Name of Representative</label>
                <input
                  type="text"
                  name="representativeName"
                  value={formData.representativeName || ''}
                  onChange={handleNameSelection}
                  placeholder="e.g. John Smith"
                  required={true}
                  list="representative-names"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
                <datalist id="representative-names">
                  {representativeOptions.map(option => (
                    <option key={option.id} value={option.name} />
                  ))}
                </datalist>
              </div>
            </div>

            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">ID of Representative</label>
                <input
                  type="text"
                  name="representativeId"
                  value={formData.representativeId || ''}
                  onChange={handleIdSelection}
                  placeholder="e.g. 8001015009087"
                  required={true}
                  list="representative-ids"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
                <datalist id="representative-ids">
                  {representativeOptions.map(option => (
                    <option key={option.id} value={option.id} />
                  ))}
                </datalist>
              </div>
            </div>
          </div>

          <p className="text-xs text-stone-400 mt-1">
            Select from the dropdown or enter custom details of the person representing the client during the SAPS inspection
          </p>
        </div>
      </FormSection>
    </div>
  )
}

export default RepresentativeInfo
