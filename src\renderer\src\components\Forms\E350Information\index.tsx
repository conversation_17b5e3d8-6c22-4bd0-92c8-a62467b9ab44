import { useState, useEffect } from 'react'
import { FormData, initialFormData } from '../../../types/FormData'
import {
  FormLayout,
  FormSectionType
} from '../../FormComponents'

// Import section components
import PeriodInfo from './sections/PeriodInfo'
import FirearmDetails from './sections/FirearmDetails'
import ReceivedFrom from './sections/ReceivedFrom'
import TemplateStatus from './sections/TemplateStatus'

// Import types
import { ValidationStatus, TemplateStatus as TemplateStatusType } from './utils/types'

// Constants
const TEMPLATE_URL =
  'https://app.gunlicence.co.za/storage/v1/object/public/templates/MISC/E350A_Infomation.docx'
const TEMPLATE_NAME = 'E350A_Infomation.docx'

// Types
interface E350InformationFormProps {
  onSubmit: (data: FormData) => void
}

/**
 * E350 Information Form Component
 *
 * A form for processing E350 information documents
 * that passes data to DocScript for document generation.
 */
export default function E350Information({ onSubmit }: E350InformationFormProps): JSX.Element {
  // Form state
  const [formData, setFormData] = useState<FormData>({
    ...initialFormData
  })
  const [currentStep, setCurrentStep] = useState<number>(1)

  // Template state
  const [templateStatus, setTemplateStatus] = useState<TemplateStatusType>('loading')
  const [templateError] = useState<string | null>(null)

  // Submission state
  const [submissionStatus, setSubmissionStatus] = useState<ValidationStatus>('idle')
  const [submissionMessage, setSubmissionMessage] = useState<string>('')

  // Set template URL and name
  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      templateUrl: TEMPLATE_URL,
      templateName: TEMPLATE_NAME
    }))

    // Simulate template loading
    const timer = setTimeout(() => {
      setTemplateStatus('ready')
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Update form data with multiple fields
  const updateFormData = (newData: Partial<FormData>) => {
    setFormData((prev) => ({ ...prev, ...newData }))
  }

  // Define form sections
  const sections: FormSectionType[] = [
    { id: 'period', title: '350 INFORMATION' },
    { id: 'firearm', title: 'FIREARM DETAILS' },
    { id: 'receiver', title: 'RECEIVED FROM' }
  ]

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setSubmissionStatus('processing')
      setSubmissionMessage('Processing your form...')

      // Set document as processed
      const finalFormData = {
        ...formData,
        documentProcessed: true
      }

      // Submit the form data
      onSubmit(finalFormData)

      // Update submission status
      setSubmissionStatus('success')
      setSubmissionMessage('Form submitted successfully!')
    } catch (error) {
      console.error('Error submitting form:', error)
      setSubmissionStatus('error')
      setSubmissionMessage('Error submitting form. Please try again.')
    }
  }



  // Render the current step based on section ID
  const renderCurrentStep = () => {
    const sectionId = sections[currentStep - 1].id

    switch (sectionId) {
      case 'period':
        return (
          <PeriodInfo
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'firearm':
        return (
          <FirearmDetails
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'receiver':
        return (
          <ReceivedFrom
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      default:
        return null
    }
  }

  // Render template status component
  const renderTemplateStatus = () => (
    <TemplateStatus
      status={templateStatus}
      templateName={TEMPLATE_NAME}
      error={templateError}
    />
  )

  // Handle cancel submission
  const onCancelSubmission = () => {
    setSubmissionStatus('idle')
  }

  // Main component render
  return (
    <FormLayout
      title="E350 Information Form"
      sections={sections}
      currentStep={currentStep}
      setCurrentStep={setCurrentStep}
      onSubmit={handleSubmit}
      submissionStatus={submissionStatus}
      submissionMessage={submissionMessage}
      onCancelSubmission={onCancelSubmission}
      isDocScriptForm={true}
    >
      {templateStatus === 'error' && renderTemplateStatus()}
      {renderCurrentStep()}
    </FormLayout>
  )
}
