import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Client Information section component for SAPS Inspection Report form
 */
const ClientInfo: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Client Information</h3>

      <FormSection title="Client Details" subtitle="Enter the client's information">
        <div className="space-y-3">
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Client Name</label>
                <input
                  type="text"
                  name="clientName"
                  value={formData.clientName || ''}
                  onChange={handleChange}
                  placeholder="e.g. Jane Doe"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Client ID</label>
                <input
                  type="text"
                  name="clientId"
                  value={formData.clientId || ''}
                  onChange={handleChange}
                  placeholder="e.g. 8001015009087"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
          
          <p className="text-xs text-stone-400 mt-1">
            Enter the details of the client who owns the firearm being inspected
          </p>
        </div>
      </FormSection>
    </div>
  )
}

export default ClientInfo
