import React from 'react'
import { RenewLicenceData } from '../../../../types/RenewLicenceData'
import { FormField, FormSection } from '../../../FormComponents'

interface JuristicPersonDetailsProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  handleAddressChange: (
    address: string,
    postalCode?: string,
    houseNumber?: string,
    isWorkAddress?: boolean
  ) => void
  handleHouseNumberChange: (houseNumber: string, isWorkAddress?: boolean) => void
  className?: string
}

const JuristicPersonDetails: React.FC<JuristicPersonDetailsProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Juristic Person's Details</h3>
      <FormSection
        title="Juristic Person's Details"
        subtitle="Please provide details about the juristic person"
      >
        <div className="space-y-4">
          <FormField
            label="Registered Company Name"
            name="companyName"
            value={formData.companyName || ''}
            onChange={handleChange}
            placeholder="Enter the registered company name"
            required
          />

          <FormField
            label="Trading as Name"
            name="tradingAsName"
            value={formData.tradingAsName || ''}
            onChange={handleChange}
            placeholder="Enter the trading as name"
            required
          />

          <FormField
            label="FAR Number"
            name="farNumber"
            value={formData.farNumber || ''}
            onChange={handleChange}
            placeholder="Enter the FAR number"
            required
          />

          <FormField
            label="Postal Address"
            name="postalAddress"
            value={formData.postalAddress || ''}
            onChange={handleChange}
            placeholder="Enter the postal address"
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Postal Code"
              name="workPostalCode"
              value={formData.workPostalCode || ''}
              onChange={handleChange}
              placeholder="Enter the postal code"
              required
            />

            <FormField
              label="Business Telephone Number"
              name="businessTelNumber"
              value={formData.businessTelNumber || ''}
              onChange={handleChange}
              placeholder="Enter the business telephone number"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Work Number"
              name="workNumber"
              value={formData.workNumber || ''}
              onChange={handleChange}
              placeholder="Enter the work number"
              required
            />

            <FormField
              label="Email Address"
              name="companyEmail"
              value={formData.companyEmail || ''}
              onChange={handleChange}
              type="email"
              placeholder="Enter the email address"
            />
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default JuristicPersonDetails
