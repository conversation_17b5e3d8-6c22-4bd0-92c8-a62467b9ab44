import React from 'react'
import { RenewLicenceData } from '../../../../types/RenewLicenceData'
import { FormField, CheckboxGroup, FormSection } from '../../../FormComponents'

interface FirearmDetailsProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  className?: string
}

const FirearmDetails: React.FC<FirearmDetailsProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    updateFormData({
      [name]: checked
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Firearm Details</h3>
      <FormSection title="Firearm Details" subtitle="Please provide details about your firearm">
        <div className="space-y-4">
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">Firearm Type</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                label="Barrel Serial Number"
                name="bsn"
                value={formData.bsn}
                onChange={handleChange}
                placeholder="Enter barrel serial number"
              />

              <FormField
                label="Frame Serial Number"
                name="fsn"
                value={formData.fsn}
                onChange={handleChange}
                placeholder="Enter frame serial number"
              />

              <FormField
                label="Receiver Serial Number"
                name="rsn"
                value={formData.rsn}
                onChange={handleChange}
                placeholder="Enter receiver serial number"
              />
            </div>
          </div>

          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">Component Makes</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                label="Barrel Make"
                name="bsnm"
                value={formData.bsnm}
                onChange={handleChange}
                placeholder="Enter barrel make"
              />

              <FormField
                label="Frame Make"
                name="fsnm"
                value={formData.fsnm}
                onChange={handleChange}
                placeholder="Enter frame make"
              />

              <FormField
                label="Receiver Make"
                name="rsnm"
                value={formData.rsnm}
                onChange={handleChange}
                placeholder="Enter receiver make"
              />
            </div>
          </div>

          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">Action Type</h3>
            <CheckboxGroup
              options={[
                { name: 'semi', label: 'Semi-automatic', checked: formData.semi },
                { name: 'auto', label: 'Automatic', checked: formData.auto },
                { name: 'man', label: 'Manual', checked: formData.man },
                { name: 'comb', label: 'Combination', checked: formData.comb },
                { name: 'otherDesign', label: 'Other', checked: formData.otherDesign }
              ]}
              onChange={handleCheckboxChange}
            />
          </div>

          {formData.otherDesign && (
            <FormField
              label="Other Design Information"
              name="otherDesignE"
              value={formData.otherDesignE}
              onChange={handleChange}
              placeholder="Please specify the other design"
              type="textarea"
              rows={2}
            />
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default FirearmDetails
