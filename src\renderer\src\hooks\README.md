# Custom Hooks

This directory contains custom React hooks that can be reused across the application.

## useDebounce

The `useDebounce` hook provides two functions:

1. `useDebounce` - Debounces a value
2. `useDebouncedCallback` - Debounces a callback function

### Usage Examples

#### Debouncing a value

```tsx
import { useDebounce } from '../hooks/useDebounce'

function MyComponent() {
  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, 300)
  
  // Effect will only run when debouncedSearchTerm changes
  useEffect(() => {
    if (debouncedSearchTerm) {
      performSearch(debouncedSearchTerm)
    }
  }, [debouncedSearchTerm])
  
  return (
    <input
      type="text"
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      placeholder="Search..."
    />
  )
}
```

#### Debouncing a callback

```tsx
import { useDebouncedCallback } from '../hooks/useDebounce'

function MyComponent() {
  const [searchTerm, setSearchTerm] = useState('')
  
  const debouncedSearch = useDebouncedCallback(
    (value: string) => {
      performSearch(value)
    },
    300
  )
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    debouncedSearch(value)
  }
  
  return (
    <input
      type="text"
      value={searchTerm}
      onChange={handleInputChange}
      placeholder="Search..."
    />
  )
}
```

## useSearchDebounce

The `useSearchDebounce` hook is a specialized hook for handling debounced search inputs. It combines state management with debounced callbacks.

### Usage Example

```tsx
import { useSearchDebounce } from '../hooks/useSearchDebounce'

function SearchComponent({ onSearch }) {
  const {
    searchTerm,
    handleSearchChange,
    clearSearch
  } = useSearchDebounce(onSearch, 300)
  
  return (
    <div>
      <input
        type="text"
        value={searchTerm}
        onChange={handleSearchChange}
        placeholder="Search..."
      />
      {searchTerm && (
        <button onClick={clearSearch}>
          Clear
        </button>
      )}
    </div>
  )
}
```

You can also use the pre-built `DebouncedSearchInput` component which uses this hook internally.
