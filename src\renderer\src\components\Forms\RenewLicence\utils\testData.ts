import { RenewLicenceData } from '../../../../types/RenewLicenceData'

/**
 * Test data for the RenewLicence form with Section 13 (Self-defence)
 * This data can be used to quickly fill the form for testing purposes
 */
export const testSelfDefenceData: RenewLicenceData = {
  // Type of Licence
  s13: true, // Self-defence
  s15: false,
  s16: false,
  s20: false,
  s20a: false,
  s20b: false,
  s20c: false,

  // Details of Original Licence
  originalLicenceNumber: 'LIC123456',
  originalLicenceIssueDate: '2018-06-15',
  originalLicenceExpiryDate: '2023-06-15',
  additionalOriginalLicences: [
    {
      licenceNumber: 'LIC789012',
      issueDate: '2019-08-20',
      expiryDate: '2024-08-20'
    }
  ],

  // Personal Information
  firstName: 'John',
  lastName: 'Doe',
  initials: 'JD',
  saId: true,
  fId: false,
  permRes: false,
  idNumber: '8001015009087',
  passport: '',
  permResNumber: '',
  physicalAddress: '123 Test Street, Pretoria',
  postalCode: '0001',
  houseUnitNumber: '123',
  workAddress: '456 Work Street, Johannesburg',
  workPostalCode: '2000',
  workHouseUnitNumber: '456',
  phoneNumber: '0821234567',
  workNumber: '0113456789',
  email: '<EMAIL>',

  // Other Information
  before90DaysYes: true,
  before90DaysNo: false,
  before90DaysReason: '',
  afterDueBeforeExpiryYes: false,
  afterDueBeforeExpiryNo: true,
  afterDueBeforeExpiryReason: '',
  afterExpiryYes: false,
  afterExpiryNo: true,
  afterExpiryReason: '',
  prevLicenceNumber: '',
  prevLicenceIssueDate: '',
  prevLicenceExpiryDate: '',
  standardRenewal: false,
  lostLicence: false,
  lostLicenceCaseNumber: '',
  damagedLicence: false,
  mainHF: false,
  addHF: false,
  comb: false,
  otherDesign: false,
  otherDesignE: '',
  semi: false,
  auto: false,
  man: false,
  otherF: '',
  engg: '',
  bsn: '',
  fsn: '',
  rsn: '',
  bsnm: '',
  fsnm: '',
  rsnm: '',
  safeYes: false,
  safeNo: false,
  safeH: false,
  safeR: false,
  safeS: false,
  safeSe: '',
  safeD: false,
  safeDInfo: '',
  safeMountYes: false,
  safeMountNo: false,
  safeWall: false,
  safeFloor: false,
  fullName: '',
  companyName: '',
  tradeProfession: '',
  sexM: false,
  sexF: false,
  singles: false,
  married: false,
  divorced: false,
  widower: false,
  widow: false,
  maritalStatus: '',
  citizenType: ''
}

/**
 * Test data for the RenewLicence form with Section 16 (Dedicated hunting/sport)
 * This data can be used to quickly fill the form for testing purposes
 */
export const testDedicatedHuntingData: RenewLicenceData = {
  ...testSelfDefenceData,
  // Type of Licence
  s13: false,
  s15: false,
  s16: true, // Dedicated hunting/sport
  s20: false,
  s20a: false,
  s20b: false,
  s20c: false,

  // Different original licence details
  originalLicenceNumber: 'LIC654321',
  originalLicenceIssueDate: '2019-03-10',
  originalLicenceExpiryDate: '2024-03-10',
  additionalOriginalLicences: [
    {
      licenceNumber: 'LIC987654',
      issueDate: '2020-05-15',
      expiryDate: '2025-05-15'
    }
  ],

  // Different personal information
  firstName: 'Jane',
  lastName: 'Smith',
  initials: 'JS',
  idNumber: '7501015009087',
  physicalAddress: '456 Hunter Street, Cape Town',
  postalCode: '8001',
  phoneNumber: '0835678901',
  email: '<EMAIL>'
}

/**
 * Test data for the RenewLicence form with Section 20 (Business-hunting)
 * This data can be used to quickly fill the form for testing purposes
 */
export const testBusinessHuntingData: RenewLicenceData = {
  ...testSelfDefenceData,
  // Type of Licence
  s13: false,
  s15: false,
  s16: false,
  s20: true, // Business-hunting
  s20a: false,
  s20b: false,
  s20c: false,

  // Different original licence details
  originalLicenceNumber: 'LIC246810',
  originalLicenceIssueDate: '2020-01-20',
  originalLicenceExpiryDate: '2025-01-20',
  additionalOriginalLicences: [
    {
      licenceNumber: 'LIC135790',
      issueDate: '2021-02-25',
      expiryDate: '2026-02-25'
    }
  ],

  // Different personal information
  firstName: 'Robert',
  lastName: 'Johnson',
  initials: 'RJ',
  idNumber: '7201015009087',
  physicalAddress: '789 Safari Road, Nelspruit',
  postalCode: '1200',
  phoneNumber: '0845678901',
  email: '<EMAIL>',

  // Juristic Person's Details
  companyName: 'Safari Hunting Enterprises',
  tradingAsName: 'Safari Hunts',
  farNumber: 'FAR12345',
  postalAddress: 'PO Box 123, Nelspruit',
  workPostalCode: '1200',
  businessTelNumber: '0137654321',
  workNumber: '0137654322',
  companyEmail: '<EMAIL>',

  // Responsible Person's Details
  responsiblePersonName: 'Robert Johnson',
  responsiblePersonSaId: true,
  responsiblePersonPassport: false,
  responsiblePersonIdNumber: '7201015009087',
  responsiblePersonPassportNumber: '',
  responsiblePersonCellNumber: '0845678901',
  responsiblePersonAddress: '789 Safari Road, Nelspruit',
  responsiblePersonPostalCode: '1200',
  responsiblePersonHouseNumber: '789'
}

/**
 * Test data for the RenewLicence form with Section 20B (Business-security)
 * This data can be used to quickly fill the form for testing purposes
 */
export const testBusinessSecurityData: RenewLicenceData = {
  ...testSelfDefenceData,
  // Type of Licence
  s13: false,
  s15: false,
  s16: false,
  s20: false,
  s20a: false,
  s20b: true, // Business-security
  s20c: false,

  // Different original licence details
  originalLicenceNumber: 'LIC369258',
  originalLicenceIssueDate: '2019-11-05',
  originalLicenceExpiryDate: '2024-11-05',
  additionalOriginalLicences: [
    {
      licenceNumber: 'LIC147258',
      issueDate: '2020-12-10',
      expiryDate: '2025-12-10'
    }
  ],

  // Different personal information
  firstName: 'Michael',
  lastName: 'Brown',
  initials: 'MB',
  idNumber: '7601015009087',
  physicalAddress: '123 Security Avenue, Johannesburg',
  postalCode: '2000',
  phoneNumber: '0825678901',
  email: '<EMAIL>',

  // Juristic Person's Details
  companyName: 'Secure Solutions Ltd',
  tradingAsName: 'SecureSol',
  farNumber: 'FAR67890',
  postalAddress: 'PO Box 456, Johannesburg',
  workPostalCode: '2000',
  businessTelNumber: '0112345678',
  workNumber: '0112345679',
  companyEmail: '<EMAIL>',

  // Responsible Person's Details
  responsiblePersonName: 'Michael Brown',
  responsiblePersonSaId: true,
  responsiblePersonPassport: false,
  responsiblePersonIdNumber: '7601015009087',
  responsiblePersonPassportNumber: '',
  responsiblePersonCellNumber: '0825678901',
  responsiblePersonAddress: '123 Security Avenue, Johannesburg',
  responsiblePersonPostalCode: '2000',
  responsiblePersonHouseNumber: '123'
}
