import { useState, useEffect, useCallback } from 'react'
import { ClientWallet, Client } from '../../../types'
import { DashboardIcons } from '../../icons/DashboardIcons'
import { getSupabase } from '../../../lib/supabase'
import { v4 as uuidv4 } from 'uuid'
import { sendWalletTransactionNotification } from '../../../utils/whatsAppNotificationService'

interface WalletTransactionFormProps {
  clientId: string
  onClose: () => void
  onSuccess: (updatedWallet: ClientWallet) => void
  transactionType: 'add' | 'deduct'
}

export const WalletTransactionForm = ({
  clientId,
  onClose,
  onSuccess,
  transactionType
}: WalletTransactionFormProps): JSX.Element => {
  const [amount, setAmount] = useState<number>(0)
  const [description, setDescription] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [wallet, setWallet] = useState<ClientWallet | null>(null)
  const [client, setClient] = useState<Client | null>(null)

  // Prevent scroll wheel from changing number input values
  const preventWheelChange = useCallback((e: React.WheelEvent<HTMLInputElement>) => {
    // Prevent the default scroll behavior on number inputs
    e.currentTarget.blur();
  }, []);

  // Fetch client wallet and client information on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const supabase = getSupabase()

        // Fetch wallet information
        const { data: walletData, error: walletError } = await supabase
          .from('client_credit_wallets')
          .select('*')
          .eq('client_id', clientId)
          .single()

        if (walletError && walletError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          setError('Failed to fetch wallet information')
          return
        }

        if (walletData) {
          setWallet(walletData as ClientWallet)
        }

        // Fetch client information
        const { data: clientData, error: clientError } = await supabase
          .from('clients')
          .select('*')
          .eq('id', clientId)
          .single()

        if (clientError) {
          console.error('Failed to fetch client information:', clientError)
          return
        }

        if (clientData) {
          setClient(clientData as Client)
        }
      } catch (err) {
        setError('An unexpected error occurred')
      }
    }

    fetchData()
  }, [clientId])

  // Add funds to wallet
  const addFunds = async (clientId: string, amount: number, description: string): Promise<{ success: boolean, wallet: ClientWallet | null }> => {
    if (amount <= 0) {
      setError('Amount must be greater than zero')
      return { success: false, wallet: null }
    }

    setLoading(true)
    setError(null)

    try {
      // Get or create wallet
      let walletId = wallet?.id

      if (!walletId) {
        // Create a new wallet
        const supabase = getSupabase()
        const now = new Date().toISOString()
        const newWalletId = uuidv4()

        const { error: createWalletError } = await supabase
          .from('client_credit_wallets')
          .insert({
            id: newWalletId,
            client_id: clientId,
            balance: 0, // Initial balance is 0, will be updated by trigger
            created_at: now,
            updated_at: now
          })

        if (createWalletError) {
          setError('Failed to create wallet')
          return { success: false, wallet: null }
        }

        walletId = newWalletId
      }

      // Create transaction record
      const supabase = getSupabase()
      const now = new Date().toISOString()
      const transactionId = uuidv4()

      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          id: transactionId,
          wallet_id: walletId,
          amount: amount, // Positive amount for adding funds
          transaction_type: 'payment',
          reference_type: 'manual',
          description: description || 'Manual credit addition',
          transaction_date: now,
          created_at: now,
          updated_at: now
        })

      if (transactionError) {
        setError('Failed to process transaction')
        return { success: false, wallet }
      }

      // Fetch updated wallet balance
      const { data: updatedWallet, error: fetchError } = await supabase
        .from('client_credit_wallets')
        .select('*')
        .eq('id', walletId)
        .single()

      if (fetchError) {
        setError('Transaction processed but failed to fetch updated balance')
        return { success: true, wallet } // Return original wallet as fallback
      }

      const result = { success: true, wallet: updatedWallet as ClientWallet }
      onSuccess(updatedWallet as ClientWallet)
      return result
    } catch (err) {
      setError('An unexpected error occurred')
      return { success: false, wallet: null }
    } finally {
      setLoading(false)
    }
  }

  // Deduct funds from wallet
  const deductFunds = async (_clientId: string, amount: number, description: string): Promise<{ success: boolean, wallet: ClientWallet | null }> => {
    if (amount <= 0) {
      setError('Amount must be greater than zero')
      return { success: false, wallet: null }
    }

    setLoading(true)
    setError(null)

    try {
      // Check if wallet exists and has sufficient funds
      if (!wallet) {
        setError('No wallet found for this client')
        return { success: false, wallet: null }
      }

      // Check if wallet has sufficient funds
      if (wallet.balance < amount) {
        setError('Insufficient funds in wallet')
        return { success: false, wallet }
      }

      // Create transaction record
      const supabase = getSupabase()
      const now = new Date().toISOString()
      const transactionId = uuidv4()

      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          id: transactionId,
          wallet_id: wallet.id,
          amount: -amount, // Negative amount for deducting funds
          transaction_type: 'charge',
          reference_type: 'manual',
          description: description || 'Manual deduction',
          transaction_date: now,
          created_at: now,
          updated_at: now
        })

      if (transactionError) {
        setError('Failed to process transaction')
        return { success: false, wallet }
      }

      // Fetch updated wallet balance
      const { data: updatedWallet, error: fetchError } = await supabase
        .from('client_credit_wallets')
        .select('*')
        .eq('id', wallet.id)
        .single()

      if (fetchError) {
        setError('Transaction processed but failed to fetch updated balance')
        return { success: true, wallet } // Return original wallet as fallback
      }

      const result = { success: true, wallet: updatedWallet as ClientWallet }
      onSuccess(updatedWallet as ClientWallet)
      return result
    } catch (err) {
      setError('An unexpected error occurred')
      return { success: false, wallet: null }
    } finally {
      setLoading(false)
    }
  }

  // Send WhatsApp notification for wallet transaction
  const sendWhatsAppNotification = async (transactionAmount: number, updatedBalance: number) => {
    try {
      // Skip if client data is missing or no phone number
      if (!client || !client.phone) {
        console.log('Cannot send WhatsApp notification: Missing client data or phone number')
        return
      }

      console.log('Preparing to send WhatsApp payment notification for wallet transaction')

      // Get client name
      const clientName = `${client.first_name} ${client.last_name}`

      // Send notification using the shared service
      const result = await sendWalletTransactionNotification(
        clientName,
        client.phone,
        transactionAmount,
        updatedBalance,
        transactionType
      )

      if (result.success) {
        console.log('Successfully sent WhatsApp notification')
      } else {
        console.error('Failed to send WhatsApp notification:', result.error)
      }
    } catch (error) {
      console.error('Error sending WhatsApp notification:', error)
      // Don't throw error - we don't want to fail transaction if WhatsApp fails
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (amount <= 0) {
      return // Form validation should prevent this
    }

    try {
      let result;
      if (transactionType === 'add') {
        result = await addFunds(clientId, amount, description)
      } else {
        result = await deductFunds(clientId, amount, description)
      }

      // Send WhatsApp notification if transaction was successful and we have updated wallet data
      if (result.success && result.wallet) {
        await sendWhatsAppNotification(amount, result.wallet.balance)
      }
    } catch (err) {
      setError('Error processing transaction')
    }
  }

  return (
    <div className="bg-stone-900 rounded-lg p-6 shadow-lg max-w-md w-full mx-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-white">
          {transactionType === 'add' ? 'Add Funds to Wallet' : 'Deduct Funds from Wallet'}
        </h2>
        <button
          onClick={onClose}
          className="text-stone-400 hover:text-white transition-colors"
          aria-label="Close"
        >
          <DashboardIcons.Close className="w-5 h-5" />
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-md text-white">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="amount" className="block text-sm font-medium text-stone-300 mb-1">
            Amount (R)
          </label>
          <input
            type="number"
            id="amount"
            value={amount || ''}
            onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
            onWheel={preventWheelChange}
            min="0.01"
            step="0.01"
            required
            className="w-full px-3 py-2 bg-stone-800 border border-stone-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-stone-300 mb-1">
            Description
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 bg-stone-800 border border-stone-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
            placeholder={`Reason for ${transactionType === 'add' ? 'adding' : 'deducting'} funds...`}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-2">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-stone-800 hover:bg-stone-700 text-white rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading || amount <= 0}
            className={`px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-md transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? 'Processing...' : transactionType === 'add' ? 'Add Funds' : 'Deduct Funds'}
          </button>
        </div>
      </form>
    </div>
  )
}

export default WalletTransactionForm
