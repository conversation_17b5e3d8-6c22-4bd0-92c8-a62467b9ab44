import React, { useState, useEffect } from 'react'
import { getWhatsAppSessionId, setWhatsAppSessionId } from '../../utils/whatsAppNotificationService'
import { useWhatsAppSettings } from './hooks/useWhatsAppSettings'
import { DashboardIcons } from '../icons/DashboardIcons'
import { Check, AlertCircle, MessageSquare } from 'lucide-react'

interface WhatsAppNotificationSettingsProps {
  activeSection: string
}

const WhatsAppNotificationSettings: React.FC<WhatsAppNotificationSettingsProps> = ({ activeSection }) => {
  const [sessionId, setSessionId] = useState<string>('')
  const [saveSuccess, setSaveSuccess] = useState<boolean>(false)
  const [saveError, setSaveError] = useState<string>('')

  // Get available sessions from WhatsApp settings
  const {
    availableSessionIds,
    availableSessions,
    getAllWhatsAppSessions,
    isLoadingSessions
  } = useWhatsAppSettings(activeSection)

  // Load the current session ID from storage on component mount
  useEffect(() => {
    if (activeSection === 'whatsapp-notifications') {
      setSessionId(getWhatsAppSessionId())
      getAllWhatsAppSessions()
    }
  }, [activeSection, getAllWhatsAppSessions])

  // Handle session ID change
  const handleSessionIdChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSessionId(e.target.value)
    setSaveSuccess(false)
    setSaveError('')
  }

  // Save the session ID to storage
  const handleSaveSessionId = () => {
    try {
      setWhatsAppSessionId(sessionId)
      setSaveSuccess(true)
      setSaveError('')

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false)
      }, 3000)
    } catch (error) {
      setSaveError('Failed to save session ID')
      setSaveSuccess(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-4">
        <MessageSquare className="text-orange-400" size={20} />
        <h2 className="text-lg font-semibold text-white">WhatsApp Notification Settings</h2>
      </div>

      <div className="bg-stone-800 rounded-lg p-4 border border-stone-700">
        <h3 className="text-md font-medium text-white mb-4">Default WhatsApp Session</h3>

        <p className="text-stone-400 text-sm mb-4">
          Select the WhatsApp session to use for sending notifications. This session will be used for all
          WhatsApp notifications sent from the application, including payment confirmations, wallet transactions,
          and firearm storage payments.
        </p>

        <div className="space-y-4">
          <div>
            <label htmlFor="sessionId" className="block text-sm font-medium text-stone-300 mb-1">
              WhatsApp Session
            </label>

            <div className="flex gap-2">
              <select
                id="sessionId"
                value={sessionId}
                onChange={handleSessionIdChange}
                className="w-full px-3 py-2 bg-stone-700 border border-stone-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="">Select a session</option>
                {availableSessionIds.map((id) => (
                  <option key={id} value={id}>
                    {id} {availableSessions.find(s => s.id === id)?.status ? `(${availableSessions.find(s => s.id === id)?.status})` : ''}
                  </option>
                ))}
              </select>

              <button
                onClick={getAllWhatsAppSessions}
                disabled={isLoadingSessions}
                className="px-3 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 hover:text-white rounded-md transition-colors"
                title="Refresh sessions"
              >
                <DashboardIcons.Refresh className={`w-5 h-5 ${isLoadingSessions ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              onClick={handleSaveSessionId}
              disabled={!sessionId}
              className={`px-4 py-2 rounded-md text-white flex items-center gap-2 ${
                !sessionId
                  ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
                  : 'bg-orange-600 hover:bg-orange-500'
              }`}
            >
              <Check size={16} />
              Save Session
            </button>
          </div>

          {saveSuccess && (
            <div className="p-3 bg-green-900/30 border border-green-700 rounded-md text-green-400 flex items-center gap-2">
              <Check size={16} />
              Session ID saved successfully
            </div>
          )}

          {saveError && (
            <div className="p-3 bg-red-900/30 border border-red-700 rounded-md text-red-400 flex items-center gap-2">
              <AlertCircle size={16} />
              {saveError}
            </div>
          )}
        </div>
      </div>

      <div className="bg-stone-800 rounded-lg p-4 border border-stone-700">
        <h3 className="text-md font-medium text-white mb-4">WhatsApp Notification Information</h3>

        <div className="space-y-4 text-stone-400 text-sm">
          <p>
            WhatsApp notifications are sent <strong className="text-orange-400">exclusively</strong> using the WhatsApp Business API
            configured in the WhatsApp Settings tab. The application will use the
            session ID configured above to send all notifications.
          </p>

          <p>
            To set up a WhatsApp session, go to the WhatsApp Settings tab and create a new session.
            Once the session is connected, you can select it here to use for notifications.
          </p>

          <p>
            If no session is selected or the selected session is not connected, notifications will not be sent.
            The system does not use any alternative WhatsApp integration methods (such as WhatsApp IPC Bridge to Windows).
          </p>

          <p className="text-orange-400 font-medium">
            All components that send WhatsApp notifications (wallet transactions, payments, deductions, etc.)
            use this same WhatsApp Business API configuration.
          </p>
        </div>
      </div>
    </div>
  )
}

export default WhatsAppNotificationSettings
