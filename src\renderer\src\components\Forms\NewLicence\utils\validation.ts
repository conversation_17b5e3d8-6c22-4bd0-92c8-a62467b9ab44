import { NewLicenceData } from '../../../../types/NewLicenceData'

/**
 * Validates the entire form data
 * @param _formData - The form data to validate
 * @returns Error message or null if valid
 */
export const validateFormData = (_formData: NewLicenceData): string | null => {
  // All fields are optional, so no validation is needed
  return null
}

/**
 * Validates the application type section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateApplicationType = (_formData: NewLicenceData): boolean => {
  // All fields are optional
  return true
}

/**
 * Validates the personal information section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validatePersonalInfo = (_formData: NewLicenceData): boolean => {
  // All fields are optional
  return true
}

/**
 * Validates the firearm type section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateFirearmType = (_formData: NewLicenceData): boolean => {
  // All fields are optional
  return true
}

/**
 * Validates the firearm details section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateFirearmDetails = (_formData: NewLicenceData): boolean => {
  // All fields are optional
  return true
}

/**
 * Validates the criminal history section
 * @param formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateCriminalHistory = (_formData: NewLicenceData): boolean => {
  return true // Optional validations
}

/**
 * Validates the current owner section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateCurrentOwner = (_formData: NewLicenceData): boolean => {
  // All fields are optional
  return true
}

/**
 * Validates the competency certificate section
 * @param formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateCompetencyCertificate = (_formData: NewLicenceData): boolean => {
  return true // Optional section
}

/**
 * Validates the training institution section
 * @param formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateTrainingInstitution = (_formData: NewLicenceData): boolean => {
  return true // Optional section
}

/**
 * Validates the association membership section
 * @param formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateAssociationMembership = (_formData: NewLicenceData): boolean => {
  return true // Optional section
}

/**
 * Validates the juristic person section
 * @param formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateJuristicPerson = (_formData: NewLicenceData): boolean => {
  return true // Optional section
}

/**
 * Validates the safe storage section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateSafeStorage = (_formData: NewLicenceData): boolean => {
  // All fields are optional
  return true
}
