import React from 'react';

interface ConnectionDialogProps {
  isOpen: boolean;
  onRetry: () => void;
}

export function ConnectionDialog({
  isOpen}: ConnectionDialogProps): React.JSX.Element | null {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-40 flex items-center justify-center bg-black/75 backdrop-blur-sm"
      style={{ 
        top: '40px', // Leave space for the title bar (which is 40px high)
        height: 'calc(100% - 40px)' // Adjust height to exclude title bar
      }}
    >
      <div className="bg-stone-900 border border-stone-700/30 rounded-lg shadow-xl p-6 w-[400px] text-center">
        <div className="flex flex-col items-center justify-center mb-5">
          <div className="w-16 h-16 mb-4 flex items-center justify-center rounded-full bg-stone-800 border border-stone-700/50">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-8 w-8 text-orange-500" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Connection Lost</h3>
        </div>
        <p className="text-stone-300">
          Your internet connection has been lost. The application will resume when the connection is restored.
        </p>
      </div>
    </div>
  );
} 