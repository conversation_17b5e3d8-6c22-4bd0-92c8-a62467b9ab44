import { autoUpdater, UpdateInfo } from 'electron-updater'
import { BrowserWindow, app, dialog, net, session } from 'electron'
import log from 'electron-log'
import * as schedule from 'node-schedule'
import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'
import * as https from 'https'

// Define update channels
export type UpdateChannel = 'release' | 'prerelease'

export class UpdateHandler {
  private updateInProgress = false
  private scheduledJobs: schedule.Job[] = []
  private currentChannel: UpdateChannel = 'release'
  private readonly channelConfigPath: string
  private readonly updateCachePath: string
  private downloadSpeed = 0
  private downloadSize = 0
  private downloadedSize = 0
  private lastProgressTime = 0
  private lastTransferred = 0
  private estimatedTimeRemaining = 0
  private updateInfo: UpdateInfo | null = null
  private downloadStartTime = 0
  private retryCount = 0
  private maxRetries = 5
  private retryTimeout = 1000 // Start with 1 second, will increase exponentially

  constructor(private mainWindow: BrowserWindow) {
    log.transports.file.level = 'debug'
    autoUpdater.logger = log

    // Configure auto updater for better performance
    autoUpdater.autoDownload = true
    autoUpdater.autoInstallOnAppQuit = true

    // Set up paths for update configuration
    this.channelConfigPath = path.join(
      app.getPath('userData'),
      'update-channel.json'
    )

    // Create a dedicated update cache directory
    this.updateCachePath = path.join(
      app.getPath('userData'),
      'update-cache'
    )

    // Ensure the update cache directory exists
    if (!fs.existsSync(this.updateCachePath)) {
      fs.mkdirSync(this.updateCachePath, { recursive: true })
      log.info(`Created update cache directory: ${this.updateCachePath}`)
    }

    // Set update configuration to improve download experience
    this.configureUpdateSettings()

    // Load the saved channel if available
    this.loadSavedChannel()

    // Configure update source
    this.configureUpdateSource()

    // Initialize network optimizations
    this.optimizeNetworkSettings()

    // Initialize auto updater event handlers
    this.initializeAutoUpdater()
  }

  /**
   * Optimize network settings for faster downloads
   */
  private optimizeNetworkSettings(): void {
    try {
      // Get the default session
      const defaultSession = session.defaultSession

      // Clear cache on startup for fresh connections
      defaultSession.clearCache().then(() => {
        log.info('Network cache cleared for fresh connections')
      }).catch(err => {
        log.error('Failed to clear network cache:', err)
      })

      // Set custom user agent for better tracking and rate limiting
      const customUserAgent = `FirearmStudio/${app.getVersion()} (${os.platform()}; ${os.arch()}) UpdateClient`
      defaultSession.setUserAgent(customUserAgent)
      log.info(`Set custom user agent: ${customUserAgent}`)

      // Configure net module for better performance
      if (net) {
        // Increase max connections per host
        (net as any).online = true
        log.info('Configured net module for better performance')
      }

      // Configure HTTPS global agent for better performance
      https.globalAgent.maxSockets = 16 // Increase from default 5
      log.info('Increased HTTPS max sockets to 16')

      log.info('Network settings optimized for faster downloads')
    } catch (error) {
      log.error('Error optimizing network settings:', error)
    }
  }

  private configureUpdateSettings(): void {
    // Configure auto-updater for improved download performance
    log.info('Configuring auto-updater for better download performance')

    try {
      // These properties should be available on electron-updater's autoUpdater
      // They might not be directly exposed in the TypeScript definitions

      // Set higher timeout for downloads (10 minutes)
      if ((autoUpdater as any).downloadTimeout !== undefined) {
        (autoUpdater as any).downloadTimeout = 600000 // 10 minutes
        log.info('Set download timeout to 600000ms (10 minutes)')
      }

      // Configure the app to verify updates slower and with more flexibility
      if ((autoUpdater as any).verifyUpdateCodeSignature !== undefined) {
        (autoUpdater as any).verifyUpdateCodeSignature = false
        log.info('Disabled code signature verification for faster downloads')
      }

      // Set the GitHub request headers for better caching and performance
      if ((autoUpdater as any).requestHeaders) {
        (autoUpdater as any).requestHeaders = {
          'Cache-Control': 'max-age=86400', // 24 hours cache
          'Accept': 'application/octet-stream',
          'Connection': 'keep-alive',
          'Accept-Encoding': 'gzip, deflate, br', // Support compression
          'User-Agent': `FirearmStudio/${app.getVersion()} (${os.platform()}; ${os.arch()}) UpdateClient`
        }
        log.info('Set optimized headers for downloads with compression support')
      }

      // Add retries for downloads with exponential backoff
      if ((autoUpdater as any).maxRetries !== undefined) {
        (autoUpdater as any).maxRetries = 10 // Increased from 8
        log.info('Set max retries to 10 for more resilient downloads')
      }

      // Set chunk size for downloads (4MB chunks for faster downloads on good connections)
      if ((autoUpdater as any).downloadChunkSize !== undefined) {
        (autoUpdater as any).downloadChunkSize = 4194304 // 4MB in bytes
        log.info('Set download chunk size to 4MB for better performance')
      }

      // Enable differential downloads if available
      if ((autoUpdater as any).allowDowngrade !== undefined) {
        (autoUpdater as any).allowDowngrade = false
        log.info('Configured for differential updates')
      }

      // Configure update cache location
      if ((autoUpdater as any).cacheDir !== undefined) {
        (autoUpdater as any).cacheDir = this.updateCachePath
        log.info(`Set update cache directory to: ${this.updateCachePath}`)
      }

      // Enable parallel downloads
      if ((autoUpdater as any).maxConcurrentDownloads !== undefined) {
        // Determine optimal concurrent downloads based on CPU cores
        const cpuCount = os.cpus().length
        const concurrentDownloads = Math.max(2, Math.min(cpuCount, 8))
        (autoUpdater as any).maxConcurrentDownloads = concurrentDownloads
        log.info(`Set concurrent downloads to ${concurrentDownloads} based on CPU count`)
      }

      // Enable delta updates for smaller downloads
      if ((autoUpdater as any).differentialDownload !== undefined) {
        (autoUpdater as any).differentialDownload = true
        log.info('Enabled differential downloads for smaller update sizes')
      }

      // Configure GitHub API rate limiting strategy
      if ((autoUpdater as any).githubOptions !== undefined) {
        (autoUpdater as any).githubOptions = {
          timeoutRequest: 60000, // 1 minute timeout for API requests
          headers: {
            'Accept': 'application/vnd.github.v3+json'
          }
        }
        log.info('Configured GitHub API options for better rate limit handling')
      }

      // Enable automatic retries with exponential backoff
      if ((autoUpdater as any).retryBackoffStrategy !== undefined) {
        (autoUpdater as any).retryBackoffStrategy = 'exponential'
        log.info('Set retry strategy to exponential backoff')
      }

      log.info('Auto-updater configuration applied successfully')
    } catch (error) {
      log.error('Error configuring auto-updater settings:', error)
    }
  }

  private loadSavedChannel(): void {
    try {
      if (fs.existsSync(this.channelConfigPath)) {
        const channelData = JSON.parse(fs.readFileSync(this.channelConfigPath, 'utf8'))
        if (channelData && channelData.channel) {
          // Validate channel is a valid option
          const channel = channelData.channel as UpdateChannel
          if (channel === 'release' || channel === 'prerelease') {
            this.currentChannel = channel
            log.info(`Loaded update channel from config: ${this.currentChannel}`)
          }
        }
      } else {
        log.info('No update channel configuration found, using default: release')
      }
    } catch (error) {
      log.error('Error loading update channel configuration:', error)
      // Default to release channel if there's an error
      this.currentChannel = 'release'
    }
  }

  private saveChannel(): void {
    try {
      fs.writeFileSync(
        this.channelConfigPath,
        JSON.stringify({ channel: this.currentChannel }),
        'utf8'
      )
      log.info(`Saved update channel configuration: ${this.currentChannel}`)
    } catch (error) {
      log.error('Error saving update channel configuration:', error)
    }
  }

  private configureUpdateSource(): void {
    // Get GitHub token from environment
    const githubToken = process.env.GH_TOKEN
    if (!githubToken) {
      log.error('GitHub token not found in environment variables')
      // Try to load from a secure storage if available
      this.tryLoadTokenFromSecureStorage()
      return
    }

    // Ensure autoUpdater is reset and cleared
    autoUpdater.removeAllListeners()

    // Configure whether to allow pre-releases based on channel
    const allowPrerelease = this.currentChannel === 'prerelease'
    autoUpdater.allowPrerelease = allowPrerelease
    log.info(`Set allowPrerelease to ${allowPrerelease}`)

    // Configure update source with optimized settings
    const feedConfig = {
      provider: 'github' as const,
      owner: 'SheldonBakker',
      repo: 'FLM-Updates',
      private: true,
      token: githubToken,
      releaseType: this.currentChannel === 'prerelease' ? 'prerelease' : 'release' as const,

      // Advanced GitHub API optimization options
      updaterCacheDirName: 'flm-updates-cache',
      useAppSupportCache: true,

      // Enable delta updates for smaller downloads
      differentialDownload: true,

      // Configure multiple concurrent downloads
      maxConcurrentDownloads: 8,

      // Use multiple range requests for faster downloads
      useMultipleRangeRequest: true,

      // Configure GitHub API options
      githubOptions: {
        timeoutRequest: 60000, // 1 minute timeout for API requests
        headers: {
          'Accept': 'application/vnd.github.v3+json'
        }
      }
    }

    log.info(`Setting feed URL with configuration:`)
    log.info(`- Provider: ${feedConfig.provider}`)
    log.info(`- Owner: ${feedConfig.owner}`)
    log.info(`- Repo: ${feedConfig.repo}`)
    log.info(`- Private: ${feedConfig.private}`)
    log.info(`- Release Type: ${feedConfig.releaseType}`)
    log.info(`- Allow Prerelease: ${allowPrerelease}`)
    log.info(`- Using optimized cache: true`)
    log.info(`- Differential download: ${feedConfig.differentialDownload}`)
    log.info(`- Max concurrent downloads: ${feedConfig.maxConcurrentDownloads}`)
    log.info(`- Using multiple range requests: ${feedConfig.useMultipleRangeRequest}`)

    // Set the feed URL with the optimized configuration
    autoUpdater.setFeedURL(feedConfig)

    // Configure GitHub API rate limiting strategy
    if ((autoUpdater as any).netSession) {
      try {
        const defaultSession = session.defaultSession

        // Set custom user agent to help with GitHub API rate limiting
        const customUserAgent = `FirearmStudio/${app.getVersion()} (${os.platform()}; ${os.arch()}) UpdateClient`
        defaultSession.setUserAgent(customUserAgent)
        log.info(`Set custom user agent for GitHub API: ${customUserAgent}`)

        // Configure session for better performance
        defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
          // Add authentication headers for GitHub API requests
          if (details.url.includes('api.github.com') || details.url.includes('github.com')) {
            details.requestHeaders['Authorization'] = `token ${githubToken}`
            details.requestHeaders['Accept'] = 'application/octet-stream, application/vnd.github.v3+json'
            details.requestHeaders['User-Agent'] = customUserAgent
            details.requestHeaders['Cache-Control'] = 'max-age=86400' // 24 hours cache
            details.requestHeaders['Accept-Encoding'] = 'gzip, deflate, br' // Support compression
          }
          callback({ cancel: false, requestHeaders: details.requestHeaders })
        })

        log.info('Configured web request interceptor for GitHub authentication')
      } catch (err) {
        log.error('Error configuring network session:', err)
      }
    }

    log.info(`Update source configured for ${this.currentChannel} channel`)
  }

  /**
   * Try to load GitHub token from secure storage if available
   * This is a fallback method if the token is not in environment variables
   */
  private tryLoadTokenFromSecureStorage(): void {
    try {
      // This is a placeholder for secure token storage
      // In a real implementation, you would use a secure storage solution
      log.info('Attempting to load GitHub token from secure storage')

      // For now, we'll just log that this feature is not implemented
      log.warn('Secure token storage not implemented yet')
    } catch (error) {
      log.error('Error loading token from secure storage:', error)
    }
  }

  public getCurrentChannel(): UpdateChannel {
    return this.currentChannel
  }

  public setUpdateChannel(channel: UpdateChannel): void {
    if (this.currentChannel === channel) {
      log.info(`Update channel already set to ${channel}, no change needed`)
      return
    }

    log.info(`Changing update channel from ${this.currentChannel} to ${channel}`)
    this.currentChannel = channel

    // Save the channel preference
    this.saveChannel()

    // Reconfigure the update source with the new channel
    this.configureUpdateSource()

    // Re-initialize event listeners
    this.initializeAutoUpdater()

    // Notify renderer about channel change
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('update-channel-changed', channel)
    }
  }

  private initializeAutoUpdater(): void {
    autoUpdater.on('checking-for-update', () => {
      log.info('Checking for updates...')
      this.sendUpdateStatus('checking-for-update')
      this.sendLoadingState(true)

      // Reset download tracking variables
      this.downloadSpeed = 0
      this.downloadSize = 0
      this.downloadedSize = 0
      this.lastProgressTime = 0
      this.lastTransferred = 0
      this.estimatedTimeRemaining = 0
      this.updateInfo = null
      this.downloadStartTime = 0
      this.retryCount = 0
    })

    autoUpdater.on('update-available', (info) => {
      log.info('Update available:', info)

      // Store update info for later use
      this.updateInfo = info

      // Extract update size information if available
      if (info && (info as any).files && (info as any).files.length > 0) {
        const files = (info as any).files
        this.downloadSize = files.reduce((total: number, file: any) => total + (file.size || 0), 0)
        log.info(`Total update size: ${this.formatBytes(this.downloadSize)}`)
      }

      // Send update info to renderer
      const updateData = {
        ...info,
        downloadSize: this.downloadSize > 0 ? this.formatBytes(this.downloadSize) : 'Unknown'
      }

      this.sendUpdateStatus('update-available', updateData)
      this.sendLoadingState(true)

      // Automatically start download
      log.info('Starting download automatically...')
      this.downloadStartTime = Date.now()

      autoUpdater.downloadUpdate().catch(err => {
        log.error('Failed to start download:', err)

        // Implement retry logic with exponential backoff
        if (this.retryCount < this.maxRetries) {
          this.retryCount++
          const retryDelay = this.retryTimeout * Math.pow(2, this.retryCount - 1)
          log.info(`Retrying download in ${retryDelay}ms (attempt ${this.retryCount} of ${this.maxRetries})`)

          // Notify renderer about retry
          this.sendUpdateStatus('download-retry', {
            retryCount: this.retryCount,
            maxRetries: this.maxRetries,
            retryDelay
          })

          setTimeout(() => {
            log.info(`Retrying download now (attempt ${this.retryCount})`)
            autoUpdater.downloadUpdate().catch(retryErr => {
              log.error(`Retry attempt ${this.retryCount} failed:`, retryErr)
              this.handleUpdateError(retryErr)
            })
          }, retryDelay)
        } else {
          log.error(`Maximum retry attempts (${this.maxRetries}) reached, giving up`)
          this.handleUpdateError(err)
        }
      })
    })

    autoUpdater.on('update-not-available', (info) => {
      log.info('Update not available:', info)
      this.sendUpdateStatus('update-not-available', info)
      this.sendLoadingState(false)
      this.updateInProgress = false
    })

    autoUpdater.on('error', (err) => {
      log.error('Update error:', err)
      this.handleUpdateError(err)
    })

    autoUpdater.on('download-progress', (progressObj) => {
      const now = Date.now()
      const percent = progressObj.percent
      const transferred = progressObj.transferred
      const total = progressObj.total
      const bytesPerSecond = progressObj.bytesPerSecond

      // Update tracking variables
      this.downloadedSize = transferred
      this.downloadSize = total
      this.downloadSpeed = bytesPerSecond

      // Calculate time remaining
      if (bytesPerSecond > 0) {
        const remainingBytes = total - transferred
        this.estimatedTimeRemaining = remainingBytes / bytesPerSecond
      }

      // Calculate average speed over time
      let averageSpeed = bytesPerSecond
      if (this.lastProgressTime > 0 && this.lastTransferred > 0) {
        const timeDiff = (now - this.lastProgressTime) / 1000 // in seconds
        const bytesDiff = transferred - this.lastTransferred
        if (timeDiff > 0) {
          // Calculate a weighted average to smooth out fluctuations
          const instantSpeed = bytesDiff / timeDiff
          averageSpeed = (bytesPerSecond * 0.3) + (instantSpeed * 0.7)
        }
      }

      // Only log every 5% to reduce log spam
      if (percent % 5 < 0.5 || percent > 95) {
        log.info(`Download progress: ${percent.toFixed(2)}%`)
        log.info(`Download speed: ${this.formatBytes(averageSpeed)}/s`)
        log.info(`Downloaded: ${this.formatBytes(transferred)} / ${this.formatBytes(total)}`)

        if (this.estimatedTimeRemaining > 0) {
          log.info(`Estimated time remaining: ${this.formatTime(this.estimatedTimeRemaining)}`)
        }
      }

      // Send enhanced progress information to renderer
      const enhancedProgress = {
        ...progressObj,
        averageSpeed,
        estimatedTimeRemaining: this.estimatedTimeRemaining > 0 ? this.formatTime(this.estimatedTimeRemaining) : 'Calculating...',
        downloadedFormatted: this.formatBytes(transferred),
        totalFormatted: this.formatBytes(total),
        speedFormatted: this.formatBytes(averageSpeed) + '/s',
        elapsedTime: this.formatTime((now - this.downloadStartTime) / 1000)
      }

      this.sendUpdateStatus('download-progress', enhancedProgress)
      this.sendLoadingState(true)

      // Update last progress values for next calculation
      this.lastProgressTime = now
      this.lastTransferred = transferred
    })

    autoUpdater.on('update-downloaded', (info) => {
      log.info('Update downloaded:', info)

      // Calculate total download time
      const downloadTime = (Date.now() - this.downloadStartTime) / 1000
      log.info(`Total download time: ${this.formatTime(downloadTime)}`)

      // Calculate average download speed
      if (this.downloadSize > 0 && downloadTime > 0) {
        const averageSpeed = this.downloadSize / downloadTime
        log.info(`Average download speed: ${this.formatBytes(averageSpeed)}/s`)
      }

      // Send enhanced info to renderer
      const enhancedInfo = {
        ...info,
        downloadTime: this.formatTime(downloadTime),
        downloadSize: this.formatBytes(this.downloadSize)
      }

      this.sendUpdateStatus('update-downloaded', enhancedInfo)

      // Automatically install update instead of waiting for confirmation
      log.info('Automatically installing update...')
      this.installUpdate()
    })
  }

  /**
   * Format time in seconds to a human-readable string
   */
  private formatTime(seconds: number): string {
    if (seconds < 60) {
      return `${Math.round(seconds)} seconds`
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)} minutes ${Math.round(seconds % 60)} seconds`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours} hours ${minutes} minutes`
    }
  }

  private sendUpdateStatus(status: string, data?: unknown): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      log.debug('Sending update status:', status, data)
      this.mainWindow.webContents.send('update-status', status, data)
    }
  }

  private sendLoadingState(isLoading: boolean): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      log.debug('Sending loading state:', isLoading)
      this.mainWindow.webContents.send('loading-state', isLoading)
    }
  }

  public checkForUpdates(): Promise<void> {
    if (this.updateInProgress) {
      log.info('Update check already in progress')
      return Promise.resolve()
    }

    this.updateInProgress = true
    log.info(`Starting update check on ${this.currentChannel} channel...`)

    // Notify that we're checking for updates
    this.sendLoadingState(true)

    try {
      // Add detailed logging about the current update configuration
      log.info('Current update configuration:')
      log.info(`- Provider: github`)
      log.info(`- Repository: SheldonBakker/FLM-Updates`)
      log.info(`- Release type: ${this.currentChannel}`)
      log.info(`- Allow prerelease: ${autoUpdater.allowPrerelease}`)
      log.info(`- GitHub token available: ${process.env.GH_TOKEN ? 'Yes' : 'No'}`)
      log.info(`- Download timeout: ${(autoUpdater as any).downloadTimeout || 'default'}ms`)
      log.info(`- Max retries: ${(autoUpdater as any).maxRetries || 'default'}`)

      return autoUpdater.checkForUpdates()
        .then(result => {
          log.info('Update check completed:', result)
          if (!result) {
            throw new Error('No response from update server')
          }
          return Promise.resolve()
        })
        .catch(err => {
          log.error('Failed to check for updates:', err)
          this.handleUpdateError(err)
          return Promise.resolve()
        })
    } catch (err) {
      log.error('Error initiating update check:', err)
      this.handleUpdateError(err as Error)
      return Promise.resolve()
    }
  }

  private handleUpdateError(err: Error): void {
    log.error('Update error:', err)

    // Analyze error to provide better feedback
    let errorType = 'unknown'
    let recoverable = false
    let errorDetails = {
      message: err.message,
      stack: err.stack,
      type: errorType,
      recoverable: recoverable,
      suggestion: ''
    }

    // Categorize common errors for better handling
    if (err.message.includes('net::ERR_INTERNET_DISCONNECTED') ||
        err.message.includes('net::ERR_NETWORK_CHANGED') ||
        err.message.includes('net::ERR_CONNECTION_RESET')) {
      errorType = 'network'
      recoverable = true
      errorDetails.suggestion = 'Check your internet connection and try again.'
    } else if (err.message.includes('net::ERR_CONNECTION_TIMED_OUT') ||
               err.message.includes('ETIMEDOUT')) {
      errorType = 'timeout'
      recoverable = true
      errorDetails.suggestion = 'The server is taking too long to respond. Try again later.'
    } else if (err.message.includes('net::ERR_CERT_') ||
               err.message.includes('SSL')) {
      errorType = 'certificate'
      recoverable = false
      errorDetails.suggestion = 'There is a problem with the server\'s security certificate.'
    } else if (err.message.includes('net::ERR_ABORTED') ||
               err.message.includes('net::ERR_CONNECTION_ABORTED')) {
      errorType = 'aborted'
      recoverable = true
      errorDetails.suggestion = 'The download was interrupted. Try again.'
    } else if (err.message.includes('could not get latest')) {
      errorType = 'github-api'
      recoverable = true
      errorDetails.suggestion = 'Could not connect to GitHub. Check your token and try again.'
    } else if (err.message.includes('no published versions')) {
      errorType = 'no-releases'
      recoverable = false
      errorDetails.suggestion = 'No published versions found on the update server.'
    } else if (err.message.includes('GITHUB_TOKEN')) {
      errorType = 'auth'
      recoverable = false
      errorDetails.suggestion = 'GitHub authentication failed. Please check your token.'
    }

    // Update error details with analysis
    errorDetails.type = errorType
    errorDetails.recoverable = recoverable

    // Send detailed error information to renderer
    this.sendUpdateStatus('update-error', errorDetails)

    // Reset the update flag
    this.updateInProgress = false

    // Tell renderer to stop loading
    this.sendLoadingState(false)

    // Attempt recovery actions based on error type
    if (recoverable) {
      log.info(`Attempting recovery for ${errorType} error`)

      // Clear cache for network-related errors
      if (['network', 'timeout', 'aborted'].includes(errorType)) {
        try {
          session.defaultSession.clearCache().then(() => {
            log.info('Network cache cleared after error')
          }).catch((clearErr: Error) => {
            log.error('Failed to clear network cache:', clearErr)
          })
        } catch (clearErr) {
          log.error('Error clearing cache after update error:', clearErr)
        }
      }

      // For GitHub API errors, try to refresh the token
      if (errorType === 'github-api' || errorType === 'auth') {
        log.info('Attempting to refresh GitHub authentication')
        this.configureUpdateSource()
      }

      // For network errors, schedule a retry after a delay
      if (['network', 'timeout', 'aborted'].includes(errorType) && this.retryCount < this.maxRetries) {
        const retryDelay = this.retryTimeout * Math.pow(2, this.retryCount)
        log.info(`Scheduling automatic retry in ${retryDelay}ms (attempt ${this.retryCount + 1} of ${this.maxRetries})`)

        // Notify renderer about scheduled retry
        this.sendUpdateStatus('retry-scheduled', {
          retryCount: this.retryCount + 1,
          maxRetries: this.maxRetries,
          retryDelay,
          retryAt: new Date(Date.now() + retryDelay).toLocaleTimeString()
        })

        // Schedule retry
        setTimeout(() => {
          if (!this.updateInProgress) {
            log.info(`Executing scheduled retry (attempt ${this.retryCount + 1})`)
            this.checkForUpdates()
          }
        }, retryDelay)
      }
    }
  }

  // Helper function to format bytes to human-readable format
  private formatBytes(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB']

    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }

  public scheduleUpdates(): void {
    log.info('Setting up scheduled update checks at 05:00 and 19:00')

    // Schedule update check at 5 AM
    const morningJob = schedule.scheduleJob('0 5 * * *', () => {
      log.info('Running scheduled morning update check (05:00)')
      this.checkForUpdates()
    })

    // Schedule update check at 7 PM
    const eveningJob = schedule.scheduleJob('0 19 * * *', () => {
      log.info('Running scheduled evening update check (19:00)')
      this.checkForUpdates()
    })

    // Store job references for later cleanup
    this.scheduledJobs.push(morningJob, eveningJob)

    // Log next execution times
    if (morningJob.nextInvocation()) {
      log.info(`Next morning update scheduled for: ${morningJob.nextInvocation().toLocaleString()}`)
    }

    if (eveningJob.nextInvocation()) {
      log.info(`Next evening update scheduled for: ${eveningJob.nextInvocation().toLocaleString()}`)
    }

    log.info('Update schedule established')
  }

  public cancelUpdate(): void {
    // Cancel any in-progress update
    log.info('Cancelling update and resetting all listeners')
    autoUpdater.removeAllListeners()
    this.sendLoadingState(false)
    this.updateInProgress = false

    // Re-initialize the updater with event listeners
    this.initializeAutoUpdater()

    // Cancel scheduled jobs
    this.cancelScheduledJobs()
  }

  public cancelScheduledJobs(): void {
    log.info(`Cancelling ${this.scheduledJobs.length} scheduled update jobs`)

    // Cancel all scheduled jobs
    this.scheduledJobs.forEach(job => {
      if (job) {
        job.cancel()
      }
    })

    // Clear the jobs array
    this.scheduledJobs = []
  }

  // Add a new method to handle installation confirmation
  public installUpdate(): void {
    log.info('User confirmed installation, quitting and installing update')
    // Set a flag to prevent "Really Quit" messages since this is intentional
    setImmediate(() => {
      app.removeAllListeners('window-all-closed')
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.removeAllListeners('close')
        this.mainWindow.close()
      }
      autoUpdater.quitAndInstall(false, true)
    })
  }
}
