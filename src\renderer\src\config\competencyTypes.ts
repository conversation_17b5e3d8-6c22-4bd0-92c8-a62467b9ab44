// Configuration for competency certificate types

// Base URL for all templates
const TEMPLATE_BASE_URL = 'https://ybsarkhaayjzlgamlcry.supabase.co/storage/v1/object/public/templates/Competency';

export interface CompetencyTemplateConfig {
  id: string;
  name: string;
  displayName: string;
  description: string;
  templateUrl: string;
  fileName: string;
}

export type CompetencyTypeId = 'new' | 'further' | 'renew';

// Competency certificate type definitions
export const COMPETENCY_TYPES: Record<CompetencyTypeId, CompetencyTemplateConfig> = {
  new: {
    id: 'new',
    name: 'new-competency',
    displayName: 'New Competency Application',
    description: 'For first-time competency applications',
    templateUrl: `${TEMPLATE_BASE_URL}/517_SAPS_Form.docx`,
    fileName: '517_SAPS_Form.docx'
  },
  further: {
    id: 'further',
    name: 'further-competency',
    displayName: 'Further Competency Application',
    description: 'For renewals or additional competency applications',
    templateUrl: `${TEMPLATE_BASE_URL}/517A_SAPS_Form.docx`,
    fileName: '517A_SAPS_Form.docx'
  },
  renew: {
    id: 'renew',
    name: 'renew-competency',
    displayName: 'Renew Competency',
    description: 'For renewing existing competency certificates',
    templateUrl: `${TEMPLATE_BASE_URL}/517G_SAPS_Form.docx`,
    fileName: '517G_SAPS_Form.docx'
  }
};

// Helper function to get competency type by ID
export const getCompetencyTypeById = (id: CompetencyTypeId): CompetencyTemplateConfig => {
  return COMPETENCY_TYPES[id];
};

// Helper function to get all competency types as an array
export const getAllCompetencyTypes = (): CompetencyTemplateConfig[] => {
  return Object.values(COMPETENCY_TYPES);
};