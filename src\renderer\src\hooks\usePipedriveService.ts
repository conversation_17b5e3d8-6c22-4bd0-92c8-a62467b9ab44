import { useState, useEffect, useCallback, useMemo } from 'react';
import { getOrInitSupabase } from '../lib/supabase';
import { Pipeline, PipelineStage, Deal } from '../types/pipedrive';
import { v4 as uuidv4 } from 'uuid';

// Cache for document URLs to prevent redundant signed URL generation
const documentUrlCache: Record<string, { url: string, expiry: number }> = {};

export const usePipedriveService = () => {
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [stages, setStages] = useState<PipelineStage[]>([]);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activePipeline, setActivePipeline] = useState<string | null>(null);

  // Fetch pipelines
  const fetchPipelines = useCallback(async () => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();
      const { data, error } = await supabase
        .from('pipelines')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      } else {
        setPipelines(data || []);

        // Set active pipeline to the first one if none is selected
        if (data && data.length > 0 && !activePipeline) {
          setActivePipeline(data[0].id);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [activePipeline]);

  // Fetch stages for active pipeline
  const fetchStages = useCallback(async () => {
    if (!activePipeline) return;

    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();
      const { data, error } = await supabase
        .from('pipeline_stages')
        .select('*')
        .eq('pipeline_id', activePipeline)
        .order('position', { ascending: true });

      if (error) {
        throw error;
      } else {
        setStages(data || []);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [activePipeline]);

  // Fetch deals for active pipeline
  const fetchDeals = useCallback(async () => {
    if (!activePipeline || stages.length === 0) return;

    try {
      setLoading(true);

      // Get stage IDs for the active pipeline
      const stageIds = stages.map(stage => stage.id);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Fetch deals with comprehensive client information
      const { data, error } = await supabase
        .from('deals')
        .select(`
          *,
          client:clients(id, first_name, last_name, phone, email, address, city, state, postal_code, id_number)
        `)
        .in('stage_id', stageIds);

      if (error) {
        throw error;
      } else {
        setDeals(data || []);
      }
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [activePipeline, stages]);

  // Create a new pipeline
  const createPipeline = async (name: string, description: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      const { data, error } = await supabase
        .from('pipelines')
        .insert({
          name,
          description
        })
        .select()
        .single();

      if (error) {
        throw error;
      } else {
        // Add default stages
        if (data) {
          const defaultStages = [
            { name: 'Lead', position: 1 },
            { name: 'Contact Made', position: 2 },
            { name: 'Proposal', position: 3 },
            { name: 'Negotiation', position: 4 },
            { name: 'Won', position: 5 }
          ];

          for (const stage of defaultStages) {
            await supabase
              .from('pipeline_stages')
              .insert({
                pipeline_id: data.id,
                name: stage.name,
                position: stage.position
              });
          }

          // Refresh pipelines
          await fetchPipelines();
          setActivePipeline(data.id);
        }
      }
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Create a new stage
  const createStage = async (name: string, description: string | null = null) => {
    if (!activePipeline) return;

    try {
      setLoading(true);

      // Get the highest position
      const highestPosition = stages.length > 0
        ? Math.max(...stages.map(s => s.position))
        : 0;

      // Use real database
      const supabase = await getOrInitSupabase();

      const { error } = await supabase
        .from('pipeline_stages')
        .insert({
          pipeline_id: activePipeline,
          name,
          description,
          position: highestPosition + 1
        });

      if (error) {
        throw error;
      }

      // Refresh stages
      await fetchStages();
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Update an existing stage
  const updateStage = async (stageId: string, name: string, description: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      const { error } = await supabase
        .from('pipeline_stages')
        .update({
          name,
          description,
          updated_at: new Date().toISOString()
        })
        .eq('id', stageId);

      if (error) {
        throw error;
      }

      // Refresh stages
      await fetchStages();
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a stage
  const deleteStage = async (stageId: string) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // First, check if there are any deals in this stage
      const { data: dealsData, error: dealsError } = await supabase
        .from('deals')
        .select('id')
        .eq('stage_id', stageId);

      if (dealsError) {
        throw dealsError;
      }

      // If there are deals, delete them first
      if (dealsData && dealsData.length > 0) {
        const dealIds = dealsData.map(deal => deal.id);

        // Delete all deal documents
        for (const dealId of dealIds) {
          // Get documents for this deal
          const { data: documentsData } = await supabase
            .from('deal_documents')
            .select('file_path')
            .eq('deal_id', dealId);

          if (documentsData && documentsData.length > 0) {
            // Delete files from storage
            for (const doc of documentsData) {
              await supabase.storage
                .from('deal_documents')
                .remove([doc.file_path]);
            }

            // Delete document records
            await supabase
              .from('deal_documents')
              .delete()
              .eq('deal_id', dealId);
          }
        }

        // Delete all deals in this stage
        const { error: deleteDealsError } = await supabase
          .from('deals')
          .delete()
          .eq('stage_id', stageId);

        if (deleteDealsError) {
          throw deleteDealsError;
        }
      }

      // Finally delete the stage
      const { error } = await supabase
        .from('pipeline_stages')
        .delete()
        .eq('id', stageId);

      if (error) {
        throw error;
      }

      // Refresh stages
      await fetchStages();
      // Also refresh deals since we might have deleted some
      await fetchDeals();
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Create a new deal
  const createDeal = async (stageId: string, clientId: string, title: string, notes: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      const { data, error } = await supabase
        .from('deals')
        .insert({
          stage_id: stageId,
          client_id: clientId,
          title,
          notes
        })
        .select('id')
        .single();

      if (error) {
        throw error;
      }

      // Refresh deals
      await fetchDeals();

      // Return the created deal ID
      return data;
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update an existing deal
  const updateDeal = async (dealId: string, title: string, notes: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      const { error } = await supabase
        .from('deals')
        .update({
          title,
          notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', dealId);

      if (error) {
        throw error;
      }

      // Refresh deals
      await fetchDeals();
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Move a deal to a different stage
  const moveDeal = async (dealId: string, newStageId: string) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      const { error } = await supabase
        .from('deals')
        .update({ stage_id: newStageId })
        .eq('id', dealId);

      if (error) {
        throw error;
      }

      // Refresh deals
      await fetchDeals();
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Upload a document for a deal
  const uploadDocument = async (dealId: string, file: File) => {
    try {
      setLoading(true);
      

      // Use real database
      const supabase = await getOrInitSupabase();

      // Skip bucket existence check since we know it exists
      // Our tests confirmed the bucket exists and is accessible
      

      // Generate a unique file path
      const uniqueId = uuidv4();
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_'); // Sanitize filename
      const filePath = `deals/${dealId}/${uniqueId}-${sanitizedFileName}`;

      
      

      // Upload file to storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('deal_documents')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        
        throw new Error(`Failed to upload file: ${uploadError.message}`);
      }

      

      // Create document record in the database
      
      const { data: recordData, error: recordError } = await supabase
        .from('deal_documents')
        .insert({
          deal_id: dealId,
          file_name: file.name,
          file_path: filePath,
          file_type: file.type,
          file_size: file.size
        })
        .select()
        .single();

      if (recordError) {
        

        // Try to clean up the uploaded file if the record creation fails
        await supabase.storage
          .from('deal_documents')
          .remove([filePath]);

        throw new Error(`Failed to create document record: ${recordError.message}`);
      }

      

      // Refresh deals to update UI
      await fetchDeals();

      
      return recordData;
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred during document upload');
      throw err; // Re-throw to allow handling in the component
    } finally {
      setLoading(false);
    }
  };

  // Fetch documents for a deal
  const fetchDealDocuments = async (dealId: string) => {
    try {
      

      // Use real database
      const supabase = await getOrInitSupabase();

      // Get document records from the database
      const { data, error } = await supabase
        .from('deal_documents')
        .select('*')
        .eq('deal_id', dealId)
        .order('created_at', { ascending: false });

      if (error) {
        
        throw error;
      }

      

      // Generate public URLs for each document
      if (data && data.length > 0) {
        

        const documentsWithUrls = await Promise.all(
          data.map(async (doc) => {
            try {
              

              // First check if the file exists in storage
              const { data: fileExists } = await supabase.storage
                .from('deal_documents')
                .list(`deals/${dealId}`, {
                  search: doc.file_path.split('/').pop()
                });

              if (!fileExists || fileExists.length === 0) {
                
              }

              // Check if we have a valid cached URL
              const cacheKey = `${doc.id}-${doc.file_path}`;
              const now = Date.now();
              const cachedData = documentUrlCache[cacheKey];

              // Use cached URL if it exists and hasn't expired (still has 10 minutes left)
              if (cachedData && cachedData.expiry > now + 10 * 60 * 1000) {
                
                return {
                  ...doc,
                  url: cachedData.url
                };
              }

              // Create a signed URL with 1 hour expiry
              const { data: urlData, error: urlError } = await supabase.storage
                .from('deal_documents')
                .createSignedUrl(doc.file_path, 60 * 60);

              // Cache the URL with expiry time
              if (urlData?.signedUrl) {
                documentUrlCache[cacheKey] = {
                  url: urlData.signedUrl,
                  expiry: now + 60 * 60 * 1000 // 1 hour from now
                };
              }

              if (urlError) {
                
                return {
                  ...doc,
                  url: null,
                  error: urlError.message
                };
              }

              
              return {
                ...doc,
                url: urlData?.signedUrl || null
              };
            } catch (urlErr) {
              
              return {
                ...doc,
                url: null,
                error: urlErr instanceof Error ? urlErr.message : 'Unknown error'
              };
            }
          })
        );

        
        return documentsWithUrls;
      }

      return data || [];
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      return [];
    }
  };

  // Delete a document
  const deleteDocument = async (documentId: string, filePath: string) => {
    try {
      setLoading(true);
      

      // Use real database
      const supabase = await getOrInitSupabase();

      // First delete the file from storage
      const { error: storageError } = await supabase.storage
        .from('deal_documents')
        .remove([filePath]);

      if (storageError) {
        
        // Continue with deletion of the record even if file deletion fails
      }

      // Then delete the document record
      const { error: recordError } = await supabase
        .from('deal_documents')
        .delete()
        .eq('id', documentId);

      if (recordError) {
        
        throw recordError;
      }

      

      // Refresh deals to update UI
      await fetchDeals();

      return true;
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a deal
  const deleteDeal = async (dealId: string) => {
    try {
      setLoading(true);
      

      // Use real database
      const supabase = await getOrInitSupabase();

      // First, delete any associated documents
      // 1. Get all document records for this deal
      const { data: documentData, error: docQueryError } = await supabase
        .from('deal_documents')
        .select('file_path')
        .eq('deal_id', dealId);

      if (docQueryError) {
        
        // Continue with deletion even if document fetching fails
      }

      // 2. Delete files from storage
      if (documentData && documentData.length > 0) {
        
        const filePaths = documentData.map(doc => doc.file_path);

        const { error: storageError } = await supabase.storage
          .from('deal_documents')
          .remove(filePaths);

        if (storageError) {
          
          // Continue with deletion even if file deletion fails
        }
      }

      // 3. Delete document records
      const { error: docDeleteError } = await supabase
        .from('deal_documents')
        .delete()
        .eq('deal_id', dealId);

      if (docDeleteError) {
        
        // Continue with deletion even if document record deletion fails
      }

      // Finally delete the deal
      const { error } = await supabase
        .from('deals')
        .delete()
        .eq('id', dealId);

      if (error) throw error;

      

      // Refresh deals
      await fetchDeals();
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update an existing pipeline
  const updatePipeline = async (pipelineId: string, name: string, description: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      const { error } = await supabase
        .from('pipelines')
        .update({
          name,
          description,
          updated_at: new Date().toISOString()
        })
        .eq('id', pipelineId);

      if (error) {
        throw error;
      }

      // Refresh pipelines
      await fetchPipelines();
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a pipeline
  const deletePipeline = async (pipelineId: string) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Get all stages for this pipeline
      const { data: stageData } = await supabase
        .from('pipeline_stages')
        .select('id')
        .eq('pipeline_id', pipelineId);

      if (stageData && stageData.length > 0) {
        const stageIds = stageData.map(stage => stage.id);

        // Delete all deals in these stages
        await supabase
          .from('deals')
          .delete()
          .in('stage_id', stageIds);

        // Delete all stages
        await supabase
          .from('pipeline_stages')
          .delete()
          .eq('pipeline_id', pipelineId);
      }

      // Finally delete the pipeline
      const { error } = await supabase
        .from('pipelines')
        .delete()
        .eq('id', pipelineId);

      if (error) throw error;

      // Refresh pipelines
      await fetchPipelines();
    } catch (err) {
      
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Load initial data
  useEffect(() => {
    fetchPipelines();
  }, [fetchPipelines]);

  // Load stages when active pipeline changes
  useEffect(() => {
    if (activePipeline) {
      fetchStages();
    }
  }, [activePipeline, fetchStages]);

  // Load deals when stages change
  useEffect(() => {
    if (stages.length > 0) {
      fetchDeals();
    }
  }, [stages, fetchDeals]);

  return {
    pipelines,
    stages,
    deals,
    loading,
    error,
    activePipeline,
    setActivePipeline,
    createPipeline,
    updatePipeline,
    createStage,
    updateStage,
    deleteStage,
    createDeal,
    updateDeal,
    moveDeal,
    deleteDeal,
    uploadDocument,
    fetchDealDocuments,
    deleteDocument,
    deletePipeline,
    refreshData: () => {
      fetchPipelines();
      fetchStages();
      fetchDeals();
    }
  };
};


