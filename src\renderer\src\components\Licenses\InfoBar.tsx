import React from 'react'
import { FilterState } from './types'

interface InfoBarProps {
  visibleCount: number
  totalCount: number
  searchQuery: string
  activeFilter: FilterState['activeFilter']
}

export const InfoBar: React.FC<InfoBarProps> = ({
  visibleCount,
  totalCount,
  searchQuery,
  activeFilter
}) => {
  return (
    <div className="bg-stone-800/50 rounded-lg px-4 py-3 border border-stone-700/50">
      <div className="text-stone-300">
        {visibleCount} of {totalCount} licenses
        {activeFilter !== 'all' && (
          <span>
            {' '}
            filtered by{' '}
            <span className="text-orange-400 font-medium">
              {activeFilter === 'expiring' ? 'Expiring Soon' : 'Expired'}
            </span>
          </span>
        )}
        {searchQuery && (
          <span>
            {' '}
            {activeFilter !== 'all' ? 'and' : 'for'} "
            <span className="text-orange-400 font-medium">{searchQuery}</span>"
          </span>
        )}
      </div>
    </div>
  )
}
