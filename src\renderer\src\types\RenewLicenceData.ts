import { FormData } from './FormData'

// Define interface for original licence details
export interface OriginalLicenceDetails {
  licenceNumber: string;
  issueDate: string;
  expiryDate: string;
}

// Define the interface for Renew Licence form data
export interface RenewLicenceData extends FormData {
  // Additional identification fields
  passport?: string;
  permResNumber?: string;
  initials?: string;

  // Juristic person fields
  companyEmail?: string;
  tradingAsName?: string;
  farNumber?: string;
  postalAddress?: string;
  businessTelNumber?: string;

  // Responsible person fields
  responsiblePersonName?: string;
  responsiblePersonSaId?: boolean;
  responsiblePersonPassport?: boolean;
  responsiblePersonIdNumber?: string;
  responsiblePersonPassportNumber?: string;
  responsiblePersonCellNumber?: string;
  responsiblePersonAddress?: string;
  responsiblePersonPostalCode?: string;
  responsiblePersonHouseNumber?: string;

  // Other Information fields
  before90DaysYes?: boolean;
  before90DaysNo?: boolean;
  before90DaysReason?: string;
  afterDueBeforeExpiryYes?: boolean;
  afterDueBeforeExpiryNo?: boolean;
  afterDueBeforeExpiryReason?: string;
  afterExpiryYes?: boolean;
  afterExpiryNo?: boolean;
  afterExpiryReason?: string;
  // Original licence details
  originalLicenceNumber?: string;
  originalLicenceIssueDate?: string;
  originalLicenceExpiryDate?: string;

  // Additional original licences
  additionalOriginalLicences?: OriginalLicenceDetails[];

  // Flag to show additional licences form
  showAdditionalLicences?: boolean;

  // Licence renewal specific details
  prevLicenceNumber: string;
  prevLicenceIssueDate: string;
  prevLicenceExpiryDate: string;

  // Reason for renewal
  standardRenewal: boolean;
  lostLicence: boolean;
  lostLicenceCaseNumber: string;
  damagedLicence: boolean;

  // Additional properties from NewLicenceData we also need
  mainHF: boolean;
  addHF: boolean;

  // Licence type
  s13: boolean; // Self-defence
  s15: boolean; // Occasional hunting/sport
  s16: boolean; // Dedicated hunting/sport
  s20: boolean; // Business - hunting
  s20a: boolean; // Business - other
  s20b: boolean; // Business - security
  s20c: boolean; // Business - training

  // Firearm type
  comb: boolean; // Combination
  otherDesign: boolean;
  otherDesignE: string;

  // Firearm details
  semi: boolean; // Semi-automatic
  auto: boolean; // Automatic
  man: boolean; // Manual
  otherF: string;
  engg: string;

  // Serial numbers
  bsn: string; // Barrel serial number
  fsn: string; // Frame serial number
  rsn: string; // Receiver serial number

  // Component makes
  bsnm: string; // Barrel make
  fsnm: string; // Frame make
  rsnm: string; // Receiver make

  // Safe information
  safeYes: boolean;
  safeNo: boolean;
  safeH: boolean; // Handgun safe
  safeR: boolean; // Rifle safe
  safeS: boolean; // Strongroom
  safeSe: string; // Strongroom info
  safeD: boolean; // Device
  safeDInfo: string; // Device info
  safeMountYes: boolean;
  safeMountNo: boolean;
  safeWall: boolean;
  safeFloor: boolean;
}

// Initial form data with placeholders for Renew Licence
export const initialRenewLicenceData: RenewLicenceData = {
  // Standard FormData properties
  fullName: '',
  firstName: '',
  lastName: '',
  idNumber: '',
  phoneNumber: '',
  email: '',
  physicalAddress: '',
  documentProcessed: false,
  companyName: '',
  tradingAsName: '',
  farNumber: '',
  postalAddress: '',
  workPostalCode: '',
  businessTelNumber: '',
  workNumber: '',
  companyEmail: '',
  responsiblePersonName: '',
  responsiblePersonSaId: false,
  responsiblePersonPassport: false,
  responsiblePersonIdNumber: '',
  responsiblePersonPassportNumber: '',
  responsiblePersonCellNumber: '',
  responsiblePersonAddress: '',
  responsiblePersonPostalCode: '',
  responsiblePersonHouseNumber: '',

  // Other Information fields
  before90DaysYes: false,
  before90DaysNo: false,
  before90DaysReason: '',
  afterDueBeforeExpiryYes: false,
  afterDueBeforeExpiryNo: false,
  afterDueBeforeExpiryReason: '',
  afterExpiryYes: false,
  afterExpiryNo: false,
  afterExpiryReason: '',
  saId: false,
  fId: false,
  permRes: false,
  passport: '',
  permResNumber: '',
  initials: '',
  sexM: false,
  sexF: false,
  singles: false,
  married: false,
  divorced: false,
  widower: false,
  widow: false,
  citizenType: '',
  maritalStatus: '',
  spouseIdType: 'none',

  // Original licence details
  originalLicenceNumber: '',
  originalLicenceIssueDate: '',
  originalLicenceExpiryDate: '',

  // Additional original licences
  additionalOriginalLicences: [],
  showAdditionalLicences: false,

  // Renewal specific
  prevLicenceNumber: '',
  prevLicenceIssueDate: '',
  prevLicenceExpiryDate: '',

  standardRenewal: true,
  lostLicence: false,
  lostLicenceCaseNumber: '',
  damagedLicence: false,

  // Application type
  mainHF: true,
  addHF: false,

  // Licence type
  s13: false,
  s15: false,
  s16: false,
  s20: false,
  s20a: false,
  s20b: false,
  s20c: false,

  // Firearm type
  comb: false,
  otherDesign: false,
  otherDesignE: '',

  // Firearm details
  semi: false,
  auto: false,
  man: false,
  otherF: '',
  engg: '',

  // Serial numbers
  bsn: '',
  fsn: '',
  rsn: '',

  // Component makes
  bsnm: '',
  fsnm: '',
  rsnm: '',

  // Safe information
  safeYes: false,
  safeNo: false,
  safeH: false,
  safeR: false,
  safeS: false,
  safeSe: '',
  safeD: false,
  safeDInfo: '',
  safeMountYes: false,
  safeMountNo: false,
  safeWall: false,
  safeFloor: false,
  tradeProfession: '',
  workAddress: ''
};
