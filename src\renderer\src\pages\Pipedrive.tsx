import React, { useState, Suspense, lazy, useCallback, useMemo, useEffect } from 'react';
import { usePipedriveService } from '../services/usePipedriveService';
import { Pipeline, PipelineStage, Deal } from '../types/pipedrive';
import { SkeletonDashboard } from '../components/SkeletonLoading';
import Toast from '../components/Toast';
import { Dialog } from '../components/Dialog';
import { DashboardIcons } from '../components/icons/DashboardIcons';
import PipelineView from '../components/Pipedrive/PipelineView';
import PipelineList from '../components/Pipedrive/PipelineList';

// Lazy load unified form components
const UnifiedPipelineForm = lazy(() => import('../components/Forms/Pipedrive/PipelineForm'));
const UnifiedStageForm = lazy(() => import('../components/Forms/Pipedrive/StageForm'));
const UnifiedDealForm = lazy(() => import('../components/Forms/Pipedrive/DealForm'));

function Pipedrive(): React.JSX.Element {
  const {
    pipelines,
    stages,
    deals,
    loading,
    activePipeline,
    setActivePipeline,
    createPipeline,
    updatePipeline,
    createStage,
    updateStage,
    deleteStage,
    createDeal,
    updateDeal,
    moveDeal,
    deleteDeal,
    uploadDocument,
    deletePipeline,
    refreshData
  } = usePipedriveService();

  const [formState, setFormState] = useState<{
    type: 'pipeline' | 'pipeline-edit' | 'stage' | 'stage-edit' | 'deal' | 'deal-edit' | null;
    isOpen: boolean;
    stageId?: string;
    stage?: PipelineStage;
    pipeline?: Pipeline;
    deal?: Deal;
  }>({
    type: null,
    isOpen: false
  });

  const [toast, setToast] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error' | 'info';
  }>({
    show: false,
    message: '',
    type: 'info'
  });

  // State for delete confirmation dialogs
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    type: 'pipeline' | 'stage' | 'deal' | null;
    pipelineId: string | null;
    pipelineName: string | null;
    stageId: string | null;
    stageName: string | null;
    dealId: string | null;
    dealTitle: string | null;
  }>({
    isOpen: false,
    type: null,
    pipelineId: null,
    pipelineName: null,
    stageId: null,
    stageName: null,
    dealId: null,
    dealTitle: null
  });

  // Search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredDeals, setFilteredDeals] = useState<Deal[]>([]);

  // Filter deals based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      // If search query is empty, don't filter
      setFilteredDeals(deals);
      return;
    }

    const query = searchQuery.toLowerCase().trim();

    // Filter deals based on search query
    const filtered = deals.filter(deal => {
      // Search in deal title
      if (deal.title.toLowerCase().includes(query)) return true;

      // Search in notes
      if (deal.notes && deal.notes.toLowerCase().includes(query)) return true;

      // Search in client information if available
      if (deal.client) {
        // Search in client name
        const fullName = `${deal.client.first_name} ${deal.client.last_name}`.toLowerCase();
        if (fullName.includes(query)) return true;

        // Search in client email
        if (deal.client.email && deal.client.email.toLowerCase().includes(query)) return true;

        // Search in client phone
        if (deal.client.phone && deal.client.phone.includes(query)) return true;
      }

      return false;
    });

    setFilteredDeals(filtered);
  }, [searchQuery, deals]);

  // Show toast message - memoized to prevent recreation on each render
  const showToast = useCallback((message: string, type: 'success' | 'error' | 'info') => {
    setToast({
      show: true,
      message,
      type
    });

    // Auto-hide toast after 3 seconds
    setTimeout(() => {
      setToast(prev => ({ ...prev, show: false }));
    }, 3000);
  }, []);

  // Close form - memoized
  const closeForm = useCallback(() => {
    setFormState({
      type: null,
      isOpen: false
    });
  }, []);

  // Handle form submission - memoized to prevent recreation on each render
  const handleFormSubmit = useCallback(async (formData: any) => {
    try {
      if (formState.type === 'pipeline') {
        await createPipeline(formData.name, formData.description);
        showToast('Pipeline created successfully', 'success');
        closeForm();
        refreshData();
        return undefined;
      } else if (formState.type === 'pipeline-edit') {
        await updatePipeline(formData.pipelineId, formData.name, formData.description);
        showToast('Pipeline renamed successfully', 'success');
        closeForm();
        refreshData();
        return undefined;
      } else if (formState.type === 'stage') {
        await createStage(formData.name, formData.description);
        showToast('Stage created successfully', 'success');
        closeForm();
        refreshData();
        return undefined;
      } else if (formState.type === 'stage-edit') {
        await updateStage(formData.stageId, formData.name, formData.description);
        showToast('Stage updated successfully', 'success');
        closeForm();
        refreshData();
        return undefined;
      } else if (formState.type === 'deal') {
        const result = await createDeal(
          formData.stageId,
          formData.clientId,
          formData.title,
          formData.notes
        );
        showToast('Deal created successfully', 'success');
        refreshData();
        // Return the created deal ID for document upload
        return result?.id ? { id: result.id } : undefined;
      } else if (formState.type === 'deal-edit') {
        await updateDeal(formData.dealId, formData.title, formData.notes);
        showToast('Deal updated successfully', 'success');
        closeForm();
        refreshData();
        return undefined;
      }

      // Default return for any other case
      return undefined;
    } catch (err) {
      showToast(
        err instanceof Error ? err.message : 'An error occurred',
        'error'
      );
      throw err;
    }
  }, [
    formState,
    createPipeline,
    updatePipeline,
    createStage,
    updateStage,
    createDeal,
    updateDeal,
    closeForm,
    refreshData,
    showToast
  ]);

  // Open form - memoized to prevent recreation on each render
  const openForm = useCallback((type: 'pipeline' | 'stage' | 'deal', stageId?: string) => {
    setFormState({
      type,
      isOpen: true,
      stageId
    });
  }, []);

  // Open pipeline edit form - memoized
  const openPipelineEditForm = useCallback((pipeline: Pipeline) => {
    setFormState({
      type: 'pipeline-edit',
      isOpen: true,
      pipeline
    });
  }, []);

  // Open stage edit form - memoized
  const openStageEditForm = useCallback((stage: PipelineStage) => {
    setFormState({
      type: 'stage-edit',
      isOpen: true,
      stage
    });
  }, []);

  // Open deal edit form - memoized
  const openDealEditForm = useCallback((deal: Deal) => {
    setFormState({
      type: 'deal-edit',
      isOpen: true,
      deal
    });
  }, []);

  // Handle deletion (pipeline, stage, or deal) - memoized
  const handleDelete = useCallback(async () => {
    try {
      if (deleteDialog.type === 'pipeline' && deleteDialog.pipelineId) {
        // Delete pipeline
        await deletePipeline(deleteDialog.pipelineId);
        showToast(`Pipeline "${deleteDialog.pipelineName}" deleted successfully`, 'success');

        // If the deleted pipeline was active, set activePipeline to null
        if (activePipeline === deleteDialog.pipelineId) {
          setActivePipeline(null);
        }
      } else if (deleteDialog.type === 'stage' && deleteDialog.stageId) {
        // Delete stage
        await deleteStage(deleteDialog.stageId);
        showToast(`Stage "${deleteDialog.stageName}" deleted successfully`, 'success');
      } else if (deleteDialog.type === 'deal' && deleteDialog.dealId) {
        // Delete deal
        await deleteDeal(deleteDialog.dealId);
        showToast(`Deal "${deleteDialog.dealTitle}" deleted successfully`, 'success');
      } else {
        showToast('Unable to delete: missing required information', 'error');
      }

      // Close the dialog
      setDeleteDialog({
        isOpen: false,
        type: null,
        pipelineId: null,
        pipelineName: null,
        stageId: null,
        stageName: null,
        dealId: null,
        dealTitle: null
      });
    } catch (err) {
      showToast(
        err instanceof Error ? err.message : 'Failed to delete item',
        'error'
      );
    }
  }, [deleteDialog, deletePipeline, deleteStage, deleteDeal, activePipeline, setActivePipeline, showToast]);

  // Open delete pipeline dialog - memoized
  const openDeletePipelineDialog = useCallback((id: string, name: string) => {
    setDeleteDialog({
      isOpen: true,
      type: 'pipeline',
      pipelineId: id,
      pipelineName: name,
      stageId: null,
      stageName: null,
      dealId: null,
      dealTitle: null
    });
  }, []);

  // Open delete stage dialog - memoized
  const openDeleteStageDialog = useCallback((stage: PipelineStage) => {
    setDeleteDialog({
      isOpen: true,
      type: 'stage',
      pipelineId: null,
      pipelineName: null,
      stageId: stage.id,
      stageName: stage.name,
      dealId: null,
      dealTitle: null
    });
  }, []);

  // Open delete deal dialog - memoized
  const openDeleteDealDialog = useCallback((deal: Deal) => {
    setDeleteDialog({
      isOpen: true,
      type: 'deal',
      pipelineId: null,
      pipelineName: null,
      stageId: null,
      stageName: null,
      dealId: deal.id,
      dealTitle: deal.title
    });
  }, []);

  // Render form based on type - memoized to prevent unnecessary re-renders
  const renderForm = useMemo(() => {
    if (!formState.isOpen) return null;

    switch (formState.type) {
      case 'pipeline':
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <UnifiedPipelineForm
              mode="create"
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
            />
          </Suspense>
        );
      case 'pipeline-edit':
        return (
          <Suspense fallback={<div>Loading...</div>}>
            {formState.pipeline && (
              <UnifiedPipelineForm
                mode="edit"
                pipeline={formState.pipeline}
                onSubmit={handleFormSubmit}
                onCancel={closeForm}
              />
            )}
          </Suspense>
        );
      case 'stage':
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <UnifiedStageForm
              mode="create"
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
            />
          </Suspense>
        );
      case 'stage-edit':
        return (
          <Suspense fallback={<div>Loading...</div>}>
            {formState.stage && (
              <UnifiedStageForm
                mode="edit"
                stage={formState.stage}
                onSubmit={handleFormSubmit}
                onCancel={closeForm}
              />
            )}
          </Suspense>
        );
      case 'deal':
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <UnifiedDealForm
              mode="create"
              onSubmit={(formData) => {
                // Add stageId to the form data
                return handleFormSubmit({
                  ...formData,
                  stageId: formState.stageId
                });
              }}
              onCancel={closeForm}
              onUploadDocument={uploadDocument}
            />
          </Suspense>
        );
      case 'deal-edit':
        return (
          <Suspense fallback={<div>Loading...</div>}>
            {formState.deal && (
              <UnifiedDealForm
                mode="edit"
                deal={formState.deal}
                onSubmit={handleFormSubmit}
                onCancel={closeForm}
                onUploadDocument={uploadDocument}
              />
            )}
          </Suspense>
        );
      default:
        return null;
    }
  }, [formState, handleFormSubmit, closeForm, uploadDocument]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-stone-900 to-stone-800 p-6 overflow-hidden">
      {/* Toast notifications */}
      {toast.show && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(prev => ({ ...prev, show: false }))}
        />
      )}

      {/* Page title with pipeline count */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white flex items-center gap-3">
          Pipedrive Management
          {pipelines.length > 0 && (
            <div className="px-3 py-1 bg-stone-700/50 rounded-full text-sm text-stone-300">
              Total Pipelines: {pipelines.length}
            </div>
          )}
        </h1>

        <div className="flex space-x-3">
          {/* Refresh Button */}
          <button
            onClick={() => refreshData()}
            disabled={loading}
            className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-2 rounded-lg
              transition-colors flex items-center gap-1 disabled:opacity-50 h-10"
          >
            <DashboardIcons.Refresh className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
          </button>

          <button
            onClick={() => openForm('pipeline')}
            className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
              text-white px-3 py-2 rounded-lg shadow-md shadow-orange-500/20
              transition-all duration-200 flex items-center gap-2 h-10 whitespace-nowrap"
          >
            <DashboardIcons.Add className="w-4 h-4" />
            <span>New Pipeline</span>
          </button>
          {activePipeline && (
            <button
              onClick={() => openForm('stage')}
              className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-2 rounded-lg
                transition-colors flex items-center gap-2 h-10"
            >
              <DashboardIcons.Add className="w-4 h-4" />
              <span>New Stage</span>
            </button>
          )}
        </div>
      </div>

      {/* Two-column layout - always side by side */}
      <div className="flex flex-row h-[calc(100vh-180px)] overflow-hidden">
        {/* Left column: Pipeline list sidebar */}
        <div className="w-80 bg-stone-800/60 rounded-lg shadow-lg p-4 flex-shrink-0 overflow-hidden">
          {/* Search Input */}
          <div className="relative mb-4">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <DashboardIcons.Search className="w-4 h-4 text-stone-400" />
            </div>
            <input
              type="text"
              placeholder="Search deals..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-stone-700/50 border border-stone-600/50 text-white h-10 pl-10 pr-10 py-2 rounded-lg
                focus:outline-none focus:ring-1 focus:ring-orange-500 w-full"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-stone-400 hover:text-white"
              >
                <DashboardIcons.Close className="w-4 h-4" />
              </button>
            )}
          </div>

          <h3 className="text-sm font-medium text-stone-300 mb-4">Pipelines</h3>
          <PipelineList
            pipelines={pipelines}
            activePipeline={activePipeline}
            onSelectPipeline={setActivePipeline}
            onEditPipeline={openPipelineEditForm}
            onDeletePipeline={(id, name) => openDeletePipelineDialog(id, name)}
          />
        </div>

        {/* Right column: Pipeline view */}
        <div className="flex-1 ml-6 overflow-y-auto bg-stone-800/30 rounded-lg p-4 h-[calc(100vh-180px)]">
          {/* Search results indicator */}
          {searchQuery.trim() && (
            <div className="mb-4 px-3 py-2 bg-stone-700/50 rounded-lg flex items-center justify-between">
              <div className="text-sm text-stone-300">
                {filteredDeals.length === 0
                  ? 'No deals found matching your search'
                  : `Found ${filteredDeals.length} deal${filteredDeals.length === 1 ? '' : 's'} matching "${searchQuery}"`}
              </div>
              <button
                onClick={() => setSearchQuery('')}
                className="text-sm text-orange-400 hover:text-orange-300 flex items-center gap-1"
              >
                <DashboardIcons.Close className="w-3 h-3" />
                <span>Clear search</span>
              </button>
            </div>
          )}

          {loading && pipelines.length === 0 ? (
            <div className="h-full">
              <SkeletonDashboard cardCount={5} />
            </div>
          ) : activePipeline ? (
            <PipelineView
              stages={stages}
              deals={searchQuery.trim() ? filteredDeals : deals}
              onAddDeal={(stageId) => openForm('deal', stageId)}
              onEditStage={openStageEditForm}
              onDeleteStage={openDeleteStageDialog}
              onEditDeal={openDealEditForm}
              onDeleteDeal={openDeleteDealDialog}
              onMoveDeal={moveDeal}
            />
          ) : (
            <div className="h-full flex flex-col items-center justify-center">
              <DashboardIcons.Filter className="w-16 h-16 text-stone-700 mb-4" />
              <p className="text-stone-400 text-lg mb-2">
                {pipelines.length === 0
                  ? 'No pipelines found'
                  : 'Select a pipeline from the sidebar'}
              </p>
              <p className="text-stone-500 text-sm max-w-md text-center">
                {pipelines.length === 0
                  ? 'Create a new pipeline to get started with your sales process'
                  : 'Choose a pipeline from the list to view its stages and deals'}
              </p>
              {pipelines.length === 0 && (
                <button
                  onClick={() => openForm('pipeline')}
                  className="mt-6 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                    text-white px-4 py-2 rounded-lg shadow-md shadow-orange-500/20
                    transition-all duration-200 flex items-center gap-2"
                >
                  <DashboardIcons.Add className="w-4 h-4" />
                  <span>Create First Pipeline</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Form modal */}
      {renderForm}

      {/* Delete confirmation dialog */}
      <Dialog
        isOpen={deleteDialog.isOpen}
        title={
          deleteDialog.type === 'pipeline'
            ? 'Delete Pipeline'
            : deleteDialog.type === 'stage'
              ? 'Delete Stage'
              : 'Delete Deal'
        }
        message={
          deleteDialog.type === 'pipeline'
            ? `Are you sure you want to delete the pipeline "${deleteDialog.pipelineName}"? This will permanently delete all stages and deals in this pipeline. This action cannot be undone.`
            : deleteDialog.type === 'stage'
              ? `Are you sure you want to delete the stage "${deleteDialog.stageName}"? This will permanently delete all deals in this stage. This action cannot be undone.`
              : `Are you sure you want to delete the deal "${deleteDialog.dealTitle}"? This will permanently delete all documents associated with this deal. This action cannot be undone.`
        }
        onConfirm={handleDelete}
        onCancel={() => {
          setDeleteDialog({
            isOpen: false,
            type: null,
            pipelineId: null,
            pipelineName: null,
            stageId: null,
            stageName: null,
            dealId: null,
            dealTitle: null
          });
        }}
        confirmText="Delete"
        confirmButtonClass="bg-red-500 hover:bg-red-600 text-white shadow-md shadow-red-500/20"
      />
    </div>
  );
}

export default Pipedrive;
