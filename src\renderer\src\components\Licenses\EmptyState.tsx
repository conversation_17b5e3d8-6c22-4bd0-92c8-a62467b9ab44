import React from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'
import { FilterState } from './types'

interface EmptyStateProps {
  searchQuery: string
  activeFilter: FilterState['activeFilter']
  onAddLicense: () => void
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  searchQuery,
  activeFilter,
  onAddLicense
}) => {
  return (
    <div className="flex flex-col items-center justify-center h-64 text-stone-400">
      <DashboardIcons.Search className="w-12 h-12 mb-4" />
      {searchQuery ? (
        <>
          <p className="text-lg">No results found for "{searchQuery}"</p>
          <p className="text-sm mt-2">Try searching by make, type, caliber, or client name</p>
        </>
      ) : activeFilter !== 'all' ? (
        <>
          <p className="text-lg">No licenses found with the selected filter</p>
          <p className="text-sm mt-2">Try a different filter or add a new license</p>
        </>
      ) : (
        <>
          <p className="text-lg">No licenses found</p>
          <p className="text-sm mt-2">Add a license to a client to get started</p>
          <button
            onClick={onAddLicense}
            className="mt-4 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg
            inline-flex items-center gap-2 transition-colors"
          >
            <DashboardIcons.Add className="w-4 h-4" />
            Add New License
          </button>
        </>
      )}
    </div>
  )
}
