import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { encryptEnvFile } from './encryption';

/**
 * Script to encrypt .env file for production builds
 * Usage: ts-node encrypt-env.ts
 * 
 * This script should be run before packaging the app
 * It will:
 * 1. Encrypt the .env file
 * 2. Create a key file with the encryption password
 */

const ROOT_DIR = path.resolve(__dirname, '../..');
const ENV_FILE = path.join(ROOT_DIR, '.env');
const ENCRYPTED_ENV_FILE = path.join(ROOT_DIR, '.env.encrypted');

// Create two key files - one for development and one for production
// Use .js files directly to avoid TypeScript compilation issues
const DEV_KEY_FILE = path.join(ROOT_DIR, 'src/utils/env-key.js');
const MAIN_KEY_FILE = path.join(ROOT_DIR, 'src/main/env-key.js');

// Check if .env file exists
if (!fs.existsSync(ENV_FILE)) {
  console.error('Error: .env file not found at', ENV_FILE);
  process.exit(1);
}

// Encrypt the .env file
try {
  console.log('Encrypting .env file...');
  console.log('Source: ', ENV_FILE);
  console.log('Destination: ', ENCRYPTED_ENV_FILE);
  
  const password = encryptEnvFile(ENV_FILE, ENCRYPTED_ENV_FILE);
  
  // Create the key files with the password as plain JavaScript files
  // This avoids TypeScript compilation issues with string values
  const keyFileContent = `
// AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
// This file contains the encryption key for the .env.encrypted file
// It is generated automatically during the build process

// Using CommonJS exports to avoid any issues with ESM/CJS conversion
exports.ENV_ENCRYPTION_KEY = '${password}';
`;

  // Write the key to both locations
  fs.writeFileSync(DEV_KEY_FILE, keyFileContent);
  fs.writeFileSync(MAIN_KEY_FILE, keyFileContent);
  
  // Verify files were created
  if (fs.existsSync(ENCRYPTED_ENV_FILE) && fs.existsSync(DEV_KEY_FILE) && fs.existsSync(MAIN_KEY_FILE)) {
    const encryptedSize = fs.statSync(ENCRYPTED_ENV_FILE).size;
    console.log(`✅ .env file encrypted successfully! (${encryptedSize} bytes)`);
    console.log(`✅ Encryption key saved to development location: ${DEV_KEY_FILE}`);
    console.log(`✅ Encryption key saved to production location: ${MAIN_KEY_FILE}`);
    
    // Print the actual key value for verification
    console.log(`✅ Key value (first 10 chars): ${password.substring(0, 10)}...`);
  } else {
    if (!fs.existsSync(ENCRYPTED_ENV_FILE)) {
      console.error('❌ Failed to create encrypted file at', ENCRYPTED_ENV_FILE);
    }
    if (!fs.existsSync(DEV_KEY_FILE)) {
      console.error('❌ Failed to create development key file at', DEV_KEY_FILE);
    }
    if (!fs.existsSync(MAIN_KEY_FILE)) {
      console.error('❌ Failed to create production key file at', MAIN_KEY_FILE);
    }
    process.exit(1);
  }
  
  console.log('\nNext steps:');
  console.log('1. Add both key files to .gitignore to keep the key secure');
  console.log('2. The application will now use the correct key file in both development and production');
} catch (error: unknown) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error('❌ Error encrypting .env file:', errorMessage);
  if (error instanceof Error && error.stack) {
    console.error('Error stack:', error.stack);
  }
  process.exit(1);
} 