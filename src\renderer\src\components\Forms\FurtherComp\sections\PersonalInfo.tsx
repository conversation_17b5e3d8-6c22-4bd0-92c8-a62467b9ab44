import React from 'react'
import { FormSection } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionProps } from '../utils/types'
import { HOUSE_TYPE_OPTIONS } from '../utils/constants'

const PersonalInfo: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target
    const updatedData: any = {}

    // Handle checkboxes
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement
      updatedData[name] = checkbox.checked
    } else {
      updatedData[name] = value
    }

    // For other input types
    if (name === 'firstName' && value) {
      // Extract initials from first name
      const initials = value
        .split(' ')
        .map((name) => name.charAt(0).toUpperCase())
        .join(' ')

      updatedData.initials = initials
    }

    updateFormData(updatedData)
  }

  // Handle address field changes
  const handleAddressChange = (address: string, postalCode?: string) => {
    updateFormData({
      physicalAddress: address,
      postalCode: postalCode || formData.postalCode
    })
  }

  // Handle citizenship type change
  const handleCitizenTypeChange = (value: string) => {
    updateFormData({
      citizenType: value as 'saId' | 'fId' | '',
      saId: value === 'saId',
      fId: value === 'fId'
    })
  }

  const citizenTypeOptions = [
    { value: 'saId', label: 'SA Citizen' },
    { value: 'fId', label: 'Foreign' }
  ]

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Personal Information</h3>

      <FormSection title="Basic Information" subtitle="Your personal details">
        <div className="space-y-3">
          {/* First Names and Last Name side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">First Names</label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName || ''}
                  onChange={handleChange}
                  placeholder="Enter first name"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
                {formData.initials && (
                  <p className="text-xs text-stone-400 mt-1">Initials: {formData.initials}</p>
                )}
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Last Name</label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName || ''}
                  onChange={handleChange}
                  placeholder="Enter last name"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          {/* ID Number and Citizenship side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col">
                <label className="block text-sm font-medium text-stone-300 mb-1">ID Number</label>
                <input
                  type="text"
                  name="idNumber"
                  value={formData.idNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. 8001015009087"
                  required={true}
                  pattern="[0-9]{13}"
                  maxLength={13}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col">
                <label className="block text-sm font-medium text-stone-300 mb-1">Citizenship</label>
                <div className="bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 h-[42px] flex items-center">
                  {citizenTypeOptions.map((option) => (
                    <label key={option.value} className="inline-flex items-center mr-4">
                      <input
                        type="radio"
                        name="citizenType"
                        value={option.value}
                        checked={formData.citizenType === option.value}
                        onChange={() => handleCitizenTypeChange(option.value)}
                        className="h-4 w-4 text-orange-500 border-stone-600 focus:ring-orange-500/50"
                      />
                      <span className="ml-2 text-sm text-stone-300">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </FormSection>

      <FormSection title="Contact Information" subtitle="Your contact details">
        <div className="space-y-3">
          {/* Phone Number and Email Address side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Phone Number</label>
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. ************"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Email Address</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email || ''}
                  onChange={handleChange}
                  placeholder="e.g. <EMAIL>"
                  required={false}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </FormSection>

      <FormSection title="Physical Address" subtitle="Your residential address">
        <AddressInput
          label="Physical Address"
          value={formData.physicalAddress || ''}
          postalCode={formData.postalCode || ''}
          onChange={(address, postalCode) =>
            handleAddressChange(address, postalCode)
          }
          placeholder="Enter your full physical address"
          isTextarea={false}
          required={true}
          postalCodeRequired={true}
        />
        <p className="text-xs text-stone-400 mt-1 mb-3">
          Enter your full physical address and postal code.
        </p>

        <div className="mt-2">
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">House Type</label>
                <select
                  name="houseType"
                  value={formData.houseType || ''}
                  onChange={handleChange}
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                >
                  <option value="">Select house type</option>
                  {HOUSE_TYPE_OPTIONS.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>
      </FormSection>

      {/* Hidden field for initials */}
      <input type="hidden" name="initials" value={formData.initials || ''} />
    </div>
  )
}

export default PersonalInfo
