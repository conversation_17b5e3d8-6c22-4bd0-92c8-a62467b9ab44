import React, { ErrorInfo, ReactNode } from 'react';
import ErrorBoundary from './ErrorBoundary';

interface FormErrorBoundaryProps {
  children: ReactNode;
  formName?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  onReset?: () => void;
  resetKeys?: any[];
}

/**
 * An error boundary specifically designed for wrapping form components.
 * Provides a form-specific error UI and reset functionality.
 */
const FormErrorBoundary: React.FC<FormErrorBoundaryProps> = ({ 
  children, 
  formName = 'this form',
  onError,
  onReset,
  resetKeys
}) => {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    // Log the error
    console.error(`Error in ${formName}:`, error, errorInfo);
    
    // Call the onError callback if provided
    if (onError) {
      onError(error, errorInfo);
    }
  };

  const handleReset = () => {
    if (onReset) {
      onReset();
    }
  };

  const FormErrorFallback = () => (
    <div className="p-6 rounded-lg bg-red-500/10 border border-red-500/30 text-white">
      <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mb-4 mx-auto">
        <svg
          className="w-8 h-8 text-red-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </div>
      <h3 className="text-xl font-semibold text-white mb-2 text-center">Form Error</h3>
      <p className="text-stone-300 text-center mb-4">
        We encountered an error while loading {formName}. 
        You can try again or contact support if the problem persists.
      </p>
      <div className="flex justify-center">
        <button
          onClick={handleReset}
          className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    </div>
  );

  return (
    <ErrorBoundary 
      fallback={<FormErrorFallback />} 
      onError={handleError}
      resetKeys={resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

export default FormErrorBoundary;
