import { Loan, LoanPayment } from '../types';
import { formatCurrency } from './formatters';

// Interfaces for payment tracking
export interface MonthlyPaymentPeriod {
  month: Date;            // Month period (28th to 28th)
  description: string;    // Month name/year for display
  isPast: boolean;        // Whether this period is in the past
  isCurrentMonth: boolean; // Whether this is the current month
  isGracePeriod: boolean; // Whether this is the initial grace period
}

export interface MonthlyPaymentStatus extends MonthlyPaymentPeriod {
  required: number;       // Required payment amount for this month
  paid: number;           // Amount already paid toward this month
  remaining: number;      // Amount still needed for this month
  status: 'paid' | 'partial' | 'unpaid' | 'grace' | 'future' | 'current';
}

export interface PaymentAllocation {
  month: Date;
  description: string;
  amount: number;
  isPastDue: boolean;
}

/**
 * Generate monthly payment periods from loan start date
 */
export function generateMonthlyPaymentPeriods(loan: Loan): MonthlyPaymentPeriod[] {
  const periods: MonthlyPaymentPeriod[] = [];
  const startDate = new Date(loan.start_date);
  const currentDate = new Date();
  
  // Generate monthly periods from loan start to current date (or loan term)
  let currentPeriod = new Date(startDate);
  currentPeriod.setDate(28);  // All payments due on 28th
  
  const endDate = new Date();
  // Ensure we have at least loan_term months
  if (loan.loan_term) {
    const loanEndDate = new Date(startDate);
    loanEndDate.setMonth(startDate.getMonth() + loan.loan_term);
    if (loanEndDate > endDate) {
      endDate.setTime(loanEndDate.getTime());
    }
  }
  
  // Add each month as a payment period
  while (currentPeriod <= endDate) {
    const description = currentPeriod.toLocaleString('default', { month: 'long', year: 'numeric' });
    const isPast = currentPeriod < currentDate;
    const isCurrentMonth = 
      currentPeriod.getMonth() === currentDate.getMonth() && 
      currentPeriod.getFullYear() === currentDate.getFullYear();
    const isGracePeriod = periods.length === 0; // First month is grace period
    
    periods.push({
      month: new Date(currentPeriod),
      description,
      isPast,
      isCurrentMonth,
      isGracePeriod
    });
    
    // Move to next month
    currentPeriod.setMonth(currentPeriod.getMonth() + 1);
  }
  
  return periods;
}

/**
 * Calculate payment status for each month
 */
export function getMonthlyPaymentStatuses(loan: Loan, payments: LoanPayment[]): MonthlyPaymentStatus[] {
  const periods = generateMonthlyPaymentPeriods(loan);
  const monthlyPayment = Math.ceil(loan.loan_amount / (loan.loan_term || 12));
  
  // Initialize payment statuses
  const statuses: MonthlyPaymentStatus[] = periods.map(period => ({
    ...period,
    required: period.isGracePeriod ? 0 : monthlyPayment,
    paid: 0,
    remaining: period.isGracePeriod ? 0 : monthlyPayment,
    status: period.isGracePeriod ? 'grace' : 
           (period.isPast ? 'unpaid' : 
            (period.isCurrentMonth ? 'current' : 'future'))
  }));
  
  // Sort payments by date (oldest first)
  const sortedPayments = [...payments].sort((a, b) => 
    new Date(a.payment_date).getTime() - new Date(b.payment_date).getTime()
  );
  
  // Distribute paid amounts to months (FIFO)
  for (const payment of sortedPayments) {
    let remainingAmount = payment.amount;
    
    // Apply to each month in sequence until payment is fully allocated
    for (const status of statuses) {
      if (remainingAmount <= 0) break;
      if (status.required === 0) continue; // Skip grace period
      
      const neededForMonth = status.required - status.paid;
      if (neededForMonth <= 0) continue; // Skip if month is fully paid
      
      const amountToAllocate = Math.min(remainingAmount, neededForMonth);
      status.paid += amountToAllocate;
      status.remaining = status.required - status.paid;
      remainingAmount -= amountToAllocate;
      
      // Update status
      if (status.paid >= status.required) {
        status.status = 'paid';
      } else if (status.paid > 0) {
        status.status = 'partial';
      }
    }
  }
  
  return statuses;
}

/**
 * Calculate payment allocation for a new payment amount
 */
export function allocatePaymentToMonths(
  paymentAmount: number, 
  monthStatuses: MonthlyPaymentStatus[]
): {monthAllocations: PaymentAllocation[]} {
  
  const allocations: PaymentAllocation[] = [];
  let remainingAmount = paymentAmount;
  
  // Only consider months that need payment
  const monthsNeedingPayment = monthStatuses.filter(
    month => month.remaining > 0 && month.status !== 'grace' && month.status !== 'future'
  );
  
  // First, allocate to past due months
  const pastDueMonths = monthsNeedingPayment.filter(
    month => month.isPast && !month.isCurrentMonth
  );
  
  for (const month of pastDueMonths) {
    if (remainingAmount <= 0) break;
    
    const amountToAllocate = Math.min(remainingAmount, month.remaining);
    if (amountToAllocate > 0) {
      allocations.push({
        month: month.month,
        description: month.description,
        amount: amountToAllocate,
        isPastDue: true
      });
      
      remainingAmount -= amountToAllocate;
    }
  }
  
  // Then allocate to current month
  const currentMonth = monthsNeedingPayment.find(month => month.isCurrentMonth);
  if (currentMonth && remainingAmount > 0) {
    const amountToAllocate = Math.min(remainingAmount, currentMonth.remaining);
    if (amountToAllocate > 0) {
      allocations.push({
        month: currentMonth.month,
        description: currentMonth.description,
        amount: amountToAllocate,
        isPastDue: false
      });
      
      remainingAmount -= amountToAllocate;
    }
  }
  
  // If there's still money left, allocate to future months
  if (remainingAmount > 0) {
    const futureMonths = monthStatuses.filter(
      month => month.status === 'future' && month.remaining > 0
    );
    
    for (const month of futureMonths) {
      if (remainingAmount <= 0) break;
      
      const amountToAllocate = Math.min(remainingAmount, month.remaining);
      if (amountToAllocate > 0) {
        allocations.push({
          month: month.month,
          description: month.description,
          amount: amountToAllocate,
          isPastDue: false
        });
        
        remainingAmount -= amountToAllocate;
      }
    }
  }
  
  return { monthAllocations: allocations };
}

/**
 * Generate a human-readable note about payment allocation
 */
export function getPaymentAllocationNotes(allocations: PaymentAllocation[]): string {
  if (!allocations.length) return "";
  
  const allocationDetails = allocations.map(allocation => 
    `${allocation.description}: ${formatCurrency(allocation.amount)}${allocation.isPastDue ? ' (missed)' : ''}`
  ).join(", ");
  
  return `Payment allocated to: ${allocationDetails}`;
} 