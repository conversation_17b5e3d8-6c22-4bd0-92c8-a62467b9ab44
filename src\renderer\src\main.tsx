import './assets/main.css'
import './assets/index.css'
import './styles/focus-mode.css'

import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import { ErrorBoundary } from './components/ErrorBoundary'

// Global error handler for uncaught errors
const GlobalErrorFallback = () => (
  <div className="flex flex-col items-center justify-center h-screen bg-stone-900 p-8">
    <div className="w-24 h-24 text-red-500 mb-6">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
    </div>
    <h1 className="text-3xl font-bold text-white mb-4">Application Error</h1>
    <p className="text-stone-400 text-center mb-8 max-w-md">
      The application encountered a critical error and needs to be restarted.
    </p>
    <button
      onClick={() => window.location.reload()}
      className="px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors flex items-center"
    >
      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
      Restart Application
    </button>
  </div>
);

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <ErrorBoundary fallback={<GlobalErrorFallback />}>
      <App />
    </ErrorBoundary>
  </React.StrictMode>
)
