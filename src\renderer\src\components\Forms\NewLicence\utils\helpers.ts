/**
 * Helper functions for the NewLicence form
 */

import { NewLicenceData } from '../../../../types/NewLicenceData';

const markWithXPlaceholders = [
  { field: 'mainHF', placeholder: '{MainHF}' },
  { field: 'addHF', placeholder: '{AddHF}' },
  { field: 's13', placeholder: '{S13}' },
  { field: 's15', placeholder: '{S15}' },
  { field: 's16', placeholder: '{S16}' },
  { field: 's20', placeholder: '{S20}' },
  { field: 's20a', placeholder: '{S20A}' },
  { field: 's20b', placeholder: '{S20B}' },
  { field: 's20c', placeholder: '{S20C}' },
  { field: 'rifle', placeholder: '{Rifle}' },
  { field: 'shotgun', placeholder: '{Shotgun}' },
  { field: 'pistol', placeholder: '{Pistol}' },
  { field: 'comb', placeholder: '{Comb}' },
  { field: 'otherDesign', placeholder: '{OtherDesign}' },
  { field: 'semi', placeholder: '{Semi}' },
  { field: 'auto', placeholder: '{Auto}' },
  { field: 'man', placeholder: '{Man}' }
];

export function applyMarkWithXPlaceholders(template: string, formData: NewLicenceData): string {
  let result = template;
  for (const { field, placeholder } of markWithXPlaceholders) {
    result = result.replaceAll(placeholder, formData[field] ? 'X' : '');
  }
  return result;
}


/**
 * Extracts information from a South African ID number
 * @param idNumber - 13-digit South African ID number
 * @returns Object with extracted information or null if invalid
 */
export const extractFromIdNumber = (idNumber: string) => {
  if (!/^\d{13}$/.test(idNumber)) {
    return null
  }

  try {
    // Extract date components
    const year = idNumber.substring(0, 2)
    const month = idNumber.substring(2, 4)
    const day = idNumber.substring(4, 6)

    // Extract gender - 7th digit (index 6)
    const genderDigit = parseInt(idNumber.substring(6, 7))
    const isMale = genderDigit >= 5
    const isFemale = genderDigit < 5

    // Determine century
    const currentYear = new Date().getFullYear()
    const currentCentury = Math.floor(currentYear / 100) * 100
    const previousCentury = currentCentury - 100

    // If year is greater than current year's last 2 digits, assume previous century
    const fullYear =
      parseInt(year) > parseInt(currentYear.toString().substring(2, 4))
        ? previousCentury + parseInt(year)
        : currentCentury + parseInt(year)

    // Format date as YYYY-MM-DD
    const birthDate = `${fullYear}-${month}-${day}`

    // Calculate age
    const today = new Date()
    const birthDateTime = new Date(birthDate)
    let age = today.getFullYear() - birthDateTime.getFullYear()

    // Adjust age if birthday hasn't occurred yet this year
    const monthDiff = today.getMonth() - birthDateTime.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateTime.getDate())) {
      age--
    }

    return {
      birthDate,
      age: age.toString(),
      npsexmale: isMale,
      npsexfemale: isFemale
    }
  } catch (error) {
    console.error('Error parsing ID number:', error)
    return null
  }
}

/**
 * Generates initials from a full name
 * @param fullName - Full name to generate initials from
 * @returns Initials string
 */
export const generateInitials = (fullName: string): string => {
  if (!fullName) return ''
  
  return fullName
    .split(' ')
    .map((namePart) => namePart.charAt(0).toUpperCase())
    .join(' ')
}
