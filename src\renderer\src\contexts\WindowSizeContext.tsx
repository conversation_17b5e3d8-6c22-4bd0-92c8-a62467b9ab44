import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface WindowSizeContextType {
  width: number
  height: number
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  calculateResponsiveHeight: (percentage: number) => number
  calculateResponsiveWidth: (percentage: number) => number
}

const defaultContext: WindowSizeContextType = {
  width: window.innerWidth,
  height: window.innerHeight,
  isMobile: window.innerWidth < 640,
  isTablet: window.innerWidth >= 640 && window.innerWidth < 1024,
  isDesktop: window.innerWidth >= 1024,
  calculateResponsiveHeight: (percentage) => (window.innerHeight * percentage) / 100,
  calculateResponsiveWidth: (percentage) => (window.innerWidth * percentage) / 100
}

const WindowSizeContext = createContext<WindowSizeContextType>(defaultContext)

export const useWindowSize = () => useContext(WindowSizeContext)

interface WindowSizeProviderProps {
  children: ReactNode
}

export const WindowSizeProvider: React.FC<WindowSizeProviderProps> = ({ children }) => {
  const [windowSize, setWindowSize] = useState<{
    width: number
    height: number
    isMobile: boolean
    isTablet: boolean
    isDesktop: boolean
  }>({
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile: window.innerWidth < 640,
    isTablet: window.innerWidth >= 640 && window.innerWidth < 1024,
    isDesktop: window.innerWidth >= 1024
  })

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setWindowSize({
        width,
        height,
        isMobile: width < 640,
        isTablet: width >= 640 && width < 1024,
        isDesktop: width >= 1024
      })
    }

    // Set initial size
    handleResize()

    // Add event listener
    window.addEventListener('resize', handleResize)

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const calculateResponsiveHeight = (percentage: number) => {
    return (windowSize.height * percentage) / 100
  }

  const calculateResponsiveWidth = (percentage: number) => {
    return (windowSize.width * percentage) / 100
  }

  const value = {
    ...windowSize,
    calculateResponsiveHeight,
    calculateResponsiveWidth
  }

  return (
    <WindowSizeContext.Provider value={value}>
      {children}
    </WindowSizeContext.Provider>
  )
}

export default WindowSizeProvider
