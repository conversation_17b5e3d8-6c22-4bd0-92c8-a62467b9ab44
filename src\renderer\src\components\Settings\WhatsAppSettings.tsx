import React from 'react'
import { Download, RefreshCw, Check, AlertCircle, Plus, List, Trash, Clock, Activity, ToggleLeft, ToggleRight, MessageSquare } from 'lucide-react'
import { useWhatsAppSettings } from './hooks/useWhatsAppSettings'

interface WhatsAppSettingsProps {
  activeSection: string
}

const WhatsAppSettings: React.FC<WhatsAppSettingsProps> = ({ activeSection }) => {
  const {
    whatsappSessionId,
    setWhatsappSessionId,
    whatsappQrCode,
    whatsappStatus,
    isSendingTestMessage,
    whatsappError,
    testMessageForm,
    setTestMessageForm,
    whatsappSuccess,
    isCheckingServer,
    serverStatus,
    availableSessionIds,
    availableSessions,
    getWhatsappQrCode,
    checkWhatsappSessionStatus,
    restartWhatsappSession,
    sendWhatsappTestMessage,
    checkServerAlive,
    // Auto-check server status
    autoCheckEnabled,
    setAutoCheckEnabled,
    // Confirmation dialog
    showConfirmDialog,
    setShowConfirmDialog,
    confirmMessage,
    confirmAction,
    // New session management functions
    newSessionId,
    setNewSessionId,
    isCreatingSession,
    isLoadingSessions,
    createWhatsAppSession,
    getAllWhatsAppSessions,
    initiateTerminateSession
  } = useWhatsAppSettings(activeSection)

  // Format date for display
  const formatDate = (date: Date | undefined) => {
    if (!date) return 'N/A';
    return date.toLocaleString();
  }

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-white border-b border-stone-700/50 pb-1 mb-4">
        WhatsApp Integration
      </h2>

      {/* Server Status */}
      <div className="flex flex-col space-y-1 mb-4">
        <div className="flex justify-between items-center">
          <label className="text-stone-300 font-medium text-sm">API Server Status</label>
          <div className="flex items-center gap-2">
            <span className="text-xs text-stone-400">Auto-check:</span>
            <button
              onClick={() => setAutoCheckEnabled(!autoCheckEnabled)}
              className="text-xs flex items-center gap-1"
            >
              {autoCheckEnabled ? (
                <>
                  <ToggleRight size={16} className="text-green-500" />
                  <span className="text-green-400">ON</span>
                </>
              ) : (
                <>
                  <ToggleLeft size={16} className="text-stone-500" />
                  <span className="text-stone-400">OFF</span>
                </>
              )}
            </button>
          </div>
        </div>

        <div className="bg-stone-800 rounded-md p-3 mt-1">
          <div className="flex items-center gap-2 mb-2">
            <div
              className={`w-4 h-4 rounded-full ${
                serverStatus?.alive
                  ? 'bg-green-500'
                  : serverStatus === null
                    ? 'bg-stone-500'
                    : 'bg-red-500'
              } flex items-center justify-center`}
            >
              {serverStatus?.alive && <Check size={10} className="text-white" />}
            </div>

            <span className="text-sm font-medium text-white">
              {serverStatus?.alive
                ? 'Server Online'
                : serverStatus === null
                  ? 'Checking...'
                  : 'Server Offline'}
            </span>

            {serverStatus?.responseTime && serverStatus.alive && (
              <div className="flex items-center gap-1 ml-2 px-2 py-0.5 bg-stone-700 rounded-full">
                <Activity size={10} className="text-green-400" />
                <span className={`text-xs ${
                  serverStatus.responseTime < 300
                    ? 'text-green-400'
                    : serverStatus.responseTime < 1000
                      ? 'text-yellow-400'
                      : 'text-red-400'
                }`}>
                  {serverStatus.responseTime}ms
                </span>
              </div>
            )}

            <button
              onClick={checkServerAlive}
              disabled={isCheckingServer}
              className={`ml-auto px-2 py-1 rounded-md text-xs ${
                isCheckingServer
                  ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
                  : 'bg-stone-700 hover:bg-stone-600 text-stone-300 hover:text-white'
              } transition-colors flex items-center gap-1`}
            >
              {isCheckingServer ? (
                <>
                  <RefreshCw className="animate-spin" size={10} />
                  <span>Checking...</span>
                </>
              ) : (
                <>
                  <RefreshCw size={10} />
                  <span>Check Now</span>
                </>
              )}
            </button>
          </div>

          {/* Server details */}
          <div className="grid grid-cols-2 gap-2 text-xs mt-2">
            <div className="flex items-center gap-1 text-stone-400">
              <Clock size={10} />
              <span>Last checked:</span>
              <span className="text-stone-300">
                {serverStatus?.lastChecked
                  ? new Date(serverStatus.lastChecked).toLocaleTimeString()
                  : 'Never'}
              </span>
            </div>

            {serverStatus?.message && serverStatus.alive && (
              <div className="flex items-center gap-1 text-green-400">
                <Check size={10} />
                <span>{serverStatus.message}</span>
              </div>
            )}

            {serverStatus?.error && !serverStatus.alive && (
              <div className="flex items-center gap-1 text-red-400 col-span-2">
                <AlertCircle size={10} />
                <span>{serverStatus.error}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Session Management */}
      <div className="flex flex-col space-y-1 mb-4">
        <div className="flex justify-between items-center">
          <label className="text-stone-300 font-medium text-sm">Session Management</label>
          <button
            onClick={getAllWhatsAppSessions}
            disabled={isLoadingSessions}
            className={`px-2 py-1 rounded-md text-xs ${
              isLoadingSessions
                ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-500 text-white'
            } transition-colors flex items-center gap-1 flex-shrink-0`}
          >
            {isLoadingSessions ? (
              <>
                <RefreshCw className="animate-spin" size={12} />
                <span>Refreshing...</span>
              </>
            ) : (
              <>
                <RefreshCw size={12} />
                <span>Refresh Sessions</span>
              </>
            )}
          </button>
        </div>

        <p className="text-stone-400 text-xs">
          Create a new WhatsApp session or select an existing one.
          {availableSessionIds.length > 0 && (
            <span className="text-green-400 ml-1">({availableSessionIds.length} sessions available)</span>
          )}
        </p>

        {/* Create New Session */}
        <div className="bg-stone-800 rounded-md p-3 mt-2">
          <h3 className="text-sm font-medium text-white mb-2 flex items-center gap-1">
            <Plus size={14} className="text-green-500" />
            Create New Session
          </h3>

          <div className="flex gap-2">
            <input
              type="text"
              value={newSessionId}
              onChange={(e) => setNewSessionId(e.target.value)}
              placeholder="Enter new session ID (e.g., 'gunnery', 'firearmstudio')"
              className="flex-1 bg-stone-900 border border-stone-700 rounded-md px-2 py-1.5 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
            />
            <button
              onClick={createWhatsAppSession}
              disabled={isCreatingSession || !newSessionId}
              className={`px-3 py-1.5 rounded-md text-sm ${
                isCreatingSession || !newSessionId
                  ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-500 text-white'
              } transition-colors flex items-center gap-1 flex-shrink-0`}
            >
              {isCreatingSession ? (
                <>
                  <RefreshCw className="animate-spin" size={14} />
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <Plus size={14} />
                  <span>Create</span>
                </>
              )}
            </button>
          </div>

          <p className="text-xs text-stone-400 mt-1">
            Choose a meaningful name for your session. This will be used to identify the WhatsApp connection.
          </p>
        </div>

        {/* Available Sessions */}
        <div className="mt-3">
          <h3 className="text-sm font-medium text-white mb-2 flex items-center gap-1">
            <List size={14} className="text-blue-500" />
            Available Sessions
          </h3>

          {availableSessionIds.length === 0 && !isLoadingSessions ? (
            <div className="px-3 py-2 bg-stone-800 rounded-md text-stone-400 text-sm flex items-center gap-2">
              <AlertCircle size={14} className="text-amber-500" />
              <span>No sessions found. Please create a new session using the form above.</span>
            </div>
          ) : (
            <div className="bg-stone-800 rounded-md overflow-hidden">
              {availableSessions.map((session) => (
                <div
                  key={session.id}
                  className={`border-b border-stone-700/30 last:border-b-0 p-3 ${
                    whatsappSessionId === session.id ? 'bg-stone-700/30' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          session.status === 'CONNECTED'
                            ? 'bg-green-500'
                            : session.status === 'CONNECTING'
                              ? 'bg-yellow-500'
                              : 'bg-stone-500'
                        }`}
                      ></div>
                      <span className="text-sm font-medium text-white">{session.id}</span>
                      {session.isDefault && (
                        <span className="text-xs bg-blue-500/20 text-blue-400 px-1.5 py-0.5 rounded">Default</span>
                      )}
                    </div>

                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => {
                          setWhatsappSessionId(session.id);
                          checkWhatsappSessionStatus();
                        }}
                        className="px-2 py-1 bg-stone-700 hover:bg-stone-600 text-stone-300 hover:text-white rounded text-xs transition-colors"
                      >
                        Select
                      </button>

                      {/* Only show QR code button if session is not connected */}
                      {session.status !== 'CONNECTED' && (
                        <button
                          onClick={() => {
                            setWhatsappSessionId(session.id);
                            getWhatsappQrCode();
                          }}
                          className="p-1 text-orange-400 hover:text-orange-300 rounded transition-colors"
                          title="Get QR Code"
                        >
                          <Download size={14} />
                        </button>
                      )}

                      <button
                        onClick={() => {
                          setWhatsappSessionId(session.id);
                          restartWhatsappSession();
                        }}
                        className="p-1 text-blue-400 hover:text-blue-300 rounded transition-colors"
                        title="Restart Session"
                      >
                        <RefreshCw size={14} />
                      </button>

                      <button
                        onClick={() => initiateTerminateSession(session.id)}
                        className="p-1 text-red-400 hover:text-red-300 rounded transition-colors"
                        title="Terminate Session"
                      >
                        <Trash size={14} />
                      </button>
                    </div>
                  </div>

                  {session.lastActive && (
                    <div className="mt-1 text-xs text-stone-400 flex items-center gap-1">
                      <Clock size={10} />
                      <span>Last active: {formatDate(session.lastActive)}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {whatsappError && whatsappError.includes('Failed to retrieve sessions') && (
            <div className="mt-2 px-3 py-2 bg-amber-500/20 border border-amber-500/30 rounded-md text-amber-400 flex items-center gap-1 text-sm">
              <AlertCircle size={14} />
              <span>
                {whatsappError}
                {' Please create a new session using the form above.'}
              </span>
            </div>
          )}
        </div>
      </div>



      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-stone-800 rounded-lg p-4 max-w-md w-full mx-4">
            <h3 className="text-white font-medium mb-2 flex items-center gap-2">
              <AlertCircle size={18} className="text-amber-500" />
              Confirm Action
            </h3>
            <p className="text-stone-300 text-sm mb-4">{confirmMessage}</p>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowConfirmDialog(false)}
                className="px-3 py-1.5 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  confirmAction();
                }}
                className="px-3 py-1.5 bg-red-600 hover:bg-red-500 text-white rounded-md text-sm transition-colors"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}

      {/* QR Code Display */}
      {whatsappQrCode && (
        <div className="flex flex-col items-center space-y-2 bg-stone-800 p-4 rounded-lg border border-stone-700">
          <div className="bg-white p-4 rounded-lg">
            <p className="text-black font-medium text-sm mb-2 text-center">Scan this QR code with your WhatsApp</p>
            <img src={whatsappQrCode} alt="WhatsApp QR Code" className="w-48 h-48 object-contain" />
          </div>
          <p className="text-stone-400 text-xs text-center">
            Open WhatsApp on your phone, tap Menu or Settings and select WhatsApp Web.<br />
            Point your phone to this screen to capture the code.
          </p>
        </div>
      )}

      {/* Error/Success Messages */}
      {whatsappError && !whatsappError.includes('Failed to retrieve sessions') && (
        <div className="px-3 py-2 bg-red-500/20 border border-red-500/30 rounded-md text-red-400 flex items-center gap-1 text-sm">
          <AlertCircle size={14} />
          <span>{whatsappError}</span>
        </div>
      )}

      {whatsappSuccess && (
        <div className="px-3 py-2 bg-green-500/20 border border-green-500/30 rounded-md text-green-400 flex items-center gap-1 text-sm">
          <Check size={14} />
          <span>{whatsappSuccess}</span>
        </div>
      )}



      {/* Test Message Form - Only show when connected */}
      {whatsappStatus === 'CONNECTED' && (
        <div className="mt-4 border-t border-stone-700/30 pt-4">
          <div className="flex flex-col space-y-2">
            <div className="flex justify-between items-center">
              <h3 className="text-md font-medium text-white flex items-center gap-2">
                <MessageSquare size={16} className="text-green-500" />
                Send Test Message
              </h3>
              <div className="text-xs bg-stone-700 px-2 py-1 rounded-md text-stone-300 flex items-center gap-1">
                <span>Using session:</span>
                <span className="text-orange-400 font-medium">{whatsappSessionId}</span>
              </div>
            </div>

            <div className="space-y-1 mb-2">
              <label className="text-stone-300 text-xs">Recipient Phone Number</label>
              <input
                type="text"
                value={testMessageForm.recipient}
                onChange={(e) =>
                  setTestMessageForm((prev) => ({ ...prev, recipient: e.target.value }))
                }
                placeholder="e.g., 0681501196 or 27681501196"
                className="w-full bg-stone-900 border border-stone-700 rounded-md px-2 py-1.5 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
              />
              <p className="text-xs text-stone-400">
                Enter South African phone number with or without leading 0 (country code 27 will be
                added automatically if missing)
              </p>
            </div>

            <div className="space-y-1 mb-2">
              <label className="text-stone-300 text-xs">Message</label>
              <textarea
                value={testMessageForm.message}
                onChange={(e) =>
                  setTestMessageForm((prev) => ({ ...prev, message: e.target.value }))
                }
                placeholder="Enter your test message"
                className="w-full bg-stone-900 border border-stone-700 rounded-md px-2 py-1.5 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 min-h-[60px]"
              />
            </div>

            <button
              onClick={sendWhatsappTestMessage}
              disabled={
                isSendingTestMessage || !testMessageForm.recipient || !testMessageForm.message
              }
              className={`px-3 py-1 rounded-md text-sm ${
                isSendingTestMessage || !testMessageForm.recipient || !testMessageForm.message
                  ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-500 text-white'
              } transition-colors flex items-center gap-1 w-fit`}
            >
              {isSendingTestMessage ? (
                <>
                  <RefreshCw className="animate-spin" size={14} />
                  <span>Sending...</span>
                </>
              ) : (
                <>
                  <Check size={14} />
                  <span>Send Test Message</span>
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default WhatsAppSettings
