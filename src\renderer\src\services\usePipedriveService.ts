import { useState, useEffect, useCallback } from 'react';
import { getOrInitSupabase } from '../lib/supabase';
import { Pipeline, PipelineStage, Deal } from '../types/pipedrive';
import { v4 as uuidv4 } from 'uuid';
import { logPipedriveAction } from '../utils/pipedriveLogger';

// Cache for document URLs to prevent redundant signed URL generation
const documentUrlCache: Record<string, { url: string, expiry: number }> = {};

export const usePipedriveService = () => {
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [stages, setStages] = useState<PipelineStage[]>([]);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activePipeline, setActivePipeline] = useState<string | null>(null);

  // Fetch pipelines
  const fetchPipelines = useCallback(async () => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();
      const { data, error } = await supabase
        .from('pipelines')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      } else {
        setPipelines(data || []);

        // Set active pipeline to the first one if none is selected
        if (data && data.length > 0 && !activePipeline) {
          setActivePipeline(data[0].id);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [activePipeline]);

  // Fetch stages for active pipeline
  const fetchStages = useCallback(async () => {
    if (!activePipeline) return;

    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();
      const { data, error } = await supabase
        .from('pipeline_stages')
        .select('*')
        .eq('pipeline_id', activePipeline)
        .order('position', { ascending: true });

      if (error) {
        throw error;
      } else {
        setStages(data || []);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [activePipeline]);

  // Fetch deals for active pipeline
  const fetchDeals = useCallback(async () => {
    if (!activePipeline || stages.length === 0) return;

    try {
      setLoading(true);

      // Get stage IDs for the active pipeline
      const stageIds = stages.map(stage => stage.id);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Fetch deals with comprehensive client information
      const { data, error } = await supabase
        .from('deals')
        .select(`
          *,
          client:clients(id, first_name, last_name, phone, email, address, city, state, postal_code, id_number)
        `)
        .in('stage_id', stageIds);

      if (error) {
        throw error;
      } else {
        setDeals(data || []);
      }
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [activePipeline, stages]);

  // Create a new pipeline
  const createPipeline = async (name: string, description: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      const { data, error } = await supabase
        .from('pipelines')
        .insert({
          name,
          description
        })
        .select()
        .single();

      if (error) {
        throw error;
      } else {
        // Add default stages
        if (data) {
          const defaultStages = [
            { name: 'Lead', position: 1 },
            { name: 'Contact Made', position: 2 },
            { name: 'Proposal', position: 3 },
            { name: 'Negotiation', position: 4 },
            { name: 'Won', position: 5 }
          ];

          // Log the pipeline creation
          await logPipedriveAction(
            'create',
            'pipeline',
            data.id,
            {
              details: { name, description },
              status: 'success'
            }
          );

          for (const stage of defaultStages) {
            const { data: stageData } = await supabase
              .from('pipeline_stages')
              .insert({
                pipeline_id: data.id,
                name: stage.name,
                position: stage.position
              })
              .select()
              .single();

            // Log each stage creation
            if (stageData) {
              await logPipedriveAction(
                'create',
                'stage',
                stageData.id,
                {
                  details: { name: stage.name, position: stage.position },
                  status: 'success',
                  relatedEntities: [
                    { type: 'pipeline', id: data.id, name }
                  ]
                }
              );
            }
          }

          // Refresh pipelines
          await fetchPipelines();
          setActivePipeline(data.id);
        }
      }
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Create a new stage
  const createStage = async (name: string, description: string | null = null) => {
    if (!activePipeline) return;

    try {
      setLoading(true);

      // Get the highest position
      const highestPosition = stages.length > 0
        ? Math.max(...stages.map(s => s.position))
        : 0;

      // Use real database
      const supabase = await getOrInitSupabase();

      const { data, error } = await supabase
        .from('pipeline_stages')
        .insert({
          pipeline_id: activePipeline,
          name,
          description,
          position: highestPosition + 1
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log the stage creation
      if (data) {
        await logPipedriveAction(
          'create',
          'stage',
          data.id,
          {
            details: {
              name,
              description,
              pipeline_id: activePipeline,
              position: highestPosition + 1
            },
            status: 'success'
          }
        );
      }

      // Refresh stages
      await fetchStages();
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Update an existing stage
  const updateStage = async (stageId: string, name: string, description: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Get the current stage data for logging
      const { data: currentStage } = await supabase
        .from('pipeline_stages')
        .select('*')
        .eq('id', stageId)
        .single();

      const { error } = await supabase
        .from('pipeline_stages')
        .update({
          name,
          description,
          updated_at: new Date().toISOString()
        })
        .eq('id', stageId);

      if (error) {
        throw error;
      }

      // Log the stage update
      if (currentStage) {
        await logPipedriveAction(
          'update',
          'stage',
          stageId,
          {
            details: {
              old: currentStage,
              new: { name, description }
            },
            status: 'success'
          }
        );
      }

      // Refresh stages
      await fetchStages();
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a stage
  const deleteStage = async (stageId: string) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Get stage data for logging
      const { data: stageData } = await supabase
        .from('pipeline_stages')
        .select('*')
        .eq('id', stageId)
        .single();

      // First, check if there are any deals in this stage
      const { data: dealsData, error: dealsError } = await supabase
        .from('deals')
        .select('*')
        .eq('stage_id', stageId);

      if (dealsError) {
        throw dealsError;
      }

      // If there are deals, delete them first
      if (dealsData && dealsData.length > 0) {
        const dealIds = dealsData.map(deal => deal.id);

        // Log each deal deletion
        for (const deal of dealsData) {
          await logPipedriveAction(
            'delete',
            'deal',
            deal.id,
            {
              details: { deal },
              status: 'success'
            }
          );
        }

        // Delete all deal documents
        for (const dealId of dealIds) {
          // Get documents for this deal
          const { data: documentsData } = await supabase
            .from('deal_documents')
            .select('*')
            .eq('deal_id', dealId);

          if (documentsData && documentsData.length > 0) {
            // Log each document deletion
            for (const doc of documentsData) {
              await logPipedriveAction(
                'delete',
                'document',
                doc.id,
                {
                  details: { document: doc },
                  status: 'success'
                }
              );

              // Delete files from storage
              await supabase.storage
                .from('deal_documents')
                .remove([doc.file_path]);
            }

            // Delete document records
            await supabase
              .from('deal_documents')
              .delete()
              .eq('deal_id', dealId);
          }
        }

        // Delete all deals in this stage
        const { error: deleteDealsError } = await supabase
          .from('deals')
          .delete()
          .eq('stage_id', stageId);

        if (deleteDealsError) {
          throw deleteDealsError;
        }
      }

      // Finally delete the stage
      const { error } = await supabase
        .from('pipeline_stages')
        .delete()
        .eq('id', stageId);

      if (error) {
        throw error;
      }

      // Log the stage deletion
      if (stageData) {
        await logPipedriveAction(
          'delete',
          'stage',
          stageId,
          {
            details: { stage: stageData },
            status: 'success'
          }
        );
      }

      // Refresh stages
      await fetchStages();
      // Also refresh deals since we might have deleted some
      await fetchDeals();
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Create a new deal
  const createDeal = async (stageId: string, clientId: string, title: string, notes: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Create the deal with current timestamp
      const now = new Date().toISOString();
      const { data, error } = await supabase
        .from('deals')
        .insert({
          stage_id: stageId,
          client_id: clientId,
          title,
          notes,
          created_at: now,
          updated_at: now
        })
        .select('id')
        .single();

      if (error) {
        throw error;
      }

      // Log the deal creation with detailed information
      if (data) {
        // Get stage information
        const { data: stageData, error: stageError } = await supabase
          .from('pipeline_stages')
          .select('name, pipeline_id')
          .eq('id', stageId)
          .single();

        if (stageError) {
          console.warn('Could not fetch stage information:', stageError);
        }

        // Get client information
        const { data: clientData, error: clientError } = await supabase
          .from('clients')
          .select('first_name, last_name, id_number, phone, email')
          .eq('id', clientId)
          .single();

        if (clientError) {
          console.warn('Could not fetch client information:', clientError);
        }

        // Get pipeline information if available
        let pipelineName = null;
        let pipelineId = null;
        if (stageData?.pipeline_id) {
          pipelineId = stageData.pipeline_id;
          const { data: pipelineData, error: pipelineError } = await supabase
            .from('pipelines')
            .select('name')
            .eq('id', stageData.pipeline_id)
            .single();

          if (pipelineError) {
            console.warn('Could not fetch pipeline information:', pipelineError);
          } else if (pipelineData) {
            pipelineName = pipelineData.name;
          }
        }

        // Prepare related entities for the log
        const relatedEntities: Array<{
          type: 'pipeline' | 'stage' | 'deal' | 'document';
          id: string;
          name?: string;
        }> = [];

        // Add stage to related entities
        if (stageData) {
          relatedEntities.push({
            type: 'stage',
            id: stageId,
            name: stageData.name || 'Unknown Stage'
          });
        }

        // Add pipeline to related entities if available
        if (pipelineId) {
          relatedEntities.push({
            type: 'pipeline',
            id: pipelineId,
            name: pipelineName || 'Unknown Pipeline'
          });
        }

        // Format client name if available
        let clientName: string | undefined = undefined;
        if (clientData) {
          clientName = `${clientData.first_name} ${clientData.last_name}`.trim();
        }

        await logPipedriveAction(
          'create',
          'deal',
          data.id,
          {
            details: {
              deal_title: title,
              notes: notes,
              stage: stageData?.name || 'Unknown',
              pipeline: pipelineName || 'Unknown',
              client_details: clientData ? {
                name: clientName,
                id_number: clientData.id_number,
                phone: clientData.phone,
                email: clientData.email
              } : 'Unknown Client',
              created_at: now
            },
            clientId,
            clientName,
            status: 'success',
            relatedEntities
          }
        );
      }

      // Refresh deals
      await fetchDeals();

      // Return the created deal ID
      return data;
    } catch (err) {
      console.error('Error creating deal:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update an existing deal
  const updateDeal = async (dealId: string, title: string, notes: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Get the current deal data for logging
      const { data: currentDeal } = await supabase
        .from('deals')
        .select('*')
        .eq('id', dealId)
        .single();

      const { error } = await supabase
        .from('deals')
        .update({
          title,
          notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', dealId);

      if (error) {
        throw error;
      }

      // Log the deal update
      if (currentDeal) {
        await logPipedriveAction(
          'update',
          'deal',
          dealId,
          {
            details: {
              old: currentDeal,
              new: { title, notes }
            },
            status: 'success'
          }
        );
      }

      // Refresh deals
      await fetchDeals();
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Move a deal to a different stage (within the same pipeline or to another pipeline)
  const moveDeal = async (dealId: string, newStageId: string) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Get the current deal data with client information for logging
      const { data: currentDeal, error: dealError } = await supabase
        .from('deals')
        .select(`
          *,
          client:clients(id, first_name, last_name)
        `)
        .eq('id', dealId)
        .single();

      if (dealError) {
        throw dealError;
      }

      // Get source stage information
      const { data: sourceStage, error: sourceStageError } = await supabase
        .from('pipeline_stages')
        .select('name, pipeline_id')
        .eq('id', currentDeal.stage_id)
        .single();

      if (sourceStageError) {
        console.warn('Could not fetch source stage information:', sourceStageError);
      }

      // Get destination stage information
      const { data: destStage, error: destStageError } = await supabase
        .from('pipeline_stages')
        .select('name, pipeline_id')
        .eq('id', newStageId)
        .single();

      if (destStageError) {
        console.warn('Could not fetch destination stage information:', destStageError);
      }

      // Get source pipeline information
      let sourcePipelineName = null;
      if (sourceStage?.pipeline_id) {
        const { data: pipelineData } = await supabase
          .from('pipelines')
          .select('name')
          .eq('id', sourceStage.pipeline_id)
          .single();

        if (pipelineData) {
          sourcePipelineName = pipelineData.name;
        }
      }

      // Get destination pipeline information
      let destPipelineName = null;
      if (destStage?.pipeline_id) {
        const { data: pipelineData } = await supabase
          .from('pipelines')
          .select('name')
          .eq('id', destStage.pipeline_id)
          .single();

        if (pipelineData) {
          destPipelineName = pipelineData.name;
        }
      }

      // Check if this is a cross-pipeline move
      const isCrossPipelineMove = sourceStage?.pipeline_id !== destStage?.pipeline_id;

      // Update the deal's stage
      const { error } = await supabase
        .from('deals')
        .update({
          stage_id: newStageId,
          updated_at: new Date().toISOString()
        })
        .eq('id', dealId);

      if (error) {
        throw error;
      }

      // Log the deal move with detailed information
      if (currentDeal) {
        // Prepare related entities for the log
        const relatedEntities: Array<{
          type: 'pipeline' | 'stage' | 'deal' | 'document';
          id: string;
          name?: string;
        }> = [
          { type: 'stage', id: currentDeal.stage_id, name: sourceStage?.name || 'Unknown Source Stage' },
          { type: 'stage', id: newStageId, name: destStage?.name || 'Unknown Destination Stage' }
        ];

        // Add source pipeline to related entities if available
        if (sourceStage?.pipeline_id) {
          relatedEntities.push({
            type: 'pipeline',
            id: sourceStage.pipeline_id,
            name: sourcePipelineName || 'Unknown Source Pipeline'
          });
        }

        // Add destination pipeline to related entities if it's different from source
        if (destStage?.pipeline_id && isCrossPipelineMove) {
          relatedEntities.push({
            type: 'pipeline',
            id: destStage.pipeline_id,
            name: destPipelineName || 'Unknown Destination Pipeline'
          });
        }

        // Format client name if available
        let clientName: string | undefined = undefined;
        if (currentDeal.client) {
          clientName = `${currentDeal.client.first_name} ${currentDeal.client.last_name}`.trim();
        }

        await logPipedriveAction(
          'move',
          'deal',
          dealId,
          {
            details: {
              deal_title: currentDeal.title,
              from_stage: sourceStage?.name || 'Unknown',
              to_stage: destStage?.name || 'Unknown',
              from_pipeline: sourcePipelineName || 'Unknown',
              to_pipeline: destPipelineName || 'Unknown',
              is_cross_pipeline_move: isCrossPipelineMove,
              moved_at: new Date().toISOString(),
              old_stage_id: currentDeal.stage_id,
              new_stage_id: newStageId
            },
            clientId: currentDeal.client_id,
            clientName: clientName,
            status: 'success',
            relatedEntities
          }
        );
      }

      // Refresh deals
      await fetchDeals();
    } catch (err) {
      console.error('Error moving deal:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Get all stages across all pipelines
  const getAllStages = useCallback(async () => {
    try {
      const supabase = await getOrInitSupabase();

      // Fetch all stages with pipeline information
      const { data, error } = await supabase
        .from('pipeline_stages')
        .select(`
          *,
          pipeline:pipelines(id, name)
        `)
        .order('position', { ascending: true });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (err) {
      console.error('Error fetching all stages:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      return [];
    }
  }, []);

  // Upload a document for a deal
  const uploadDocument = async (dealId: string, file: File) => {
    try {
      setLoading(true);


      // Use real database
      const supabase = await getOrInitSupabase();

      // Skip bucket existence check since we know it exists
      // Our tests confirmed the bucket exists and is accessible


      // Generate a unique file path
      const uniqueId = uuidv4();
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_'); // Sanitize filename
      const filePath = `deals/${dealId}/${uniqueId}-${sanitizedFileName}`;




      // Upload file to storage
      const { error: uploadError } = await supabase.storage
        .from('deal_documents')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {

        throw new Error(`Failed to upload file: ${uploadError.message}`);
      }



      // Create document record in the database

      const { data: recordData, error: recordError } = await supabase
        .from('deal_documents')
        .insert({
          deal_id: dealId,
          file_name: file.name,
          file_path: filePath,
          file_type: file.type,
          file_size: file.size
        })
        .select()
        .single();

      if (recordError) {


        // Try to clean up the uploaded file if the record creation fails
        await supabase.storage
          .from('deal_documents')
          .remove([filePath]);

        throw new Error(`Failed to create document record: ${recordError.message}`);
      }

      // Log the document upload
      if (recordData) {
        // Get deal information for related entities
        const { data: dealData } = await supabase
          .from('deals')
          .select('title, client_id, stage_id')
          .eq('id', dealId)
          .single();

        const relatedEntities: Array<{
          type: 'pipeline' | 'stage' | 'deal' | 'document';
          id: string;
          name?: string;
        }> = [{ type: 'deal', id: dealId, name: dealData?.title }];

        // Get client information if available
        if (dealData?.client_id) {
          const { data: clientData } = await supabase
            .from('clients')
            .select('first_name, last_name')
            .eq('id', dealData.client_id)
            .single();

          if (clientData) {
            await logPipedriveAction(
              'upload',
              'document',
              recordData.id,
              {
                details: {
                  file_name: file.name,
                  file_type: file.type,
                  file_size: file.size,
                  file_path: filePath
                },
                clientId: dealData.client_id,
                clientName: `${clientData.first_name} ${clientData.last_name}`.trim(),
                status: 'success',
                relatedEntities
              }
            );
          } else {
            await logPipedriveAction(
              'upload',
              'document',
              recordData.id,
              {
                details: {
                  file_name: file.name,
                  file_type: file.type,
                  file_size: file.size,
                  file_path: filePath
                },
                status: 'success',
                relatedEntities
              }
            );
          }
        } else {
          await logPipedriveAction(
            'upload',
            'document',
            recordData.id,
            {
              details: {
                file_name: file.name,
                file_type: file.type,
                file_size: file.size,
                file_path: filePath
              },
              status: 'success',
              relatedEntities
            }
          );
        }
      }

      // Refresh deals to update UI
      await fetchDeals();


      return recordData;
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred during document upload');
      throw err; // Re-throw to allow handling in the component
    } finally {
      setLoading(false);
    }
  };

  // Fetch documents for a deal
  const fetchDealDocuments = async (dealId: string) => {
    try {


      // Use real database
      const supabase = await getOrInitSupabase();

      // Get document records from the database
      const { data, error } = await supabase
        .from('deal_documents')
        .select('*')
        .eq('deal_id', dealId)
        .order('created_at', { ascending: false });

      if (error) {

        throw error;
      }



      // Generate public URLs for each document
      if (data && data.length > 0) {


        const documentsWithUrls = await Promise.all(
          data.map(async (doc) => {
            try {


              // First check if the file exists in storage
              const { data: fileExists } = await supabase.storage
                .from('deal_documents')
                .list(`deals/${dealId}`, {
                  search: doc.file_path.split('/').pop()
                });

              if (!fileExists || fileExists.length === 0) {

              }

              // Check if we have a valid cached URL
              const cacheKey = `${doc.id}-${doc.file_path}`;
              const now = Date.now();
              const cachedData = documentUrlCache[cacheKey];

              // Use cached URL if it exists and hasn't expired (still has 10 minutes left)
              if (cachedData && cachedData.expiry > now + 10 * 60 * 1000) {

                return {
                  ...doc,
                  url: cachedData.url
                };
              }

              // Create a signed URL with 1 hour expiry
              const { data: urlData, error: urlError } = await supabase.storage
                .from('deal_documents')
                .createSignedUrl(doc.file_path, 60 * 60);

              // Cache the URL with expiry time
              if (urlData?.signedUrl) {
                documentUrlCache[cacheKey] = {
                  url: urlData.signedUrl,
                  expiry: now + 60 * 60 * 1000 // 1 hour from now
                };

                // Log the document download (URL generation)
                await logPipedriveAction(
                  'download',
                  'document',
                  doc.id,
                  {
                    details: {
                      deal_id: doc.deal_id,
                      file_name: doc.file_name
                    },
                    status: 'success'
                  }
                );
              }

              if (urlError) {

                return {
                  ...doc,
                  url: null,
                  error: urlError.message
                };
              }


              return {
                ...doc,
                url: urlData?.signedUrl || null
              };
            } catch (urlErr) {

              return {
                ...doc,
                url: null,
                error: urlErr instanceof Error ? urlErr.message : 'Unknown error'
              };
            }
          })
        );


        return documentsWithUrls;
      }

      return data || [];
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
      return [];
    }
  };

  // Delete a document
  const deleteDocument = async (documentId: string, filePath: string) => {
    try {
      setLoading(true);


      // Use real database
      const supabase = await getOrInitSupabase();

      // Get document data for logging
      const { data: documentData } = await supabase
        .from('deal_documents')
        .select('*')
        .eq('id', documentId)
        .single();

      // First delete the file from storage
      const { error: storageError } = await supabase.storage
        .from('deal_documents')
        .remove([filePath]);

      if (storageError) {

        // Continue with deletion of the record even if file deletion fails
      }

      // Then delete the document record
      const { error: recordError } = await supabase
        .from('deal_documents')
        .delete()
        .eq('id', documentId);

      if (recordError) {

        throw recordError;
      }

      // Log the document deletion
      if (documentData) {
        await logPipedriveAction(
          'delete',
          'document',
          documentId,
          {
            details: { document: documentData },
            status: 'success'
          }
        );
      }

      // Refresh deals to update UI
      await fetchDeals();

      return true;
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a deal
  const deleteDeal = async (dealId: string) => {
    try {
      setLoading(true);


      // Use real database
      const supabase = await getOrInitSupabase();

      // Get deal data for logging
      const { data: dealData } = await supabase
        .from('deals')
        .select('*')
        .eq('id', dealId)
        .single();

      // First, delete any associated documents
      // 1. Get all document records for this deal
      const { data: documentData, error: docQueryError } = await supabase
        .from('deal_documents')
        .select('*')
        .eq('deal_id', dealId);

      if (docQueryError) {

        // Continue with deletion even if document fetching fails
      }

      // 2. Log and delete documents
      if (documentData && documentData.length > 0) {
        // Log each document deletion
        for (const doc of documentData) {
          await logPipedriveAction(
            'delete',
            'document',
            doc.id,
            {
              details: { document: doc },
              status: 'success'
            }
          );
        }

        const filePaths = documentData.map(doc => doc.file_path);

        const { error: storageError } = await supabase.storage
          .from('deal_documents')
          .remove(filePaths);

        if (storageError) {

          // Continue with deletion even if file deletion fails
        }
      }

      // 3. Delete document records
      const { error: docDeleteError } = await supabase
        .from('deal_documents')
        .delete()
        .eq('deal_id', dealId);

      if (docDeleteError) {

        // Continue with deletion even if document record deletion fails
      }

      // Finally delete the deal
      const { error } = await supabase
        .from('deals')
        .delete()
        .eq('id', dealId);

      if (error) throw error;

      // Log the deal deletion
      if (dealData) {
        await logPipedriveAction(
          'delete',
          'deal',
          dealId,
          {
            details: { deal: dealData },
            status: 'success'
          }
        );
      }

      // Refresh deals
      await fetchDeals();
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update an existing pipeline
  const updatePipeline = async (pipelineId: string, name: string, description: string | null = null) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Get the current pipeline data for logging
      const { data: currentPipeline } = await supabase
        .from('pipelines')
        .select('*')
        .eq('id', pipelineId)
        .single();

      const { error } = await supabase
        .from('pipelines')
        .update({
          name,
          description,
          updated_at: new Date().toISOString()
        })
        .eq('id', pipelineId);

      if (error) {
        throw error;
      }

      // Log the pipeline update
      await logPipedriveAction(
        'update',
        'pipeline',
        pipelineId,
        {
          details: {
            old: currentPipeline,
            new: { name, description }
          },
          status: 'success'
        }
      );

      // Refresh pipelines
      await fetchPipelines();
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a pipeline
  const deletePipeline = async (pipelineId: string) => {
    try {
      setLoading(true);

      // Use real database
      const supabase = await getOrInitSupabase();

      // Get pipeline data for logging
      const { data: pipelineData } = await supabase
        .from('pipelines')
        .select('*')
        .eq('id', pipelineId)
        .single();

      // Get all stages for this pipeline
      const { data: stageData } = await supabase
        .from('pipeline_stages')
        .select('*')
        .eq('pipeline_id', pipelineId);

      if (stageData && stageData.length > 0) {
        const stageIds = stageData.map(stage => stage.id);

        // Get all deals in these stages for logging
        const { data: dealData } = await supabase
          .from('deals')
          .select('*')
          .in('stage_id', stageIds);

        // Log deal deletions
        if (dealData && dealData.length > 0) {
          for (const deal of dealData) {
            await logPipedriveAction(
              'delete',
              'deal',
              deal.id,
              {
                details: { deal },
                status: 'success'
              }
            );
          }

          // Delete all deals in these stages
          await supabase
            .from('deals')
            .delete()
            .in('stage_id', stageIds);
        }

        // Log stage deletions
        for (const stage of stageData) {
          await logPipedriveAction(
            'delete',
            'stage',
            stage.id,
            {
              details: { stage },
              status: 'success'
            }
          );
        }

        // Delete all stages
        await supabase
          .from('pipeline_stages')
          .delete()
          .eq('pipeline_id', pipelineId);
      }

      // Finally delete the pipeline
      const { error } = await supabase
        .from('pipelines')
        .delete()
        .eq('id', pipelineId);

      if (error) throw error;

      // Log pipeline deletion
      if (pipelineData) {
        await logPipedriveAction(
          'delete',
          'pipeline',
          pipelineId,
          {
            details: { pipeline: pipelineData },
            status: 'success'
          }
        );
      }

      // Refresh pipelines
      await fetchPipelines();
    } catch (err) {

      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Load initial data
  useEffect(() => {
    fetchPipelines();
  }, [fetchPipelines]);

  // Load stages when active pipeline changes
  useEffect(() => {
    if (activePipeline) {
      fetchStages();
    }
  }, [activePipeline, fetchStages]);

  // Load deals when stages change
  useEffect(() => {
    if (stages.length > 0) {
      fetchDeals();
    }
  }, [stages, fetchDeals]);

  return {
    pipelines,
    stages,
    deals,
    loading,
    error,
    activePipeline,
    setActivePipeline,
    createPipeline,
    updatePipeline,
    createStage,
    updateStage,
    deleteStage,
    createDeal,
    updateDeal,
    moveDeal,
    deleteDeal,
    uploadDocument,
    fetchDealDocuments,
    deleteDocument,
    deletePipeline,
    getAllStages,
    refreshData: () => {
      fetchPipelines();
      fetchStages();
      fetchDeals();
    }
  };
};


