import React, { useState } from 'react';

interface StageFormProps {
  onSubmit: (formData: { name: string; description: string | null }) => void;
  onCancel: () => void;
}

const StageForm: React.FC<StageFormProps> = ({ onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      name: formData.name,
      description: null
    });
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
        <h2 className="text-lg font-semibold text-white mb-4">Create New Stage</h2>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-stone-300 mb-1">
              Stage Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
              required
            />
          </div>

          {/* Description field removed */}

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
            >
              Create Stage
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StageForm;
