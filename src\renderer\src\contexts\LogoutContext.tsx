import React, { createContext, useState, useContext, ReactNode } from 'react'

interface LogoutContextType {
  isLoggingOut: boolean
  setIsLoggingOut: (value: boolean) => void
}

const LogoutContext = createContext<LogoutContextType | undefined>(undefined)

export const useLogout = (): LogoutContextType => {
  const context = useContext(LogoutContext)
  if (!context) {
    throw new Error('useLogout must be used within a LogoutProvider')
  }
  return context
}

interface LogoutProviderProps {
  children: ReactNode
}

export const LogoutProvider: React.FC<LogoutProviderProps> = ({ children }) => {
  const [isLoggingOut, setIsLoggingOut] = useState(false)

  return (
    <LogoutContext.Provider value={{ isLoggingOut, setIsLoggingOut }}>
      {children}
    </LogoutContext.Provider>
  )
}
