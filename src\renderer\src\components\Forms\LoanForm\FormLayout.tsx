import React from 'react'
import { DashboardIcons } from '../../icons/DashboardIcons'

interface FormLayoutProps {
  title: string
  onClose: () => void
  children: React.ReactNode
  className?: string
}

const FormLayout: React.FC<FormLayoutProps> = ({
  title,
  onClose,
  children,
  className = ''
}) => {
  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className={`bg-stone-800 rounded-xl shadow-2xl w-full max-h-[90vh] overflow-y-auto ${className}`}>
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-white">{title}</h2>
            <button
              type="button"
              onClick={onClose}
              className="text-stone-400 hover:text-white transition-colors"
            >
              <DashboardIcons.Close className="w-6 h-6" />
            </button>
          </div>
          <div>
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default FormLayout
