import {
  Pencil,
  Plus,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Search,
  RefreshCw,
  Copy,
  CreditCard,
  Loader,
  X,
  Save,
  CornerDownLeft,
  Check,
  Clock,
  Bell,
  BellOff,
  ArrowUp,
  ArrowDown,
  FileQuestion,
  Maximize,
  Minimize,
  Filter,
  UserPlus,
  Upload,
  ExternalLink,
  FileText,
  Image,
  FileText as Notes,
  Minus,
  List,
  Wallet,
  AlertTriangle,
  MoveHorizontal
} from 'lucide-react'
import React from 'react'

// Custom Firearm icon component
const FirearmIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M4 10h12c.3 0 .5-.1.7-.3l3-3c.2-.2.3-.4.3-.7s-.1-.5-.3-.7l-3-3c-.2-.2-.4-.3-.7-.3H4c-.6 0-1 .4-1 1v6c0 .6.4 1 1 1z" />
    <path d="M18 10v10c0 .6-.4 1-1 1H5c-.6 0-1-.4-1-1v-9" />
    <path d="M4.5 15.5h3" />
    <path d="M4.5 13.5h3" />
    <path d="M6 13.5v2" />
  </svg>
)

export const DashboardIcons = {
  Edit: Pencil,
  Add: Plus,
  Delete: Trash2,
  PrevPage: ChevronLeft,
  NextPage: ChevronRight,
  Search: Search,
  Refresh: RefreshCw,
  Copy: Copy,
  Payment: CreditCard,
  Spinner: Loader,
  Close: X,
  Save: Save,
  Enter: CornerDownLeft,
  Check: Check,
  History: Clock,
  Bell: Bell,
  BellOff: BellOff,
  SortUp: ArrowUp,
  SortDown: ArrowDown,
  EmptyState: FileQuestion,
  Maximize: Maximize,
  Minimize: Minimize,
  Filter: Filter,
  Assign: UserPlus,
  Firearm: FirearmIcon,
  ChevronDown: ChevronRight,
  Upload: Upload,
  ChevronUp: ChevronLeft,
  ExternalLink: ExternalLink,
  Document: FileText,
  Image: Image,
  Notes: Notes,
  Remove: Minus,
  List: List,
  Wallet: Wallet,
  Error: AlertTriangle,
  Move: MoveHorizontal
} as const

export type DashboardIconType = keyof typeof DashboardIcons
export default DashboardIcons
