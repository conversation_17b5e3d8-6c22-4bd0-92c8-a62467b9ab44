import React from 'react'
import { License, Client } from '../../types'
import { DashboardIcons } from '../icons/DashboardIcons'
import { formatDateForUser } from '../../utils/dateUtils'

interface LicenseCardProps {
  license: License
  onEditLicense: (license: License, clientId: string) => void
  onDeleteLicense: (licenseId: string) => void
  onFocusToggle: (licenseId: string | null) => void
  isFocused: boolean
  isOtherCardFocused: boolean
}

export default function LicenseCard({
  license,
  onEditLicense,
  onDeleteLicense,
  onFocusToggle,
  isFocused,
  isOtherCardFocused
}: LicenseCardProps): React.JSX.Element {
  // Format date function
  const formatDate = (dateString: string): string => {
    return formatDateForUser(dateString)
  }

  // Calculate expiry status
  const calculateExpiryStatus = (expiryDate: string): string => {
    if (!expiryDate) return 'bg-gray-500 text-white'

    const today = new Date()
    const expiry = new Date(expiryDate)
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

    if (daysUntilExpiry < 0) {
      return 'bg-red-600 text-white' // Expired
    } else if (daysUntilExpiry <= 30) {
      return 'bg-orange-500 text-white' // Expiring soon
    } else if (daysUntilExpiry <= 90) {
      return 'bg-yellow-500 text-white' // Expiring in 3 months
    } else {
      return 'bg-green-600 text-white' // Valid
    }
  }

  // Get expiry message
  const getExpiryMessage = (expiryDate: string): string => {
    if (!expiryDate) return 'No expiry date'

    const today = new Date()
    const expiry = new Date(expiryDate)
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

    if (daysUntilExpiry < 0) {
      return `Expired ${Math.abs(daysUntilExpiry)} days ago`
    } else if (daysUntilExpiry === 0) {
      return 'Expires today'
    } else if (daysUntilExpiry === 1) {
      return 'Expires tomorrow'
    } else if (daysUntilExpiry <= 30) {
      return `Expires in ${daysUntilExpiry} days`
    } else {
      return `Expires on ${formatDate(expiryDate)}`
    }
  }

  // Handle focus toggle
  const handleFocusToggle = () => {
    onFocusToggle(isFocused ? null : license.id)
  }

  // Get client name
  const getClientName = (): string => {
    const client = license.client as Client
    if (!client) return 'Unknown Client'
    return `${client.first_name} ${client.last_name}`
  }

  return (
    <>
      <div
        className={`px-2 py-1 transition-all duration-300 ${
          isFocused && !isOtherCardFocused
            ? 'z-10'
            : isOtherCardFocused
              ? 'scale-98 opacity-40'
              : ''
        }`}
      >
        <div
          className={`bg-gradient-to-br from-stone-800/90 to-stone-900/90 backdrop-blur-md rounded-2xl p-6 shadow-[0_0_25px_rgba(0,0,0,0.3)] border transition-all duration-300 ease-in-out ${
            isFocused ? 'border-orange-500 shadow-lg shadow-orange-500/30' : 'border-orange-500/30'
          } relative overflow-hidden`}
        >
          {/* Decorative element */}
          <div className="absolute -top-24 -right-24 w-48 h-48 bg-orange-500/10 rounded-full blur-2xl" />
          <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-orange-500/5 rounded-full blur-2xl" />

          <div className="relative">
            <div className="flex justify-between items-start mb-6">
              <div className="flex items-center">
                <h2 className="text-2xl font-semibold text-white flex items-center">
                  <span className="inline-block mr-3 w-1.5 h-6 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                  License Details
                </h2>

                {/* Focus button - only shown when not focused */}
                {!isFocused && (
                  <button
                    onClick={handleFocusToggle}
                    className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-stone-600/80 hover:text-white"
                    title="Focus on this license"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                    </svg>
                  </button>
                )}

                {/* Close button - shown only when focused */}
                {isFocused && (
                  <button
                    onClick={handleFocusToggle}
                    className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-red-500/80 hover:text-white"
                    title="Exit focus mode"
                  >
                    <DashboardIcons.Close className="w-4 h-4" />
                  </button>
                )}
              </div>

              {/* Status Badge */}
              <div
                className={`text-xs py-1 px-3 rounded-full ${calculateExpiryStatus(license.expiry_date)}`}
              >
                {getExpiryMessage(license.expiry_date)}
              </div>
            </div>

            {/* Main content */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left column */}
              <div>
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                    Firearm Information
                  </h3>
                  <div className="bg-stone-800/50 rounded-lg p-4 border border-stone-700/50">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Make</p>
                        <p className="text-sm text-white">{license.make || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Type</p>
                        <p className="text-sm text-white">{license.firearm_type || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Caliber</p>
                        <p className="text-sm text-white">{license.caliber || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Serial Number</p>
                        <p className="text-sm text-white">{license.serial_number || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                    License Information
                  </h3>
                  <div className="bg-stone-800/50 rounded-lg p-4 border border-stone-700/50">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <p className="text-xs text-stone-400 mb-1">License Number</p>
                        <p className="text-sm text-white">{license.license_number || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Section</p>
                        <p className="text-sm text-white">{license.section || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Issue Date</p>
                        <p className="text-sm text-white">{formatDate(license.issue_date)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Expiry Date</p>
                        <p className="text-sm text-white">{formatDate(license.expiry_date)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right column */}
              <div>
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                    Client Information
                  </h3>
                  <div className="bg-stone-800/50 rounded-lg p-4 border border-stone-700/50">
                    <div className="grid grid-cols-1 gap-3">
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Client Name</p>
                        <p className="text-sm text-white">{getClientName()}</p>
                      </div>
                      <div>
                        <p className="text-xs text-stone-400 mb-1">ID Number</p>
                        <p className="text-sm text-white">
                          {(license.client as Client)?.id_number || 'N/A'}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-stone-400 mb-1">Contact</p>
                        <p className="text-sm text-white">
                          {(license.client as Client)?.phone || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                    <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                    Actions
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    <button
                      onClick={() => onEditLicense(license, (license.client as Client)?.id || '')}
                      className="flex items-center gap-2 px-4 py-2.5 bg-blue-600/90 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg border border-blue-500/20"
                    >
                      <DashboardIcons.Edit className="w-4 h-4" />
                      Edit License
                    </button>
                    <button
                      onClick={() => onDeleteLicense(license.id)}
                      className="flex items-center gap-2 px-4 py-2.5 bg-red-600/90 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg border border-red-500/20"
                    >
                      <DashboardIcons.Delete className="w-4 h-4" />
                      Delete License
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
