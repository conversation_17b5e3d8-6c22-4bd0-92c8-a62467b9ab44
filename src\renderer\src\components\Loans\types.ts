import { Loan } from '../../types'

export type LoanFilterType =
  | 'all'
  | 'active'
  | 'paid'
  | 'overdue'
  | 'pending'
  | 'days-0-30'
  | 'days-31-60'
  | 'days-61-90'
  | 'days-90-plus'

export interface FilterCounts {
  all: number
  active: number
  paid: number
  overdue: number
  pending: number
  'days-0-30': number
  'days-31-60': number
  'days-61-90': number
  'days-90-plus': number
}

export interface FormState {
  type: 'loan' | 'payment' | 'license' | 'firearm' | null
  isOpen: boolean
  selectedLoan: Loan | null | { id: string }
  selectedClientId: string | null
}

export interface PaginatedLoans {
  loans: Loan[]
  total: number
}

export interface SortConfig {
  field: string
  direction: 'asc' | 'desc'
}
