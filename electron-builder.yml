appId: co.za.firearmstudio
productName: Firearm Studio
directories:
  buildResources: build
asarUnpack:
  - resources/**
compression: maximum
compressionLevel: 9
# Enable differential updates for faster downloads
electronUpdaterCompatibility: '>=2.16'
# Optimize build for faster updates
forceCodeSigning: false
removePackageScripts: true
packageCache: true
packageLockOnly: false
strictEngineVersions: false
parallel: true
checksum: true
# Optimize build size by excluding unnecessary files
files:
  - 'dist/**/*'
  - '!**/*.js.map'
  - '!**/*.d.ts'
  - '!**/.DS_Store'
  - '!dist/**/*.map'
  - '!dist/**/*.d.ts'
  - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme,LICENSE,LICENSE.md,license,license.md,*.d.ts}'
  - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples,demo,demos}'
  - '!**/node_modules/**/*.{map,d.ts,test.js,spec.js}'
  - '!**/node_modules/.bin'
  - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
  - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes,.npmignore}'
  - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output,.coverage,.vscode}'
  - '!**/{.travis,.github,appveyor,.gitlab-ci.yml}'
  - 'src/preload/**/*'
  - '!**/*.map'
win:
  executableName: Firearm-Studio
  icon: build/icon.ico
  target: ['nsis']
  requestedExecutionLevel: 'asInvoker'
  publisherName: 'Sheldon Bakker'
  extraFiles:
    - from: 'resources'
      to: 'resources'
      filter: ['**/*']
  extraResources:
    - from: '.env.encrypted'
      to: '.env.encrypted'
    - from: 'build'
      to: 'build'
      filter: ['**/*']
  legalTrademarks: '© Sheldon Bakker'
  artifactName: '${productName}-Setup-${version}.${ext}'
  signAndEditExecutable: false
  signDlls: false
  win:
    rcedit:
      console: false
      resources:
        - id: 1
          lang: 1033
          file: build/icon.ico
nsis:
  license: build/license.txt
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  oneClick: true
  allowToChangeInstallationDirectory: false
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
  installerHeaderIcon: build/icon.ico
  deleteAppDataOnUninstall: true
  perMachine: true
  installerLanguages: ['en-US']
  language: '1033'
  displayLanguageSelector: false
  runAfterFinish: false
  createStartMenuShortcut: true
  installerSidebar: 'build/installerSidebar.bmp'
  uninstallerSidebar: 'build/installerSidebar.bmp'
  solid: true
  showComponentSizes: false
  allowElevation: false
  differentialPackage: true
  include: build/installer.nsh
  binaryOptions:
    unicode: true
    compression: 'LZMA2:ultra'
    removeUnneededFiles: true
    optimizeMemory: true
  menuCategory: false
  multiLanguageInstaller: false
  packNSISVCRedist: false
  useZip: false
npmRebuild: false
asar: true
publish:
  - provider: github
    owner: SheldonBakker
    repo: FLM-Updates
    private: true
    token: ${GH_TOKEN}
    releaseType: release
    vPrefixedTagName: true
    publishAutoUpdate: true
    prerelease: false
    # Configure GitHub CDN to improve download speed
    releaseOptions:
      host: 'github.com'
      timeout: 600000  # Increased timeout to 10 minutes for better reliability
      protocol: 'https'
      headers:
        Accept: 'application/octet-stream'
        'Cache-Control': 'max-age=86400'  # Increased cache time to 24 hours
        Connection: 'keep-alive'
        'User-Agent': 'FirearmStudio-Updater'  # Custom user agent for better tracking
    # Optimize update configuration
    updaterCacheDirName: 'flm-updates-cache'
    useMultipleRangeRequest: true
    maxConcurrentDownloads: 8  # Increased concurrent downloads
    differentialDownload: true
    useAppSupportCache: true
    # Advanced GitHub CDN optimization
    githubOptions:
      useEnterpriseApi: false
      baseUrl: 'https://api.github.com'
      timeout: 60000
      headers:
        'Accept': 'application/vnd.github.v3+json'
      private: true
      token: ${GH_TOKEN}
    # Optimize download chunks
    requestHeaders:
      'Accept-Encoding': 'gzip, deflate, br'
      'Accept-Language': 'en-US,en;q=0.9'
    # Enable delta updates for smaller downloads
    differentialPackage: true
