import React, { useState, useEffect } from 'react';
import { PipelineStage } from '../../../types/pipedrive';
import { DashboardIcons } from '../../icons/DashboardIcons';

interface UnifiedStageFormProps {
  mode: 'create' | 'edit';
  stage?: PipelineStage; // Required for edit mode
  onSubmit: (formData: any) => void;
  onCancel: () => void;
}

const UnifiedStageForm: React.FC<UnifiedStageFormProps> = ({ 
  mode, 
  stage, 
  onSubmit, 
  onCancel 
}) => {
  const [formData, setFormData] = useState({
    name: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize form with stage data in edit mode
  useEffect(() => {
    if (mode === 'edit' && stage) {
      setFormData({
        name: stage.name
      });
    }
  }, [mode, stage]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setError('Please enter a stage name');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (mode === 'create') {
        // Create mode
        await onSubmit({
          name: formData.name,
          description: null
        });
      } else if (mode === 'edit' && stage) {
        // Edit mode
        await onSubmit({
          stageId: stage.id,
          name: formData.name,
          description: null
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
        <h2 className="text-lg font-semibold text-white mb-4">
          {mode === 'create' ? 'Create New Stage' : 'Edit Stage'}
        </h2>

        {error && (
          <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-md">
            <p className="text-sm text-red-400">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-stone-300 mb-1">
              Stage Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
              required
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors flex items-center"
              disabled={loading}
            >
              {loading ? (
                <span className="flex items-center">
                  <DashboardIcons.Spinner className="w-4 h-4 animate-spin mr-2" />
                  {mode === 'create' ? 'Creating...' : 'Saving...'}
                </span>
              ) : (
                mode === 'create' ? 'Create Stage' : 'Save Changes'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UnifiedStageForm;
