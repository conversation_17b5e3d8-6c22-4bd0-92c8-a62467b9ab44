import React from 'react'
import { LoanFilterType } from './types'

interface InfoBarProps {
  visibleCount: number
  totalCount: number
  searchQuery: string
  filter: LoanFilterType
}

export const InfoBar: React.FC<InfoBarProps> = ({
  visibleCount,
  totalCount,
  searchQuery,
  filter
}) => {
  // Function to convert filter value to display text
  const getFilterText = (filterValue: LoanFilterType): string => {
    switch (filterValue) {
      case 'active':
        return 'Active Loans'
      case 'paid':
        return 'Paid Loans'
      case 'overdue':
        return 'Overdue Loans'
      case 'days-0-30':
        return '0-30 Days'
      case 'days-31-60':
        return '31-60 Days'
      case 'days-61-90':
        return '61-90 Days'
      case 'days-90-plus':
        return '90+ Days'
      default:
        return 'All Loans'
    }
  }

  return (
    <div className="text-stone-400">
      {visibleCount} of {totalCount} loans
      {searchQuery && (
        <span>
          {' '}
          for "<span className="text-orange-400 font-medium">{searchQuery}</span>"
        </span>
      )}
      {filter !== 'all' && (
        <span>
          {' '}
          filtered by <span className="text-orange-400 font-medium">{getFilterText(filter)}</span>
        </span>
      )}
    </div>
  )
}

export default InfoBar
