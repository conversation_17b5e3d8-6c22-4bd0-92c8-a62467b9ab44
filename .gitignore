# Dependencies
node_modules
node_modules/

# Build outputs
dist
out
build

# Environment and secrets
.env
.env.production
.env.encrypted
*.encrypted
src/utils/env-key.ts
src/utils/env-key.js
src/main/env-key.js
.augment-guildelines


# Logs
logs
*.log
*.log*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.cursor/rules/my-stack.mdc
.cursor/rules/my-tech-stack.mdc

# OS specific
.DS_Store

# Project specific
.augment-guidelines
/Placeholders
.roo/

# Task files
tasks.json
tasks/
