import { Client, License } from '../../types'

export interface FormState {
  type: 'client' | 'license' | null
  isOpen: boolean
  selectedClient: Client | null
  selectedLicense: License | null
  selectedClientId: string | null
}

export interface ToastState {
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
}

export interface FilterState {
  clientFilter: 'all' | 'with-licenses' | 'no-licenses'
}

export interface FilterCounts {
  all: number
  'with-licenses': number
  'no-licenses': number
}

export interface PaginationState {
  page: number
  perPage: number
}

export interface VirtualizationResult<T> {
  clients: T[]
  total: number
}

export interface ClientSearchTips {
  title: string
  items: string[]
}
