import React, { useEffect } from 'react'
import { RenewalForm } from '../components/Forms/RenewalForm'
import SearchContainer from '../components/SearchContainer'
import {
  LicenseCard,
  FilterComponent,
  Pagination,
  NotificationToast,
  useExpiredLicensesService,
  LicenseCardList
} from '../components/ExpiredLicenses'
import { SkeletonLicenses } from '../components/SkeletonLoading'

function ExpiredLicenses(): React.JSX.Element {
  const {
    // State
    loading,
    isRefreshing,
    notificationStatus,
    showRenewalForm,
    renewalFormData,
    totalLicenses,
    notificationFilter,
    paginatedLicenses,
    focusedLicenseId,
    focusedLicense,
    currentPage,

    // Actions
    setCurrentPage,
    setNotificationFilter,
    handleSearch,
    handleRefresh,
    copyToClipboard,
    markAsNotified,
    handleRenewalSubmit,
    clearAndCloseForm,
    setShowRenewalForm,
    setSelectedLicenseId,
    setRenewalFormData,
    handleLicenseFocusToggle,

    // Helpers
    fetchSearchHints,
    licenseSearchTips,
    expiredLicenses
  } = useExpiredLicensesService()

  // Add a useEffect to disable scrolling on the main content area and body
  useEffect(() => {
    // Get the main content area
    const mainContent = document.querySelector(
      '#root > div > div:nth-child(2)'
    ) as HTMLElement | null

    // Save original styles
    const originalBodyOverflow = document.body.style.overflow
    const originalHtmlOverflow = document.documentElement.style.overflow

    // Disable scrolling on body and html
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    if (mainContent) {
      // Save the original style
      const originalOverflow = mainContent.style.overflowY

      // Disable scrolling
      mainContent.style.overflowY = 'hidden'

      // Restore original styles when component unmounts
      return () => {
        mainContent.style.overflowY = originalOverflow
        document.body.style.overflow = originalBodyOverflow
        document.documentElement.style.overflow = originalHtmlOverflow
      }
    }

    // Restore original styles when component unmounts (if mainContent not found)
    return () => {
      document.body.style.overflow = originalBodyOverflow
      document.documentElement.style.overflow = originalHtmlOverflow
    }
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-b from-stone-900 to-stone-800 p-6 overflow-hidden">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white flex items-center gap-3">
          Expiring Licenses
          <div className="px-3 py-1 bg-stone-700/50 rounded-full text-sm text-stone-300">
            Total Licenses: {totalLicenses}
          </div>
        </h1>

        {/* Pagination controls next to page title */}
        {paginatedLicenses.licenses.length > 0 && (
          <Pagination
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            totalItems={paginatedLicenses.totalLicenses}
            itemsPerPage={5}
          />
        )}
      </div>

      <div className="flex flex-row h-[calc(100vh-180px)] overflow-hidden">
        {/* Sidebar */}
        <div className="w-80 bg-stone-800/60 rounded-lg shadow-lg p-4 flex-shrink-0 overflow-hidden">
          {/* Action Buttons */}
          <div className="flex items-center justify-between mb-4">
            {/* Refresh Button */}
            <button
              onClick={handleRefresh}
              disabled={loading || isRefreshing}
              className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-2 rounded-lg
                transition-colors flex items-center gap-1 disabled:opacity-50 h-10"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`w-4 h-4 ${loading || isRefreshing ? 'animate-spin' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
            </button>
          </div>

          {/* Search Container */}
          <div className="mb-6">
            <SearchContainer
              onSearch={handleSearch}
              isLoading={loading}
              placeholder="Search licenses..."
              searchHintsLoader={fetchSearchHints}
              showRefreshButton={false}
              showCreateButton={false}
              searchTipsContent={
                <>
                  <p>{licenseSearchTips.title}</p>
                  <ul className="list-disc pl-4 mt-1 space-y-1">
                    {licenseSearchTips.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </>
              }
              debounceTime={400}
              className="w-full"
            />
          </div>

          {/* Filter Component */}
          <FilterComponent
            notificationFilter={notificationFilter}
            setNotificationFilter={setNotificationFilter}
            expiredLicenses={expiredLicenses}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 ml-6 overflow-y-auto bg-stone-800/30 rounded-lg p-4 h-[calc(100vh-180px)]">
          {/* Search Results Section */}
          {loading ? (
            <div className="h-full">
              <SkeletonLicenses count={6} />
            </div>
          ) : (
            <>
              {paginatedLicenses.licenses.length > 0 ? (
                <div className="space-y-4">
                  {/* Licenses view */}
                  {focusedLicenseId && focusedLicense ? (
                    <div className="focus-mode-container">
                      <div className="focus-mode-card">
                        <LicenseCard
                          key={focusedLicense.id}
                          license={focusedLicense}
                          onRenew={(licenseId) => {
                            setSelectedLicenseId(licenseId)
                            setShowRenewalForm(true)
                          }}
                          onToggleNotifications={markAsNotified}
                          notificationStatus={notificationStatus}
                          copyToClipboard={copyToClipboard}
                          isFocused={true}
                          isOtherCardFocused={false}
                          onFocusToggle={handleLicenseFocusToggle}
                        />
                      </div>
                    </div>
                  ) : (
                    <LicenseCardList
                      licenses={paginatedLicenses.licenses}
                      onRenew={(licenseId) => {
                        setSelectedLicenseId(licenseId)
                        setShowRenewalForm(true)
                      }}
                      onToggleNotifications={markAsNotified}
                      notificationStatus={notificationStatus}
                      copyToClipboard={(text) => copyToClipboard(text)}
                      onFocusToggle={handleLicenseFocusToggle}
                      focusedLicenseId={focusedLicenseId}
                    />
                  )}
                </div>
              ) : (
                // Content to show when no licenses are found (and not loading)
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-stone-400 text-lg">No expiring licenses found.</p>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Notification Toast */}
      <NotificationToast notificationStatus={notificationStatus} />

      {/* Renewal Form */}
      {showRenewalForm && (
        <RenewalForm
          renewalFormData={renewalFormData}
          onRenewalSubmit={handleRenewalSubmit}
          onClose={clearAndCloseForm}
          onFormDataChange={setRenewalFormData}
        />
      )}
    </div>
  )
}

export default ExpiredLicenses
