import React, { memo } from 'react'
import { License } from '../../types'
import { SkeletonLicenses } from '../SkeletonLoading'
import LicenseCard from './LicenseCard'
import { useVirtualization } from '../../hooks/useVirtualization'

interface LicenseCardListProps {
  licenses: License[]
  onEditLicense: (license: License) => void
  onDeleteLicense: (licenseId: string) => void
  onFocusToggle: (licenseId: string | null) => void
  focusedLicenseId: string | null
}

// Number of items to load initially and in each batch
const INITIAL_BATCH_SIZE = 9
const BATCH_INCREMENT = 6

const LicenseCardList: React.FC<LicenseCardListProps> = memo(({
  licenses,
  onEditLicense,
  onDeleteLicense,
  onFocusToggle,
  focusedLicenseId
}) => {
  // Use the virtualization hook
  const {
    items: visibleLicenses,
    total,
    isLoading: isLoadingMore,
    handleScroll,
    handleLoadMore
  } = useVirtualization<License>(licenses, INITIAL_BATCH_SIZE, BATCH_INCREMENT)

  return (
    <div className="flex flex-col h-full">
      {/* Scrollable license cards container */}
      <div className="overflow-y-auto flex-1 pr-2" onScroll={handleScroll}>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {visibleLicenses.map((license) => (
            <LicenseCard
              key={license.id}
              license={license}
              onEditLicense={onEditLicense}
              onDeleteLicense={onDeleteLicense}
              onFocusToggle={onFocusToggle}
              isFocused={focusedLicenseId === license.id}
              isOtherCardFocused={
                focusedLicenseId !== null && focusedLicenseId !== license.id
              }
            />
          ))}
        </div>

        {isLoadingMore && (
          <div className="py-4">
            <SkeletonLicenses count={3} />
          </div>
        )}

        {total > visibleLicenses.length && !isLoadingMore && (
          <div className="text-center py-4">
            <button
              className="text-orange-500 hover:text-orange-400 text-sm"
              onClick={handleLoadMore}
            >
              Load more licenses ({total - visibleLicenses.length} remaining)
            </button>
          </div>
        )}
      </div>
    </div>
  )
})

export default LicenseCardList
