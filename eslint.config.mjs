import js from "@eslint/js";
import ts from "@typescript-eslint/eslint-plugin";
import tsParser from "@typescript-eslint/parser";
import react from "eslint-plugin-react";
import importPlugin from "eslint-plugin-import";
import globals from "globals";
import security from "eslint-plugin-security";
import eslintElectron from "eslint-plugin-electron";

export default [
  js.configs.recommended,
  ts.configs.recommended,
  react.configs.recommended,
  {
    languageOptions: {
      parser: tsParser,
      sourceType: "module",
      ecmaVersion: "latest",
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
    plugins: {
      "@typescript-eslint": ts,
      "react": react,
      "import": importPlugin,
      "security": security,
      "electron": eslintElectron,
    },
    rules: {
      // General Best Practices
      "no-console": "warn",
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": ["warn"],
      "import/order": [
        "error",
        {
          "groups": ["builtin", "external", "internal", "parent", "sibling", "index"],
          "alphabetize": { "order": "asc", "caseInsensitive": true },
        },
      ],
      
      // TypeScript Specific
      "@typescript-eslint/explicit-module-boundary-types": "off",
      "@typescript-eslint/no-explicit-any": "warn",
      
      // React Specific (if using React in renderer)
      "react/react-in-jsx-scope": "off",
      "react/jsx-uses-react": "off",
      "react/jsx-runtime": "error",

      // Electron Security Best Practices
      "security/detect-non-literal-fs-filename": "warn",
      "security/detect-object-injection": "warn",
      "electron/no-node-access": "off",
      "electron/no-remote-module": "error",
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },
];
