import { useState, useEffect } from 'react'

export function Version(): React.JSX.Element {
  const [version, setVersion] = useState<string>('1.0.0')

  useEffect(() => {
    const getVersion = async (): Promise<void> => {
      try {
        const api = window.electronAPI as any
        if (api?.getAppVersion) {
          const info = await api.getAppVersion()
          setVersion(info.version)
        }
      } catch (error) {
        console.error('Error fetching app version:', error)
      }
    }
    getVersion()
  }, [])

  // Format version to remove the 'v' prefix if present
  const formattedVersion = version.startsWith('v') ? 
    version.substring(1) : version
  
  // Check if version contains beta or prerelease indicator
  const isBeta = formattedVersion.includes('beta') || 
                 formattedVersion.includes('alpha') || 
                 formattedVersion.includes('rc')

  return (
    <div className="fixed bottom-4 right-4 text-stone-600 text-xs cursor-pointer hover:text-orange-500 transition-colors duration-200 flex items-center">
      <span>v{formattedVersion}</span>
      {isBeta && (
        <span className="ml-1 px-1 py-0.5 bg-orange-500 text-white text-[10px] rounded-sm">BETA</span>
      )}
    </div>
  )
}
