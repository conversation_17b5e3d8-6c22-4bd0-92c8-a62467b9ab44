import React from 'react'
import { FormField, FormSection, RadioGroup } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionWithAddressProps } from '../utils/types'

/**
 * PersonalInfo - Personal Information Section Component
 *
 * Renders the personal information form section for the Renew Competency form
 */
export default function PersonalInfo({
  formData,
  updateFormData,
  handleAddressChange,
  handleHouseNumberChange,
  className = ''
}: SectionWithAddressProps): JSX.Element {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target

    // Handle checkboxes
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement
      updateFormData({
        [name]: checkbox.checked
      })
      return
    }

    // For other input types
    const updatedData: any = { [name]: value }

    // Extract initials from first name if that field changed
    if (name === 'firstName' && value) {
      updatedData.initials = value
        .split(' ')
        .map((name) => name.charAt(0).toUpperCase())
        .join(' ')
    }

    // Set fullName when first or last name changes
    if ((name === 'firstName' || name === 'lastName') && (formData.firstName || formData.lastName)) {
      updatedData.fullName =
        `${name === 'firstName' ? value : formData.firstName} ${name === 'lastName' ? value : formData.lastName}`.trim()
    }

    updateFormData(updatedData)
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Personal Information</h3>

      {/* Row 1: First Name and Last Name */}
      <div className="grid grid-cols-2 gap-4">
        <FormField
          label="First Names"
          name="firstName"
          value={formData.firstName}
          onChange={handleChange}
          placeholder="Enter first name"
          required
          additionalContent={
            formData.initials && (
              <p className="text-xs text-stone-400 mt-1">Initials: {formData.initials}</p>
            )
          }
        />

        <FormField
          label="Last Name"
          name="lastName"
          value={formData.lastName}
          onChange={handleChange}
          placeholder="Enter last name"
          required
        />
      </div>

      {/* Hidden field for initials */}
      <input type="hidden" name="initials" value={formData.initials || ''} />

      {/* Row 2: ID Number and Citizenship */}
      <div className="grid grid-cols-2 gap-4">
        <FormField
          label="ID Number"
          name="idNumber"
          value={formData.idNumber}
          onChange={handleChange}
          placeholder="e.g. 8001015009087"
          required
        />

        <FormSection title="Citizenship" className="py-0">
          <RadioGroup
            name="citizenType"
            value={formData.citizenType}
            onChange={(value) => {
              const isSaId = value === 'saId'
              const isFId = value === 'fId'
              updateFormData({
                citizenType: value as 'saId' | 'fId' | '',
                saId: isSaId,
                fId: isFId
              })
            }}
            options={[
              { value: 'saId', label: 'SA Citizen' },
              { value: 'fId', label: 'Foreign' }
            ]}
          />
        </FormSection>
      </div>

      {/* Row 3: Phone Number and Email */}
      <div className="grid grid-cols-2 gap-4">
        <FormField
          label="Phone Number"
          name="phoneNumber"
          value={formData.phoneNumber}
          onChange={handleChange}
          placeholder="e.g. ************"
          required
          type="tel"
        />

        <FormField
          label="Email Address"
          name="email"
          value={formData.email}
          onChange={handleChange}
          placeholder="e.g. <EMAIL>"
          type="email"
        />
      </div>

      {/* Physical Address Section */}
      <div className="mt-2">
        <AddressInput
          label="Physical Address"
          value={formData.physicalAddress}
          postalCode={formData.postalCode}
          onChange={(address, postalCode) =>
            handleAddressChange(address, postalCode)
          }
          placeholder="Enter your full physical address"
          isTextarea={false}
          required={true}
          postalCodeRequired={true}
        />
        <p className="text-xs text-stone-400 mt-1 mb-3">
          Enter your full physical address and postal code.
        </p>

      </div>
    </div>
  )
}
