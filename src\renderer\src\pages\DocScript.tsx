import { useState, useEffect, Suspense, lazy } from 'react'
import { FormData, FurtherCompetencyData } from '../types/FormData'
import { getCompetencyTypeById, CompetencyTypeId } from '../config/competencyTypes'

// Import refactored components
import FileUploadSection from '../components/DocScript/FileUploadSection'
import FormTypeSelector from '../components/DocScript/FormTypeSelector'
import CompetencyTypeSelector from '../components/DocScript/CompetencyTypeSelector'
import FormModal from '../components/DocScript/FormModal'
import useFileHandling from '../components/DocScript/useFileHandling'
import { getUserFriendlyErrorMessage } from '../components/DocScript/DocumentProcessingError'
import LicenceTypeSelector from '../components/DocScript/LicenceTypeSelector'
import MiscellaneousTypeSelector from '../components/DocScript/MiscellaneousTypeSelector'
import { Skeleton } from '../components/SkeletonLoading'

// Lazy load components
const TemplateSection = lazy(() => import('../components/DocScript/TemplateSection'))

export default function DocScript(): React.JSX.Element {
  // Use the file handling hook
  const {
    selectedFiles,
    setSelectedFiles,
    fileError,
    setFileError,
    isDragging,
    fetchedTemplate,
    setFetchedTemplate,
    handleFileChange,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    fetchTemplate
  } = useFileHandling()

  // State for form data and processing
  const [formData, setFormData] = useState<FormData | null>(null)
  const [formType, setFormType] = useState<
    'select' | 'competency' | 'licence' | 'competency-select' | 'licence-select' | 'miscellaneous-select' | 'miscellaneous' | null
  >(null)
  const [competencyType, setCompetencyType] = useState<'new' | 'further' | 'renew' | null>(null)
  const [licenceType, setLicenceType] = useState<'new' | 'renew' | null>(null)
  const [miscellaneousType, setMiscellaneousType] = useState<'annexure_a_381a' | 'e350_information' | 'saps_inspection_report' | null>(null)
  // This can be any string (like 'new-competency', 'new-licence', etc.) not just the form type enum
  const [submittedFormType, setSubmittedFormType] = useState<string | null>(null)
  const [documentProcessed, setDocumentProcessed] = useState<boolean>(false)
  const [isProcessing, setIsProcessing] = useState<boolean>(false)
  const [processingError, setProcessingError] = useState<string | null>(null)

  // Reset fetchedTemplate when competencyType changes
  useEffect(() => {
    setFetchedTemplate(null)
    setSelectedFiles([])
  }, [competencyType, licenceType, miscellaneousType, setFetchedTemplate, setSelectedFiles])

  const handleFormSubmit = async (data: FormData | FurtherCompetencyData) => {
    // Set the form data
    setFormData(data as FormData)

    // Set the submitted form type based on selected options
    let formTypeValue: string | null = formType

    if (competencyType) {
      formTypeValue = `${competencyType}-competency`
    } else if (licenceType) {
      formTypeValue = `${licenceType}-licence`
    } else if (miscellaneousType) {
      formTypeValue = `${miscellaneousType}-miscellaneous`
    }

    setSubmittedFormType(formTypeValue)

    // For New Licence form, we always want to process the document in DocScript
    // For other forms, check if the document was already processed
    let shouldProcessDocument = false

    if (licenceType === 'new') {
      // Always process New Licence in DocScript
      // Set documentProcessed to false to ensure it gets processed
      shouldProcessDocument = true
      setDocumentProcessed(false)
      console.log('New Licence form detected - document will be processed')
    } else {
      // For other forms, check the documentProcessed flag
      shouldProcessDocument = 'documentProcessed' in data && !Boolean(data.documentProcessed)

      // Set the document processed state
      if ('documentProcessed' in data) {
        setDocumentProcessed(Boolean(data.documentProcessed))
      } else {
        setDocumentProcessed(false)
      }
    }

    // If it's a competency form, automatically fetch the appropriate template
    if (competencyType && ['new', 'further', 'renew'].includes(competencyType)) {
      const templateConfig = getCompetencyTypeById(competencyType as CompetencyTypeId)
      const templateFile = await fetchTemplate(templateConfig.templateUrl, templateConfig.fileName)

      if (templateFile) {
        setSelectedFiles([templateFile])

        // If the document should be processed automatically, process it
        if (shouldProcessDocument) {
          // Small delay to ensure the form data is set
          setTimeout(() => {
            handleProcessDocument()
          }, 500)
        }
      }
    } else if (licenceType === 'new' || licenceType === 'renew') {
      // For Licence forms, fetch the template but don't process it automatically
      // This prevents the double save dialog issue
      let templateUrl = ''
      if (licenceType === 'new') {
        // New Licence must use the 271_SAPS_Form.docx template
        templateUrl = 'https://app.gunlicence.co.za/storage/v1/object/public/templates/Licence/271_SAPS_Form.docx'
        console.log('DocScript: Using New Licence template URL:', templateUrl)
      } else {
        // Renew Licence uses the 518A_SAPS_Form.docx template
        templateUrl = 'https://app.gunlicence.co.za/storage/v1/object/public/templates/Licence/518A_SAPS_Form.docx'
        console.log('DocScript: Using Renew Licence template URL:', templateUrl)
      }
      const fileName = licenceType === 'new' ? 'New Licence.docx' : 'Renew Licence.docx'

      const templateFile = await fetchTemplate(templateUrl, fileName)

      if (templateFile) {
        setSelectedFiles([templateFile])
        // Don't automatically process the document
        // Let the user click the Process Document button instead
      }
    } else if (miscellaneousType === 'annexure_a_381a') {
      // For Annexure A forms, fetch the template but don't process it automatically
      const templateUrl = 'https://app.gunlicence.co.za/storage/v1/object/public/templates/MISC/Annexure_A_381A.docx'
      const fileName = 'Annexure_A_381A.docx'

      const templateFile = await fetchTemplate(templateUrl, fileName)

      if (templateFile) {
        setSelectedFiles([templateFile])
        // Don't automatically process the document
        // Let the user click the Process Document button instead
      }
    } else if (miscellaneousType === 'e350_information') {
      // For E350 Information forms, fetch the template but don't process it automatically
      const templateUrl = 'https://app.gunlicence.co.za/storage/v1/object/public/templates/MISC/E350A_Infomation.docx'
      const fileName = 'E350A_Infomation.docx'

      const templateFile = await fetchTemplate(templateUrl, fileName)

      if (templateFile) {
        setSelectedFiles([templateFile])
        // Don't automatically process the document
        // Let the user click the Process Document button instead
      }
    } else if (miscellaneousType === 'saps_inspection_report') {
      // For SAPS Inspection Report forms, fetch the template but don't process it automatically
      const templateUrl = 'https://app.gunlicence.co.za/storage/v1/object/public/templates/MISC/SAPS_Inspection_Report.docx'
      const fileName = 'SAPS_Inspection_Report.docx'

      const templateFile = await fetchTemplate(templateUrl, fileName)

      if (templateFile) {
        setSelectedFiles([templateFile])
        // Don't automatically process the document
        // Let the user click the Process Document button instead
      }
    }
  }

  // Handle clearing all form data and reset state
  const handleClearForm = () => {
    setSelectedFiles([])
    setFormData(null)
    setSubmittedFormType(null)
    setDocumentProcessed(false)
    setProcessingError(null)
  }

  const handleProcessDocument = async (): Promise<void> => {
    try {
      if (!formData) {
        setProcessingError('No form data available')
        return
      }

      // Check if we have files either uploaded manually or fetched automatically
      if (selectedFiles.length === 0 && !fetchedTemplate) {
        setProcessingError('No template file available')
        return
      }

      setIsProcessing(true)
      setProcessingError(null)

      // Reset document processed state to ensure proper processing flow
      setDocumentProcessed(false)

      // Use either the selected files or the fetched template
      // Always create a new File object to prevent caching issues
      let fileToProcess: File;

      if (selectedFiles.length > 0) {
        // Create a new File object from the selected file
        const originalFile = selectedFiles[0];
        const arrayBuffer = await originalFile.arrayBuffer();
        fileToProcess = new File([arrayBuffer], originalFile.name, { type: originalFile.type });
      } else if (fetchedTemplate) {
        // Create a new File object from the fetched template
        const arrayBuffer = await fetchedTemplate.arrayBuffer();
        fileToProcess = new File([arrayBuffer], fetchedTemplate.name, { type: fetchedTemplate.type });
      } else {
        setProcessingError('No template file available')
        setIsProcessing(false)
        return
      }

      try {
        // Import the updated document processor functions
        const { processDocument: processDoc, saveProcessedDocument } = await import('../components/DocScript/DocumentProcessor')

        // Log the form data before processing
        console.log('Processing document with form data:', formData)
        console.log('Document processed flag:', formData.documentProcessed)

        // Process the document
        const { blob, filename } = await processDoc(fileToProcess, formData)

        // Show save dialog (true = show save dialog)
        await saveProcessedDocument(blob, filename, true)

        // Mark as processed and reset processing state
        setDocumentProcessed(true)
        setIsProcessing(false)
        setFileError(null)
      } catch (error) {
        console.error('Error processing template:', error)

        // Convert to user-friendly error message
        const errorMessage = getUserFriendlyErrorMessage(error);
        setFileError(errorMessage);
        setIsProcessing(false)
      }
    } catch (error) {
      console.error('Error processing document:', error)

      // Convert to user-friendly error message
      const errorMessage = getUserFriendlyErrorMessage(error);
      setFileError(errorMessage);
      setIsProcessing(false)
    }
  }

  // Add a useEffect to disable scrolling on the main content area and body
  useEffect(() => {
    // Get the main content area
    const mainContent = document.querySelector(
      '#root > div > div:nth-child(2)'
    ) as HTMLElement | null

    // Save original styles
    const originalBodyOverflow = document.body.style.overflow
    const originalHtmlOverflow = document.documentElement.style.overflow

    // Disable scrolling on body and html
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    if (mainContent) {
      // Save the original style
      const originalOverflow = mainContent.style.overflowY

      // Disable scrolling
      mainContent.style.overflowY = 'hidden'

      // Restore original styles when component unmounts
      return () => {
        mainContent.style.overflowY = originalOverflow
        document.body.style.overflow = originalBodyOverflow
        document.documentElement.style.overflow = originalHtmlOverflow
      }
    }

    // Restore original styles when component unmounts (if mainContent not found)
    return () => {
      document.body.style.overflow = originalBodyOverflow
      document.documentElement.style.overflow = originalHtmlOverflow
    }
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-b from-stone-900 to-stone-800 p-4">
      {/* Page title with document type indicator */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold text-white flex items-center gap-3">
          Forms Templates
          {submittedFormType && (
            <div className="px-3 py-1 bg-stone-700/50 rounded-full text-sm text-stone-300">
              {submittedFormType === 'deceased' ? 'Deceased Estate' : submittedFormType}
            </div>
          )}
        </h1>
        <button
          onClick={() => setFormType('select')}
          className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
            text-white px-3 py-1.5 rounded-lg shadow-md shadow-orange-500/20
            transition-all duration-200 flex items-center gap-2 h-9"
        >
          <span className="w-4 h-4 flex items-center justify-center text-lg font-bold">+</span>
          <span>New Document</span>
        </button>
      </div>

      {/* Description text - only shown when no form is selected */}
      {!submittedFormType && (
        <p className="text-stone-400 mb-6 max-w-3xl">
          Access and fill various document templates for competency certificates, licenses, and other firearm-related documentation.
        </p>
      )}

        {/* Form Selection Modals */}
        <FormTypeSelector formType={formType} setFormType={setFormType} />
        <CompetencyTypeSelector
          formType={formType}
          setFormType={setFormType}
          setCompetencyType={setCompetencyType}
        />
        <LicenceTypeSelector
          formType={formType}
          setFormType={setFormType}
          setLicenceType={setLicenceType}
        />
        <MiscellaneousTypeSelector
          formType={formType}
          setFormType={setFormType}
          setMiscellaneousType={setMiscellaneousType}
        />

        {/* Form Modals */}
        <FormModal
          formType={formType}
          competencyType={competencyType}
          licenceType={licenceType}
          miscellaneousType={miscellaneousType}
          submittedFormType={submittedFormType}
          setFormType={setFormType}
          setCompetencyType={setCompetencyType}
          setLicenceType={setLicenceType}
          setMiscellaneousType={setMiscellaneousType}
          handleFormSubmit={handleFormSubmit}
        />

        {/* Two-column layout - always side by side */}
        <div className="flex flex-row h-[calc(100vh-160px)]">
          {/* Left column: Page tools */}
          <div className="w-80 bg-stone-800/60 rounded-lg shadow-lg p-3 flex-shrink-0 flex flex-col h-[calc(100vh-160px)]">
            {/* Action Buttons */}
            <div className="flex items-center justify-between mb-2">
              {/* Reset Button */}
              <button
                onClick={handleClearForm}
                disabled={!submittedFormType}
                className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-1.5 rounded-lg
                  transition-colors flex items-center gap-1 disabled:opacity-50 h-8 text-sm"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                <span>{!submittedFormType ? 'Reset' : 'Reset'}</span>
              </button>

              {/* New Document Button - only shown when a form is already submitted */}
              {submittedFormType && (
                <button
                  onClick={() => setFormType('select')}
                  className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                    text-white px-3 py-1.5 rounded-lg shadow-md shadow-orange-500/20
                    transition-all duration-200 flex items-center gap-2 h-8 text-sm"
                >
                  <span className="w-4 h-4 flex items-center justify-center text-lg font-bold">+</span>
                  <span>New</span>
                </button>
              )}
            </div>

            {/* Document Upload Section - takes remaining height */}
            <div className="flex-1">
              {submittedFormType ? (
                <FileUploadSection
                  selectedFiles={selectedFiles}
                  setSelectedFiles={setSelectedFiles}
                  fileError={fileError}
                  setFileError={setFileError}
                  formData={formData}
                  submittedFormType={submittedFormType}
                  competencyType={competencyType}
                  fetchedTemplate={fetchedTemplate}
                  documentProcessed={documentProcessed}
                  isProcessing={isProcessing}
                  processingError={processingError}
                  handleProcessDocument={handleProcessDocument}
                  setDocumentProcessed={setDocumentProcessed}
                  isDragging={isDragging}
                  handleDragOver={handleDragOver}
                  handleDragLeave={handleDragLeave}
                  handleDrop={handleDrop}
                  handleFileChange={handleFileChange}
                  handleClearForm={handleClearForm}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center p-4">
                    <svg className="w-12 h-12 mx-auto text-stone-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <p className="text-stone-400 text-sm">Select "New Document" to begin</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right column: Template content */}
          <div className="flex-1 ml-4 flex flex-col">
            <div className="bg-stone-800/30 rounded-lg p-3 flex flex-col h-full">
              <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                <span className="inline-block mr-2 w-1 h-5 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                Template Reference
              </h3>
              <div className="overflow-y-auto flex-1 pr-2">
                <Suspense fallback={
                  <div className="space-y-4">
                    {Array.from({ length: 5 }).map((_, index) => (
                      <div key={index} className="mb-4">
                        <Skeleton height="1.5rem" width="60%" className="mb-2" />
                        <div className="space-y-2">
                          {Array.from({ length: 3 }).map((_, i) => (
                            <Skeleton key={i} height="1rem" width={`${80 - i * 15}%`} />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                }>
                  <TemplateSection />
                </Suspense>
              </div>
            </div>
          </div>
        </div>
      </div>
  )
}
