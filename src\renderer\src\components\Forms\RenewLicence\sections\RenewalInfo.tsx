import React from 'react'
import { RenewLicenceData } from '../../../../types/RenewLicenceData'
import { FormField, RadioGroup, FormSection } from '../../../FormComponents'

interface RenewalInfoProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  className?: string
}

const RenewalInfo: React.FC<RenewalInfoProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Licence Renewal Information</h3>
      <FormSection
        title="Licence Renewal Information"
        subtitle="Please provide details about your existing licence"
      >
        <div className="space-y-4">
          <FormField
            label="Previous Licence Number"
            name="prevLicenceNumber"
            value={formData.prevLicenceNumber}
            onChange={handleChange}
            placeholder="Enter your previous licence number"
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Previous Licence Issue Date"
              name="prevLicenceIssueDate"
              value={formData.prevLicenceIssueDate}
              onChange={handleChange}
              type="date"
              required
            />

            <FormField
              label="Previous Licence Expiry Date"
              name="prevLicenceExpiryDate"
              value={formData.prevLicenceExpiryDate}
              onChange={handleChange}
              type="date"
              required
            />
          </div>

          <div className="mt-4">
            <RadioGroup
              name="renewalType"
              value={
                formData.standardRenewal ? 'standard' : formData.lostLicence ? 'lost' : 'damaged'
              }
              onChange={(value) => {
                updateFormData({
                  standardRenewal: value === 'standard',
                  lostLicence: value === 'lost',
                  damagedLicence: value === 'damaged'
                })
              }}
              options={[
                { value: 'standard', label: 'Standard Renewal' },
                { value: 'lost', label: 'Lost Licence' },
                { value: 'damaged', label: 'Damaged Licence' }
              ]}
              label="Reason for Renewal"
            />
          </div>

          {formData.lostLicence && (
            <div className="ml-6 border-l-2 border-orange-500 pl-4">
              <FormField
                label="Case Number for Lost Licence"
                name="lostLicenceCaseNumber"
                value={formData.lostLicenceCaseNumber}
                onChange={handleChange}
                placeholder="Enter SAPS case number"
                required
              />
            </div>
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default RenewalInfo
