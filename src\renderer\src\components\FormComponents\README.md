# Form Components

This directory contains standardized form components that follow a consistent design pattern and color schema.

## Overview

The form components provide a unified look and feel across all forms in the application. They use a consistent color palette, typography, spacing, and interaction patterns. This helps ensure a cohesive user experience and makes the application easier to maintain.

## Design System

The form components are built on a design system defined in `FormTheme.ts`. This file provides:

- **Color palette** - Consistent colors for text, backgrounds, borders, and status indicators
- **Typography** - Font sizes, weights, and families
- **Spacing** - Standard spacing values
- **Borders** - Border radii and widths
- **Shadows** - Elevation effects
- **Transitions** - Animation speeds and easing functions

## Components

### FormField

A standard input field with label and error handling.

```tsx
<FormField
  label="First Name"
  name="firstName"
  value={formData.firstName}
  onChange={handleChange}
  required
  error={errors.firstName}
/>
```

### SelectField

A dropdown selection component.

```tsx
<SelectField
  label="Country"
  name="country"
  value={formData.country}
  onChange={handleChange}
  options={[
    { value: 'us', label: 'United States' },
    { value: 'ca', label: 'Canada' },
    { value: 'uk', label: 'United Kingdom' }
  ]}
  required
  error={errors.country}
/>
```

### RadioGroup

A set of radio buttons for selecting a single option from a list.

```tsx
<RadioGroup
  label="Gender"
  name="gender"
  value={formData.gender}
  onChange={(value) => setFormData({...formData, gender: value})}
  options={[
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' }
  ]}
  required
  error={errors.gender}
/>
```

### CheckboxGroup

A group of checkboxes for selecting multiple options.

```tsx
<CheckboxGroup
  title="Interests"
  options={[
    { name: 'sports', label: 'Sports', checked: formData.interests.sports },
    { name: 'music', label: 'Music', checked: formData.interests.music },
    { name: 'reading', label: 'Reading', checked: formData.interests.reading }
  ]}
  onChange={handleCheckboxChange}
  columns={3}
  error={errors.interests}
/>
```

### FormSection

A container for grouping related form fields.

```tsx
<FormSection 
  title="Personal Information" 
  subtitle="Please provide your details"
  variant="default"
  collapsible
>
  <div className="grid grid-cols-2 gap-4">
    <FormField 
      label="First Name" 
      name="firstName" 
      value={formData.firstName} 
      onChange={handleChange} 
      required 
    />
    <FormField 
      label="Last Name" 
      name="lastName" 
      value={formData.lastName} 
      onChange={handleChange} 
      required 
    />
  </div>
</FormSection>
```

FormSection variants:
- `default` - Standard styling
- `highlight` - Highlighted with orange accents
- `nested` - Darker background for nested sections
- `success` - Green accents for success states
- `error` - Red accents for error states
- `warning` - Amber accents for warning states

### FormLayout

A complete form layout with multi-step navigation.

```tsx
<FormLayout
  title="Client Registration"
  sections={[
    { id: 'personal', title: 'Personal Information' },
    { id: 'contact', title: 'Contact Details' },
    { id: 'preferences', title: 'Preferences' }
  ]}
  currentStep={currentStep}
  setCurrentStep={setCurrentStep}
  onSubmit={handleSubmit}
  submissionStatus={submissionStatus}
  submissionMessage={submissionMessage}
  onCancelSubmission={handleCancelSubmission}
>
  {currentStep === 1 && (
    <div>
      {/* Personal Information Fields */}
    </div>
  )}
  {currentStep === 2 && (
    <div>
      {/* Contact Details Fields */}
    </div>
  )}
  {currentStep === 3 && (
    <div>
      {/* Preference Fields */}
    </div>
  )}
</FormLayout>
```

## Theme Integration

To use the theme in your components, import the theme variables:

```tsx
import { colors, typography, spacing, formStyles } from '../FormComponents';
```

You can then use these variables to style your components consistently.

## Best Practices

1. **Use the provided components** - Avoid creating custom form components when the provided ones will work
2. **Follow the grid system** - Use the grid utilities for consistent layouts
3. **Maintain consistency** - Use the same patterns across all forms
4. **Responsive design** - All components are designed to work on different screen sizes
5. **Accessibility** - Components include proper labeling, focus states, and error handling
6. **Error handling** - Always use the built-in error handling to provide user feedback
7. **Validation** - Validate form data and provide clear error messages