import React from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'

interface PaginationProps {
  page: number
  setPage: (page: number) => void
  totalItems: number
  itemsPerPage: number
}

const Pagination: React.FC<PaginationProps> = ({ page, setPage, totalItems, itemsPerPage }) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage)

  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null

  // Calculate the range of pages to show
  const getPageNumbers = () => {
    const maxPagesToShow = 5
    const pages: Array<number> = [] // Properly type the array as number[]

    if (totalPages <= maxPagesToShow) {
      // If total pages is less than max pages to show, show all pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Always include first page
      pages.push(1)

      // Calculate middle pages
      let startPage = Math.max(2, page - 1)
      let endPage = Math.min(totalPages - 1, page + 1)

      // Adjust if we're at the start or end
      if (page <= 2) {
        endPage = 3
      } else if (page >= totalPages - 1) {
        startPage = totalPages - 2
      }

      // Add ellipsis if needed
      if (startPage > 2) {
        pages.push(-1) // -1 represents ellipsis
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i)
      }

      // Add ellipsis if needed
      if (endPage < totalPages - 1) {
        pages.push(-2) // -2 represents ellipsis
      }

      // Always include last page
      pages.push(totalPages)
    }

    return pages
  }

  const pageNumbers = getPageNumbers()

  return (
    <div className="flex items-center space-x-1">
      {/* Previous button */}
      <button
        onClick={() => setPage(Math.max(1, page - 1))}
        disabled={page === 1}
        className={`p-1.5 rounded-md ${
          page === 1
            ? 'text-stone-500 cursor-not-allowed'
            : 'text-stone-400 hover:text-white hover:bg-stone-700/50'
        }`}
      >
        <DashboardIcons.PrevPage className="w-4 h-4" />
      </button>

      {/* Page numbers */}
      {pageNumbers.map((pageNumber, index) => {
        // Render ellipsis
        if (pageNumber < 0) {
          return (
            <span key={`ellipsis-${index}`} className="text-stone-500 px-2">
              ...
            </span>
          )
        }

        // Render page number
        return (
          <button
            key={pageNumber}
            onClick={() => setPage(pageNumber)}
            className={`w-8 h-8 flex items-center justify-center rounded-md ${
              page === pageNumber
                ? 'bg-orange-500 text-white'
                : 'text-stone-400 hover:text-white hover:bg-stone-700/50'
            }`}
          >
            {pageNumber}
          </button>
        )
      })}

      {/* Next button */}
      <button
        onClick={() => setPage(Math.min(totalPages, page + 1))}
        disabled={page === totalPages}
        className={`p-1.5 rounded-md ${
          page === totalPages
            ? 'text-stone-500 cursor-not-allowed'
            : 'text-stone-400 hover:text-white hover:bg-stone-700/50'
        }`}
      >
        <DashboardIcons.NextPage className="w-4 h-4" />
      </button>
    </div>
  )
}

export default Pagination
