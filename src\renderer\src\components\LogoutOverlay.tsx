import React from 'react'

interface LogoutOverlayProps {
  isVisible: boolean
}

const LogoutOverlay: React.FC<LogoutOverlayProps> = ({ isVisible }) => {
  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-stone-900/80 flex items-center justify-center z-[10000]">
      <div className="bg-stone-800 p-8 rounded-lg shadow-lg flex flex-col items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4" />
        <h2 className="text-xl font-semibold text-white mb-2">Logging out...</h2>
        <p className="text-stone-400 text-center">Please wait while you are being logged out.</p>
      </div>
    </div>
  )
}

export default LogoutOverlay
