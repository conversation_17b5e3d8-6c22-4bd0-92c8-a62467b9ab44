import { useState } from 'react'
import { fetchTemplateFile } from './DocumentProcessor'

interface UseFileHandlingResult {
  selectedFiles: File[]
  setSelectedFiles: React.Dispatch<React.SetStateAction<File[]>>
  fileError: string | null
  setFileError: React.Dispatch<React.SetStateAction<string | null>>
  isDragging: boolean
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>
  isFetchingTemplate: boolean
  setIsFetchingTemplate: React.Dispatch<React.SetStateAction<boolean>>
  fetchedTemplate: File | null
  setFetchedTemplate: React.Dispatch<React.SetStateAction<File | null>>
  validateAndSetFiles: (files: File[]) => void
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleDragOver: (e: React.DragEvent<HTMLDivElement>) => void
  handleDragLeave: (e: React.DragEvent<HTMLDivElement>) => void
  handleDrop: (e: React.DragEvent<HTMLDivElement>) => void
  fetchTemplate: (url: string, fileName: string) => Promise<File | null>
}

export const useFileHandling = (): UseFileHandlingResult => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [fileError, setFileError] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState<boolean>(false)
  const [isFetchingTemplate, setIsFetchingTemplate] = useState<boolean>(false)
  const [fetchedTemplate, setFetchedTemplate] = useState<File | null>(null)

  const validateAndSetFiles = (files: File[]): void => {
    const validFiles = Array.from(files).filter(
      (file) =>
        file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    )

    if (validFiles.length > 0) {
      setSelectedFiles(validFiles)
      setFileError(null)
    } else {
      setSelectedFiles([])
      setFileError('Only DOCX files are allowed')
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    if (e.target.files) {
      validateAndSetFiles(Array.from(e.target.files))
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
    if (e.dataTransfer.files) {
      validateAndSetFiles(Array.from(e.dataTransfer.files))
    }
  }

  const fetchTemplate = async (url: string, fileName: string): Promise<File | null> => {
    try {
      setIsFetchingTemplate(true)
      setFileError(null)

      // Always bypass cache to ensure we get a fresh template
      const file = await fetchTemplateFile(url, fileName, true)

      setIsFetchingTemplate(false)
      if (file) {
        setFetchedTemplate(file)
        return file
      }
      return null
    } catch (error) {
      console.error('Error fetching template file:', error)
      setFileError(
        `Error fetching template: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
      setIsFetchingTemplate(false)
      return null
    }
  }

  return {
    selectedFiles,
    setSelectedFiles,
    fileError,
    setFileError,
    isDragging,
    setIsDragging,
    isFetchingTemplate,
    setIsFetchingTemplate,
    fetchedTemplate,
    setFetchedTemplate,
    validateAndSetFiles,
    handleFileChange,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    fetchTemplate
  }
}

export default useFileHandling
