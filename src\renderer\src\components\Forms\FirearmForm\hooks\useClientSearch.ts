import { useState, useCallback } from 'react';
import { debounce } from 'lodash';
import { getOrInitSupabase } from '../../../../lib/supabase';
import { SimpleClient } from './useFirearmForm';

export const useClientSearch = (setError: (error: string | null) => void) => {
  const [clients, setClients] = useState<SimpleClient[]>([]);
  const [clientSearchTerm, setClientSearchTerm] = useState('');
  const [isLoadingClients, setIsLoadingClients] = useState(false);

  // Fetch clients with debounce for search
  const fetchClients = useCallback(
    debounce(async (searchTerm: string) => {
      try {
        setIsLoadingClients(true);
        // Use getOrInitSupabase instead of getSupabase to ensure authentication is properly handled
        const supabase = await getOrInitSupabase();
        let query = supabase.from('clients').select('id, first_name, last_name, email, phone, id_number');

        if (searchTerm) {
          const trimmedTerm = searchTerm.trim();
          const terms = trimmedTerm.split(' ').filter(t => t.length > 0);

          // Check if it's an ID number (only digits)
          if (/^\d+$/.test(trimmedTerm)) {
            query = query.ilike('id_number', `%${trimmedTerm}%`);
          }
          // If multiple terms (likely a full name)
          else if (terms.length > 1) {
            // Try different combinations for first name and last name
            const possibleFirstName = terms[0];
            const possibleLastName = terms[terms.length - 1];

            // First name + last name pattern (most common case)
            query = query.or(
              `and(first_name.ilike.%${possibleFirstName}%,last_name.ilike.%${possibleLastName}%),` +
              // Also try the reverse in case names are entered in reverse order
              `and(first_name.ilike.%${possibleLastName}%,last_name.ilike.%${possibleFirstName}%),` +
              // Also check if the full search term is in either field (for compound names)
              `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%`
            );
          }
          // Single term (could be first name, last name, or partial ID)
          else {
            query = query.or(
              `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%,id_number.ilike.%${trimmedTerm}%`
            );
          }
        }

        const { data, error } = await query.limit(10);

        if (error) {
          // Handle specific authentication errors
          if (error.message?.includes('JWT') || error.message?.includes('token') || error.message?.includes('auth')) {
            console.error('Authentication error while fetching clients:', error);
            // Try to refresh the session
            const { error: refreshError } = await supabase.auth.refreshSession();
            if (refreshError) {
              console.error('Failed to refresh authentication session:', refreshError);
              setError('Authentication error. Please try again or reload the page.');
            } else {
              // If refresh was successful, try the query again
              const { data: refreshedData, error: retryError } = await query.limit(10);
              if (retryError) {
                throw retryError;
              }
              setClients(refreshedData || []);
            }
          } else {
            throw error;
          }
        } else {
          setClients(data || []);
        }
      } catch (error) {
        console.error('Error fetching clients:', error);
        // Don't show error in the UI for search operations to avoid disrupting the user experience
      } finally {
        setIsLoadingClients(false);
      }
    }, 300),
    [setError]
  );

  // Handle client search change
  const handleClientSearchChange = useCallback((searchTerm: string) => {
    setClientSearchTerm(searchTerm);
    fetchClients(searchTerm);
  }, [fetchClients]);

  return {
    clients,
    clientSearchTerm,
    isLoadingClients,
    setClientSearchTerm,
    handleClientSearchChange
  };
};
