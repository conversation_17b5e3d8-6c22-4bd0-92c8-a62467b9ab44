import { useState } from 'react';
import { ErrorBoundary, PageErrorBoundary, FormErrorBoundary, CardListErrorBoundary } from './index';

// A component that will throw an error when a button is clicked
const BuggyCounter = () => {
  const [counter, setCounter] = useState(0);
  
  const handleClick = () => {
    setCounter(prevCounter => {
      // This will cause an error when counter reaches 5
      if (prevCounter === 5) {
        throw new Error('Simulated error: Counter reached 5!');
      }
      return prevCounter + 1;
    });
  };

  return (
    <div className="p-4 bg-stone-800 rounded-lg">
      <p className="mb-2 text-white">Counter: {counter}</p>
      <button 
        onClick={handleClick}
        className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
      >
        Increment Counter
      </button>
      <p className="mt-2 text-stone-400 text-sm">
        (This component will crash when counter reaches 5)
      </p>
    </div>
  );
};

// A component that will throw an error immediately when rendered
const ImmediateErrorComponent = () => {
  throw new Error('This component failed immediately!');
  return <div>This will never render</div>;
};

// Demo component to showcase different error boundaries
const ErrorBoundaryDemo = () => {
  return (
    <div className="p-6 space-y-8">
      <h1 className="text-2xl font-bold text-white mb-6">Error Boundary Demo</h1>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-white">Basic Error Boundary</h2>
        <p className="text-stone-400 mb-4">
          This demonstrates the basic error boundary with default fallback UI.
        </p>
        <ErrorBoundary>
          <BuggyCounter />
        </ErrorBoundary>
      </div>

      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-white">Page Error Boundary</h2>
        <p className="text-stone-400 mb-4">
          This demonstrates the page-level error boundary with a more detailed UI.
        </p>
        <PageErrorBoundary pageName="Demo Page">
          <BuggyCounter />
        </PageErrorBoundary>
      </div>

      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-white">Form Error Boundary</h2>
        <p className="text-stone-400 mb-4">
          This demonstrates the form-specific error boundary.
        </p>
        <FormErrorBoundary formName="Demo Form">
          <BuggyCounter />
        </FormErrorBoundary>
      </div>

      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-white">Card List Error Boundary</h2>
        <p className="text-stone-400 mb-4">
          This demonstrates the card list error boundary.
        </p>
        <CardListErrorBoundary listName="demo items">
          <BuggyCounter />
        </CardListErrorBoundary>
      </div>

      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-white">Immediate Error Example</h2>
        <p className="text-stone-400 mb-4">
          This demonstrates catching an error that occurs during rendering.
        </p>
        <ErrorBoundary>
          <ImmediateErrorComponent />
        </ErrorBoundary>
      </div>
    </div>
  );
};

export default ErrorBoundaryDemo;
