interface TemplateStatusProps {
  status: 'loading' | 'ready' | 'error' | 'processing' | 'success'
  templateName?: string
  error?: string | null
  className?: string
  title?: string
  message?: string
  onRetry?: () => void
}

const TemplateStatus = ({ 
  status, 
  templateName, 
  error, 
  className = 'mb-6 p-4 bg-stone-700/30 rounded-lg',
  title = 'Template Status',
  message,
  onRetry
}: TemplateStatusProps) => {
  return (
    <div className={className}>
      <h4 className="text-md font-medium text-orange-400 mb-3">{title}</h4>
      
      {status === 'loading' ? (
        <div className="flex items-center text-stone-300">
          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500 mr-2"></div>
          <span>{message || 'Checking template availability...'}</span>
        </div>
      ) : status === 'processing' ? (
        <div className="flex items-center text-stone-300">
          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500 mr-2"></div>
          <span>{message || 'Processing document...'}</span>
        </div>
      ) : status === 'error' ? (
        <div className="text-red-400 text-sm py-2">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{error || message || 'Error accessing template'}</span>
          </div>
          {onRetry && (
            <button 
              onClick={onRetry}
              className="mt-2 text-xs text-orange-500 hover:text-orange-400 transition-colors flex items-center"
            >
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Retry
            </button>
          )}
        </div>
      ) : status === 'success' ? (
        <div className="flex items-center text-green-400">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <span>{message || 'Operation completed successfully'}</span>
        </div>
      ) : (
        <div className="flex items-center text-green-400">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <span>Template ready: {templateName}</span>
        </div>
      )}
    </div>
  )
}

export default TemplateStatus 