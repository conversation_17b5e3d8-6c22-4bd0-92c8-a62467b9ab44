import React, { useState, useEffect } from 'react';
import { Pipeline } from '../../../types/pipedrive';
import { DashboardIcons } from '../../icons/DashboardIcons';

interface UnifiedPipelineFormProps {
  mode: 'create' | 'edit';
  pipeline?: Pipeline; // Required for edit mode
  onSubmit: (formData: any) => void;
  onCancel: () => void;
}

const UnifiedPipelineForm: React.FC<UnifiedPipelineFormProps> = ({ 
  mode, 
  pipeline, 
  onSubmit, 
  onCancel 
}) => {
  const [formData, setFormData] = useState({
    name: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize form with pipeline data in edit mode
  useEffect(() => {
    if (mode === 'edit' && pipeline) {
      setFormData({
        name: pipeline.name
      });
    }
  }, [mode, pipeline]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setError('Please enter a pipeline name');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (mode === 'create') {
        // Create mode
        await onSubmit({
          name: formData.name,
          description: null
        });
      } else if (mode === 'edit' && pipeline) {
        // Edit mode
        await onSubmit({
          pipelineId: pipeline.id,
          name: formData.name,
          description: null
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
        <h2 className="text-lg font-semibold text-white mb-4">
          {mode === 'create' ? 'Create New Pipeline' : 'Edit Pipeline'}
        </h2>

        {error && (
          <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-md">
            <p className="text-sm text-red-400">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-stone-300 mb-1">
              Pipeline Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
              required
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors flex items-center"
              disabled={loading}
            >
              {loading ? (
                <span className="flex items-center">
                  <DashboardIcons.Spinner className="w-4 h-4 animate-spin mr-2" />
                  {mode === 'create' ? 'Creating...' : 'Saving...'}
                </span>
              ) : (
                mode === 'create' ? 'Create Pipeline' : 'Save Changes'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UnifiedPipelineForm;
