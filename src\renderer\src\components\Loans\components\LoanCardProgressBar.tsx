import React from 'react'

interface LoanCardProgressBarProps {
  progress: number
  missedMonthsDetails: { description: string; amount: number }[]
  penalties: number
  isCreationMonth: boolean
  formatCurrency: (amount: number) => string
}

const LoanCardProgressBar: React.FC<LoanCardProgressBarProps> = ({
  progress,
  missedMonthsDetails,
  penalties,
  isCreationMonth,
  formatCurrency
}) => {
  return (
    <div className="mb-4">
      <div className="flex justify-between text-xs text-stone-300 font-medium mb-1.5">
        <div className="flex items-center gap-2">
          <span className="flex items-center">
            <span className="w-2 h-2 bg-orange-500 rounded-full mr-1"></span>Loan Progress
          </span>
        </div>
        <div
          className={`font-medium px-2 py-0.5 rounded ${
            progress === 100
              ? 'bg-green-500/20 text-green-400'
              : progress >= 75
                ? 'bg-lime-500/20 text-lime-400'
                : progress >= 50
                  ? 'bg-yellow-500/20 text-yellow-400'
                  : progress >= 25
                    ? 'bg-orange-500/20 text-orange-400'
                    : 'bg-red-500/20 text-red-400'
          }`}
        >
          {progress}%
        </div>
      </div>
      <div className="bg-stone-700/50 rounded-full h-3 w-full overflow-hidden relative">
        {/* Milestone markers */}
        <div className="absolute inset-0 flex justify-between px-[1px] py-0 pointer-events-none">
          <div className="w-px h-full bg-stone-600/70"></div>
          <div className="w-px h-full bg-stone-600/70"></div>
          <div className="w-px h-full bg-stone-600/70"></div>
          <div className="w-px h-full bg-stone-600/70"></div>
          <div className="w-px h-full bg-stone-600/70"></div>
        </div>
        <div
          className={`h-full rounded-full transition-all duration-300 ${
            progress === 100
              ? 'bg-green-500'
              : progress >= 75
                ? 'bg-gradient-to-r from-lime-500 to-green-500'
                : progress >= 50
                  ? 'bg-gradient-to-r from-yellow-500 to-lime-500'
                  : progress >= 25
                    ? 'bg-gradient-to-r from-orange-500 to-yellow-500'
                    : 'bg-gradient-to-r from-red-500 to-orange-500'
          }`}
          style={{ width: `${progress}%` }}
        >
          {progress > 10 && (
            <div className="absolute right-1 inset-y-0 flex items-center">
              <div className="h-1.5 w-1.5 rounded-full bg-white animate-pulse"></div>
            </div>
          )}
        </div>
      </div>

      {/* Progress index under the bar */}
      <div className="flex justify-between px-0.5 mt-1 text-[10px] text-stone-500">
        <div>0%</div>
        <div>25%</div>
        <div>50%</div>
        <div>75%</div>
        <div>100%</div>
      </div>

      {/* Compact indicators for missed months and penalties */}
      {(missedMonthsDetails.length > 0 || penalties > 0 || isCreationMonth) && (
        <div className="flex items-center gap-4 mt-2 text-xs">
          {missedMonthsDetails.length > 0 && (
            <div className="flex items-center text-xs">
              <span className="w-1.5 h-1.5 rounded-full bg-red-500 mr-1.5"></span>
              <span className="text-red-400 font-medium">
                {missedMonthsDetails.length === 1
                  ? '1 missed month'
                  : `${missedMonthsDetails.length} missed months`}
              </span>
            </div>
          )}

          {penalties > 0 && (
            <div className="flex items-center text-xs">
              <span className="w-1.5 h-1.5 rounded-full bg-orange-500 mr-1.5"></span>
              <span className="text-orange-400 font-medium">
                Penalties: {formatCurrency(penalties)}
              </span>
            </div>
          )}

          {isCreationMonth && (
            <div className="flex items-center text-xs">
              <span className="w-1.5 h-1.5 rounded-full bg-green-500 mr-1.5"></span>
              <span className="text-green-400 font-medium">Grace period</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default React.memo(LoanCardProgressBar)
