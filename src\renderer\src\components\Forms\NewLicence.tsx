
import { NewLicenceData } from '../../types/NewLicenceData'
import NewLicenceForm from './NewLicence/index'

// Types
interface NewLicenceFormProps {
  onSubmit: (data: NewLicenceData) => void
  templateName?: string
}

/**
 * NewLicence - New Firearm Licence Application Form Component
 *
 * This is a wrapper component that uses the refactored NewLicenceForm component
 */
export default function NewLicence({ onSubmit, templateName }: NewLicenceFormProps): JSX.Element {
  return (
    <NewLicenceForm onSubmit={onSubmit} templateName={templateName} />
  )
}
