import React from 'react'
import { DashboardIcons } from '../../components/icons/DashboardIcons'

interface PaginationProps {
  currentPage: number
  setCurrentPage: (page: number) => void
  totalItems: number
  itemsPerPage: number
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  setCurrentPage,
  totalItems,
  itemsPerPage
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage)

  return (
    <div className="flex justify-between items-center py-4">
      <button
        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className="p-2 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-stone-700
          rounded-lg transition-colors"
      >
        <DashboardIcons.PrevPage className="w-5 h-5 text-white" />
      </button>

      <span className="text-white">
        Page {currentPage} of {totalPages}
      </span>

      <button
        onClick={() => setCurrentPage(currentPage + 1)}
        disabled={currentPage >= totalPages}
        className="p-2 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-stone-700
          rounded-lg transition-colors"
      >
        <DashboardIcons.NextPage className="w-5 h-5 text-white" />
      </button>
    </div>
  )
}
