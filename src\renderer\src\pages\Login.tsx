import { useState, useEffect, useCallback } from 'react'
import '../assets/main.css'
import logo from '../assets/logo.png'
import { getOrInitSupabase, clearSupabaseInstances } from '../lib/supabase'
import { useDatabaseStatus } from '../hooks/useDatabaseStatus'
import { handleUserLogout } from '../utils/authUtils'

// Minimum time (in milliseconds) to show loading animations
const MIN_ANIMATION_TIME = 1000

// Database status indicator component
const DatabaseStatusIndicator = ({ isDatabaseOnline, isChecking }: { isDatabaseOnline: boolean | null, isChecking: boolean }) => {

  return (
    <div className="absolute bottom-12 text-center w-full">
      <div className={`flex items-center justify-center gap-2 ${isDatabaseOnline === false && !isChecking ? 'p-2 bg-red-900/20 rounded-md animate-pulse' : ''}`}>
        <div
          className={`w-2.5 h-2.5 rounded-full ${
            isChecking
              ? 'bg-stone-500'
              : isDatabaseOnline === true
                ? 'bg-green-500'
                : isDatabaseOnline === false
                  ? 'bg-red-500'
                  : 'bg-stone-500'
          } ${isDatabaseOnline === true ? 'animate-pulse' : ''}`}
          style={{ boxShadow: '0 0 5px rgba(0, 0, 0, 0.3)' }}
        />
        <span className={`text-xs ${
          isChecking
            ? 'text-stone-400'
            : isDatabaseOnline === true
              ? 'text-green-400'
              : isDatabaseOnline === false
                ? 'text-red-400 font-medium'
                : 'text-stone-400'
        }`}>
          {isChecking
            ? 'Checking database...'
            : isDatabaseOnline === true
              ? 'Database online'
              : isDatabaseOnline === false
                ? 'Database is Offline. Please try again later'
                : 'Checking database status...'}
        </span>
      </div>
    </div>
  )
}

function Login({ onLoginSuccess }: { onLoginSuccess: () => void }): React.JSX.Element {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isAuthChecking, setIsAuthChecking] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [appVersion, setAppVersion] = useState<{ version: string }>({ version: '1.0.0' })

  // Fetch app version
  useEffect(() => {
    const fetchAppVersion = async (): Promise<void> => {
      try {
        const api = window.electronAPI as any
        if (api?.getAppVersion) {
          const info = await api.getAppVersion()
          setAppVersion(info)
        }
      } catch (error) {
        console.error('Error fetching app version:', error)
      }
    }

    fetchAppVersion()
  }, [])

  // Get database status
  const { isDatabaseOnline, isChecking } = useDatabaseStatus()

  const checkAuth = useCallback(async (): Promise<void> => {
    setIsAuthChecking(true)
    setShowForm(false)
    const startTime = Date.now()

    // Skip auth check if database is offline
    // Only check if we know the database status (not null)
    if (isDatabaseOnline === false) {
      // Ensure animation shows for at least MIN_ANIMATION_TIME
      const elapsed = Date.now() - startTime
      const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

      if (remainingTime > 0) {
        setTimeout(() => {
          setIsAuthChecking(false)
          setShowForm(true)
          setError('Database is Offline. Please try again later')
        }, remainingTime)
      } else {
        setIsAuthChecking(false)
        setShowForm(true)
        setError('Database is Offline. Please try again later')
      }
      return
    }

    try {
      const supabase = await getOrInitSupabase()
      const {
        data: { session }
      } = await supabase.auth.getSession()
      if (session) {
        const { data: userData, error: userError } = await supabase
          .from('clients')
          .select('role')
          .eq('email', session.user.email)
          .single()

        if (userError || userData?.role !== 'admin') {
          try {
            await handleUserLogout()
          } catch (error) {
            console.error('Error signing out:', error)
          }
          setError('Access denied. Admin privileges required.')
        } else {
          // Ensure animation shows for at least MIN_ANIMATION_TIME before redirecting
          const elapsed = Date.now() - startTime
          const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

          if (remainingTime > 0) {
            await new Promise((resolve) => setTimeout(resolve, remainingTime))
          }

          // Notify main process that user has logged in
          const electron = window.electron as any
          if (electron?.ipcRenderer?.send) {
            electron.ipcRenderer.send('user-logged-in')
          }

          // Call onLoginSuccess callback only - remove redundant navigation
          onLoginSuccess()
          return
        }
      }
    } catch (error) {
      console.error('Auth check error:', error)
    } finally {
      // Ensure animation shows for at least MIN_ANIMATION_TIME
      const elapsed = Date.now() - startTime
      const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

      if (remainingTime > 0) {
        setTimeout(() => {
          setIsAuthChecking(false)
          setShowForm(true)
        }, remainingTime)
      } else {
        setIsAuthChecking(false)
        setShowForm(true)
      }
    }
  }, [onLoginSuccess, isDatabaseOnline])

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  const handleLogin = useCallback(async (): Promise<void> => {
    setIsLoading(true)
    const startTime = Date.now()

    // Check if database is offline before attempting login
    // Only check if we know the database status (not null)
    if (isDatabaseOnline === false) {
      setError('Database is Offline. Please try again later')

      // Ensure animation shows for at least MIN_ANIMATION_TIME
      const elapsed = Date.now() - startTime
      const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

      if (remainingTime > 0) {
        setTimeout(() => setIsLoading(false), remainingTime)
      } else {
        setIsLoading(false)
      }
      return
    }

    try {
      const supabase = await getOrInitSupabase()
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (signInError) {
        const errorMessage =
          signInError.message === 'Why no Email Hmmmm??' ? 'Missing email' : signInError.message
        setError(errorMessage)
        setMessage('')

        // Ensure animation shows for at least MIN_ANIMATION_TIME
        const elapsed = Date.now() - startTime
        const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

        if (remainingTime > 0) {
          setTimeout(() => setIsLoading(false), remainingTime)
        } else {
          setIsLoading(false)
        }
        return
      }

      // Check if user is admin before proceeding
      const { data: userData, error: userError } = await supabase
        .from('clients')
        .select('role')
        .eq('email', email)
        .single()

      if (userError || !userData) {
        setError('Error verifying user permissions')
        try {
          await handleUserLogout()
        } catch (error) {
          console.error('Error signing out:', error)
        }

        // Ensure animation shows for at least MIN_ANIMATION_TIME
        const elapsed = Date.now() - startTime
        const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

        if (remainingTime > 0) {
          setTimeout(() => setIsLoading(false), remainingTime)
        } else {
          setIsLoading(false)
        }
        return
      }

      if (userData.role !== 'admin') {
        setError('Access denied. Admin privileges required.')
        try {
          await handleUserLogout()
        } catch (error) {
          console.error('Error signing out:', error)
        }

        // Ensure animation shows for at least MIN_ANIMATION_TIME
        const elapsed = Date.now() - startTime
        const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

        if (remainingTime > 0) {
          setTimeout(() => setIsLoading(false), remainingTime)
        } else {
          setIsLoading(false)
        }
        return
      }

      // Ensure animation shows for at least MIN_ANIMATION_TIME before redirecting
      const elapsed = Date.now() - startTime
      const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

      if (remainingTime > 0) {
        await new Promise((resolve) => setTimeout(resolve, remainingTime))
      }

      // Notify main process that user has logged in
      const electron = window.electron as any
      if (electron?.ipcRenderer?.send) {
        electron.ipcRenderer.send('user-logged-in')
      }

      // Call onLoginSuccess callback only - remove redundant navigation
      onLoginSuccess()
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Login error:', err)

      // Ensure animation shows for at least MIN_ANIMATION_TIME
      const elapsed = Date.now() - startTime
      const remainingTime = Math.max(0, MIN_ANIMATION_TIME - elapsed)

      if (remainingTime > 0) {
        setTimeout(() => setIsLoading(false), remainingTime)
      } else {
        setIsLoading(false)
      }
    }
  }, [email, password, onLoginSuccess, isDatabaseOnline])

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault()
      handleLogin()
    },
    [handleLogin]
  )

  return (
    <div className="flex flex-col items-center justify-center h-full bg-gradient-to-b from-stone-900 via-stone-900 to-stone-950 app-drag-region">
      {/* Window controls in top-right corner */}
      <div className="absolute top-0 right-0 flex items-center h-10 app-no-drag z-10">
        <button
          onClick={() => window.electronAPI?.windowMinimize()}
          className="flex items-center justify-center w-10 h-10 hover:bg-stone-800 transition-colors"
          aria-label="Minimize"
        >
          <svg width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
            <rect x="1" y="6" width="10" height="1" fill="white" />
          </svg>
        </button>
        <button
          onClick={() => window.electronAPI?.windowClose()}
          className="flex items-center justify-center w-10 h-10 hover:bg-red-600 transition-colors"
          aria-label="Close"
        >
          <svg width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
            <line x1="1" y1="1" x2="11" y2="11" stroke="white" strokeWidth="1" />
            <line x1="1" y1="11" x2="11" y2="1" stroke="white" strokeWidth="1" />
          </svg>
        </button>
      </div>

      <div className="relative mb-12 group">
        <div className="absolute -inset-1 bg-gradient-to-r from-orange-600 to-orange-900 rounded-full blur opacity-20 group-hover:opacity-30 transition duration-1000 group-hover:duration-200" />
        <img
          src={logo}
          alt="Firearm Studio Logo"
          className={`relative h-32 mx-auto drop-shadow-2xl ${isAuthChecking || isLoading ? 'animate-spin' : ''}`}
          style={{ animationDuration: '4s' }}
        />
      </div>

      {/* Login form that slides up when ready */}
      {showForm && !isLoading && (
        <div className="w-full max-w-sm animate-slideUp">
          <form onSubmit={handleSubmit} className="space-y-5">
            <div className="text-center">
              <h2 className="text-2xl font-bold tracking-tight text-white">Welcome back</h2>
              <p className="text-sm text-stone-400 mb-5">Please sign in to your account</p>
            </div>

            <div className="mb-3 mx-auto max-w-xs app-no-drag">
              <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 text-sm rounded-md bg-stone-800/20 border border-stone-700/20 text-white placeholder-stone-400 focus:outline-none focus:ring-2 focus:ring-orange-500/30 focus:border-transparent transition-all"
              />
            </div>
            <div className="mb-5 mx-auto max-w-xs app-no-drag">
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 text-sm rounded-md bg-stone-800/20 border border-stone-700/20 text-white placeholder-stone-400 focus:outline-none focus:ring-2 focus:ring-orange-500/30 focus:border-transparent transition-all"
              />
            </div>
            <div className="mx-auto max-w-xs app-no-drag">
              <button
                type="submit"
                disabled={isDatabaseOnline === false}
                className={`w-full py-2 px-3 text-sm rounded-md font-medium shadow-sm transition-all duration-200 transform ${
                  isDatabaseOnline === false
                    ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:from-orange-600 hover:to-orange-700 hover:scale-[1.02] active:scale-[0.98]'
                }`}
              >
                {isDatabaseOnline === false ? 'Database is Offline' : 'Sign in'}
              </button>
            </div>
          </form>

          {error && (
            <div className="mt-4 text-center">
              <p className="text-sm text-red-400">{error}</p>
            </div>
          )}
          {message && (
            <div className="mt-4 text-center">
              <p className="text-sm text-green-400">{message}</p>
            </div>
          )}
        </div>
      )}

      {/* Status text during checking/loading */}
      {(isAuthChecking || isLoading) && (
        <div className="text-center">
          <p className="mt-6 text-stone-400 text-sm font-medium tracking-wide">
            {isAuthChecking ? 'Checking credentials' : 'Signing in'}
            <span className="animate-pulse">...</span>
          </p>
        </div>
      )}

      {/* Database status indicator */}
      <DatabaseStatusIndicator isDatabaseOnline={isDatabaseOnline} isChecking={isChecking} />

      {/* App version display at bottom */}
      <div className="absolute bottom-3 text-center w-full">
        <p className="text-stone-500 text-xs flex justify-center items-center">
          <span>
            Version{' '}
            {appVersion.version.startsWith('v')
              ? appVersion.version.substring(1)
              : appVersion.version}
          </span>
          {(appVersion.version.includes('beta') ||
            appVersion.version.includes('alpha') ||
            appVersion.version.includes('rc')) && (
            <span className="ml-1 px-1 py-0.5 bg-orange-500 text-white text-[10px] rounded-sm">
              BETA
            </span>
          )}
        </p>
      </div>
    </div>
  )
}

export default Login
