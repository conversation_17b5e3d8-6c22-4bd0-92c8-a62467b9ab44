import React from 'react'
import { RenewLicenceData } from '../../../../types/RenewLicenceData'
import { FormField, RadioGroup, CheckboxGroup, FormSection } from '../../../FormComponents'

interface SafeStorageProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  className?: string
}

const SafeStorage: React.FC<SafeStorageProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    updateFormData({
      [name]: checked
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Safe Storage</h3>
      <FormSection
        title="Safe Storage"
        subtitle="Please provide information about your firearm safe storage"
      >
        <div className="space-y-4">
          <div className="mb-4">
            <RadioGroup
              name="safe"
              value={formData.safeYes ? 'yes' : 'no'}
              onChange={(value) => {
                updateFormData({
                  safeYes: value === 'yes',
                  safeNo: value !== 'yes'
                })
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Do you have a safe for your firearm?"
            />
          </div>

          {formData.safeYes && (
            <>
              <div className="mb-4">
                <h3 className="text-lg font-semibold mb-2">Safe Type</h3>
                <CheckboxGroup
                  options={[
                    { name: 'safeH', label: 'Handgun Safe', checked: formData.safeH },
                    { name: 'safeR', label: 'Rifle Safe', checked: formData.safeR },
                    { name: 'safeS', label: 'Strongroom', checked: formData.safeS },
                    { name: 'safeD', label: 'Device', checked: formData.safeD }
                  ]}
                  onChange={handleCheckboxChange}
                />
              </div>

              {formData.safeS && (
                <FormField
                  label="Strongroom Information"
                  name="safeSe"
                  value={formData.safeSe}
                  onChange={handleChange}
                  placeholder="Provide details about the strongroom"
                  type="textarea"
                  rows={2}
                />
              )}

              {formData.safeD && (
                <FormField
                  label="Device Information"
                  name="safeDInfo"
                  value={formData.safeDInfo}
                  onChange={handleChange}
                  placeholder="Provide details about the device"
                  type="textarea"
                  rows={2}
                />
              )}

              <div className="mb-4">
                <RadioGroup
                  name="safeMounted"
                  value={formData.safeMountYes ? 'yes' : 'no'}
                  onChange={(value) => {
                    updateFormData({
                      safeMountYes: value === 'yes',
                      safeMountNo: value !== 'yes'
                    })
                  }}
                  options={[
                    { value: 'yes', label: 'Yes' },
                    { value: 'no', label: 'No' }
                  ]}
                  label="Is the safe mounted?"
                />
              </div>

              {formData.safeMountYes && (
                <div className="ml-6 border-l-2 border-orange-500 pl-4">
                  <CheckboxGroup
                    options={[
                      { name: 'safeWall', label: 'Wall', checked: formData.safeWall },
                      { name: 'safeFloor', label: 'Floor', checked: formData.safeFloor }
                    ]}
                    title="Where is it mounted?"
                    onChange={handleCheckboxChange}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default SafeStorage
