import React, { useState, useEffect, useRef, memo, useCallback } from 'react'
import { getSupabase } from '../../lib/supabase'
import { validateSouthAfricanID } from '../../utils/idNumberValidator'
import { FormField } from '../../components/FormComponents'
import { DashboardIcons } from '../../components/icons/DashboardIcons'
import { AlertTriangle, User, Phone, MapPin, FileText } from 'lucide-react'
import RadioGroup from './common/RadioGroup'
import { FormErrorBoundary } from '../../components/ErrorBoundary'

interface Client {
  id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  postal_code: string
  id_number: string
  id_type?: 'sa' | 'foreign' | string
}

interface ClientFormProps {
  client?: Client | null
  onClose: () => void
  onSuccess: () => void
}

interface ClientFormState {
  first_name: string
  last_name: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  postal_code: string
  id_number: string
  id_type: 'sa' | 'foreign' | ''
}

export default memo(function ClientForm({
  client,
  onClose,
  onSuccess
}: ClientFormProps): JSX.Element {
  const [formData, setFormData] = useState<ClientFormState>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    id_number: '',
    id_type: 'sa' // Default to South African ID
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const addressInputRef = useRef<HTMLInputElement>(null)

  // Prevent form submission on enter key for address input
  useEffect(() => {
    if (!addressInputRef.current) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter') e.preventDefault();
    };

    addressInputRef.current.addEventListener('keydown', handleKeyDown);

    return () => {
      if (addressInputRef.current) {
        addressInputRef.current.removeEventListener('keydown', handleKeyDown);
      }
    };
  }, []);

  useEffect(() => {
    // Initialize form data based on whether we're editing or creating
    setFormData({
      first_name: client?.first_name || '',
      last_name: client?.last_name || '',
      email: client?.email || '',
      phone: client?.phone || '',
      address: client?.address || '',
      city: client?.city || '',
      state: client?.state || '',
      postal_code: client?.postal_code || '',
      id_number: client?.id_number || '',
      // If editing a client with an ID number, determine ID type based on whether it's a valid SA ID
      // Otherwise, default to South African ID
      id_type: client?.id_number ? (validateSouthAfricanID(client.id_number) ? 'sa' : 'foreign') : 'sa'
    })
  }, [client])

  // No debounced validation - only validate on form submission

  // Handler for ID type radio buttons
  const handleIdTypeChange = useCallback((value: string): void => {
    setFormData((prev) => ({
      ...prev,
      id_type: value as 'sa' | 'foreign'
    }))
    // No need to clear errors as we only validate on form submission
  }, [])

  // Enhanced validation - only called when the form is submitted
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {}

    // Required fields validation
    if (!formData.first_name.trim()) {
      newErrors.first_name = 'First name is required'
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Last name is required'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!/^(\+27|0)[6-8][0-9]{8}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid South African phone number'
    }

    // Optional fields validation - only validate if they have a value
    if (
      formData.email &&
      !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(formData.email)
    ) {
      newErrors.email = 'Please enter a valid email address'
    }

    // ID number validation based on type (front-end validation only) - only if ID is provided
    if (formData.id_number) {
      if ((formData.id_type === 'sa' || !formData.id_type) && !validateSouthAfricanID(formData.id_number)) {
        // Validate South African ID format (default if id_type is not specified)
        newErrors.id_number = 'Please enter a valid South African ID number'
      } else if (formData.id_type === 'foreign' && formData.id_number.length < 3) {
        // Basic validation for foreign IDs - ensure it's at least 3 characters
        newErrors.id_number = 'Foreign ID/Passport number must be at least 3 characters'
      }
    }
    // Note: id_type is only used for validation and is not stored in the database

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData])

  // ID validation is now handled by the imported validateSouthAfricanID function

  const handleClose = useCallback((): void => {
    setFormData({
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      state: '',
      postal_code: '',
      id_number: '',
      id_type: 'sa' // Reset to South African ID
    })
    setError(null)
    setErrors({})
    onClose()
  }, [onClose])

  // No longer need the generateDefaultPassword function since we're not creating auth users

  const handleSubmit = useCallback(
    async (e: React.FormEvent): Promise<void> => {
      e.preventDefault()
      setError(null)
      setErrors({}) // Clear any previous errors

      // Validate the form only when the Save button is clicked
      if (!validateForm()) {
        return // Stop submission if validation fails
      }

      setLoading(true)

      try {
        // Check for duplicates before proceeding (only if email or ID is provided)
        if (formData.email || formData.id_number) {
          const noDuplicates = await checkForDuplicates()
          if (!noDuplicates) {
            setLoading(false)
            return
          }
        }

        const supabase = getSupabase()

        // Prepare the data to be submitted
        // Both SA ID and Foreign ID/Passport will use the same id_number field
        // Remove id_type as it's only for front-end validation
        const { id_type, ...submissionData } = formData

        if (client?.id) {
          // Update existing client - id_type is already removed from submissionData
          const { error } = await supabase.from('clients').update(submissionData).eq('id', client.id)
          if (error) throw error
        } else {
          // Add new client directly to the clients table without creating auth user
          const { error: clientError } = await supabase.from('clients').insert([submissionData])

          if (clientError) {
            throw new Error(`Failed to create client record: ${clientError.message}`)
          }

          // Show success message
          alert('Client created successfully!')
        }
        onSuccess()
        handleClose()
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred')
      } finally {
        setLoading(false)
      }
    },
    [client, formData, onClose, onSuccess, validateForm]
  )

  const checkForDuplicates = async (): Promise<boolean> => {
    // Skip duplicate check when editing existing client
    if (client?.id) return true

    try {
      // Check for duplicate email only if email is provided
      if (formData.email) {
        const { data: emailData, error: emailError } = await getSupabase()
          .from('clients')
          .select('id')
          .eq('email', formData.email)
          .maybeSingle()

        if (emailError) throw emailError
        if (emailData) {
          setErrors((prev) => ({
            ...prev,
            email: 'This email is already registered'
          }))
          return false
        }
      }

      // Check for duplicate ID number only if ID is provided
      // Both SA and foreign IDs are stored in the same id_number column
      // id_type is not stored in the database, only used for front-end validation
      if (formData.id_number) {
        const { data: idData, error: idError } = await getSupabase()
          .from('clients')
          .select('id')
          .eq('id_number', formData.id_number)
          .maybeSingle()

        if (idError) throw idError
        if (idData) {
          setErrors((prev) => ({
            ...prev,
            id_number: 'This ID number is already registered'
          }))
          return false
        }
      }

      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error checking for duplicates')
      return false
    }
  }

  // This form uses a fixed width, no need for responsive dimensions

  const isEditing = !!client;

  // Handle input changes for all fields
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Convert text inputs to uppercase
    const uppercaseValue = typeof value === 'string' ? value.toUpperCase() : value;

    setFormData(prev => ({
      ...prev,
      [name]: uppercaseValue
    }));
  }, []);

  // Handle form errors
  const handleFormError = (error: Error) => {
    console.error('Error in client form:', error);
    // You could also report this error to a monitoring service
  };

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-black/80 via-stone-900/90 to-stone-800/90 flex items-center justify-center z-50 p-3 overflow-y-auto">
      <div className="bg-stone-800/95 rounded-2xl shadow-2xl border border-orange-400/10 w-full max-w-xl relative max-h-[90vh] backdrop-blur-xl">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-stone-400 hover:text-white focus:outline-none focus:text-white transition-colors z-10"
          aria-label="Close form"
        >
          <DashboardIcons.Close className="w-4 h-4" />
        </button>

        <div className="p-4 overflow-y-auto max-h-[90vh] flex flex-col gap-3">
          <h1 className="text-xl font-extrabold text-white mb-2 flex items-center gap-2 tracking-tight drop-shadow-lg">
            <span className="inline-flex items-center justify-center bg-gradient-to-tr from-orange-500/80 to-orange-400/70 rounded-full p-1.5 shadow-md">
              <User className="w-5 h-5 text-white" />
            </span>
            {isEditing ? 'Edit Client' : 'Add New Client'}
          </h1>

          <FormErrorBoundary
            formName={isEditing ? 'Edit Client' : 'Add New Client'}
            onError={handleFormError}
            resetKeys={[client?.id]}
          >
            <form onSubmit={handleSubmit} className="overflow-y-auto">
              {error && (
                <div className="flex items-center gap-1.5 bg-red-600/20 text-red-200 border border-red-500/40 p-1.5 rounded-lg mb-1.5 text-xs animate-fade-in shadow">
                  <AlertTriangle className="w-3.5 h-3.5 text-red-300" />
                  <span>{error}</span>
                </div>
              )}

            <div className="grid grid-cols-1 gap-3">
              {/* Personal Information */}
              <div>
                <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                  <User className="w-3.5 h-3.5 text-orange-400" />
                  Personal Information
                </h2>
                <div className="grid grid-cols-2 gap-2">
                  <FormField
                    label="First Name"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleChange}
                    required
                    error={errors.first_name}
                    inputClassName="premium-field"
                  />
                  <FormField
                    label="Last Name"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleChange}
                    required
                    error={errors.last_name}
                    inputClassName="premium-field"
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                  <Phone className="w-3.5 h-3.5 text-orange-400" />
                  Contact Information
                </h2>
                <div className="grid grid-cols-2 gap-2">
                  <FormField
                    label="Phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    error={errors.phone}
                    inputClassName="premium-field"
                  />
                  <FormField
                    label="Email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    error={errors.email}
                    inputClassName="premium-field"
                    additionalContent={
                      <p className="text-xs text-stone-400 mt-1">
                        Optional - enter a valid email address
                      </p>
                    }
                  />
                </div>
              </div>

              {/* Address Information */}
              <div>
                <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                  <MapPin className="w-3.5 h-3.5 text-orange-400" />
                  Address Information
                </h2>
                <div className="grid grid-cols-1 gap-2">
                  <div>
                    <label className="block text-xs font-medium text-stone-300 mb-0.5">
                      Address <span className="text-stone-400 text-xs">(Optional)</span>
                    </label>
                    <input
                      type="text"
                      ref={addressInputRef}
                      id="address"
                      name="address"
                      value={formData.address}
                      onChange={handleChange}
                      className="w-full bg-stone-700/80 border border-orange-400/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500/60 focus:border-orange-400 transition-all duration-150 text-xs shadow-sm premium-field"
                      placeholder="Enter Address"
                      autoComplete="off"
                    />
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <FormField
                      label="City"
                      name="city"
                      value={formData.city}
                      onChange={handleChange}
                      error={errors.city}
                      inputClassName="premium-field"
                    />
                    <FormField
                      label="Province"
                      name="state"
                      value={formData.state}
                      onChange={handleChange}
                      error={errors.state}
                      inputClassName="premium-field"
                    />
                    <FormField
                      label="Postal Code"
                      name="postal_code"
                      value={formData.postal_code}
                      onChange={handleChange}
                      error={errors.postal_code}
                      inputClassName="premium-field"
                    />
                  </div>
                </div>
              </div>

              {/* ID Information */}
              <div>
                <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                  <FileText className="w-3.5 h-3.5 text-orange-400" />
                  Identification
                </h2>
                <div className="grid grid-cols-1 gap-2">
                  <div>
                    <label className="block text-xs font-medium text-stone-300 mb-0.5">ID Type</label>
                    <RadioGroup
                      name="id_type"
                      value={formData.id_type || 'sa'} /* Ensure SA ID is selected by default */
                      onChange={handleIdTypeChange}
                      options={[
                        { value: 'sa', label: 'South African ID' },
                        { value: 'foreign', label: 'Foreign ID / Passport' }
                      ]}
                      required
                    />
                  </div>
                  <FormField
                    label={(formData.id_type === 'sa' || !formData.id_type) ? 'South African ID Number' : 'Foreign ID / Passport Number'}
                    name="id_number"
                    value={formData.id_number}
                    onChange={handleChange}
                    error={errors.id_number}
                    placeholder={(formData.id_type === 'sa' || !formData.id_type) ? 'e.g. 8001015009087' : 'Enter ID or passport number'}
                    inputClassName="premium-field"
                    additionalContent={
                      <p className="text-xs text-stone-400 mt-1">
                        {(formData.id_type === 'sa' || !formData.id_type)
                          ? 'Optional - enter your 13-digit South African ID number'
                          : 'Optional - enter your foreign ID or passport number'}
                      </p>
                    }
                  />
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-2 mt-1">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-3 py-1.5 bg-stone-700 hover:bg-stone-600 text-white rounded-lg text-xs font-semibold shadow transition-all duration-150 active:scale-95 focus:ring-2 focus:ring-orange-400/50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-3 py-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-lg flex items-center gap-2 text-xs font-semibold shadow-lg transition-all duration-150 active:scale-95 focus:ring-2 focus:ring-orange-400/50 disabled:opacity-60 disabled:cursor-not-allowed"
                >
                  {loading && <DashboardIcons.Spinner className="w-3.5 h-3.5 animate-spin" />}
                  {isEditing ? 'Update Client' : 'Add Client'}
                </button>
              </div>
            </div>
          </form>
          </FormErrorBoundary>

          {/* Premium input field style override */}
          <style>{`
            .premium-field {
              background-color: rgba(68, 64, 60, 0.8);
              border: 1px solid rgba(251, 146, 60, 0.2);
              border-radius: 0.5rem;
              padding: 0.5rem 0.75rem;
              color: white;
              font-size: 0.875rem;
              box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
              transition-property: all;
              transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
              transition-duration: 150ms;
            }
            .premium-field:focus {
              outline: none;
              --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
              --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) rgba(251, 146, 60, 0.6);
              box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
              border-color: rgb(251, 146, 60);
            }
          `}</style>
        </div>
      </div>
    </div>
  )
})
