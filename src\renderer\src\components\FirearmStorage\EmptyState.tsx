import React from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'

interface EmptyStateProps {
  searchQuery: string
  activeFilter: string
  onAddFirearm: () => void
}

const EmptyState: React.FC<EmptyStateProps> = ({ searchQuery, activeFilter, onAddFirearm }) => {
  // Different messages based on whether we're searching or filtering
  const isSearching = searchQuery.length > 0
  const isFiltering = activeFilter !== 'all'

  let title = 'No firearms found'
  let message = 'Get started by adding your first firearm to the storage system.'
  let icon = <DashboardIcons.Firearm className="w-16 h-16 text-stone-600" />

  if (isSearching) {
    title = 'No results found'
    message = `We couldn't find any firearms matching "${searchQuery}". Try a different search term or clear your search.`
    icon = <DashboardIcons.Search className="w-16 h-16 text-stone-600" />
  } else if (isFiltering) {
    title = 'No firearms in this category'
    message = 'Try a different filter or add a new firearm.'
    icon = <DashboardIcons.Filter className="w-16 h-16 text-stone-600" />
  }

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
      {icon}
      <h3 className="mt-4 text-xl font-semibold text-white">{title}</h3>
      <p className="mt-2 text-stone-400 max-w-md">{message}</p>
      
      <button
        onClick={onAddFirearm}
        className="mt-6 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
          text-white px-4 py-2 rounded-lg shadow-md shadow-orange-500/20
          transition-all duration-200 flex items-center gap-2"
      >
        <DashboardIcons.Add className="w-4 h-4" />
        <span>Add Firearm</span>
      </button>
    </div>
  )
}

export default EmptyState
