import React, { useState, useEffect, useRef } from 'react';
import { Deal } from '../../../types/pipedrive';
import { DashboardIcons } from '../../icons/DashboardIcons';
import DocumentUpload from '../../Pipedrive/DocumentUpload';
import DocumentList, { DocumentListRef } from '../DocumentList';
import NotesDisplay from '../NotesDisplay';

interface DealEditFormProps {
  deal: Deal;
  onSubmit: (formData: { dealId: string; title: string; notes: string | null }) => void;
  onCancel: () => void;
  onUploadDocument?: (dealId: string, file: File) => Promise<void>;
}

const DealEditForm: React.FC<DealEditFormProps> = ({ deal, onSubmit, onCancel, onUploadDocument }) => {
  const [formData, setFormData] = useState({
    title: '',
    notes: ''
  });
  const [newNote, setNewNote] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDocuments, setShowDocuments] = useState(false);
  const [showNotesEditor, setShowNotesEditor] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const documentListRef = useRef<DocumentListRef>(null);

  // Initialize form with deal data
  useEffect(() => {
    setFormData({
      title: deal.title,
      notes: deal.notes || ''
    });
  }, [deal]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle adding a new note
  const handleAddNote = () => {
    if (!newNote.trim()) return;

    const dateStamp = formatDateStamp();
    const stampedNote = `[${dateStamp}] ${newNote.trim()}`;

    // Append the new note to existing notes
    const updatedNotes = formData.notes
      ? `${formData.notes}\n\n${stampedNote}`
      : stampedNote;

    setFormData(prev => ({ ...prev, notes: updatedNotes }));
    setNewNote(''); // Clear the new note input
  };

  // Handle document upload
  const handleUploadDocument = async (dealId: string, file: File) => {
    if (!onUploadDocument) return;

    try {
      await onUploadDocument(dealId, file);
      setUploadSuccess(true);

      // Refresh the document list after successful upload
      if (documentListRef.current) {
        documentListRef.current.refreshDocuments();
      }

      return true;
    } catch (error) {
      console.error('Error uploading document:', error);
      setError(error instanceof Error ? error.message : 'Failed to upload document');
      return false;
    }
  };

  // Format the current date for the date stamp
  const formatDateStamp = () => {
    const now = new Date();
    return now.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Add date stamp to notes
  const addDateStampToNotes = (notes: string, originalNotes: string | null) => {
    if (!notes.trim()) return null;

    // If notes haven't changed, don't add a new date stamp
    if (notes === originalNotes) return notes;

    const dateStamp = formatDateStamp();

    // Check if the notes already have a date stamp
    const dateStampRegex = /^\[.*?\]/;
    if (dateStampRegex.test(notes.trim())) {
      // Notes already have a date stamp, don't add another one
      return notes;
    }
    // If there are already notes, add the new note with a date stamp
    if (originalNotes && originalNotes.trim()) {
      // Check if the new notes contain the original notes
      if (notes.includes(originalNotes)) {
        // Extract only the new content
        const newContent = notes.trim() === originalNotes.trim()
          ? ''
          : notes.replace(originalNotes, '').trim();

        if (newContent) {
          return `${originalNotes}\n\n[${dateStamp}] ${newContent}`;
        } else {
          return originalNotes; // No new content added
        }
      } else {
        // If the notes have been completely changed, treat as new notes
        return `[${dateStamp}] ${notes}`;
      }
    }

    // If this is a new note, just add the date stamp
    return `[${dateStamp}] ${notes}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      setError('Please enter a deal title');
      return;
    }

    try {
      setLoading(true);

      // Add date stamp to notes if they've changed
      const notesWithDateStamp = addDateStampToNotes(formData.notes, deal.notes);

      await onSubmit({
        dealId: deal.id,
        title: formData.title,
        notes: notesWithDateStamp
      });

      showToast('Deal updated successfully');

      // Show document upload section after successful update
      setShowDocuments(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const showToast = (message: string) => {
    // This is just a placeholder for now
    console.log(message);
  };

  return (
    <>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Edit Deal</h2>

          {error && (
            <div className="mb-4 p-3 bg-red-900/30 border border-red-800 rounded-md">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={(e) => {
              // Only submit the form if we're not in document management mode
              if (showDocuments) {
                e.preventDefault();
              } else {
                handleSubmit(e);
              }
            }}>
            {/* Display client info (read-only) */}
            {deal.client && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-stone-300 mb-1">
                  Client
                </label>
                <div className="p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                  <div className="space-y-2">
                    {/* Client Full Name */}
                    <div>
                      <h3 className="font-semibold text-white text-sm">
                        {deal.client.first_name.toUpperCase()} {deal.client.last_name.toUpperCase()}
                      </h3>
                    </div>

                    {/* Contact Number */}
                    <div className="flex">
                      <span className="text-stone-400 text-xs w-20">Contact:</span>
                      <a
                        href={`tel:${deal.client.phone}`}
                        className="text-orange-400 hover:text-orange-300 text-xs"
                      >
                        {deal.client.phone}
                      </a>
                    </div>

                    {/* Email Address */}
                    <div className="flex">
                      <span className="text-stone-400 text-xs w-20">Email:</span>
                      <a
                        href={`mailto:${deal.client.email}`}
                        className="text-orange-400 hover:text-orange-300 text-xs truncate"
                      >
                        {deal.client.email}
                      </a>
                    </div>

                    {/* ID Number (for reference) */}
                    <div className="flex">
                      <span className="text-stone-400 text-xs w-20">ID Number:</span>
                      <span className="text-stone-300 text-xs">{deal.client.id_number}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="mb-4">
              <label htmlFor="title" className="block text-sm font-medium text-stone-300 mb-1">
                Deal Title
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                required
              />
            </div>

            <div className="mb-4">
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium text-stone-300">
                  Notes History
                </label>
                <button
                  type="button"
                  onClick={() => setShowNotesEditor(true)}
                  className="text-xs text-orange-400 hover:text-orange-300"
                >
                  Edit All Notes
                </button>
              </div>

              {/* Display existing notes in a formatted way */}
              <NotesDisplay notes={formData.notes} />

              {/* Add new note section */}
              <div className="mt-3">
                <label htmlFor="newNote" className="block text-sm font-medium text-stone-300 mb-1">
                  Add New Note
                </label>
                <div className="flex">
                  <input
                    type="text"
                    id="newNote"
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    placeholder="Type a new note here..."
                    className="flex-1 bg-stone-700 border border-stone-600 rounded-l-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleAddNote();
                      }
                    }}
                  />
                  <button
                    type="button"
                    onClick={handleAddNote}
                    disabled={!newNote.trim()}
                    className="bg-orange-500 hover:bg-orange-600 disabled:bg-stone-600 disabled:text-stone-400 text-white px-3 py-2 rounded-r-md transition-colors"
                  >
                    Add
                  </button>
                </div>
                <p className="text-xs text-stone-500 mt-1">
                  Press Enter to add a note. A date stamp will be added automatically.
                </p>
              </div>

              {/* Hidden input for notes value */}
              <input
                type="hidden"
                id="notes"
                name="notes"
                value={formData.notes}
              />
            </div>

            {/* Document Upload Section */}
            {showDocuments ? (
              <div className="mb-6">
                <DocumentUpload
                  dealId={deal.id}
                  onUpload={handleUploadDocument}
                />
                {uploadSuccess && (
                  <p className="mt-2 text-xs text-green-400 text-center">
                    Document uploaded successfully!
                  </p>
                )}

                {/* Document List with delete functionality */}
                <DocumentList ref={documentListRef} dealId={deal.id} />

                <div className="mt-4 flex justify-end">
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
                  >
                    Done
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-1">
                    <label className="block text-sm font-medium text-stone-300">
                      Documents
                    </label>
                    <button
                      type="button"
                      onClick={() => setShowDocuments(true)}
                      className="text-xs text-orange-400 hover:text-orange-300"
                    >
                      Manage Documents
                    </button>
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
                    disabled={loading}
                  >
                    {loading ? (
                      <span className="flex items-center">
                        <DashboardIcons.Spinner className="w-4 h-4 animate-spin mr-2" />
                        Saving...
                      </span>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                </div>
              </>
            )}
          </form>
        </div>
      </div>

      {/* Notes Editor Modal */}
      {showNotesEditor && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[60]">
          <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Edit All Notes</h3>
              <button
                type="button"
                onClick={() => setShowNotesEditor(false)}
                className="text-stone-400 hover:text-stone-300"
              >
                <DashboardIcons.Close className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-4">
              <p className="text-xs text-stone-400 mb-2">
                Edit the notes below. Date stamps will be preserved.
              </p>
              <textarea
                value={formData.notes}
                onChange={handleChange}
                name="notes"
                rows={10}
                className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowNotesEditor(false)}
                className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => setShowNotesEditor(false)}
                className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
              >
                Save Notes
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DealEditForm;
