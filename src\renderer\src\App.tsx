/// <reference types="../../preload" />
import { useState, useEffect } from 'react'
import { HashRouter as Router, Route, Routes, Navigate } from 'react-router-dom'
import Login from './pages/Login'
import { getOrInitSupabase } from './lib/supabase'
import Dashboard from './pages/Dashboard'
import ExpiredLicenses from './pages/ExpiredLicenses'
import LoadingScreen from './components/LoadingScreen'
import LicensesPage from './pages/LicensesPage'
import DocScript from './pages/DocScript'
import Loans from './pages/Loans'
import Settings from './pages/Settings'
import FirearmStorage from './pages/FirearmStorage'
import Pipedrive from './pages/Pipedrive'
import { useNetworkStatus } from './hooks/useNetworkStatus'
import TitleBar from './components/TitleBar'
import { ConnectionDialog } from './components/ConnectionDialog'
import WindowSizeProvider from './contexts/WindowSizeContext'
import { LogoutProvider } from './contexts/LogoutContext'
import { PageErrorBoundary } from './components/ErrorBoundary'

// Define update status type
type UpdateStatus = {
  status: string
  data?: unknown
}

function App(): React.JSX.Element {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [, setIsLoading] = useState(false)
  const [updateStatus, setUpdateStatus] = useState<UpdateStatus | null>(null)
  const [isCheckingUpdate, setIsCheckingUpdate] = useState(true)
  const [, setIsInitialized] = useState(false)

  // Use the network status hook
  const { isOnline, showOfflineDialog, checkConnection } = useNetworkStatus()

  // Handle update status messages
  useEffect(() => {
    if (!window.electron || process.env.NODE_ENV === 'development') {
      console.log('Development mode or electron not available - skipping update check')
      setIsCheckingUpdate(false)
      setUpdateStatus({ status: 'update-not-available' })
      return () => undefined
    }

    const handleUpdateStatus = (...args: unknown[]): void => {
      const status = args[0] as string
      const data = args[1]
      console.log('Update status received:', status, data)
      setUpdateStatus({ status, data })

      // Only proceed when we have a definitive status
      switch (status) {
        case 'update-not-available':
          console.log('No update available, proceeding with app initialization')
          setIsCheckingUpdate(false)
          break
        case 'update-error':
          console.error('Update check failed:', data)
          // In case of error, proceed with app initialization
          setIsCheckingUpdate(false)
          break
        case 'checking-for-update':
          console.log('Checking for updates...')
          break
        case 'update-available':
          console.log('Update available, waiting for download')
          break
        case 'download-progress':
          console.log('Download progress:', data)
          break
        case 'update-downloaded':
          console.log('Update downloaded, will install on restart')
          break
        default:
          console.log('Unknown update status:', status)
          // For unknown status, proceed with app initialization
          setIsCheckingUpdate(false)
      }
    }

    if (window.electron?.ipcRenderer) {
      // Listen for all update-related events
      window.electron.ipcRenderer.on('update-status', handleUpdateStatus)

      // Trigger update check
      console.log('Initiating update check...')
      window.electron.ipcRenderer.send('check-for-updates')

      return () => {
        if (window.electron?.ipcRenderer) {
          window.electron.ipcRenderer.removeListener('update-status', handleUpdateStatus)
        }
      }
    } else {
      console.warn('IPC renderer not available, skipping update check')
      setIsCheckingUpdate(false)
      setUpdateStatus({ status: 'update-not-available' })
      return () => undefined
    }
  }, [])

  // Initialize authentication only after update check is complete
  useEffect(() => {
    let subscription: { unsubscribe: () => void } | null = null

    const init = async (): Promise<void> => {
      if (isCheckingUpdate) return // Wait for update check to complete

      try {
        const supabase = await getOrInitSupabase()
        const {
          data: { session }
        } = await supabase.auth.getSession()

        const handleAuthChange = async (_event: string, session: any): Promise<void> => {
          const isAuth = !!session
          setIsAuthenticated(isAuth)

          try {
            if (isAuth) {
              window.electronAPI?.setFullWindowSize()

              // Notify main process that user is logged in
              if (window.electron?.ipcRenderer?.send) {
                window.electron.ipcRenderer.send('update-data')
              }
            } else {
              window.electronAPI?.setCompactWindowSize()
              // Notify main process that user is logged out
              if (window.electron?.ipcRenderer?.send) {
                window.electron.ipcRenderer.send('update-data')
              }
            }
          } catch (error) {
            console.warn('Window resize failed:', error)
          }
        }

        if ('locks' in navigator && navigator.locks) {
          const { data: authData } = supabase.auth.onAuthStateChange(handleAuthChange)
          subscription = authData.subscription
        } else {
          console.warn('LockManager not supported in this browser')
          await handleAuthChange('INITIAL', session)
        }

        if (session) {
          const { data: userData, error: userError } = await supabase
            .from('clients')
            .select('role')
            .eq('email', session.user.email)
            .single()

          if (userError || userData?.role !== 'admin') {
            // Use proper error handling with Supabase signOut
            const { error: signOutError } = await supabase.auth.signOut()
            if (signOutError) {
              console.error('Error signing out:', signOutError)
            }

            setIsAuthenticated(false)
            window.electronAPI?.setCompactWindowSize()
            // Add a small delay to ensure the window resize completes
            await new Promise(resolve => setTimeout(resolve, 300))
          } else {
            setIsAuthenticated(true)
            window.electronAPI?.setFullWindowSize()
          }
        } else {
          setIsAuthenticated(false)
          window.electronAPI?.setCompactWindowSize()
        }
      } catch (error) {
        console.error('Initialization error:', error)
        setIsAuthenticated(false)
        window.electronAPI?.setCompactWindowSize()
      } finally {
        setIsLoading(false)
        setIsInitialized(true)
      }
    }

    init()

    return (): void => {
      subscription?.unsubscribe()
    }
  }, [isCheckingUpdate])

  // Show loading screen during update checks, not for authentication
  if (isCheckingUpdate) {
    return (
      <div className="h-screen w-screen overflow-hidden">
        <LoadingScreen updateStatus={updateStatus} />
      </div>
    )
  }

  return (
    <WindowSizeProvider>
      <LogoutProvider>
        <Router>
          <div className="custom-scrollbar h-full w-full overflow-hidden flex flex-col">
            {isAuthenticated ? (
              <>
                <TitleBar />
                <div
                  className="custom-scrollbar flex-1 overflow-auto"
                  style={{ height: 'calc(100% - 40px)', marginTop: '40px' }}
                >
                  {isOnline ? (
                    <Routes>
                      <Route path="/" element={<Navigate to="/dashboard" />} />
                      <Route
                        path="/dashboard"
                        element={
                          <PageErrorBoundary pageName="Dashboard">
                            <Dashboard />
                          </PageErrorBoundary>
                        }
                      />
                      <Route
                        path="/expired"
                        element={
                          <PageErrorBoundary pageName="Expired Licenses">
                            <ExpiredLicenses />
                          </PageErrorBoundary>
                        }
                      />
                      <Route
                        path="/licenses"
                        element={
                          <PageErrorBoundary pageName="Licenses">
                            <LicensesPage />
                          </PageErrorBoundary>
                        }
                      />
                      <Route
                        path="/docscript"
                        element={
                          <PageErrorBoundary pageName="Document Generator">
                            <DocScript />
                          </PageErrorBoundary>
                        }
                      />
                      <Route
                        path="/loans"
                        element={
                          <PageErrorBoundary pageName="Loans">
                            <Loans />
                          </PageErrorBoundary>
                        }
                      />
                      <Route
                        path="/settings"
                        element={
                          <PageErrorBoundary pageName="Settings">
                            <Settings />
                          </PageErrorBoundary>
                        }
                      />
                      <Route
                        path="/firearm-storage"
                        element={
                          <PageErrorBoundary pageName="Firearm Storage">
                            <FirearmStorage />
                          </PageErrorBoundary>
                        }
                      />
                      <Route
                        path="/pipedrive"
                        element={
                          <PageErrorBoundary pageName="Pipedrive">
                            <Pipedrive />
                          </PageErrorBoundary>
                        }
                      />
                    </Routes>
                  ) : (
                    <div className="h-full w-full bg-stone-900"></div>
                  )}
                </div>
              </>
            ) : (
              <div className="h-screen w-screen overflow-hidden">
                <Login
                  onLoginSuccess={() => {
                    setIsAuthenticated(true)
                    setIsLoading(false)
                  }}
                />
              </div>
            )}

            <ConnectionDialog isOpen={showOfflineDialog} onRetry={checkConnection} />
          </div>
        </Router>
      </LogoutProvider>
    </WindowSizeProvider>
  )
}

export default App
