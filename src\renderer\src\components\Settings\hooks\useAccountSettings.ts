import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { getOrInitSupabase } from '../../../lib/supabase'
import { UserData, PasswordForm } from '../types'

export const useAccountSettings = () => {
  const navigate = useNavigate()
  
  // User account states
  const [userData, setUserData] = useState<UserData>({
    username: '',
    email: '',
    role: '',
    lastLogin: null
  })
  const [isLoadingUserData, setIsLoadingUserData] = useState<boolean>(true)
  const [showPasswordModal, setShowPasswordModal] = useState<boolean>(false)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [isChangingPassword, setIsChangingPassword] = useState<boolean>(false)
  const [passwordForm, setPasswordForm] = useState<PasswordForm>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [passwordError, setPasswordError] = useState<string>('')
  const [passwordSuccess, setPasswordSuccess] = useState<boolean>(false)

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      setIsLoadingUserData(true)
      setErrorMessage('')

      try {
        const supabase = await getOrInitSupabase()
        const {
          data: { user }
        } = await supabase.auth.getUser()

        if (user && user.email) {
          // Fetch additional user details from clients table
          const { data, error } = await supabase
            .from('clients')
            .select('*')
            .eq('email', user.email)
            .single()

          if (!error && data) {
            setUserData({
              username: data.username || user.email.split('@')[0],
              email: user.email,
              role: data.role || 'user',
              lastLogin: user.last_sign_in_at || null,
              avatarUrl: data.avatar_url,
              displayName: data.display_name || data.username || user.email.split('@')[0]
            })
          } else {
            // If no client record exists, use basic auth data
            setUserData({
              username: user.email.split('@')[0],
              email: user.email,
              role: 'user',
              lastLogin: user.last_sign_in_at || null,
              displayName: user.email.split('@')[0]
            })
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error)
        setErrorMessage('Failed to load user data')
      } finally {
        setIsLoadingUserData(false)
      }
    }

    fetchUserData()
  }, [])

  // Change password
  const changePassword = useCallback(async (): Promise<boolean> => {
    setIsChangingPassword(true)
    setPasswordError('')

    try {
      // Validate passwords
      if (passwordForm.newPassword.length < 6) {
        throw new Error('New password must be at least 6 characters long')
      }

      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        throw new Error('New passwords do not match')
      }

      const supabase = await getOrInitSupabase()

      // First verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: userData.email,
        password: passwordForm.currentPassword
      })

      if (signInError) {
        throw new Error('Current password is incorrect')
      }

      // Update the password
      const { error } = await supabase.auth.updateUser({
        password: passwordForm.newPassword
      })

      if (error) {
        throw new Error(error.message)
      }

      // Reset form and show success message
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })

      setPasswordSuccess(true)

      // Close modal after a delay
      setTimeout(() => {
        setShowPasswordModal(false)
        setPasswordSuccess(false)
      }, 2000)

      return true
    } catch (error) {
      console.error('Error changing password:', error)
      setPasswordError(error instanceof Error ? error.message : 'Failed to change password')
      return false
    } finally {
      setIsChangingPassword(false)
    }
  }, [passwordForm, userData.email])

  // Handle logout
  const handleLogout = useCallback(async () => {
    try {
      // First set window to compact size to ensure it happens before navigation
      window.electronAPI?.setCompactWindowSize()

      // Add a small delay to ensure the window resize completes before navigation
      await new Promise(resolve => setTimeout(resolve, 300))

      // Use proper error handling with Supabase signOut
      const supabase = await getOrInitSupabase()
      const { error } = await supabase.auth.signOut()

      if (error) {
        throw error
      }

      // Notify main process that user is logged out
      if (window.electron?.ipcRenderer?.send) {
        // Cast to any to bypass type checking for custom event
        ;(window.electron.ipcRenderer as any).send('user-logged-out')
      }

      // Add another small delay before navigation
      await new Promise(resolve => setTimeout(resolve, 300))

      navigate('/')
    } catch (error) {
      console.error('Error logging out:', error)
      setErrorMessage('Failed to log out')
    }
  }, [navigate])

  return {
    userData,
    isLoadingUserData,
    showPasswordModal,
    setShowPasswordModal,
    errorMessage,
    isChangingPassword,
    passwordForm,
    setPasswordForm,
    passwordError,
    passwordSuccess,
    changePassword,
    handleLogout
  }
}
