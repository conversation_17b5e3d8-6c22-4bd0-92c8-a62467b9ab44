import { useState, useCallback, useEffect } from 'react';
import { Firearm, FirearmStorageType } from '../../../../types/firearm';
import { getOrInitSupabase } from '../../../../lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { ClientCreditWallet } from '../../../../types/clientWallet';

// Define a simplified client type for the form
export interface SimpleClient {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  id_number: string;
}

interface UseFirearmFormProps {
  firearm?: Firearm | null;
  loanId?: string;
  clientId?: string;
  loan?: any;
  onSuccess: (firearmId?: string, assignmentId?: string) => void;
  onClose: () => void;
}

export const useFirearmForm = ({
  firearm,
  loanId,
  clientId,
  loan,
  onSuccess}: UseFirearmFormProps) => {
  const isEditing = !!firearm;

  // Form data state
  const [formData, setFormData] = useState<Partial<Firearm>>({
    stock_number: '',
    dealer_id_number: '',
    full_name: '',
    make: '',
    model: '',
    serial: '',
    date_signed_in: new Date().toISOString().split('T')[0],
    date_signed_out: null,
    amount_due: 0,
    notes: '',
    storage_type: loanId ? 'Dealer' : 'Owner',
  });

  // UI state
  const [depositAmount, setDepositAmount] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedClient, setSelectedClient] = useState<SimpleClient | null>(null);

  // Initialize form data if editing or if clientId is provided
  useEffect(() => {
    if (firearm) {
      // Format dates for input fields
      const formattedSignedIn = firearm.date_signed_in
        ? new Date(firearm.date_signed_in).toISOString().split('T')[0]
        : '';

      const formattedSignedOut = firearm.date_signed_out
        ? new Date(firearm.date_signed_out).toISOString().split('T')[0]
        : null;

      setFormData({
        ...firearm,
        date_signed_in: formattedSignedIn,
        date_signed_out: formattedSignedOut
      });
    } else if (loanId && clientId) {
      // If adding a firearm from loans, prefill with Dealer storage type
      setFormData(prev => ({
        ...prev,
        storage_type: 'Dealer',
        full_name: 'CK TSAI GUNNERY',
        dealer_id_number: '100648',
        notes: loan && loan.invoice_number
          ? `QUOTE ${loan.invoice_number}`
          : `QUOTE: Unknown`
      }));

      // Fetch client info to display and store for assignment
      const fetchClientInfo = async () => {
        try {
          const supabase = await getOrInitSupabase();
          const { data, error } = await supabase
            .from('clients')
            .select('id, first_name, last_name, email, phone, id_number')
            .eq('id', clientId)
            .single();

          if (error) {
            // Handle specific authentication errors
            if (error.message?.includes('JWT') || error.message?.includes('token') || error.message?.includes('auth')) {
              console.error('Authentication error while fetching client info:', error);
              // Try to refresh the session
              const { error: refreshError } = await supabase.auth.refreshSession();
              if (refreshError) {
                console.error('Failed to refresh authentication session:', refreshError);
                setError('Authentication error. Please try again or reload the page.');
              } else {
                // If refresh was successful, try the query again
                const { data: refreshedData, error: retryError } = await supabase
                  .from('clients')
                  .select('id, first_name, last_name, email, phone, id_number')
                  .eq('id', clientId)
                  .single();

                if (retryError) throw retryError;
                if (refreshedData) {
                  setSelectedClient(refreshedData);
                }
              }
            } else {
              throw error;
            }
          } else if (data) {
            // Store client info for later use in assignment
            setSelectedClient(data);
          }
        } catch (error) {
          console.error('Error fetching client info:', error);
        }
      };

      fetchClientInfo();
    }
  }, [firearm, loanId, clientId, loan]);

  // Calculate days between two dates
  const calculateDaysBetween = useCallback((startDate: Date, endDate: Date): number => {
    // Reset time parts to get accurate day calculation
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    const end = new Date(endDate);
    end.setHours(0, 0, 0, 0);

    // Calculate difference in milliseconds and convert to days
    const differenceMs = end.getTime() - start.getTime();
    return Math.floor(differenceMs / (1000 * 60 * 60 * 24));
  }, []);

  // Handle backdated charges for Owner type firearms
  const handleBackdatedCharges = useCallback(async (
    clientId: string,
    firearmId: string,
    signInDate: string,
    firearmDetails: string
  ): Promise<{ success: boolean; walletId?: string; chargeAmount?: number; error?: string }> => {
    try {
      const supabase = await getOrInitSupabase();
      const now = new Date();
      const signInDateTime = new Date(signInDate);

      // Calculate days between sign-in date and now
      const daysDifference = calculateDaysBetween(signInDateTime, now);

      // If not backdated or only backdated by 1 day (same as today), no charge needed
      if (daysDifference <= 1) {
        return { success: true, chargeAmount: 0 };
      }

      // Daily rate is R7.50
      const DAILY_RATE = 7.5;
      const chargeAmount = daysDifference * DAILY_RATE;

      // Get or create wallet for the client
      let wallet: ClientCreditWallet | null = null;

      // First check if wallet exists
      const { data: existingWallet, error: walletError } = await supabase
        .from('client_credit_wallets')
        .select('*')
        .eq('client_id', clientId)
        .single();

      if (walletError) {
        if (walletError.code !== 'PGRST116') { // Not "no rows returned"
          console.error('Error checking for wallet:', walletError);
          return { success: false, error: 'Failed to check wallet status' };
        }

        // Create a new wallet
        const newWalletId = uuidv4();
        const { error: createError } = await supabase
          .from('client_credit_wallets')
          .insert({
            id: newWalletId,
            client_id: clientId,
            balance: 0, // Initial balance is 0, will be updated by trigger
            created_at: now.toISOString(),
            updated_at: now.toISOString()
          });

        if (createError) {
          console.error('Error creating wallet:', createError);
          return { success: false, error: 'Failed to create wallet' };
        }

        // Fetch the newly created wallet
        const { data: newWallet, error: fetchError } = await supabase
          .from('client_credit_wallets')
          .select('*')
          .eq('id', newWalletId)
          .single();

        if (fetchError) {
          console.error('Error fetching new wallet:', fetchError);
          return { success: false, error: 'Wallet created but failed to fetch details' };
        }

        wallet = newWallet;
      } else {
        wallet = existingWallet;
      }

      if (!wallet) {
        return { success: false, error: 'Failed to get or create wallet' };
      }

      // Create a transaction for the backdated charge
      const transactionId = uuidv4();
      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          id: transactionId,
          wallet_id: wallet.id,
          amount: -chargeAmount, // Negative amount for a charge
          transaction_type: 'charge',
          reference_type: 'firearm',
          reference_id: firearmId,
          description: `Backdated storage charge for ${daysDifference} days at R${DAILY_RATE} per day (${firearmDetails})`,
          transaction_date: now.toISOString(),
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        });

      if (transactionError) {
        console.error('Error creating transaction:', transactionError);
        return { success: false, error: 'Failed to create transaction' };
      }

      // Create a charge record
      const { error: chargeError } = await supabase
        .from('firearm_storage_charges')
        .insert({
          id: uuidv4(),
          firearm_id: firearmId,
          client_id: clientId,
          wallet_id: wallet.id,
          transaction_id: transactionId,
          charge_date: now.toISOString(),
          days_charged: daysDifference,
          daily_rate: DAILY_RATE,
          total_amount: chargeAmount,
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        });

      if (chargeError) {
        console.error('Error creating charge record:', chargeError);
        // Don't fail the whole operation if just the charge record fails
      }

      return {
        success: true,
        walletId: wallet.id,
        chargeAmount
      };
    } catch (error) {
      console.error('Error handling backdated charges:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error processing backdated charges'
      };
    }
  }, [calculateDaysBetween]);

  // Prevent scroll wheel from changing number input values
  const preventWheelChange = useCallback((e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  }, []);

  // Handle input changes
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Handle number inputs
    if (type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }));
    } else {
      // Convert text inputs to uppercase
      const uppercaseValue = typeof value === 'string' ? value.toUpperCase() : value;

      setFormData(prev => ({
        ...prev,
        [name]: uppercaseValue
      }));
    }
  }, []);

  // Handle credit amount changes
  const handleCreditChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const value = e.target.value;
    setDepositAmount(value ? parseFloat(value) : null);
  }, []);

  // Handle storage type change
  const handleStorageTypeChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const storageType = e.target.value as FirearmStorageType;

    // If changing from Owner to another type, clear any selected client and client ID
    if (formData.storage_type === 'Owner' && storageType !== 'Owner') {
      setSelectedClient(null);
    }

    // If changing to Dealer, prefill with CK TSAI GUNNERY and Dealer ID
    if (storageType === 'Dealer') {
      setFormData(prev => ({
        ...prev,
        storage_type: storageType,
        full_name: 'CK TSAI GUNNERY',
        dealer_id_number: '100648'
      }));
    } else if (storageType === 'Owner') {
      // For Owner type, clear fields to be set by client selection
      setFormData(prev => ({
        ...prev,
        storage_type: storageType,
        full_name: '',
        dealer_id_number: '' // This will be filled by client selection but hidden in UI
      }));
    } else {
      // For Private type, just update the storage type and clear client-related fields
      setFormData(prev => ({
        ...prev,
        storage_type: storageType,
        // Clear fields if coming from Owner type
        ...(formData.storage_type === 'Owner' ? { dealer_id_number: '' } : {})
      }));
    }
  }, [formData.storage_type]);

  // Handle client selection
  const handleClientSelect = useCallback((client: SimpleClient) => {
    setSelectedClient(client);
    // Set the full_name field to the client's full name and dealer_id_number to client's ID number
    // Add null checks to prevent errors with null values
    setFormData(prev => ({
      ...prev,
      full_name: `${client.first_name || ''} ${client.last_name || ''}`.toUpperCase(),
      dealer_id_number: client.id_number ? client.id_number.toUpperCase() : ''
    }));
  }, []);

  // Handle client removal
  const handleClientRemove = useCallback(() => {
    setSelectedClient(null);
    setFormData(prev => ({
      ...prev,
      full_name: '',
      dealer_id_number: ''
    }));
  }, []);

  // Validate form data
  const validateForm = useCallback(() => {
    // Special validation for Owner storage type
    if (formData.storage_type === 'Owner') {
      if (!formData.full_name || !selectedClient) {
        setError('Please select a client as the owner');
        return false;
      }

      // For Owner type, dealer_id_number is filled automatically from client selection
      // so we don't need to validate it separately
    }

    // Determine which fields to validate based on storage type
    const baseRequiredFields = [
      'stock_number',
      'full_name',
      'make',
      'model',
      'serial',
      'date_signed_in'
    ];

    // Add dealer_id_number to required fields only if not Owner type
    const requiredFields = formData.storage_type !== 'Owner'
      ? [...baseRequiredFields, 'dealer_id_number']
      : baseRequiredFields;

    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);

    if (missingFields.length > 0) {
      setError(`Please fill in all required fields: ${missingFields.join(', ')}`);
      return false;
    }

    return true;
  }, [formData, selectedClient]);

  // Prepare submission data
  const prepareSubmissionData = useCallback(() => {
    const {
      stock_number,
      dealer_id_number,
      full_name,
      make,
      model,
      serial,
      date_signed_in,
      date_signed_out,
      notes,
      storage_type
    } = formData;

    // Convert all text fields to uppercase
    const convertToUppercase = (value: any): any => {
      return typeof value === 'string' ? value.toUpperCase() : value;
    };

    // Create a clean submission object without virtual fields and convert text to uppercase
    return {
      stock_number: convertToUppercase(stock_number),
      dealer_id_number: convertToUppercase(dealer_id_number),
      full_name: convertToUppercase(full_name),
      make: convertToUppercase(make),
      model: convertToUppercase(model),
      serial: convertToUppercase(serial),
      date_signed_in, // Keep date as is
      date_signed_out, // Keep date as is
      amount_due: 0, // Set amount_due to 0 as we're using credit_balance instead
      notes: convertToUppercase(notes),
      storage_type, // Keep enum value as is
      updated_at: new Date().toISOString()
    };
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Validate form
    if (!validateForm()) {
      setLoading(false);
      return;
    }

    try {
      const supabase = await getOrInitSupabase();
      let newAssignmentId: string | undefined;
      const submissionData = prepareSubmissionData();
      let newFirearmId: string | null = null;

      if (isEditing && firearm) {
        // Update existing firearm
        const { error: updateError } = await supabase
          .from('firearms')
          .update(submissionData)
          .eq('id', firearm.id);

        if (updateError) {
          throw updateError;
        }

        newFirearmId = firearm.id;

        // Handle owner assignment and wallet operations for editing
        if (formData.storage_type === 'Owner' && selectedClient) {
          // First, check if there are any existing assignments
          const { data: existingAssignments, error: checkError } = await supabase
            .from('firearm_assignments')
            .select('*')
            .eq('firearm_id', firearm.id)
            .is('return_date', null);

          if (checkError) {
            console.error('Error checking existing assignments:', checkError);
            throw new Error('Failed to check existing assignments: ' + checkError.message);
          }

          const now = new Date().toISOString();

          // If there are existing assignments to other clients, end them
          if (existingAssignments && existingAssignments.length > 0) {
            // Check if the firearm is already assigned to the selected owner
            const ownerAssignment = existingAssignments.find(
              assignment => assignment.client_id === selectedClient.id
            );

            if (ownerAssignment) {
              // Already assigned to this owner, no need to create a new assignment
              newAssignmentId = ownerAssignment.id;

              // If the sign-in date has changed and is backdated, handle backdated charges
              if (formData.date_signed_in && firearm.date_signed_in !== formData.date_signed_in) {
                const signInDate = new Date(formData.date_signed_in);
                const originalSignInDate = new Date(firearm.date_signed_in);

                // Only process if the new date is earlier than the original date
                if (signInDate < originalSignInDate) {
                  const firearmDetails = `${formData.make} ${formData.model} ${formData.serial}`.toUpperCase();
                  const backdatedResult = await handleBackdatedCharges(
                    selectedClient.id,
                    firearm.id,
                    formData.date_signed_in,
                    firearmDetails
                  );

                  if (!backdatedResult.success) {
                    console.warn('Failed to process backdated charges:', backdatedResult.error);
                    // Continue with the firearm update even if backdated charges fail
                  } else if (backdatedResult.chargeAmount && backdatedResult.chargeAmount > 0) {
                    console.log(`Successfully charged R${backdatedResult.chargeAmount} for backdated storage`);
                  }
                }
              }
            } else {
              // End all existing assignments
              for (const assignment of existingAssignments) {
                const { error: endError } = await supabase
                  .from('firearm_assignments')
                  .update({
                    return_date: now,
                    updated_at: now
                  })
                  .eq('id', assignment.id);

                if (endError) {
                  console.error('Error ending existing assignment:', endError);
                  throw new Error('Failed to end existing assignment: ' + endError.message);
                }
              }

              // Create a new assignment for the owner
              const assignmentId = uuidv4();
              const assignedDate = formData.date_signed_in || now.split('T')[0];

              // Create the assignment record
              const { error: assignmentError } = await supabase
                .from('firearm_assignments')
                .insert({
                  id: assignmentId,
                  firearm_id: firearm.id,
                  client_id: selectedClient.id,
                  assigned_date: assignedDate,
                  return_date: null, // Active assignment
                  notes: 'Owner assignment (edited)',
                  free_storage_until: null, // Owners don't get free storage period
                  last_charge_date: now,
                  created_at: now,
                  updated_at: now
                });

              if (assignmentError) {
                console.error('Error creating owner assignment:', assignmentError);
                throw new Error('Failed to assign firearm to owner: ' + assignmentError.message);
              }

              // Store the assignment ID for return
              newAssignmentId = assignmentId;

              // WhatsApp notification functionality has been removed
            }
          } else {
            // No existing assignments, create a new one for the owner
            const assignmentId = uuidv4();
            const assignedDate = formData.date_signed_in || now.split('T')[0];

            // Create the assignment record
            const { error: assignmentError } = await supabase
              .from('firearm_assignments')
              .insert({
                id: assignmentId,
                firearm_id: firearm.id,
                client_id: selectedClient.id,
                assigned_date: assignedDate,
                return_date: null, // Active assignment
                notes: 'Owner assignment',
                free_storage_until: null, // Owners don't get free storage period
                last_charge_date: now,
                created_at: now,
                updated_at: now
              });

            if (assignmentError) {
              console.error('Error creating owner assignment:', assignmentError);
              throw new Error('Failed to assign firearm to owner: ' + assignmentError.message);
            }

            // Store the assignment ID for return
            newAssignmentId = assignmentId;

            // Handle backdated charges if the sign-in date is in the past
            if (formData.date_signed_in) {
              const firearmDetails = `${formData.make} ${formData.model} ${formData.serial}`.toUpperCase();
              const backdatedResult = await handleBackdatedCharges(
                selectedClient.id,
                firearm.id,
                formData.date_signed_in,
                firearmDetails
              );

              if (!backdatedResult.success) {
                console.warn('Failed to process backdated charges:', backdatedResult.error);
                // Continue with the firearm update even if backdated charges fail
              } else if (backdatedResult.chargeAmount && backdatedResult.chargeAmount > 0) {
                console.log(`Successfully charged R${backdatedResult.chargeAmount} for backdated storage`);
              }
            }

            // WhatsApp notification functionality has been removed
          }
        }
      } else {
        // Create new firearm
        const newId = uuidv4();
        newFirearmId = newId;

        const { error: insertError } = await supabase
          .from('firearms')
          .insert({
            ...submissionData,
            id: newId,
            created_at: new Date().toISOString()
          });

        if (insertError) {
          throw insertError;
        }

        // Handle owner assignment and wallet operations for new firearm
        if (formData.storage_type === 'Owner' && selectedClient) {
          // Create a new assignment for the owner
          const assignmentId = uuidv4();
          const now = new Date().toISOString();
          const assignedDate = formData.date_signed_in || now.split('T')[0];

          // Create the assignment record
          const { error: assignmentError } = await supabase
            .from('firearm_assignments')
            .insert({
              id: assignmentId,
              firearm_id: newId,
              client_id: selectedClient.id,
              assigned_date: assignedDate,
              return_date: null, // Active assignment
              notes: 'Owner assignment',
              free_storage_until: null, // Owners don't get free storage period
              last_charge_date: now,
              created_at: now,
              updated_at: now
            });

          if (assignmentError) {
            console.error('Error creating owner assignment:', assignmentError);
            throw new Error('Failed to assign firearm to owner: ' + assignmentError.message);
          }

          // Store the assignment ID for return
          newAssignmentId = assignmentId;

          // Handle backdated charges if the sign-in date is in the past
          if (formData.date_signed_in) {
            const firearmDetails = `${formData.make} ${formData.model} ${formData.serial}`.toUpperCase();
            const backdatedResult = await handleBackdatedCharges(
              selectedClient.id,
              newId,
              formData.date_signed_in,
              firearmDetails
            );

            if (!backdatedResult.success) {
              console.warn('Failed to process backdated charges:', backdatedResult.error);
              // Continue with the firearm creation even if backdated charges fail
            } else if (backdatedResult.chargeAmount && backdatedResult.chargeAmount > 0) {
              console.log(`Successfully charged R${backdatedResult.chargeAmount} for backdated storage`);
            }
          }

          // WhatsApp notification functionality has been removed
        }
      }

      // Handle loan-related operations
      if (loanId && newFirearmId && clientId) {
        // Create a new assignment for the client
        const assignmentId = uuidv4();
        const now = new Date();
        const assignedDate = formData.date_signed_in || now.toISOString().split('T')[0];

        // Calculate free storage period (12 months from assignment date)
        const freeStorageUntil = new Date(assignedDate);
        freeStorageUntil.setMonth(freeStorageUntil.getMonth() + 12);
        const freeStorageUntilStr = freeStorageUntil.toISOString();

        // Create the assignment record
        const { error: assignmentError } = await supabase
          .from('firearm_assignments')
          .insert({
            id: assignmentId,
            firearm_id: newFirearmId,
            client_id: clientId,
            assigned_date: assignedDate,
            return_date: null, // Active assignment
            notes: loan && loan.invoice_number
              ? `Assigned via loan ${loan.invoice_number}`
              : 'Assigned via loan',
            free_storage_until: freeStorageUntilStr, // 12 months free storage
            last_charge_date: now.toISOString(),
            created_at: now.toISOString(),
            updated_at: now.toISOString()
          });

        if (assignmentError) {
          console.error('Error creating client assignment:', assignmentError);
          throw new Error('Failed to assign firearm to client: ' + assignmentError.message);
        }

        // Store the assignment ID for return
        newAssignmentId = assignmentId;

        // WhatsApp notification functionality has been removed
      }

      // Success - pass back the firearm ID and assignment ID if available
      if ((loanId && newFirearmId && clientId) || (formData.storage_type === 'Owner' && newAssignmentId)) {
        onSuccess(newFirearmId, newAssignmentId);
      } else {
        onSuccess(newFirearmId);
      }
    } catch (err: any) {
      console.error('Error saving firearm:', err);
      setError(err.message || 'An error occurred while saving the firearm');
    } finally {
      setLoading(false);
    }
  }, [
    validateForm,
    prepareSubmissionData,
    isEditing,
    firearm,
    formData.storage_type,
    selectedClient,
    loanId,
    clientId,
    onSuccess,
    handleBackdatedCharges
  ]);

  return {
    formData,
    setFormData,
    depositAmount,
    loading,
    error,
    selectedClient,
    isEditing,
    handleChange,
    handleCreditChange,
    handleStorageTypeChange,
    handleClientSelect,
    handleClientRemove,
    handleSubmit,
    preventWheelChange,
    calculateDaysBetween
  };
};
