import React from 'react'

interface RadioOption {
  value: string
  label: string
}

interface RadioGroupProps {
  name: string
  value: string
  onChange: (value: string) => void
  options: RadioOption[]
  label?: string
  required?: boolean
  className?: string
}

const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  value,
  onChange,
  options,
  label,
  required = false,
  className = ''
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-stone-300 mb-1">
          {label} {required && <span className="text-orange-500">*</span>}
        </label>
      )}
      <div className="flex flex-row space-x-4">
        {options.map((option) => (
          <label
            key={option.value}
            className="flex items-center space-x-2 cursor-pointer"
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={() => onChange(option.value)}
              className="w-4 h-4 text-orange-500 bg-stone-700 border-stone-600 focus:ring-orange-500/30"
              required={required}
            />
            <span className="text-sm text-stone-300">{option.label}</span>
          </label>
        ))}
      </div>
    </div>
  )
}

export default RadioGroup
