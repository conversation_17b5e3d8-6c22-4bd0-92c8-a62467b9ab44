import React from 'react'
import { FilterState } from './types'

interface FilterComponentProps {
  activeFilter: FilterState['activeFilter']
  setActiveFilter: (filter: FilterState['activeFilter']) => void
}

export const FilterComponent: React.FC<FilterComponentProps> = ({
  activeFilter,
  setActiveFilter
}) => {
  return (
    <div className="bg-stone-800/70 p-4 rounded-lg shadow-lg mb-4">
      <div className="flex flex-col gap-4">
        {/* Filter Label */}
        <div className="flex items-center">
          <div className="text-stone-300 font-medium">Filter Licenses:</div>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={() => setActiveFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between
              ${
                activeFilter === 'all'
                  ? 'bg-orange-500 text-white'
                  : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
              }`}
          >
            <span>All Licenses</span>
          </button>

          <button
            onClick={() => setActiveFilter('expiring')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between
              ${
                activeFilter === 'expiring'
                  ? 'bg-orange-500 text-white'
                  : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
              }`}
          >
            <span>Expiring Soon</span>
          </button>

          <button
            onClick={() => setActiveFilter('expired')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between
              ${
                activeFilter === 'expired'
                  ? 'bg-orange-500 text-white'
                  : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
              }`}
          >
            <span>Expired</span>
          </button>
        </div>
      </div>
    </div>
  )
}
