import React from 'react'
import { FormSection, FormField, RadioGroup, CheckboxGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Safe Storage section component
 */
const SafeStorage: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    // Prevent any form submission that might be triggered
    e.preventDefault()

    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Safe Storage</h3>
      <FormSection
        title="Safe Storage"
        subtitle="Please provide information about your firearm safe storage"
      >
        <div className="space-y-3">
          <div className="border-b border-gray-700 pb-4 mb-4">
            <RadioGroup
              name="safe"
              value={formData.safeYes ? 'yes' : 'no'}
              onChange={(value) => {
                // Only update if the value is different from the current value
                const currentValue = formData.safeYes ? 'yes' : 'no';
                if (value !== currentValue) {
                  // Prevent any form submission
                  updateFormData({
                    safeYes: value === 'yes',
                    safeNo: value !== 'yes'
                  })
                }
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Do you have a safe for your firearm?"
            />
          </div>

          {formData.safeYes && (
            <>
              <div className="border-b border-gray-700 pb-4 mb-4">
                <h3 className="text-lg font-semibold text-white mb-2">Safe Type</h3>
                <CheckboxGroup
                  options={[
                    { name: 'safeH', label: 'Handgun Safe', checked: formData.safeH },
                    { name: 'safeR', label: 'Rifle Safe', checked: formData.safeR },
                    { name: 'safeS', label: 'Strongroom', checked: formData.safeS },
                    { name: 'safeD', label: 'Device', checked: formData.safeD }
                  ]}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    e.preventDefault()
                    const { name, checked } = e.target
                    updateFormData({
                      [name]: checked
                    })
                  }}
                />
              </div>

              {formData.safeS && (
                <div className="border-b border-gray-700 pb-4 mb-4">
                  <FormField
                    label="Strongroom Information"
                    name="safeSe"
                    value={formData.safeSe}
                    onChange={handleChange}
                    placeholder="Provide details about the strongroom"
                    type="textarea"
                    rows={2}
                  />
                </div>
              )}

              {formData.safeD && (
                <div className="border-b border-gray-700 pb-4 mb-4">
                  <FormField
                    label="Device Information"
                    name="safeDInfo"
                    value={formData.safeDInfo}
                    onChange={handleChange}
                    placeholder="Provide details about the device"
                    type="textarea"
                    rows={2}
                  />
                </div>
              )}

              <div className="border-b border-gray-700 pb-4 mb-4">
                <RadioGroup
                  name="safeMounted"
                  value={formData.safeMountYes ? 'yes' : 'no'}
                  onChange={(value) => {
                    // Only update if the value is different from the current value
                    const currentValue = formData.safeMountYes ? 'yes' : 'no';
                    if (value !== currentValue) {
                      // Prevent any form submission
                      updateFormData({
                        safeMountYes: value === 'yes',
                        safeMountNo: value !== 'yes'
                      })
                    }
                  }}
                  options={[
                    { value: 'yes', label: 'Yes' },
                    { value: 'no', label: 'No' }
                  ]}
                  label="Is the safe mounted?"
                />

                {formData.safeMountYes && (
                  <div className="ml-6 border-l-2 border-orange-500 pl-4 mt-3">
                    <CheckboxGroup
                      options={[
                        { name: 'safeWall', label: 'Wall', checked: formData.safeWall },
                        { name: 'safeFloor', label: 'Floor', checked: formData.safeFloor }
                      ]}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.preventDefault()
                        const { name, checked } = e.target
                        updateFormData({
                          [name]: checked
                        })
                      }}
                      title="Where is it mounted?"
                    />
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default SafeStorage
