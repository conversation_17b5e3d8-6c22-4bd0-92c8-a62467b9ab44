/**
 * Format a date string to a more readable format
 * @param dateString Date string in YYYY-MM-DD format
 * @returns Formatted date string in DD Month YYYY format
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-ZA', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  } catch (error) {
    console.error('Error formatting date:', error)
    return dateString
  }
}

/**
 * Validate a serial number format
 * @param serialNumber Serial number to validate
 * @returns Boolean indicating if the serial number is valid
 */
export const isValidSerialNumber = (serialNumber: string): boolean => {
  // Basic validation - can be expanded based on specific requirements
  return serialNumber.length > 3
}

/**
 * Validate a date is not in the future
 * @param dateString Date string to validate
 * @returns Boolean indicating if the date is valid (not in the future)
 */
export const isValidPastDate = (dateString: string): boolean => {
  if (!dateString) return false
  
  try {
    const date = new Date(dateString)
    const today = new Date()
    
    // Set today to end of day for comparison
    today.setHours(23, 59, 59, 999)
    
    return date <= today
  } catch (error) {
    console.error('Error validating date:', error)
    return false
  }
}
