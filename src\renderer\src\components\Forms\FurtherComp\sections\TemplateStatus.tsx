import React from 'react'
import { TemplateStatus as TemplateStatusComponent } from '../../../FormComponents'
import { TemplateStatus as TemplateStatusType } from '../utils/types'

interface TemplateStatusProps {
  status: TemplateStatusType
  templateName: string
  error: string | null
}

const TemplateStatus: React.FC<TemplateStatusProps> = ({ status, templateName, error }) => {
  return (
    <TemplateStatusComponent status={status} templateName={templateName} error={error} />
  )
}

export default TemplateStatus
