interface ToastOptions {
  message: string
  type: 'success' | 'error' | 'info' | 'warning'
  duration?: number
}

/**
 * Shows a toast notification
 * @param options Toast options
 */
export const showToast = (options: ToastOptions): void => {
  const { message, type, duration = 3000 } = options

  // Create toast container if it doesn't exist
  let toastContainer = document.getElementById('toast-container')
  if (!toastContainer) {
    toastContainer = document.createElement('div')
    toastContainer.id = 'toast-container'
    toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2'
    document.body.appendChild(toastContainer)
  }

  // Create toast element
  const toast = document.createElement('div')
  toast.className = `px-4 py-2 rounded shadow-lg transition-all duration-300 transform translate-x-full opacity-0 ${getToastColorClass(
    type
  )}`
  toast.textContent = message

  // Add toast to container
  toastContainer.appendChild(toast)

  // Animate toast in
  setTimeout(() => {
    toast.classList.remove('translate-x-full', 'opacity-0')
  }, 10)

  // Remove toast after duration
  setTimeout(() => {
    toast.classList.add('translate-x-full', 'opacity-0')
    setTimeout(() => {
      if (toastContainer.contains(toast)) {
        toastContainer.removeChild(toast)
      }
      // Remove container if empty
      if (toastContainer.childNodes.length === 0) {
        document.body.removeChild(toastContainer)
      }
    }, 300)
  }, duration)
}

/**
 * Get the appropriate color class for the toast type
 * @param type Toast type
 * @returns Tailwind CSS class for the toast
 */
const getToastColorClass = (type: ToastOptions['type']): string => {
  switch (type) {
    case 'success':
      return 'bg-green-500 text-white'
    case 'error':
      return 'bg-red-500 text-white'
    case 'warning':
      return 'bg-yellow-500 text-white'
    case 'info':
      return 'bg-blue-500 text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}
