import React, { useState } from 'react';
import { parseNotes } from '../../utils/noteUtils';
import { DashboardIcons } from '../icons/DashboardIcons';

interface NotesPreviewProps {
  notes: string | null;
}

const NotesPreview: React.FC<NotesPreviewProps> = ({ notes }) => {
  const [showAllNotes, setShowAllNotes] = useState(false);
  const parsedNotes = parseNotes(notes);

  if (!parsedNotes.length) return null;

  // Enhanced modal for viewing all notes - centered popup with improved styling
  const NotesModal = () => (
    <div
      className="fixed inset-0 bg-black/80 flex items-center justify-center z-[100] backdrop-blur-sm"
      onClick={() => setShowAllNotes(false)}
      style={{ paddingTop: '40px' }} // Account for the titlebar
    >
      <div
        className="bg-gradient-to-br from-stone-800 to-stone-900 rounded-xl shadow-2xl w-full max-w-xl p-6 border border-orange-500/40 animate-fadeIn transform transition-all duration-300"
        onClick={(e) => e.stopPropagation()}
        style={{
          maxHeight: 'calc(100vh - 80px)',
          boxShadow: '0 0 30px rgba(249, 115, 22, 0.15), 0 0 15px rgba(0, 0, 0, 0.3)'
        }}
      >
        {/* Header with title and close button */}
        <div className="flex justify-between items-center mb-5 border-b border-stone-700/50 pb-3">
          <div>
            <h3 className="text-xl font-semibold text-white flex items-center">
              <DashboardIcons.Notes className="w-5 h-5 mr-2 text-orange-400" />
              Deal Notes
            </h3>
            <p className="text-stone-400 text-xs mt-1">
              {parsedNotes.length} note{parsedNotes.length !== 1 ? 's' : ''} • Grouped by date
            </p>
          </div>
          <button
            type="button"
            onClick={() => setShowAllNotes(false)}
            className="text-stone-400 hover:text-orange-400 p-1 rounded-full transition-colors duration-200"
            aria-label="Close notes"
          >
            <DashboardIcons.Close className="w-5 h-5" />
          </button>
        </div>

        {/* Notes content area - Improved for readability and space efficiency */}
        <div className="mb-5 overflow-y-auto custom-scrollbar" style={{ maxHeight: 'calc(100vh - 200px)' }}>
          {/* Group notes by date for better organization */}
          {(() => {
            // Group notes by date
            const groupedNotes = parsedNotes.reduce<Record<string, typeof parsedNotes>>((groups, note) => {
              const date = note.date || 'No Date';
              if (!groups[date]) {
                groups[date] = [];
              }
              groups[date].push(note);
              return groups;
            }, {});

            // Convert grouped notes to array for rendering
            return Object.entries(groupedNotes).map(([date, notesForDate]) => (
              <div key={date} className="mb-4">
                {/* Date header - only show if it's a valid date */}
                {date !== 'No Date' && (
                  <div className="sticky top-0 z-10 bg-stone-800/90 backdrop-blur-sm py-1.5 px-2 rounded-md mb-2 flex items-center border-l-2 border-orange-500">
                    <DashboardIcons.History className="w-3.5 h-3.5 mr-1.5 text-orange-400" />
                    <span className="text-orange-400 font-medium text-sm">{date}</span>
                  </div>
                )}

                {/* Notes for this date */}
                <div className="space-y-2 pl-2">
                  {notesForDate.map((note, noteIndex: number) => (
                    <div
                      key={noteIndex}
                      className="bg-stone-700/20 p-3 rounded-md border-l-2 border-stone-600/50"
                    >
                      <div className="text-white text-sm whitespace-pre-wrap leading-relaxed">{note.content}</div>
                    </div>
                  ))}
                </div>
              </div>
            ));
          })()}
        </div>

        {/* Footer with close button */}
        <div className="flex justify-end pt-2 border-t border-stone-700/50">
          <button
            type="button"
            onClick={() => setShowAllNotes(false)}
            className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm flex items-center transition-colors duration-200"
          >
            <DashboardIcons.Close className="w-4 h-4 mr-1" />
            Close
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <div className="flex items-start">
        <span className="text-orange-400 font-medium text-sm w-20">Notes:</span>
        <div className="flex-1 flex items-center">
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setShowAllNotes(true);
            }}
            className="text-white text-xs flex items-center bg-orange-500/80 hover:bg-orange-500/90 px-2 py-1 rounded font-medium transition-colors duration-200"
            title="Click to view all notes in a popup"
          >
            <DashboardIcons.Notes className="w-3.5 h-3.5 mr-1" />
            View {parsedNotes.length} note{parsedNotes.length !== 1 ? 's' : ''}
          </button>
        </div>
      </div>

      {showAllNotes && <NotesModal />}
    </>
  );
};

export default NotesPreview;
