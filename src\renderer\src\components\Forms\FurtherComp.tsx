import { FurtherCompetencyData } from '../../types/FormData'
import FurtherCompForm from './FurtherComp/index'

interface FurtherCompProps {
  onSubmit: (data: FurtherCompetencyData) => void
}

/**
 * FurtherComp - Further Competency Application Form Component
 *
 * A multi-step form for processing further competency applications
 * that passes data to DocScript for document generation.
 */
export default function FurtherComp({ onSubmit }: FurtherCompProps): JSX.Element {
  return <FurtherCompForm onSubmit={onSubmit} />
}