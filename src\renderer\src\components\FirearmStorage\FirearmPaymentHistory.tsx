import { useState, useEffect } from 'react'
import { getSupabase } from '../../lib/supabase'
import { DashboardIcons } from '../icons/DashboardIcons'
import { useTableVirtualization } from '../../hooks/useTableVirtualization'
import { formatDate, formatCurrency } from '../../utils/formatters'

interface FirearmPayment {
  id: string
  firearm_id: string
  client_id: string
  wallet_id: string
  transaction_id: string
  payment_date: string
  amount: number
  payment_type: string
  notes: string
  created_at: string
  updated_at: string
}

interface FirearmPaymentHistoryProps {
  firearmId: string
  clientName: string
  onClose: () => void
}

const FirearmPaymentHistory = ({
  firearmId,
  clientName,
  onClose
}: FirearmPaymentHistoryProps): JSX.Element => {
  const [payments, setPayments] = useState<FirearmPayment[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get firearm payment history
  const getFirearmPayments = async (firearmId: string): Promise<FirearmPayment[]> => {
    setLoading(true)
    setError(null)

    try {
      const supabase = getSupabase()

      // Get payments
      const { data: payments, error: paymentsError } = await supabase
        .from('firearm_storage_payments')
        .select('*')
        .eq('firearm_id', firearmId)
        .order('payment_date', { ascending: false })
        .limit(100)

      if (paymentsError) {
        setError('Failed to fetch payment history')
        return []
      }

      // Get charges
      const { data: charges, error: chargesError } = await supabase
        .from('firearm_storage_charges')
        .select('*')
        .eq('firearm_id', firearmId)
        .order('charge_date', { ascending: false })
        .limit(100)

      if (chargesError) {
        console.error('Failed to fetch charge history:', chargesError)
        // Continue with just payments if charges fail
      }

      // Convert charges to the same format as payments for display
      const formattedCharges = (charges || []).map(charge => ({
        id: charge.id,
        firearm_id: charge.firearm_id,
        client_id: charge.client_id,
        wallet_id: charge.wallet_id,
        transaction_id: charge.transaction_id,
        payment_date: charge.charge_date,
        amount: -charge.total_amount, // Negative amount for charges
        payment_type: 'charge',
        notes: `Storage charge for ${charge.days_charged} days at R${charge.daily_rate}/day`,
        created_at: charge.created_at,
        updated_at: charge.updated_at
      }))

      // Combine payments and charges, sort by date
      const combined = [...(payments || []), ...formattedCharges]
        .sort((a, b) => new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime())

      return combined as FirearmPayment[]
    } catch (err) {
      setError('An unexpected error occurred')
      return []
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const fetchPayments = async () => {
      if (firearmId) {
        const data = await getFirearmPayments(firearmId)
        setPayments(data)
      }
    }

    fetchPayments()
  }, [firearmId])

  // Use the table virtualization hook
  const virtualizedPayments = useTableVirtualization(payments, 15, 10)

  const formatPaymentType = (type: string): string => {
    switch (type) {
      case 'credit':
        return 'Credit Payment'
      case 'cash':
        return 'Cash Payment'
      case 'card':
        return 'Card Payment'
      case 'transfer':
        return 'Bank Transfer'
      case 'charge':
        return 'Storage Charge'
      default:
        return type.charAt(0).toUpperCase() + type.slice(1)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-stone-800 rounded-xl shadow-xl w-full max-w-4xl overflow-hidden border border-orange-500/30 flex flex-col max-h-[90vh]">
        <div className="p-6 flex justify-between items-center border-b border-stone-700">
          <h2 className="text-xl font-bold text-white flex items-center gap-2">
            <DashboardIcons.Payment className="w-5 h-5 text-orange-400" />
            Payment History for {clientName}
          </h2>
          <div className="flex items-center gap-3">
            <button
              onClick={onClose}
              className="text-stone-400 hover:text-white transition-colors"
              aria-label="Close"
            >
              <DashboardIcons.Close className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="p-6 flex-1 overflow-hidden">
          {error && (
            <div className="mb-4 p-3 bg-red-900/30 border border-red-700 rounded-lg text-red-400">
              {error}
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
            </div>
          ) : payments.length === 0 ? (
            <div className="text-center py-8 text-stone-400">
              No payment history available
            </div>
          ) : (
            <div className="overflow-auto flex-1 max-h-[calc(100vh-250px)]" onScroll={(e) => virtualizedPayments.handleScroll(e)}>
              <table className="min-w-full divide-y divide-stone-700">
                <thead className="bg-stone-800 sticky top-0 z-10">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                    >
                      Date
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                    >
                      Type
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                    >
                      Amount
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                    >
                      Notes
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-stone-800/30 divide-y divide-stone-700">
                  {virtualizedPayments.items.map((payment) => (
                    <tr key={payment.id} className="hover:bg-stone-800/50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-stone-300">
                        {formatDate(payment.payment_date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-stone-300">
                        {formatPaymentType(payment.payment_type)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${payment.amount >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                        {formatCurrency(Math.abs(payment.amount))}
                        {payment.amount >= 0 ? ' (+)' : ' (-)'}
                      </td>
                      <td className="px-6 py-4 text-sm text-stone-300">
                        {payment.notes || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Loading indicator at the bottom of the table */}
              {virtualizedPayments.isLoading && (
                <div className="py-2 text-center">
                  <div className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500"></div>
                  <span className="ml-2 text-sm text-stone-400">Loading more payments...</span>
                </div>
              )}

              {/* Load more button */}
              {virtualizedPayments.total > virtualizedPayments.items.length && !virtualizedPayments.isLoading && (
                <div className="text-center py-2">
                  <button
                    className="text-orange-500 hover:text-orange-400 text-sm"
                    onClick={virtualizedPayments.handleLoadMore}
                  >
                    Load more payments ({virtualizedPayments.total - virtualizedPayments.items.length} remaining)
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default FirearmPaymentHistory
