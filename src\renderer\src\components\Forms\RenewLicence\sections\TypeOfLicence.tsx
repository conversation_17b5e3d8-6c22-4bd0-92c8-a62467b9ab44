import React from 'react'
import { RenewLicenceData } from '../../../../types/RenewLicenceData'
import { RadioGroup, FormSection } from '../../../FormComponents'

interface TypeOfLicenceProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  className?: string
}

const TypeOfLicence: React.FC<TypeOfLicenceProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Type of Licence</h3>
      <FormSection title="Type of Licence" subtitle="Please select the type of licence">
        <div className="space-y-4">
          <RadioGroup
            name="licenceType"
            value={
              formData.s13 ? 's13' :
              formData.s20a ? 's20a' :
              formData.s15 ? 's15' :
              formData.s16 ? 's16' :
              formData.s20 ? 's20' :
              formData.s20b ? 's20b' : ''
            }
            onChange={(value) => {
              {
                const isBusinessLicence = value === 's20' || value === 's20a' || value === 's20b'
                const resetJuristicData = !isBusinessLicence ? {
                  // Reset Juristic Person's fields
                  companyName: '',
                  tradingAsName: '',
                  farNumber: '',
                  postalAddress: '',
                  workPostalCode: '',
                  businessTelNumber: '',
                  workNumber: '',
                  companyEmail: '',

                  // Reset Responsible Person's fields
                  responsiblePersonName: '',
                  responsiblePersonSaId: false,
                  responsiblePersonPassport: false,
                  responsiblePersonIdNumber: '',
                  responsiblePersonPassportNumber: '',
                  responsiblePersonCellNumber: '',
                  responsiblePersonAddress: '',
                  responsiblePersonPostalCode: '',
                  responsiblePersonHouseNumber: ''
                } : {}

                updateFormData({
                  s13: value === 's13',
                  s15: value === 's15',
                  s16: value === 's16',
                  s20: value === 's20',
                  s20a: value === 's20a',
                  s20b: value === 's20b',
                  s20c: false, // Reset s20c as it's not in the radio options
                  ...resetJuristicData
                })
              }
            }}
            options={[
              { value: 's13', label: 'Section 13 - Self-defence' },
              { value: 's20a', label: 'Section 20C - Business - training' },
              { value: 's15', label: 'Section 15 - Occasional hunting/sport' },
              { value: 's16', label: 'Section 16 - Dedicated hunting/sport' },
              { value: 's20', label: 'Section 20 - Business - hunting' },
              { value: 's20b', label: 'Section 20B - Business - security' }
            ]}
            label="Select the type of licence (Mark with X)"
            required
          />
        </div>
      </FormSection>
    </div>
  )
}

export default TypeOfLicence
