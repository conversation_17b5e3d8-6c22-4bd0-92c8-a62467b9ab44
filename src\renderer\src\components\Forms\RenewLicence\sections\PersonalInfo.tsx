import React from 'react'
import { RenewLicenceData } from '../../../../types/RenewLicenceData'
import { FormField, FormSection, RadioGroup } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'

interface PersonalInfoProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  handleAddressChange: (
    address: string,
    postalCode?: string,
    houseNumber?: string,
    isWorkAddress?: boolean
  ) => void
  handleHouseNumberChange: (houseNumber: string, isWorkAddress?: boolean) => void
  className?: string
}

const PersonalInfo: React.FC<PersonalInfoProps> = ({
  formData,
  updateFormData,
  handleAddressChange,
  handleHouseNumberChange,
  className = ''
}) => {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Personal Information</h3>
      <FormSection title="Personal Information" subtitle="Please provide your personal details">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Last Name"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              placeholder="Enter your last name"
              required
            />

            <FormField
              label="First Names"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              placeholder="Enter your first names"
              required
            />
          </div>

          <FormField
            label="Initials"
            name="initials"
            value={formData.initials || ''}
            onChange={handleChange}
            placeholder="Enter your initials"
            required
          />

          <div className="mb-4">
            <RadioGroup
              label="Identification Type"
              name="idType"
              value={
                formData.saId ? 'saId' :
                formData.fId ? 'fId' :
                formData.permRes ? 'permRes' : ''
              }
              onChange={(value) => {
                updateFormData({
                  saId: value === 'saId',
                  fId: value === 'fId',
                  permRes: value === 'permRes',
                  citizenType: value === 'saId' ? 'saId' : value === 'fId' ? 'fId' : ''
                })
              }}
              options={[
                { value: 'saId', label: 'SA ID' },
                { value: 'fId', label: 'Passport' },
                { value: 'permRes', label: 'Permanent Residence' }
              ]}
              required
            />
          </div>

          {formData.saId && (
            <FormField
              label="ID Number"
              name="idNumber"
              value={formData.idNumber}
              onChange={handleChange}
              placeholder="Enter your SA ID number"
              required
            />
          )}

          {formData.fId && (
            <FormField
              label="Passport Number"
              name="passport"
              value={formData.passport || ''}
              onChange={handleChange}
              placeholder="Enter your passport number"
              required
            />
          )}

          {formData.permRes && (
            <FormField
              label="Permanent Residence Number"
              name="permResNumber"
              value={formData.permResNumber || ''}
              onChange={handleChange}
              placeholder="Enter your permanent residence number"
              required
            />
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Cell Phone Number"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleChange}
              placeholder="Enter your cell phone number"
              required
            />

            <FormField
              label="Work Number"
              name="workNumber"
              value={formData.workNumber || ''}
              onChange={handleChange}
              placeholder="Enter your work number"
            />
          </div>

          <FormField
            label="Email Address"
            name="email"
            value={formData.email}
            onChange={handleChange}
            type="email"
            placeholder="Enter your email address"
          />

          <AddressInput
            label="Physical Address"
            value={formData.physicalAddress}
            postalCode={formData.postalCode || ''}
            onChange={(address, postalCode) =>
              handleAddressChange(address, postalCode, undefined)
            }
            required
            postalCodeRequired={true}
            placeholder="Enter your physical address"
          />
        </div>
      </FormSection>
    </div>
  )
}

export default PersonalInfo
