import { useMemo, useState, useCallback, useEffect } from 'react';

export interface TableVirtualizationResult<T> {
  items: T[];
  total: number;
  isLoading: boolean;
}

/**
 * A custom hook for implementing virtualized tables with dynamic loading
 * @param allItems All items that could potentially be displayed
 * @param initialBatchSize Number of items to load initially
 * @param batchIncrement Number of items to load in each subsequent batch
 * @returns An object containing the visible items, total count, and loading state
 */
export function useTableVirtualization<T>(
  allItems: T[],
  initialBatchSize: number = 10,
  batchIncrement: number = 10
): TableVirtualizationResult<T> & {
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void;
  handleLoadMore: () => void;
} {
  const [visibleItems, setVisibleItems] = useState<T[]>([]);
  const [loadedCount, setLoadedCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize with first batch of items
  useEffect(() => {
    const initialCount = Math.min(initialBatchSize, allItems.length);
    setVisibleItems(allItems.slice(0, initialCount));
    setLoadedCount(initialCount);
  }, [allItems, initialBatchSize]);

  // Handle scroll event to load more items
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      // If scrolled to bottom (with a small threshold) and more items to load
      if (scrollHeight - scrollTop - clientHeight < 100 && loadedCount < allItems.length && !isLoading) {
        setIsLoading(true);
        // Simulate network delay for smoother UX
        setTimeout(() => {
          const nextBatch = Math.min(loadedCount + batchIncrement, allItems.length);
          setVisibleItems(allItems.slice(0, nextBatch));
          setLoadedCount(nextBatch);
          setIsLoading(false);
        }, 50); // Use a shorter delay for tables
      }
    },
    [loadedCount, allItems, isLoading, batchIncrement]
  );

  // Handle manual load more
  const handleLoadMore = useCallback(() => {
    if (loadedCount < allItems.length && !isLoading) {
      setIsLoading(true);
      setTimeout(() => {
        const nextBatch = Math.min(loadedCount + batchIncrement, allItems.length);
        setVisibleItems(allItems.slice(0, nextBatch));
        setLoadedCount(nextBatch);
        setIsLoading(false);
      }, 50); // Use a shorter delay for tables
    }
  }, [loadedCount, allItems.length, isLoading, batchIncrement]);

  // Return the virtualization result
  return useMemo(
    () => ({
      items: visibleItems,
      total: allItems.length,
      isLoading,
      handleScroll,
      handleLoadMore
    }),
    [visibleItems, allItems.length, isLoading, handleScroll, handleLoadMore]
  );
}
