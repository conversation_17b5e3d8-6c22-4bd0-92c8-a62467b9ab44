import { useState, useEffect } from 'react'
import { FormData, initialFormData } from '../../../types/FormData'
import {
  FormLayout,
  FormSectionType
} from '../../FormComponents'

// Import section components
import PersonalInfo from './sections/PersonalInfo'
import ApplicationType from './sections/ApplicationType'
import FirearmDetails from './sections/FirearmDetails'
import CompetencyCertificate from './sections/CompetencyCertificate'
import TemplateStatus from './sections/TemplateStatus'

// Constants
const TEMPLATE_URL =
  'https://app.gunlicence.co.za/storage/v1/object/public/templates/MISC/Annexure_A_381A.docx'
const TEMPLATE_NAME = 'Annexure_A_381A.docx'

// Types
interface AnnexureAFormProps {
  onSubmit: (data: FormData) => void
}

type ValidationStatus = 'idle' | 'processing' | 'success' | 'error'
type TemplateStatus = 'loading' | 'ready' | 'error'

/**
 * Annexure A (SAPS 381A) Form Component
 *
 * A form for processing Annexure A documents for firearm applications
 * that passes data to DocScript for document generation.
 */
export default function AnnexureA({ onSubmit }: AnnexureAFormProps): JSX.Element {
  // Form state
  const [formData, setFormData] = useState<FormData>({
    ...initialFormData
  })
  const [currentStep, setCurrentStep] = useState<number>(1)

  // Template state
  const [templateStatus, setTemplateStatus] = useState<TemplateStatus>('loading')
  const [templateError] = useState<string | null>(null)

  // Submission state
  const [submissionStatus, setSubmissionStatus] = useState<ValidationStatus>('idle')
  const [submissionMessage, setSubmissionMessage] = useState<string>('')

  // Set template URL and name
  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      templateUrl: TEMPLATE_URL,
      templateName: TEMPLATE_NAME
    }))

    // Simulate template loading
    const timer = setTimeout(() => {
      setTemplateStatus('ready')
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Update form data functions are now handled in the section components

  // Update form data with multiple fields
  const updateFormData = (newData: Partial<FormData>) => {
    setFormData((prev) => ({ ...prev, ...newData }))
  }

  // Handle address autocomplete changes
  const handleAddressChange = (
    address: string,
    postalCode?: string,
    isWorkAddress = false
  ) => {
    if (isWorkAddress) {
      setFormData({
        ...formData,
        workAddress: address,
        workPostalCode: postalCode || ''
      })
    } else {
      setFormData({
        ...formData,
        physicalAddress: address,
        postalCode: postalCode || ''
      })
    }
  }

  // Define form sections
  const sections: FormSectionType[] = [
    { id: 'personal', title: 'Personal Information' },
    { id: 'application', title: 'Application Type' },
    { id: 'firearm', title: 'Firearm Details' },
    { id: 'competency', title: 'Competency Certificate' }
  ]

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setSubmissionStatus('processing')
      setSubmissionMessage('Processing your form...')

      // Set document as processed
      const finalFormData = {
        ...formData,
        documentProcessed: true
      }

      // Submit the form data
      onSubmit(finalFormData)

      // Update submission status
      setSubmissionStatus('success')
      setSubmissionMessage('Form submitted successfully!')
    } catch (error) {
      console.error('Error submitting form:', error)
      setSubmissionStatus('error')
      setSubmissionMessage('Error submitting form. Please try again.')
    }
  }



  // Render the current step based on section ID
  const renderCurrentStep = () => {
    const sectionId = sections[currentStep - 1].id

    switch (sectionId) {
      case 'personal':
        return (
          <PersonalInfo
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
          />
        )
      case 'application':
        return (
          <ApplicationType
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'firearm':
        return (
          <FirearmDetails
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'competency':
        return (
          <CompetencyCertificate
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      default:
        return null
    }
  }

  // Render template status component
  const renderTemplateStatus = () => (
    <TemplateStatus
      status={templateStatus}
      templateName={TEMPLATE_NAME}
      error={templateError}
    />
  )

  // Handle cancel submission
  const onCancelSubmission = () => {
    setSubmissionStatus('idle')
  }

  // Main component render
  return (
    <FormLayout
      title="Annexure A (SAPS 381A)"
      sections={sections}
      currentStep={currentStep}
      setCurrentStep={setCurrentStep}
      onSubmit={handleSubmit}
      submissionStatus={submissionStatus}
      submissionMessage={submissionMessage}
      onCancelSubmission={onCancelSubmission}
      isDocScriptForm={true}
    >
      {templateStatus === 'error' && renderTemplateStatus()}
      {renderCurrentStep()}
    </FormLayout>
  )
}
