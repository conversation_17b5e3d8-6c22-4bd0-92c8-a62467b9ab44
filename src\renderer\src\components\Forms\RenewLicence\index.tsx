import React, { useState, useEffect, useMemo } from 'react'
import { isDevelopment } from '../../../utils/environment'
import { RenewLicenceData, initialRenewLicenceData } from '../../../types/RenewLicenceData'
import {
  FormLayout,
  FormSectionType
} from '../../FormComponents'

// Import types and helpers
import { ValidationStatus, TemplateStatus as TemplateStatusType, SubmissionData } from './utils/types'
import { isBusinessLicence } from './utils/helpers'

// Import test data and toast
import {
  testSelfDefenceData,
  testDedicatedHuntingData,
  testBusinessHuntingData,
  testBusinessSecurityData
} from './utils/testData'
import { showToast } from './utils/ToastManager'

// Import section components
import PersonalInfo from './sections/PersonalInfo'
import TypeOfLicence from './sections/TypeOfLicence'
import OriginalLicenceDetails from './sections/OriginalLicenceDetails'
import JuristicPersonDetails from './sections/JuristicPersonDetails'
import ResponsiblePerson from './sections/ResponsiblePerson'
import OtherInformation from './sections/OtherInformation'
import TemplateStatus from './sections/TemplateStatus'

// Constants
const TEMPLATE_URL =
  'https://app.gunlicence.co.za/storage/v1/object/public/templates/Licence/518A_SAPS_Form.docx'
const TEMPLATE_NAME = 'Renew Licence.docx'

// Types for the component props
interface RenewLicenceFormProps {
  onSubmit: (data: RenewLicenceData) => void
  templateName?: string
}

/**
 * RenewLicence - Firearm Licence Renewal Application Form Component
 *
 * A multi-step form for processing firearm licence renewal applications
 * that passes data to DocScript for document generation.
 */
export default function RenewLicence({ onSubmit, templateName }: RenewLicenceFormProps): JSX.Element {
  // Form state
  const [formData, setFormData] = useState<RenewLicenceData>(initialRenewLicenceData)
  const [currentStep, setCurrentStep] = useState<number>(1)

  // Template state
  const [templateStatus, setTemplateStatus] = useState<TemplateStatusType>('loading')
  const [templateError, setTemplateError] = useState<string | null>(null)

  // Submission state
  const [submissionStatus, setSubmissionStatus] = useState<ValidationStatus>('idle')
  const [submissionMessage, setSubmissionMessage] = useState<string | null>(null)

  // Check if template is available
  useEffect(() => {
    const checkTemplate = async () => {
      try {
        const response = await fetch(TEMPLATE_URL, { method: 'HEAD' })
        if (response.ok) {
          setTemplateStatus('ready')
        } else {
          setTemplateStatus('error')
          setTemplateError('Template not found. Please contact support.')
        }
      } catch (error) {
        setTemplateStatus('error')
        setTemplateError('Error checking template. Please check your internet connection.')
      }
    }

    checkTemplate()
  }, [])

  // Form sections definition
  const sections: FormSectionType[] = useMemo(
    () => {
      // Base sections that are always shown
      const baseSections = [
        { id: 'licenceType', title: 'Type of Licence' },
        { id: 'originalLicence', title: 'Details of Original Licence' },
        { id: 'personal', title: 'Personal Information' },
        { id: 'otherInfo', title: 'Other Information' }
      ]

      // Add Juristic Person's Details and Responsible Person's sections only for business licence types
      if (isBusinessLicence(formData)) {
        return [
          ...baseSections,
          { id: 'juristic', title: 'Juristic Person\'s Details' },
          { id: 'responsible', title: 'Responsible Person\'s Details' }
        ]
      } else {
        return baseSections
      }
    },
    [formData.s20, formData.s20a, formData.s20b]
  )

  // Update form data helper function
  const updateFormData = (newData: Partial<RenewLicenceData>) => {
    setFormData((prev) => ({
      ...prev,
      ...newData
    }))
  }

  // Handle address field changes
  const handleAddressChange = (
    address: string,
    postalCode?: string,
    houseNumber?: string,
    isWorkAddress = false,
    isResponsiblePerson = false
  ) => {
    if (isResponsiblePerson) {
      updateFormData({
        responsiblePersonAddress: address,
        responsiblePersonPostalCode: postalCode || '',
        responsiblePersonHouseNumber: houseNumber || ''
      })
    } else if (isWorkAddress) {
      updateFormData({
        workAddress: address,
        workPostalCode: postalCode || '',
        workHouseUnitNumber: houseNumber || ''
      })
    } else {
      updateFormData({
        physicalAddress: address,
        postalCode: postalCode || '',
        houseUnitNumber: houseNumber || ''
      })
    }
  }

  // Handle house/unit number changes
  const handleHouseNumberChange = (houseNumber: string, isWorkAddress = false, isResponsiblePerson = false) => {
    if (isResponsiblePerson) {
      updateFormData({
        responsiblePersonHouseNumber: houseNumber
      })
    } else if (isWorkAddress) {
      updateFormData({
        workHouseUnitNumber: houseNumber
      })
    } else {
      updateFormData({
        houseUnitNumber: houseNumber
      })
    }
  }

  // Validate the current step
  const validateCurrentStep = () => {
    switch (currentStep) {
      case 1: // Type of Licence
        if (
          !(
            formData.s13 ||
            formData.s15 ||
            formData.s16 ||
            formData.s20 ||
            formData.s20a ||
            formData.s20b
          )
        ) {
          return 'Please select a licence type'
        }
        return null

      case 2: // Details of Original Licence
        if (!formData.originalLicenceNumber?.trim()) {
          return 'Original Licence Number is required'
        }
        if (!formData.originalLicenceIssueDate?.trim()) {
          return 'Original Licence Issue Date is required'
        }
        if (!formData.originalLicenceExpiryDate?.trim()) {
          return 'Original Licence Expiry Date is required'
        }

        // Validate additional original licences if any
        if (formData.additionalOriginalLicences && formData.additionalOriginalLicences.length > 0) {
          for (let i = 0; i < formData.additionalOriginalLicences.length; i++) {
            const licence = formData.additionalOriginalLicences[i]
            if (!licence.licenceNumber.trim()) {
              return `Original Licence ${i + 2}: Licence Number is required`
            }
            if (!licence.issueDate.trim()) {
              return `Original Licence ${i + 2}: Issue Date is required`
            }
            if (!licence.expiryDate.trim()) {
              return `Original Licence ${i + 2}: Expiry Date is required`
            }
          }
        }
        return null

      case 3: // Personal Information
        if (!formData.firstName.trim()) {
          return 'First Names are required'
        }
        if (!formData.lastName.trim()) {
          return 'Last Name is required'
        }
        if (!formData.initials?.trim()) {
          return 'Initials are required'
        }

        // Validate identification based on selected type
        if (!(formData.saId || formData.fId || formData.permRes)) {
          return 'Please select an identification type'
        }

        if (formData.saId && !formData.idNumber.trim()) {
          return 'SA ID Number is required'
        }
        if (formData.fId && !formData.passport?.trim()) {
          return 'Passport Number is required'
        }
        if (formData.permRes && !formData.permResNumber?.trim()) {
          return 'Permanent Residence Number is required'
        }

        if (!formData.phoneNumber.trim()) {
          return 'Phone Number is required'
        }
        if (!formData.email.trim()) {
          return 'Email is required'
        }
        if (!formData.physicalAddress.trim()) {
          return 'Physical Address is required'
        }
        if (!formData.postalCode?.trim()) {
          return 'Postal Code is required'
        }
        return null

      case 4: // Other Information
        // Validate 90 days before expiry question
        if (!(formData.before90DaysYes || formData.before90DaysNo)) {
          return 'Please answer if your application was handed in 90 days before expiry'
        }
        if (formData.before90DaysNo && !formData.before90DaysReason?.trim()) {
          return 'Please provide a reason for not submitting 90 days before expiry'
        }

        // Validate after due date but before expiry question
        if (!(formData.afterDueBeforeExpiryYes || formData.afterDueBeforeExpiryNo)) {
          return 'Please answer if your application was handed in after the due date but before expiry'
        }
        if (formData.afterDueBeforeExpiryYes && !formData.afterDueBeforeExpiryReason?.trim()) {
          return 'Please provide a reason for submitting after the due date but before expiry'
        }

        // Validate after expiry question
        if (!(formData.afterExpiryYes || formData.afterExpiryNo)) {
          return 'Please answer if your application was handed in after expiry'
        }
        if (formData.afterExpiryYes && !formData.afterExpiryReason?.trim()) {
          return 'Please provide a reason for submitting after expiry'
        }

        return null

      case 5: // Juristic Person's Details
        // Only validate if a business licence type is selected
        if (isBusinessLicence(formData)) {
          if (!formData.companyName?.trim()) {
            return 'Registered Company Name is required'
          }
          if (!formData.tradingAsName?.trim()) {
            return 'Trading as Name is required'
          }
          if (!formData.farNumber?.trim()) {
            return 'FAR Number is required'
          }
          if (!formData.postalAddress?.trim()) {
            return 'Postal Address is required'
          }
          if (!formData.workPostalCode?.trim()) {
            return 'Postal Code is required'
          }
          if (!formData.businessTelNumber?.trim()) {
            return 'Business Telephone Number is required'
          }
          if (!formData.workNumber?.trim()) {
            return 'Work Number is required'
          }
          if (!formData.companyEmail?.trim()) {
            return 'Email Address is required'
          }
        }
        return null

      case 6: // Responsible Person's Details
        // Only validate if a business licence type is selected
        if (isBusinessLicence(formData)) {
          if (!formData.responsiblePersonName?.trim()) {
            return 'Responsible Person Name is required'
          }
          if (!(formData.responsiblePersonSaId || formData.responsiblePersonPassport)) {
            return 'Please select an identification type for the Responsible Person'
          }
          if (formData.responsiblePersonSaId && !formData.responsiblePersonIdNumber?.trim()) {
            return 'Identity Number of Responsible Person is required'
          }
          if (formData.responsiblePersonPassport && !formData.responsiblePersonPassportNumber?.trim()) {
            return 'Passport Number of Responsible Person is required'
          }
          if (!formData.responsiblePersonCellNumber?.trim()) {
            return 'Cell Phone Number of Responsible Person is required'
          }
          if (!formData.responsiblePersonAddress?.trim()) {
            return 'Physical Address of Responsible Person is required'
          }
          if (!formData.responsiblePersonPostalCode?.trim()) {
            return 'Postal Code of Responsible Person is required'
          }
        }
        return null

      // Firearm Details section removed

      default:
        return null
    }
  }

  // Validate the form data before submission
  const validateForm = () => {
    // First validate the current step
    const currentStepError = validateCurrentStep()
    if (currentStepError) {
      updateSubmissionState('error', currentStepError)
      return false
    }

    // Then validate the entire form
    const errorMessage = validateFormData()
    if (errorMessage) {
      updateSubmissionState('error', errorMessage)
      return false
    }
    return true
  }

  // Validate form data (returns error message or null if valid)
  const validateFormData = (): string | null => {
    // Basic validation
    if (!formData.firstName) return 'First Names are required'
    if (!formData.lastName) return 'Last Name is required'
    if (!formData.initials) return 'Initials are required'

    // Validate identification based on selected type
    if (!(formData.saId || formData.fId || formData.permRes)) {
      return 'Please select an identification type'
    }

    if (formData.saId && !formData.idNumber) return 'SA ID Number is required'
    if (formData.fId && !formData.passport) return 'Passport Number is required'
    if (formData.permRes && !formData.permResNumber) return 'Permanent Residence Number is required'

    if (!formData.phoneNumber) return 'Phone Number is required'
    if (!formData.email) return 'Email is required'
    if (!formData.physicalAddress) return 'Physical Address is required'
    if (!formData.postalCode) return 'Postal Code is required'

    // Validate Other Information fields
    if (!(formData.before90DaysYes || formData.before90DaysNo)) {
      return 'Please answer if your application was handed in 90 days before expiry'
    }
    if (formData.before90DaysNo && !formData.before90DaysReason) {
      return 'Please provide a reason for not submitting 90 days before expiry'
    }

    if (!(formData.afterDueBeforeExpiryYes || formData.afterDueBeforeExpiryNo)) {
      return 'Please answer if your application was handed in after the due date but before expiry'
    }
    if (formData.afterDueBeforeExpiryYes && !formData.afterDueBeforeExpiryReason) {
      return 'Please provide a reason for submitting after the due date but before expiry'
    }

    if (!(formData.afterExpiryYes || formData.afterExpiryNo)) {
      return 'Please answer if your application was handed in after expiry'
    }
    if (formData.afterExpiryYes && !formData.afterExpiryReason) {
      return 'Please provide a reason for submitting after expiry'
    }

    // Juristic Person validation - only if a business licence type is selected
    if (formData.s20 || formData.s20a || formData.s20b) {
      if (!formData.companyName) return 'Registered Company Name is required'
      if (!formData.tradingAsName) return 'Trading as Name is required'
      if (!formData.farNumber) return 'FAR Number is required'
      if (!formData.postalAddress) return 'Postal Address is required'
      if (!formData.workPostalCode) return 'Postal Code is required'
      if (!formData.businessTelNumber) return 'Business Telephone Number is required'
      if (!formData.workNumber) return 'Work Number is required'
      if (!formData.companyEmail) return 'Email Address is required'

      // Responsible Person validation
      if (!formData.responsiblePersonName) return 'Responsible Person Name is required'
      if (!(formData.responsiblePersonSaId || formData.responsiblePersonPassport)) {
        return 'Please select an identification type for the Responsible Person'
      }
      if (formData.responsiblePersonSaId && !formData.responsiblePersonIdNumber) {
        return 'Identity Number of Responsible Person is required'
      }
      if (formData.responsiblePersonPassport && !formData.responsiblePersonPassportNumber) {
        return 'Passport Number of Responsible Person is required'
      }
      if (!formData.responsiblePersonCellNumber) {
        return 'Cell Phone Number of Responsible Person is required'
      }
      if (!formData.responsiblePersonAddress) {
        return 'Physical Address of Responsible Person is required'
      }
      if (!formData.responsiblePersonPostalCode) {
        return 'Postal Code of Responsible Person is required'
      }
    }

    // Type of Licence validation
    if (
      !(
        formData.s13 ||
        formData.s15 ||
        formData.s16 ||
        formData.s20 ||
        formData.s20a ||
        formData.s20b
      )
    ) {
      return 'Please select a licence type'
    }

    // Original licence details validation
    if (!formData.originalLicenceNumber) return 'Original Licence Number is required'
    if (!formData.originalLicenceIssueDate) return 'Original Licence Issue Date is required'
    if (!formData.originalLicenceExpiryDate) return 'Original Licence Expiry Date is required'

    // Validate additional original licences if any
    if (formData.additionalOriginalLicences && formData.additionalOriginalLicences.length > 0) {
      for (let i = 0; i < formData.additionalOriginalLicences.length; i++) {
        const licence = formData.additionalOriginalLicences[i]
        if (!licence.licenceNumber.trim()) {
          return `Original Licence ${i + 2}: Licence Number is required`
        }
        if (!licence.issueDate.trim()) {
          return `Original Licence ${i + 2}: Issue Date is required`
        }
        if (!licence.expiryDate.trim()) {
          return `Original Licence ${i + 2}: Expiry Date is required`
        }
      }
    }

    // No renewal specific validation needed anymore

    return null
  }

  // Helper function to update submission state
  const updateSubmissionState = (status: ValidationStatus, message: string | null) => {
    setSubmissionStatus(status)
    setSubmissionMessage(message)
  }

  // Function to handle form submission
  const handleSubmit = (e: React.FormEvent<Element>) => {
    e.preventDefault()

    if (!validateForm()) return

    // Update submission status
    updateSubmissionState('processing', 'Processing your application...')

    // Set template name and mark as NOT processed so DocScript will process it
    // Create a separate object for placeholders to avoid type conflicts
    const placeholders = {
      // Add placeholders for Type of Licence section
      S13: formData.s13 ? 'X' : '',
      S20A: formData.s20a ? 'X' : '',
      S15: formData.s15 ? 'X' : '',
      S16: formData.s16 ? 'X' : '',
      S20: formData.s20 ? 'X' : '',
      S20B: formData.s20b ? 'X' : '',
      s13: formData.s13 ? 'X' : '',
      s20a: formData.s20a ? 'X' : '',
      s15: formData.s15 ? 'X' : '',
      s16: formData.s16 ? 'X' : '',
      s20: formData.s20 ? 'X' : '',
      s20b: formData.s20b ? 'X' : '',
    }

    // Create the submission data with all the placeholders
    const submissionData: SubmissionData = {
      ...formData,
      templateName: templateName || TEMPLATE_NAME,
      templateUrl: TEMPLATE_URL,
      documentProcessed: false,
      // Add placeholders from the placeholders object
      ...placeholders,

      // Add placeholders for Details of Original Licence section
      OrigLicNum: formData.originalLicenceNumber || '',
      ORIGLICNUM: formData.originalLicenceNumber || '',
      OriginalLicenceNumber: formData.originalLicenceNumber || '',
      ORIGINALLICENCENUMBER: formData.originalLicenceNumber || '',
      OrigDateIssued: formData.originalLicenceIssueDate || '',
      ORIGDATEISSUED: formData.originalLicenceIssueDate || '',
      OriginalLicenceIssueDate: formData.originalLicenceIssueDate || '',
      ORIGINALLICENCEISSUEDATE: formData.originalLicenceIssueDate || '',
      OrigExpiryDate: formData.originalLicenceExpiryDate || '',
      ORIGEXPIRYDATE: formData.originalLicenceExpiryDate || '',
      OriginalLicenceExpiryDate: formData.originalLicenceExpiryDate || '',
      ORIGINALLICENCEEXPIRYDATE: formData.originalLicenceExpiryDate || '',

      // Add placeholders for Personal Information section
      LastName: formData.lastName || '',
      LASTNAME: formData.lastName || '',
      FirstNames: formData.firstName || '',
      FIRSTNAMES: formData.firstName || '',
      FirstName: formData.firstName || '',
      FIRSTNAME: formData.firstName || '',
      Name: formData.firstName + ' ' + formData.lastName || '',
      NAME: formData.firstName + ' ' + formData.lastName || '',
      Initials: formData.initials || '',
      INITIALS: formData.initials || '',
      Initial: formData.initials || '',  // Alternative spelling
      INITIAL: formData.initials || '',
      Initi: formData.initials || '',    // Truncated version that appears in the error
      INITI: formData.initials || '',
      Address: formData.physicalAddress || '',
      ADDRESS: formData.physicalAddress || '',
      PostalCode: formData.postalCode || '',
      POSTALCODE: formData.postalCode || '',
      WorkNumber: formData.workNumber || '',
      WORKNUMBER: formData.workNumber || '',
      PhoneNumber: formData.phoneNumber || '',
      PHONENUMBER: formData.phoneNumber || '',
      Cell: formData.phoneNumber || '',
      CELL: formData.phoneNumber || '',
      Email: formData.email || '',
      EMAIL: formData.email || '',

      // Add placeholders for Juristic Person's Details (only for business licence types)
      ...(formData.s20 || formData.s20a || formData.s20b ? {
        CompanyName: formData.companyName || '',
        COMPANYNAME: formData.companyName || '',
        TradingAs: formData.tradingAsName || '',
        TRADINGAS: formData.tradingAsName || '',
        FARNumber: formData.farNumber || '',
        FARNUMBER: formData.farNumber || '',
        PostalAddress: formData.postalAddress || '',
        POSTALADDRESS: formData.postalAddress || '',
        JuristicPostalCode: formData.workPostalCode || '',
        JURISTICPOSTALCODE: formData.workPostalCode || '',
        BusinessTelNumber: formData.businessTelNumber || '',
        BUSINESSTELNUMBER: formData.businessTelNumber || '',
        JuristicWorkNumber: formData.workNumber || '',
        JURISTICWORKNUMBER: formData.workNumber || '',
        JuristicEmail: formData.companyEmail || '',
        JURISTICEMAIL: formData.companyEmail || '',

        // Add placeholders for Responsible Person's Details
        ResponsiblePersonName: formData.responsiblePersonName || '',
        RESPONSIBLEPERSONNAME: formData.responsiblePersonName || '',
        ResponsiblePersonSAID: formData.responsiblePersonSaId ? 'X' : '',
        RESPONSIBLEPERSONSAID: formData.responsiblePersonSaId ? 'X' : '',
        ResponsiblePersonPassport: formData.responsiblePersonPassport ? 'X' : '',
        RESPONSIBLEPERSONPASSPORT: formData.responsiblePersonPassport ? 'X' : '',
        ResponsiblePersonIDNumber: formData.responsiblePersonIdNumber || '',
        RESPONSIBLEPERSONIDNUMBER: formData.responsiblePersonIdNumber || '',
        ResponsiblePersonPassportNumber: formData.responsiblePersonPassportNumber || '',
        RESPONSIBLEPERSONPASSPORTNUMBER: formData.responsiblePersonPassportNumber || '',
        ResponsiblePersonCellNumber: formData.responsiblePersonCellNumber || '',
        RESPONSIBLEPERSONCELLNUMBER: formData.responsiblePersonCellNumber || '',
        ResponsiblePersonAddress: formData.responsiblePersonAddress || '',
        RESPONSIBLEPERSONADDRESS: formData.responsiblePersonAddress || '',
        ResponsiblePersonPostalCode: formData.responsiblePersonPostalCode || '',
        RESPONSIBLEPERSONPOSTALCODE: formData.responsiblePersonPostalCode || ''
      } : {}),

      // Add placeholders for identification type
      SAID: formData.saId ? 'X' : '',
      SAId: formData.saId ? 'X' : '',
      Passport: formData.fId ? 'X' : '',
      PASSPORT: formData.fId ? 'X' : '',
      FId: formData.fId ? 'X' : '',
      FID: formData.fId ? 'X' : '',
      PermRes: formData.permRes ? 'X' : '',
      PERMRES: formData.permRes ? 'X' : '',
      IDNumber: formData.idNumber || '',
      IDNUMBER: formData.idNumber || '',
      IdNumber: formData.idNumber || '',
      ID: formData.idNumber || '',  // Alternative spelling
      Id: formData.idNumber || '',
      SAID_NUM: formData.idNumber || '',
      PassportNumber: formData.passport || '',
      PASSPORTNUMBER: formData.passport || '',
      PassportNum: formData.passport || '',
      PASSPORTNUM: formData.passport || '',
      PermResNumber: formData.permResNumber || '',
      PERMRESNUMBER: formData.permResNumber || '',
      PermResNum: formData.permResNumber || '',
      PERMRESNUM: formData.permResNumber || '',

      // Add placeholders for Other Information section using the same format as in the 518A form
      '15A': formData.before90DaysYes ? 'X' : '',
      '15a': formData.before90DaysYes ? 'X' : '',
      '15B': formData.before90DaysNo ? 'X' : '',
      '15b': formData.before90DaysNo ? 'X' : '',
      '15C': formData.before90DaysReason || '',
      '15c': formData.before90DaysReason || '',
      Before90DaysReason: formData.before90DaysReason || '',
      BEFORE90DAYSREASON: formData.before90DaysReason || '',
      '16A': formData.afterExpiryYes ? 'X' : '',
      '16a': formData.afterExpiryYes ? 'X' : '',
      '16B': formData.afterExpiryNo ? 'X' : '',
      '16b': formData.afterExpiryNo ? 'X' : '',
      '16C': formData.afterExpiryReason || '',
      '16c': formData.afterExpiryReason || '',
      AfterExpiryReason: formData.afterExpiryReason || '',
      AFTEREXPIRYREASON: formData.afterExpiryReason || '',
      // Additional placeholders for after due date but before expiry
      '17A': formData.afterDueBeforeExpiryYes ? 'X' : '',
      '17a': formData.afterDueBeforeExpiryYes ? 'X' : '',
      '17B': formData.afterDueBeforeExpiryNo ? 'X' : '',
      '17b': formData.afterDueBeforeExpiryNo ? 'X' : '',
      '17C': formData.afterDueBeforeExpiryReason || '',
      '17c': formData.afterDueBeforeExpiryReason || '',
      AfterDueBeforeExpiryReason: formData.afterDueBeforeExpiryReason || '',
      AFTERDUEBEFOREEXPIRYREASON: formData.afterDueBeforeExpiryReason || '',
      // Add placeholders for additional original licences
      OrigLicNum2: formData.additionalOriginalLicences?.[0]?.licenceNumber || '',
      ORIGLICNUM2: formData.additionalOriginalLicences?.[0]?.licenceNumber || '',
      OrigDateIssued2: formData.additionalOriginalLicences?.[0]?.issueDate || '',
      ORIGDATEISSUED2: formData.additionalOriginalLicences?.[0]?.issueDate || '',
      OrigExpiryDate2: formData.additionalOriginalLicences?.[0]?.expiryDate || '',
      ORIGEXPIRYDATE2: formData.additionalOriginalLicences?.[0]?.expiryDate || '',

      OrigLicNum3: formData.additionalOriginalLicences?.[1]?.licenceNumber || '',
      ORIGLICNUM3: formData.additionalOriginalLicences?.[1]?.licenceNumber || '',
      OrigDateIssued3: formData.additionalOriginalLicences?.[1]?.issueDate || '',
      ORIGDATEISSUED3: formData.additionalOriginalLicences?.[1]?.issueDate || '',
      OrigExpiryDate3: formData.additionalOriginalLicences?.[1]?.expiryDate || '',
      ORIGEXPIRYDATE3: formData.additionalOriginalLicences?.[1]?.expiryDate || '',

      OrigLicNum4: formData.additionalOriginalLicences?.[2]?.licenceNumber || '',
      ORIGLICNUM4: formData.additionalOriginalLicences?.[2]?.licenceNumber || '',
      OrigDateIssued4: formData.additionalOriginalLicences?.[2]?.issueDate || '',
      ORIGDATEISSUED4: formData.additionalOriginalLicences?.[2]?.issueDate || '',
      OrigExpiryDate4: formData.additionalOriginalLicences?.[2]?.expiryDate || '',
      ORIGEXPIRYDATE4: formData.additionalOriginalLicences?.[2]?.expiryDate || ''
    }

    // Call the parent component's onSubmit function
    try {
      // Log the submission data for debugging
      console.log('Submitting form data:', submissionData);

      // Cast the submission data to any and then to RenewLicenceData to satisfy the type checker
      // This is safe because we're preserving all the required RenewLicenceData properties
      // We need to use 'as any' because TypeScript can't verify that our placeholders won't conflict
      onSubmit(submissionData as any as RenewLicenceData)
      updateSubmissionState('success', 'Application submitted to DocScript for processing!')
    } catch (error) {
      console.error('Error submitting application:', error);

      // Provide more detailed error message if available
      let errorMessage = 'An error occurred while submitting the application.';
      if (error instanceof Error) {
        errorMessage = `Error: ${error.message}`;
      }

      updateSubmissionState('error', errorMessage)
    }
  }

  // Handle cancel submission
  const onCancelSubmission = () => {
    setSubmissionStatus('idle')
  }

  // Render the current step based on section ID
  const renderCurrentStep = () => {
    const sectionId = sections[currentStep - 1].id

    switch (sectionId) {
      case 'licenceType':
        return (
          <TypeOfLicence
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'originalLicence':
        return (
          <OriginalLicenceDetails
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'personal':
        return (
          <PersonalInfo
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
            handleHouseNumberChange={handleHouseNumberChange}
          />
        )
      case 'juristic':
        return (
          <JuristicPersonDetails
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
            handleHouseNumberChange={handleHouseNumberChange}
          />
        )
      case 'responsible':
        return (
          <ResponsiblePerson
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
            handleHouseNumberChange={handleHouseNumberChange}
          />
        )
      case 'otherInfo':
        return (
          <OtherInformation
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      default:
        return null
    }
  }

  // Render template status component
  const renderTemplateStatus = () => (
    <TemplateStatus
      status={templateStatus}
      templateName={TEMPLATE_NAME}
      error={templateError}
    />
  )

  // Define test data sets
  const testDataSets = {
    'self-defence': { data: testSelfDefenceData, name: 'Self-Defence (Section 13)' },
    'dedicated-hunting': { data: testDedicatedHuntingData, name: 'Dedicated Hunting (Section 16)' },
    'business-hunting': { data: testBusinessHuntingData, name: 'Business Hunting (Section 20)' },
    'business-security': { data: testBusinessSecurityData, name: 'Business Security (Section 20B)' }
  }

  // Create options for the dropdown
  const testDataOptions = [
    { name: 'Self-Defence (Section 13)', value: 'self-defence' },
    { name: 'Dedicated Hunting (Section 16)', value: 'dedicated-hunting' },
    { name: 'Business Hunting (Section 20)', value: 'business-hunting' },
    { name: 'Business Security (Section 20B)', value: 'business-security' }
  ]

  // Fill form with test data
  const fillWithTestData = (dataType: string) => {
    // Get the selected test data set
    const { data, name } = testDataSets[dataType]

    // Update the form data
    setFormData(data)

    // Show a toast notification
    showToast({
      message: `Form filled with ${name} test data`,
      type: 'info',
      duration: 3000
    })

    console.log(`Form filled with ${name} test data`)
  }

  // Main component render
  return (
    <div className="relative">
      {/* Test Data Dropdown - Only shown in development mode */}
      {isDevelopment() && (
        <div className="absolute top-4 right-4 z-10">
          <div className="flex items-center space-x-2">
            <select
              className="bg-gray-700 text-white border border-gray-600 rounded px-3 py-1 text-sm"
              onChange={(e) => fillWithTestData(e.target.value)}
              defaultValue=""
            >
              <option value="" disabled>
                Fill with test data
              </option>
              {testDataOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      <FormLayout
        title="Firearm Licence Renewal Application"
        sections={sections}
        currentStep={currentStep}
        setCurrentStep={setCurrentStep}
        onSubmit={handleSubmit}
        submissionStatus={submissionStatus}
        submissionMessage={submissionMessage}
        onCancelSubmission={onCancelSubmission}
        isDocScriptForm={true}
      >
        {templateStatus === 'error' && renderTemplateStatus()}
        {renderCurrentStep()}
      </FormLayout>
    </div>
  )
}
