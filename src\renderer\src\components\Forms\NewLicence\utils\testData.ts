import { NewLicenceData } from '../../../../types/NewLicenceData'

/**
 * Test data for the NewLicence form with Private Owner
 * This data can be used to quickly fill the form for testing purposes
 */
export const testPrivateOwnerData: NewLicenceData = {
  // Standard FormData properties
  fullName: '<PERSON> Test Doe',
  firstName: 'John',
  lastName: 'Doe',
  idNumber: '8001015009087',
  phoneNumber: '**********',
  email: '<EMAIL>',
  physicalAddress: '123 Test Street, Pretoria',
  documentProcessed: false,
  companyName: 'Test Company',
  tradeProfession: 'Software Developer',
  workAddress: '456 Work Street, Johannesburg',
  workPostalCode: '2000',
  workNumber: '**********',
  saId: true,
  fId: false,
  permRes: false,
  sexM: true,
  sexF: false,
  singles: false,
  married: true,
  divorced: false,
  widower: false,
  widow: false,
  citizenType: 'saId',
  maritalStatus: 'married',
  spouseIdType: 'spouseId',
  postalCode: '0001',
  houseUnitNumber: '123',
  workHouseUnitNumber: '456',

  // Additional properties
  mainHF: true,
  addHF: false,

  // Licence type
  s13: true, // Self-defence
  s15: false,
  s16: false,
  s20: false,
  s20a: false,
  s20b: false,
  s20c: false,

  // Firearm type
  rifle: false,
  shotgun: false,
  pistol: true,
  comb: false,
  otherDesign: false,
  otherDesignE: '',

  // SAP 350 (A) DETAILS - Firearm received from details
  sap350aName: 'John Smith',
  sap350aIdFar: '8001015009087',
  sap350aAddress: '123 Main Street, Pretoria',
  sap350aPostal: '0001',
  sap350aDate: '2023-01-15',

  // Firearm details
  semi: true,
  auto: false,
  man: false,
  otherAction: false,
  otherF: '',
  engg: 'Test Engravings',
  make: 'Glock',
  model: '19',
  caliber: '9mm',
  pafk: '123 Pretorius Street, Pretoria, South Africa',
  pafkPostal: '0001',
  flap: 'John Smith',
  flid: '8001015009087',
  designation: 'Owner',
  apDate: '2023-06-15',
  apPlace: 'Pretoria',

  // Serial numbers
  bsn: 'BSN12345',
  fsn: 'FSN12345',
  rsn: 'RSN12345',

  // Component makes
  bsnm: 'Barrel Make',
  fsnm: 'Frame Make',
  rsnm: 'Receiver Make',

  // Current owner type
  pcoa: true, // Private owner
  pcob: false,
  pcoc: false,
  pcoe: false,

  // Firearm dealer details (not used in this test data)
  fdrcn: '',
  fdtas: '',
  fdfarn: '',
  fdbadre: '',
  fdpostal: '',
  fdbcall: '',
  fdbmail: '',
  fdrpns: '',
  fdrpidsa: false,
  fdrpidno: false,
  fdrpid: '',
  fdrpcall: '',
  fdrpadd: '',
  fdrpostal: '',

  // Company details (not used in this test data)
  crcn: '',
  ctn: '',
  cfarn: '',
  cpadd: '',
  cpostal: '',
  cbtn: '',
  ccem: '',
  crpns: '',
  crpidsa: false,
  crpidno: false,
  crpid: '',
  crpcall: '',
  crpadd: '',
  crpostal: '',

  // Estate details (not used in this test data)
  executor: false,
  administrator: false,
  curatorship: false,
  trust: false,
  deLast: '',
  deFullName: '',
  deInitials: '',
  idOf: '',
  deEName: '',
  deEIdNo: '',
  deEIdSa: false,
  deEId: '',
  deEAdd: '',
  deEPostal: '',
  deECell: '',
  deEEmail: '',

  // Natural person's details
  npsaid: true,
  npidno: false,
  npid: '8001015009087',
  npname: 'John',
  nplname: 'Doe',
  npinitials: 'JD',
  npdateb: '1980-01-01',
  npage: '43',
  npsexmale: true,
  npsexfemale: false,
  npaddress: '123 Test Street, Pretoria',
  nppostal: '0001',
  nptypeofres: 'House',
  npprof: 'Software Developer',
  npecname: 'Test Company',
  npecadd: '456 Work Street, Johannesburg',
  npecpostal: '2000',
  npcell: '**********',
  npworkc: '**********',
  npemail: '<EMAIL>',

  // Private owner additional fields
  cell: '**********',
  workC: '**********',
  address: '123 Test Street, Pretoria',
  passport: '',
  afhy: false, // Additional firearm licence holders - No
  afhn: true,  // Additional firearm licence holders - Yes
  initials: 'JD',

  // Spouse details
  spouseFullName: 'Jane Doe',
  spouseIdNo: '8101015009087',
  spousePassN: '',

  // Competency certificate - using the correct placeholders
  tradeFirearm: false,
  possessFirearm: true,
  compHandgun: true, // CompHandgun - Competency for Handgun
  compRifle: false, // CompRifle - Competency for Rifle
  compShotgun: false, // CompShotgun - Competency for Shotgun
  compSelfLoading: true, // CompSelfLoading - Competency for Self-Loading
  f2ab: 'CERT12345', // F2AB - Competency Certificate Number
  f3: '2022-01-01',  // F3 - Competency Issue Date
  f4: '2027-01-01',  // F4 - Competency Expiry Date

  // Training Institution
  nameInst: 'Test Training Institution',
  serialCert: 'SERIAL12345',
  certIssueDate: '2022-01-01',
  certExpiryDate: '2027-01-01',

  // Firearms in Possession
  firearmsInPossession: [
    {
      type: 'Pistol',
      calibre: '9mm',
      make: 'Glock',
      barrelSerialNo: 'BSN12345',
      frameSerialNo: 'FSN12345',
      licenceNo: 'LIC12345'
    },
    {
      type: 'Rifle',
      calibre: '.308',
      make: 'Remington',
      barrelSerialNo: 'BSN67890',
      frameSerialNo: 'FSN67890',
      licenceNo: 'LIC67890'
    }
  ],

  // Member of association - using the correct placeholders
  f5a: true, // {F5A} - Member of Association Yes (Mark with X)
  f5b: false, // {F5B} - Member of Association No (Mark with X)
  f6: 'Test Association', // {F6} - Association Name (Fill in)
  assFarn: 'ASSFARN12345', // {ASSFARN} - Association FAR number (Fill in)
  f7: 'MEMBER12345', // {F7} - Membership Number (Fill in)
  f8: '2020-01-01', // {F8} - Date Joined Association (Fill in)
  assExpire: '2025-01-01', // {ASSEXPIRE} - Date of Expiry of Membership (Fill in)

  // Juristic person's details (not used in this test data)
  jpname: '',
  jptn: '',
  jpfarn: '',
  jpadd: '',
  jppostal: '',
  jpbtn: '',
  jpem: '',
  jpfrb: '',
  jpfrbno: '',
  jprpns: '',
  jprpidsa: false,
  jprpidno: false,
  jprpid: '',
  jprpcall: '',
  jprpadd: '',
  jprpostal: '',

  // Safe information
  safeYes: true,
  safeNo: false,
  safeH: true,
  safeR: false,
  safeS: false,
  safeSe: '',
  safeD: false,
  safeDInfo: '',
  safeMountYes: true,
  safeMountNo: false,
  safeWall: true,
  safeFloor: false,

  // Criminal history
  // Convicted of offense
  h5a: true,  // {H5A} - Convicted of offense Yes (Mark with X)
  h5b: false, // {H5B} - Convicted of offense No (Mark with X)
  h51: 'Pretoria Central',  // {H5.1} - Police station
  h52: 'CR123/2018',        // {H5.2} - Case number
  h53: 'Negligent handling of firearm', // {H5.3} - Charge
  h54: 'Guilty - Fine paid', // {H5.4} - Outcome/verdict

  // Pending cases
  h6a: true,  // {H6A} - Pending cases Yes (Mark with X)
  h6b: false, // {H6B} - Pending cases No (Mark with X)
  h61: 'Johannesburg Central', // {H6.1} - Police station
  h62: 'CR456/2023',           // {H6.2} - Case number
  h6a3: 'Alleged improper storage of firearm', // {H6.A3} - Offense

  // Firearms lost/stolen
  h7a: true,  // {H7A} - Firearms lost/stolen Yes (Mark with X)
  h7b: false, // {H7B} - Firearms lost/stolen No (Mark with X)
  h71: 'Cape Town Central',  // {H7.1} - Police station
  h72: 'CR789/2022',         // {H7.2} - Case number
  h73: 'Firearm stolen during home burglary', // {H7.3} - Circumstances
  h74: 'Glock 19, Serial #ABC123', // {H7.4} - Details of firearm

  // Case of negligence
  h8a: true,  // {H8A} - Case of negligence Yes (Mark with X)
  h8b: false, // {H8B} - Case of negligence No (Mark with X)
  h81: 'Durban Central',    // {H8.1} - Police station
  h82: 'CR321/2020',        // {H8.2} - Case number
  h83: 'Negligent discharge', // {H8.3} - Charge
  h84: 'Case dismissed',    // {H8.4} - Outcome/verdict

  // Declared unfit
  h9a: true,  // {H9A} - Declared unfit Yes (Mark with X)
  h9b: false, // {H9B} - Declared unfit No (Mark with X)
  h91: 'Bloemfontein Central', // {H9.1} - Police station
  h92: 'CR654/2019',           // {H9.2} - Case number
  h93: 'Temporary unfitness due to medical condition', // {H9.3} - Charge
  h94: '2019-06-01',        // {H9.4} - Date from which unfit
  h95: '6 months',          // {H9.5} - Period of unfitness

  // Firearm confiscated
  h10a: true,  // {H10A} - Confiscated Yes (Mark with X)
  h10b: false, // {H10B} - Confiscated No (Mark with X)
  h101: 'Port Elizabeth Central', // {H10.1} - Confiscated Police Station
  h102: 'CR987/2021',             // {H10.2} - Confiscated Case Number
  h103: 'Confiscated during investigation', // {H10.3} - Confiscated Circumstances
  h104: 'Returned after investigation completed', // {H10.4} - Confiscated Outcome/Verdict
}

/**
 * Test data for the NewLicence form with Firearm Dealer
 * This data can be used to quickly fill the form for testing purposes
 */
export const testFirearmDealerData: NewLicenceData = {
  ...testPrivateOwnerData,
  // Current owner type
  pcoa: false, // Private owner
  pcob: true,  // Firearm dealer
  pcoc: false, // Company
  pcoe: false, // Estate

  // SAP 350 (A) DETAILS - Firearm received from details
  sap350aName: 'Firearm Dealer Supply Co.',
  sap350aIdFar: 'FD98765',
  sap350aAddress: '456 Supplier Street, Cape Town',
  sap350aPostal: '8001',
  sap350aDate: '2023-02-20',

  // Different competency types for firearm dealer
  compHandgun: true,
  compRifle: true,
  compShotgun: false,
  compSelfLoading: true,

  // Criminal history - different from Private Owner
  // Convicted of offense
  h5a: false, // {H5A} - Convicted of offense Yes (Mark with X)
  h5b: true,  // {H5B} - Convicted of offense No (Mark with X)
  h51: '',
  h52: '',
  h53: '',
  h54: '',

  // Pending cases
  h6a: true,  // {H6A} - Pending cases Yes (Mark with X)
  h6b: false, // {H6B} - Pending cases No (Mark with X)
  h61: 'Pretoria North', // {H6.1} - Police station
  h62: 'CR789/2023',     // {H6.2} - Case number
  h6a3: 'Alleged improper record keeping', // {H6.A3} - Offense

  // Firearms lost/stolen
  h7a: false, // {H7A} - Firearms lost/stolen Yes (Mark with X)
  h7b: true,  // {H7B} - Firearms lost/stolen No (Mark with X)
  h71: '',
  h72: '',
  h73: '',
  h74: '',

  // Case of negligence
  h8a: false, // {H8A} - Case of negligence Yes (Mark with X)
  h8b: true,  // {H8B} - Case of negligence No (Mark with X)
  h81: '',
  h82: '',
  h83: '',
  h84: '',

  // Declared unfit
  h9a: false, // {H9A} - Declared unfit Yes (Mark with X)
  h9b: true,  // {H9B} - Declared unfit No (Mark with X)
  h91: '',
  h92: '',
  h93: '',
  h94: '',
  h95: '',

  // Firearm confiscated
  h10a: false, // {H10A} - Confiscated Yes (Mark with X)
  h10b: true,  // {H10B} - Confiscated No (Mark with X)
  h101: '',
  h102: '',
  h103: '',
  h104: '',

  // Firearm dealer details
  fdrcn: 'Test Firearm Dealer Ltd',
  fdtas: 'Test Firearms',
  fdfarn: 'FD12345',
  fdbadre: '789 Dealer Street, Cape Town',
  fdpostal: '8001',
  fdbcall: '0214567890',
  fdbmail: '<EMAIL>',
  fdrpns: 'Robert Smith',
  fdrpidsa: true,
  fdrpidno: false,
  fdrpid: '7001015009087',
  fdrpcall: '0835678901',
  fdrpadd: '123 Manager Road, Cape Town',
  fdrpostal: '8002',
}

/**
 * Test data for the NewLicence form with Company
 * This data can be used to quickly fill the form for testing purposes
 */
export const testCompanyData: NewLicenceData = {
  ...testPrivateOwnerData,
  // Current owner type
  pcoa: false, // Private owner
  pcob: false, // Firearm dealer
  pcoc: true,  // Company
  pcoe: false, // Estate

  // SAP 350 (A) DETAILS - Firearm received from details
  sap350aName: 'Corporate Firearms Ltd',
  sap350aIdFar: 'CF54321',
  sap350aAddress: '789 Business Park, Johannesburg',
  sap350aPostal: '2000',
  sap350aDate: '2023-03-15',

  // Different competency types for company
  compHandgun: false,
  compRifle: true,
  compShotgun: true,
  compSelfLoading: false,

  // Criminal history - different from Private Owner
  // Convicted of offense
  h5a: false, // {H5A} - Convicted of offense Yes (Mark with X)
  h5b: true,  // {H5B} - Convicted of offense No (Mark with X)
  h51: '',
  h52: '',
  h53: '',
  h54: '',

  // Pending cases
  h6a: false, // {H6A} - Pending cases Yes (Mark with X)
  h6b: true,  // {H6B} - Pending cases No (Mark with X)
  h61: '',
  h62: '',
  h6a3: '',

  // Firearms lost/stolen
  h7a: true,  // {H7A} - Firearms lost/stolen Yes (Mark with X)
  h7b: false, // {H7B} - Firearms lost/stolen No (Mark with X)
  h71: 'Sandton',       // {H7.1} - Police station
  h72: 'CR123/2022',    // {H7.2} - Case number
  h73: 'Firearm stolen from company safe during break-in', // {H7.3} - Circumstances
  h74: 'Beretta 92, Serial #XYZ789', // {H7.4} - Details of firearm

  // Case of negligence
  h8a: true,  // {H8A} - Case of negligence Yes (Mark with X)
  h8b: false, // {H8B} - Case of negligence No (Mark with X)
  h81: 'Johannesburg Central', // {H8.1} - Police station
  h82: 'CR456/2021',           // {H8.2} - Case number
  h83: 'Improper storage by employee', // {H8.3} - Charge
  h84: 'Fine paid',            // {H8.4} - Outcome/verdict

  // Declared unfit
  h9a: false, // {H9A} - Declared unfit Yes (Mark with X)
  h9b: true,  // {H9B} - Declared unfit No (Mark with X)
  h91: '',
  h92: '',
  h93: '',
  h94: '',
  h95: '',

  // Firearm confiscated
  h10a: false, // {H10A} - Confiscated Yes (Mark with X)
  h10b: true,  // {H10B} - Confiscated No (Mark with X)
  h101: '',
  h102: '',
  h103: '',
  h104: '',

  // Company details
  crcn: 'Test Company Holdings Ltd',
  ctn: 'Test Corp',
  cfarn: 'CO12345',
  cpadd: '456 Corporate Avenue, Johannesburg',
  cpostal: '2000',
  cbtn: '0112345678',
  ccem: '<EMAIL>',
  crpns: 'Sarah Johnson',
  crpidsa: true,
  crpidno: false,
  crpid: '7501015009087',
  crpcall: '0845678901',
  crpadd: '789 Executive Street, Sandton',
  crpostal: '2196',

  // Juristic person's details - using the correct placeholders
  jpname: 'Test Company Holdings Ltd', // {JPNAME} - Registered company name
  jptn: 'Test Corp', // {JPTN} - Trading as name
  jpfarn: 'JP12345', // {JPFARN} - FAR number
  jpadd: '456 Corporate Avenue, Johannesburg', // {JPADD} - Postal address & Business address
  jppostal: '2000', // {JPPOSTAL} - Postal Code
  jpbtn: '0112345678', // {JPBTN} - Business telephone number
  jpem: '<EMAIL>', // {JPEM} - E-mail address
  jpfrb: 'Yes', // {JPFRB} - Firearm registered TO business
  jpfrbno: '3', // {JPFRBNO} - Number of persons employed by the business to handle firearms
  jprpns: 'Sarah Johnson', // {JPRPNS} - Responsible person (Name and surname)
  jprpidsa: true, // {JPRPIDSA} - Type of identification Responsible person (Mark with an X) for South African
  jprpidno: false, // {JPRPIDNO} - Type of identification Responsible person (Mark with an X) for NONE South African
  jprpid: '7501015009087', // {JPRPID} - Identity number of responsible person
  jprpcall: '0845678901', // {JPRPCALL} - Cellphone number of responsible person
  jprpadd: '789 Executive Street, Sandton', // {JPRPADD} - Physical address of responsible person
  jprpostal: '2196', // {JPRPOSTAL} - Postal Code of responsible person
}

/**
 * Test data for the NewLicence form with Estate
 * This data can be used to quickly fill the form for testing purposes
 */
export const testEstateData: NewLicenceData = {
  ...testPrivateOwnerData,
  // Current owner type
  pcoa: false, // Private owner
  pcob: false, // Firearm dealer
  pcoc: false, // Company
  pcoe: true,  // Estate

  // SAP 350 (A) DETAILS - Firearm received from details
  sap350aName: 'Estate of James Wilson',
  sap350aIdFar: '6501015009087',
  sap350aAddress: '321 Legacy Road, Durban',
  sap350aPostal: '4001',
  sap350aDate: '2023-04-10',

  // Different competency types for estate
  compHandgun: false,
  compRifle: false,
  compShotgun: true,
  compSelfLoading: false,

  // Criminal history - different from Private Owner
  // Convicted of offense
  h5a: false, // {H5A} - Convicted of offense Yes (Mark with X)
  h5b: true,  // {H5B} - Convicted of offense No (Mark with X)
  h51: '',
  h52: '',
  h53: '',
  h54: '',

  // Pending cases
  h6a: false, // {H6A} - Pending cases Yes (Mark with X)
  h6b: true,  // {H6B} - Pending cases No (Mark with X)
  h61: '',
  h62: '',
  h6a3: '',

  // Firearms lost/stolen
  h7a: false, // {H7A} - Firearms lost/stolen Yes (Mark with X)
  h7b: true,  // {H7B} - Firearms lost/stolen No (Mark with X)
  h71: '',
  h72: '',
  h73: '',
  h74: '',

  // Case of negligence
  h8a: false, // {H8A} - Case of negligence Yes (Mark with X)
  h8b: true,  // {H8B} - Case of negligence No (Mark with X)
  h81: '',
  h82: '',
  h83: '',
  h84: '',

  // Declared unfit
  h9a: false, // {H9A} - Declared unfit Yes (Mark with X)
  h9b: true,  // {H9B} - Declared unfit No (Mark with X)
  h91: '',
  h92: '',
  h93: '',
  h94: '',
  h95: '',

  // Firearm confiscated
  h10a: false, // {H10A} - Confiscated Yes (Mark with X)
  h10b: true,  // {H10B} - Confiscated No (Mark with X)
  h101: '',
  h102: '',
  h103: '',
  h104: '',

  // Estate details - using the correct placeholders
  executor: true,         // {EXECUTOR} - Executor (Mark with X)
  administrator: false,   // {ADMINISTRATOR} - Administrator (Mark with X)
  curatorship: false,     // {CURATORSHIP} - Curatorship (Mark with X)
  trust: false,           // {TRUST} - Trust (Mark with X)
  deLast: 'Williams',     // {DELAST} - Last Name (Fill in)
  deFullName: 'Thomas Williams', // {DEFULLNAME} - Full Name (Fill in)
  deInitials: 'TW',       // {DEINITIALS} - Initials (Fill in)
  idOf: '6501015009087',  // {IDOF} - Identity number of the owner of the firearm (Fill in)
  deEName: 'James Thompson', // {DEENAME} - Name and surname of executor (Fill in)
  deEIdNo: '',            // {DEEIDNO} - Identification number of executor (Fill in) for NONE South African
  deEIdSa: true,          // {DEEIDSA} - Type of identification of executor (Mark with X) for South African
  deEId: '7201015009087', // {DEEID} - Identity number of executor (Fill in)
  deEAdd: '123 Executor Street, Durban', // {DEEADD} - Physical address of executor (Fill in)
  deEPostal: '4001',      // {DEEPOSTAL} - Postal Code of executor (Fill in)
  deECell: '0865678901',  // {DEECELL} - Cellphone number of executor (Fill in)
  deEEmail: '<EMAIL>', // {DEEEMAIL} - E-mail address of executor (Fill in)
}

/**
 * Combined test data object with all owner types
 */
export const testNewLicenceData = {
  privateOwner: testPrivateOwnerData,
  firearmDealer: testFirearmDealerData,
  company: testCompanyData,
  estate: testEstateData
}
