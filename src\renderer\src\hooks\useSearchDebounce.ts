import { useState, useCallback } from 'react'
import { useDebouncedCallback } from './useDebounce'

/**
 * A custom hook for debounced search functionality
 * 
 * @param onSearch The search callback function
 * @param delay The debounce delay in milliseconds
 * @param initialValue The initial search value
 * @returns An object with search state and handlers
 */
export function useSearchDebounce(
  onSearch: (query: string) => void,
  delay: number = 300,
  initialValue: string = ''
) {
  const [searchTerm, setSearchTerm] = useState(initialValue)
  
  // Create a debounced search function
  const debouncedSearch = useDebouncedCallback(
    (value: string) => {
      onSearch(value)
    },
    delay
  )
  
  // Handle input change
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value
      setSearchTerm(value)
      debouncedSearch(value)
    },
    [debouncedSearch]
  )
  
  // Handle direct search term updates
  const updateSearchTerm = useCallback(
    (value: string) => {
      setSearchTerm(value)
      onSearch(value) // Immediate search without debounce
    },
    [onSearch]
  )
  
  // Clear search
  const clearSearch = useCallback(() => {
    setSearchTerm('')
    onSearch('')
  }, [onSearch])
  
  return {
    searchTerm,
    setSearchTerm,
    handleSearchChange,
    updateSearchTerm,
    clearSearch
  }
}
