import React from 'react'
import { useWindowSize } from '../../contexts/WindowSizeContext'

interface MiscellaneousTypeSelectorProps {
  formType: 'select' | 'competency' | 'licence' | 'competency-select' | 'licence-select' | 'miscellaneous-select' | 'miscellaneous' | null
  setFormType: React.Dispatch<
    React.SetStateAction<'select' | 'competency' | 'licence' | 'competency-select' | 'licence-select' | 'miscellaneous-select' | 'miscellaneous' | null>
  >
  setMiscellaneousType: React.Dispatch<React.SetStateAction<'annexure_a_381a' | 'e350_information' | 'saps_inspection_report' | null>>
}

const MiscellaneousTypeSelector: React.FC<MiscellaneousTypeSelectorProps> = ({
  formType,
  setFormType,
  setMiscellaneousType
}) => {
  const { calculateResponsiveHeight } = useWindowSize()

  if (formType !== 'miscellaneous-select') return null

  const handleSelectType = (type: 'annexure_a_381a' | 'e350_information' | 'saps_inspection_report') => {
    setMiscellaneousType(type)
    setFormType('miscellaneous')
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div
        className="bg-stone-800/90 backdrop-blur-sm rounded-2xl p-8 border border-stone-700/30 shadow-2xl w-full max-w-2xl relative"
        style={{ maxHeight: `${calculateResponsiveHeight(90)}px` }}
      >
        <button
          onClick={() => setFormType('select')}
          className="absolute top-4 right-4 text-stone-400 hover:text-white transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
        <h2 className="text-2xl font-bold text-white mb-6">Select Miscellaneous Document Type</h2>
        <div className="grid grid-cols-1 gap-4">
          <button
            onClick={() => handleSelectType('annexure_a_381a')}
            className="p-6 bg-stone-700/50 rounded-lg text-left hover:bg-stone-600/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Annexure A (SAPS 381A)</h3>
            <p className="text-sm text-stone-400 mt-1">
              For firearm application annexure documents
            </p>
          </button>
          <button
            onClick={() => handleSelectType('e350_information')}
            className="p-6 bg-stone-700/50 rounded-lg text-left hover:bg-stone-600/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">E350 Information</h3>
            <p className="text-sm text-stone-400 mt-1">
              For E350 information documents
            </p>
          </button>
          <button
            onClick={() => handleSelectType('saps_inspection_report')}
            className="p-6 bg-stone-700/50 rounded-lg text-left hover:bg-stone-600/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">SAPS Inspection Report</h3>
            <p className="text-sm text-stone-400 mt-1">
              For SAPS firearm inspection reports
            </p>
          </button>
        </div>
      </div>
    </div>
  )
}

export default MiscellaneousTypeSelector
