import React from 'react'
import { FormSection, FormField, RadioGroup, CheckboxGroup } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionWithAddressProps } from '../utils/types'
import { NewLicenceData } from '../../../../types/NewLicenceData'

/**
 * Helper function to generate initials from a name
 */
const generateInitials = (name: string): string => {
  return name
    .split(' ')
    .map(part => part.charAt(0).toUpperCase())
    .join('')
}

/**
 * Helper functions to clear fields for different owner types
 */
const clearPrivateOwnerFields = (data: Partial<NewLicenceData>): void => {
  data.firstName = ''
  data.lastName = ''
  data.initials = ''
  data.idNumber = ''
  data.cell = ''
  data.workC = ''
  data.email = ''
  data.afhy = false
  data.afhn = false
  data.address = ''
  data.postalCode = ''
}

const clearFirearmDealerFields = (data: Partial<NewLicenceData>): void => {
  data.fdrcn = ''
  data.fdtas = ''
  data.fdfarn = ''
  data.fdbadre = ''
  data.fdpostal = ''
  data.fdbcall = ''
  data.fdbmail = ''
  data.fdrpns = ''
  data.fdrpidsa = false
  data.fdrpidno = false
  data.fdrpid = ''
  data.fdrpcall = ''
  data.fdrpadd = ''
  data.fdrpostal = ''
}

const clearCompanyFields = (data: Partial<NewLicenceData>): void => {
  data.crcn = ''
  data.ctn = ''
  data.cfarn = ''
  data.cpadd = ''
  data.cpostal = ''
  data.cbtn = ''
  data.ccem = ''
  data.crpns = ''
  data.crpidsa = false
  data.crpidno = false
  data.crpid = ''
  data.crpcall = ''
  data.crpadd = ''
  data.crpostal = ''
}

const clearEstateFields = (data: Partial<NewLicenceData>): void => {
  data.executor = false
  data.administrator = false
  data.curatorship = false
  data.trust = false
  data.deLast = ''
  data.deFullName = ''
  data.deInitials = ''
  data.idOf = ''
  data.deEName = ''
  data.deEIdSa = false
  data.deEIdNo = ''
  data.deEId = ''
  data.deEAdd = ''
  data.deEPostal = ''
  data.deECell = ''
  data.deEEmail = ''
}
const CurrentOwner: React.FC<SectionWithAddressProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    // Create a copy of the form data to update
    const updatedData: Partial<NewLicenceData> = {
      [name]: value
    }

    // Auto-generate initials when firstName or deFullName is updated
    if (name === 'firstName') {
      updatedData.initials = generateInitials(value)
    } else if (name === 'deFullName') {
      updatedData.deInitials = generateInitials(value)
    }

    // Update the form data
    updateFormData(updatedData)
  }

  // Function to handle the Gunnery Prefill button click
  const handleGunneryPrefill = () => {
    // Create a function to manually update the postal code fields
    const updatePostalCodeFields = () => {
      // Get the postal code input elements
      const businessPostalInput = document.querySelector('input[placeholder="4-digit postal code"]') as HTMLInputElement;
      const responsiblePersonPostalInput = document.querySelectorAll('input[placeholder="4-digit postal code"]')[1] as HTMLInputElement;

      // If the elements exist, set their values and trigger change events
      if (businessPostalInput) {
        businessPostalInput.value = '7130';
        businessPostalInput.dispatchEvent(new Event('change', { bubbles: true }));
      }

      if (responsiblePersonPostalInput) {
        responsiblePersonPostalInput.value = '7600';
        responsiblePersonPostalInput.dispatchEvent(new Event('change', { bubbles: true }));
      }
    };

    // Update the form data
    updateFormData({
      // Business Information
      fdrcn: 'CK TSAI GUNNERY (PTY) LTD',
      fdtas: 'GUNNERY ARMS & AMMO',
      fdfarn: '***********',
      fdbadre: '5 Melcksloot Village, Somerset West',
      fdpostal: '7130',
      fdbcall: '0218516548',
      fdbmail: '<EMAIL>',

      // Responsible Person Information
      fdrpns: 'CARL TSAI',
      fdrpidsa: true,
      fdrpidno: false,
      fdrpid: '9303315179084',
      fdrpcall: '0726156568',
      fdrpadd: 'Titan Street, Jamestown, Stellenbosch, South Africa',
      fdrpostal: '7600'
    });

    // Use setTimeout to ensure the DOM has updated after the form data changes
    setTimeout(updatePostalCodeFields, 100);
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Current Owner</h3>
      <FormSection
        title="Particulars of Current Owner"
        subtitle="Please select the type of current owner"
      >
        <div className="space-y-3">
          <div className="mb-4">
            <RadioGroup
              name="currentOwnerType"
              value={
                formData.pcoa
                  ? 'private'
                  : formData.pcob
                    ? 'dealer'
                    : formData.pcoc
                      ? 'company'
                      : formData.pcoe
                        ? 'estate'
                        : ''
              }
              onChange={(value) => {
                // Create an object to update form data
                const updatedData: Partial<NewLicenceData> = {
                  pcoa: value === 'private',
                  pcob: value === 'dealer',
                  pcoc: value === 'company',
                  pcoe: value === 'estate'
                }

                // Clear fields for non-selected owner types
                if (value === 'private') {
                  clearFirearmDealerFields(updatedData)
                  clearCompanyFields(updatedData)
                  clearEstateFields(updatedData)
                } else if (value === 'dealer') {
                  clearPrivateOwnerFields(updatedData)
                  clearCompanyFields(updatedData)
                  clearEstateFields(updatedData)
                } else if (value === 'company') {
                  clearPrivateOwnerFields(updatedData)
                  clearFirearmDealerFields(updatedData)
                  clearEstateFields(updatedData)
                } else if (value === 'estate') {
                  clearPrivateOwnerFields(updatedData)
                  clearFirearmDealerFields(updatedData)
                  clearCompanyFields(updatedData)
                }

                // Update the form data
                updateFormData(updatedData)
              }}
              options={[
                { value: 'private', label: 'Private Owner' },
                { value: 'dealer', label: 'Firearm Dealer' },
                { value: 'company', label: 'Company' },
                { value: 'estate', label: 'Estate' }
              ]}
              label="Current Owner Type"
            />
          </div>

          {/* TYPE A: Private owner */}
          {formData.pcoa && (
            <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
              <h3 className="text-md font-semibold text-white mb-2">Private Owner Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  label="First Names"
                  name="firstName"
                  value={formData.firstName || ''}
                  onChange={handleChange}
                  placeholder="Enter first names"
                />
                <FormField
                  label="Last Name"
                  name="lastName"
                  value={formData.lastName || ''}
                  onChange={handleChange}
                  placeholder="Enter last name"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  label="Initials (Auto-generated)"
                  name="initials"
                  value={formData.initials || ''}
                  onChange={handleChange}
                  placeholder="Auto-generated from first name"
                  readOnly
                />
                <FormField
                  label="ID Number"
                  name="idNumber"
                  value={formData.idNumber || ''}
                  onChange={handleChange}
                  placeholder="Enter ID number"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <FormField
                  label="Cell Phone Number"
                  name="cell"
                  value={formData.cell || ''}
                  onChange={handleChange}
                  placeholder="Enter cell phone number"
                />
                <FormField
                  label="Work Phone Number"
                  name="workC"
                  value={formData.workC || ''}
                  onChange={handleChange}
                  placeholder="Enter work phone number"
                />
                <FormField
                  label="Email Address"
                  name="email"
                  value={formData.email || ''}
                  onChange={handleChange}
                  placeholder="Enter email address"
                  type="email"
                />
              </div>

              <div className="mt-4">
                <RadioGroup
                  name="additionalLicenceHolders"
                  value={formData.afhy ? 'yes' : formData.afhn ? 'no' : 'no'} // Default to 'no' if neither is set
                  onChange={(value) => {
                    // Explicitly set one to true and the other to false
                    // to ensure only one option gets an X in the document
                    updateFormData({
                      afhy: value === 'yes',
                      afhn: value === 'no'
                    })
                  }}
                  options={[
                    { value: 'yes', label: 'Yes' },
                    { value: 'no', label: 'No' }
                  ]}
                  label="Are there any additional firearm licence holders for this firearm?"
                />
              </div>
              <AddressInput
                label="Physical Address"
                value={formData.address || ''}
                postalCode={formData.postalCode || ''}
                onChange={(address, postalCode) => {
                  updateFormData({
                    address: address,
                    postalCode: postalCode || ''
                  })
                }}
                placeholder="Enter physical address"
              />
            </div>
          )}

          {/* TYPE B: Firearm dealer */}
          {formData.pcob && (
            <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
              <h3 className="text-md font-semibold text-white mb-2">Firearm Dealer Details</h3>
              <div className="border-b border-gray-700 pb-4 mb-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-sm font-semibold text-white">Business Information</h4>
                  <button
                    onClick={handleGunneryPrefill}
                    className="h-8 px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white hover:text-white border border-orange-500 hover:border-orange-600 rounded-md transition-colors"
                  >
                    Gunnery Prefill
                  </button>
                </div>
                <FormField
                  label="Registered Company Name"
                  name="fdrcn"
                  value={formData.fdrcn || ''}
                  onChange={handleChange}
                  placeholder="Enter registered company name"
                />
                <FormField
                  label="Trading As"
                  name="fdtas"
                  value={formData.fdtas || ''}
                  onChange={handleChange}
                  placeholder="Enter trading as name"
                />
                <FormField
                  label="FAR Number"
                  name="fdfarn"
                  value={formData.fdfarn || ''}
                  onChange={handleChange}
                  placeholder="Enter FAR number"
                />
                <AddressInput
                  label="Business Address"
                  value={formData.fdbadre || ''}
                  postalCode={formData.fdpostal || ''}
                  onChange={(address, postalCode) => {
                    updateFormData({
                      fdbadre: address,
                      fdpostal: postalCode || ''
                    })
                  }}
                  placeholder="Enter business address"
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                  <FormField
                    label="Business Telephone Number"
                    name="fdbcall"
                    value={formData.fdbcall || ''}
                    onChange={handleChange}
                    placeholder="Enter business telephone number"
                  />
                  <FormField
                    label="Business Email Address"
                    name="fdbmail"
                    value={formData.fdbmail || ''}
                    onChange={handleChange}
                    placeholder="Enter business email address"
                    type="email"
                    additionalContent={
                      <p className="text-xs text-stone-400 mt-1">
                        Optional - enter a valid email address for correspondence
                      </p>
                    }
                  />
                </div>
              </div>

              <div className="border-b border-gray-700 pb-4 mb-4">
                <h4 className="text-sm font-semibold text-white mb-2">
                  Responsible Person Information
                </h4>
                <FormField
                  label="Responsible Person (Name and surname)"
                  name="fdrpns"
                  value={formData.fdrpns || ''}
                  onChange={handleChange}
                  placeholder="Enter name and surname of responsible person"
                />
                <div className="mt-2">
                  <RadioGroup
                    name="dealerResponsiblePersonIdType"
                    value={formData.fdrpidsa ? 'sa' : formData.fdrpidno ? 'non' : ''}
                    onChange={(value) => {
                      updateFormData({
                        fdrpidsa: value === 'sa',
                        fdrpidno: value === 'non'
                      })
                    }}
                    options={[
                      { value: 'sa', label: 'South African Citizen' },
                      { value: 'non', label: 'Non-South African Citizen' }
                    ]}
                    label="Type of Identification"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                  <FormField
                    label="Identity Number of Responsible Person"
                    name="fdrpid"
                    value={formData.fdrpid || ''}
                    onChange={handleChange}
                    placeholder="Enter ID number"
                  />
                  <FormField
                    label="Cellphone Number of Responsible Person"
                    name="fdrpcall"
                    value={formData.fdrpcall || ''}
                    onChange={handleChange}
                    placeholder="Enter cellphone number"
                  />
                </div>
                <AddressInput
                  label="Physical Address of Responsible Person"
                  value={formData.fdrpadd || ''}
                  postalCode={formData.fdrpostal || ''}
                  onChange={(address, postalCode) => {
                    updateFormData({
                      fdrpadd: address,
                      fdrpostal: postalCode || ''
                    })
                  }}
                  placeholder="Enter physical address of responsible person"
                />
              </div>
            </div>
          )}

          {/* TYPE C: Company */}
          {formData.pcoc && (
            <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
              <h3 className="text-md font-semibold text-white mb-2">Company Details</h3>
              <div className="border-b border-gray-700 pb-4 mb-4">
                <h4 className="text-sm font-semibold text-white mb-2">Business Information</h4>
                <FormField
                  label="Registered Company Name"
                  name="crcn"
                  value={formData.crcn || ''}
                  onChange={handleChange}
                  placeholder="Enter registered company name"
                />
                <FormField
                  label="Trading As"
                  name="ctn"
                  value={formData.ctn || ''}
                  onChange={handleChange}
                  placeholder="Enter trading as name"
                />
                <FormField
                  label="FAR Number"
                  name="cfarn"
                  value={formData.cfarn || ''}
                  onChange={handleChange}
                  placeholder="Enter FAR number"
                />
                <AddressInput
                  label="Business & Postal Address"
                  value={formData.cpadd || ''}
                  postalCode={formData.cpostal || ''}
                  onChange={(address, postalCode) => {
                    updateFormData({
                      cpadd: address,
                      cpostal: postalCode || ''
                    })
                  }}
                  placeholder="Enter business address"
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                  <FormField
                    label="Business Telephone Number"
                    name="cbtn"
                    value={formData.cbtn || ''}
                    onChange={handleChange}
                    placeholder="Enter business telephone number"
                  />
                  <FormField
                    label="Email Address"
                    name="ccem"
                    value={formData.ccem || ''}
                    onChange={handleChange}
                    placeholder="Enter email address"
                    type="email"
                  />
                </div>
              </div>

              <div className="border-b border-gray-700 pb-4 mb-4">
                <h4 className="text-sm font-semibold text-white mb-2">
                  Responsible Person Information
                </h4>
                <FormField
                  label="Responsible Person (Name and surname)"
                  name="crpns"
                  value={formData.crpns || ''}
                  onChange={handleChange}
                  placeholder="Enter name and surname of responsible person"
                />
                <div className="mt-2">
                  <RadioGroup
                    name="companyResponsiblePersonIdType"
                    value={formData.crpidsa ? 'sa' : formData.crpidno ? 'non' : ''}
                    onChange={(value) => {
                      updateFormData({
                        crpidsa: value === 'sa',
                        crpidno: value === 'non'
                      })
                    }}
                    options={[
                      { value: 'sa', label: 'South African Citizen' },
                      { value: 'non', label: 'Non-South African Citizen' }
                    ]}
                    label="Type of Identification"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                  <FormField
                    label="Identity Number of Responsible Person"
                    name="crpid"
                    value={formData.crpid || ''}
                    onChange={handleChange}
                    placeholder="Enter ID number"
                  />
                  <FormField
                    label="Cellphone Number of Responsible Person"
                    name="crpcall"
                    value={formData.crpcall || ''}
                    onChange={handleChange}
                    placeholder="Enter cellphone number"
                  />
                </div>
                <AddressInput
                  label="Physical Address of Responsible Person"
                  value={formData.crpadd || ''}
                  postalCode={formData.crpostal || ''}
                  onChange={(address, postalCode) => {
                    updateFormData({
                      crpadd: address,
                      crpostal: postalCode || ''
                    })
                  }}
                  placeholder="Enter physical address of responsible person"
                />
              </div>
            </div>
          )}

          {/* TYPE E: Estate */}
          {formData.pcoe && (
            <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
              <h3 className="text-md font-semibold text-white mb-2">Estate Details</h3>
              {/* These checkboxes use the {EXECUTOR}, {ADMINISTRATOR}, {CURATORSHIP}, and {TRUST} placeholders */}
              <CheckboxGroup
                options={[
                  { name: 'executor', label: 'Executor', checked: formData.executor || false },
                  {
                    name: 'administrator',
                    label: 'Administrator',
                    checked: formData.administrator || false
                  },
                  {
                    name: 'curatorship',
                    label: 'Curatorship',
                    checked: formData.curatorship || false
                  },
                  { name: 'trust', label: 'Trust', checked: formData.trust || false }
                ]}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  const { name, checked } = e.target
                  updateFormData({
                    [name]: checked
                  })
                }}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                {/* This field uses the {DELAST} placeholder */}
                <FormField
                  label="Last Name"
                  name="deLast"
                  value={formData.deLast || ''}
                  onChange={handleChange}
                  placeholder="Enter last name"
                />
                {/* This field uses the {DEFULLNAME} placeholder */}
                <FormField
                  label="Full Name"
                  name="deFullName"
                  value={formData.deFullName || ''}
                  onChange={handleChange}
                  placeholder="Enter full name"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {/* This field uses the {DEINITIALS} placeholder */}
                <FormField
                  label="Initials (Auto-generated)"
                  name="deInitials"
                  value={formData.deInitials || ''}
                  onChange={handleChange}
                  placeholder="Auto-generated from full name"
                  readOnly
                />
                {/* This field uses the {IDOF} placeholder */}
                <FormField
                  label="Identity number of the owner of the firearm"
                  name="idOf"
                  value={formData.idOf || ''}
                  onChange={handleChange}
                  placeholder="Enter ID number"
                />
              </div>

              {/* This field uses the {DEENAME} placeholder */}
              <FormField
                label="Name and surname of executor"
                name="deEName"
                value={formData.deEName || ''}
                onChange={handleChange}
                placeholder="Enter name and surname of executor"
              />

              <div className="mt-2">
                {/* These radio buttons use the {DEEIDSA} and {DEEIDNO} placeholders */}
                <RadioGroup
                  name="executorIdType"
                  value={formData.deEIdSa ? 'sa' : 'non'}
                  onChange={(value) => {
                    updateFormData({
                      deEIdSa: value === 'sa',
                      deEIdNo: value === 'non' ? 'yes' : ''
                    })
                  }}
                  options={[
                    { value: 'sa', label: 'South African Citizen' },
                    { value: 'non', label: 'Non-South African Citizen' }
                  ]}
                  label="Type of identification of executor"
                />
              </div>

              {/* This field uses the {DEEID} placeholder */}
              <FormField
                label="Identity number of executor"
                name="deEId"
                value={formData.deEId || ''}
                onChange={handleChange}
                placeholder="Enter ID number of executor"
              />

              {/* This field uses the {DEEADD} placeholder */}
              <AddressInput
                label="Physical address of executor"
                value={formData.deEAdd || ''}
                postalCode={formData.deEPostal || ''}
                onChange={(address, postalCode) => {
                  updateFormData({
                    deEAdd: address,
                    deEPostal: postalCode || ''
                  })
                }}
                placeholder="Enter physical address of executor"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                {/* This field uses the {DEECELL} placeholder */}
                <FormField
                  label="Cellphone number of executor"
                  name="deECell"
                  value={formData.deECell || ''}
                  onChange={handleChange}
                  placeholder="Enter cellphone number of executor"
                />
                {/* This field uses the {DEEEMAIL} placeholder */}
                <FormField
                  label="E-mail address of executor"
                  name="deEEmail"
                  value={formData.deEEmail || ''}
                  onChange={handleChange}
                  placeholder="Enter email address of executor"
                  type="email"
                />
              </div>
            </div>
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default CurrentOwner
