import React from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'
import { GunLicense, NotificationStatus } from './types'

interface LicenseCardProps {
  license: GunLicense
  onRenew: (licenseId: string) => void
  onToggleNotifications: (licenseId: string) => void
  notificationStatus: NotificationStatus
  copyToClipboard: (text: string) => void
  isFocused?: boolean
  isOtherCardFocused?: boolean
  onFocusToggle?: (licenseId: string | null) => void
}

export const LicenseCard: React.FC<LicenseCardProps> = ({
  license,
  onRenew,
  onToggleNotifications,
  notificationStatus,
  copyToClipboard,
  isFocused = false,
  isOtherCardFocused = false,
  onFocusToggle = () => {}
}) => {
  // Format date function
  const formatDate = (dateString: string): string => {
    if (!dateString) return 'N/A'
    const date = new Date(dateString)
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get expiry message
  const getExpiryMessage = (expiryDate: string): string => {
    if (!expiryDate) return 'No expiry date'

    const today = new Date()
    const expiry = new Date(expiryDate)
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

    if (daysUntilExpiry < 0) {
      return `Expired ${Math.abs(daysUntilExpiry)} days ago`
    } else if (daysUntilExpiry === 0) {
      return 'Expires today'
    } else if (daysUntilExpiry === 1) {
      return 'Expires tomorrow'
    } else if (daysUntilExpiry <= 30) {
      return `Expires in ${daysUntilExpiry} days`
    } else {
      return `Expires on ${formatDate(expiryDate)}`
    }
  }

  // Get status color
  const getStatusColor = (expiryDate: string): string => {
    const today = new Date()
    const expiry = new Date(expiryDate)
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

    if (daysUntilExpiry < 0) {
      return 'text-red-500'
    } else if (daysUntilExpiry <= 30) {
      return 'text-orange-500'
    } else {
      return 'text-green-500'
    }
  }

  // Handle focus toggle
  const handleFocusToggle = () => {
    onFocusToggle(isFocused ? null : license.id)
  }

  return (
    <div
      className={`px-2 py-1 transition-all duration-300 ${
        isFocused && !isOtherCardFocused
          ? 'z-10'
          : isOtherCardFocused
            ? 'scale-98 opacity-40'
            : ''
      }`}
    >
      <div
        className={`bg-gradient-to-br from-stone-800/90 to-stone-900/90 backdrop-blur-md rounded-2xl p-6 shadow-[0_0_25px_rgba(0,0,0,0.3)] border transition-all duration-300 ease-in-out ${
          isFocused ? 'border-orange-500 shadow-lg shadow-orange-500/30' : 'border-orange-500/30'
        } relative overflow-hidden`}
      >
        {/* Decorative element */}
        <div className="absolute -top-24 -right-24 w-48 h-48 bg-orange-500/10 rounded-full blur-2xl" />
        <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-orange-500/5 rounded-full blur-2xl" />

        <div className="relative">
          <div className="flex justify-between items-start mb-6">
            <div className="flex items-center">
              <h2 className="text-2xl font-semibold text-white flex items-center">
                <span className="inline-block mr-3 w-1.5 h-6 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                License Details
              </h2>

              {/* Focus button - only shown when not focused */}
              {!isFocused && onFocusToggle !== undefined && (
                <button
                  onClick={handleFocusToggle}
                  className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-stone-600/80 hover:text-white"
                  title="Focus on this license"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                  </svg>
                </button>
              )}

              {/* Close button - shown only when focused */}
              {isFocused && onFocusToggle !== undefined && (
                <button
                  onClick={handleFocusToggle}
                  className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-red-500/80 hover:text-white"
                  title="Exit focus mode"
                >
                  <DashboardIcons.Close className="w-4 h-4" />
                </button>
              )}
            </div>

            <div className="flex items-center gap-2">
              <div className={`${getStatusColor(license.expiry_date)} text-sm font-medium px-3 py-1 rounded-full bg-stone-800/80 flex items-center gap-1`}>
                {new Date(license.expiry_date) < new Date() ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
                {getExpiryMessage(license.expiry_date)}
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-2 mb-6">
            <button
              onClick={() => onRenew(license.id)}
              className="flex items-center gap-2 px-3 py-2 bg-green-600/90 hover:bg-green-700 text-white rounded-lg transition-all duration-200 hover:shadow-lg shadow-md"
            >
              <DashboardIcons.History className="w-4 h-4" />
              Renew License
            </button>
            <button
              onClick={() => onToggleNotifications(license.id)}
              disabled={notificationStatus.loading}
              className={`flex items-center gap-2 px-3 py-2 text-white rounded-lg transition-all duration-200 hover:shadow-lg shadow-md ${
                notificationStatus.loading
                  ? 'bg-stone-500/80 cursor-not-allowed'
                  : license.toggle_notifications
                  ? 'bg-green-600/80 hover:bg-green-700'
                  : 'bg-orange-500/80 hover:bg-orange-600'
              }`}
              title={license.toggle_notifications ? 'Pause notifications' : 'Enable notifications'}
            >
              {notificationStatus.loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm">Updating...</span>
                </>
              ) : license.toggle_notifications ? (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-sm">Notifications Active</span>
                </>
              ) : (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-sm">Notifications Paused</span>
                </>
              )}
            </button>
          </div>

          {/* Client Details Section */}
          <div className="mb-4 bg-stone-800/50 p-4 rounded-lg border border-stone-700/50">
            <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
              <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
              Client Information
            </h3>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <p className="text-xs text-stone-400 mb-1">Name</p>
                <p className="text-sm text-white">
                  {license.client.first_name} {license.client.last_name}
                </p>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">ID Number</p>
                <div className="flex items-center gap-1">
                  <p className="text-sm text-white">{license.client.id_number}</p>
                  <button
                    onClick={() => copyToClipboard(license.client.id_number)}
                    className="text-stone-400 hover:text-orange-400 transition-colors"
                    title="Copy to clipboard"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                      <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                  </button>
                </div>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">Phone</p>
                <div className="flex items-center gap-1">
                  <p className="text-sm text-white">{license.client.phone}</p>
                  <button
                    onClick={() => copyToClipboard(license.client.phone)}
                    className="text-stone-400 hover:text-orange-400 transition-colors"
                    title="Copy to clipboard"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                      <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                  </button>
                </div>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">Email</p>
                <p className="text-sm text-white">{license.client.email || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* License Details Section */}
          <div className="mb-4 bg-stone-800/50 p-4 rounded-lg border border-stone-700/50">
            <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
              <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
              License Information
            </h3>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <p className="text-xs text-stone-400 mb-1">License Number</p>
                <div className="flex items-center gap-1">
                  <p className="text-sm text-white">{license.lic_number || license.license_number}</p>
                  <button
                    onClick={() => copyToClipboard(license.lic_number || license.license_number)}
                    className="text-stone-400 hover:text-orange-400 transition-colors"
                    title="Copy to clipboard"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                      <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                  </button>
                </div>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">Section</p>
                <p className="text-sm text-white">{license.section || 'N/A'}</p>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">Issue Date</p>
                <p className="text-sm text-white">
                  {formatDate(license.issue_date)}
                </p>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">Expiry Date</p>
                <p className="text-sm text-white">
                  {formatDate(license.expiry_date)}
                </p>
              </div>
              {license.last_notification_date && (
                <div>
                  <p className="text-xs text-stone-400 mb-1">Last Notified</p>
                  <p className="text-sm text-white">
                    {formatDate(license.last_notification_date)}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Firearm Details Section */}
          <div className="bg-stone-800/50 p-4 rounded-lg border border-stone-700/50">
            <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
              <span className="inline-block mr-2 w-1 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
              Firearm Details
            </h3>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <p className="text-xs text-stone-400 mb-1">Make</p>
                <p className="text-sm text-white">{license.make}</p>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">Type</p>
                <p className="text-sm text-white">{license.type}</p>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">Caliber</p>
                <p className="text-sm text-white">{license.caliber || 'N/A'}</p>
              </div>
              <div>
                <p className="text-xs text-stone-400 mb-1">Serial Number</p>
                <div className="flex items-center gap-1">
                  <p className="text-sm text-white">{license.serial_number}</p>
                  <button
                    onClick={() => copyToClipboard(license.serial_number)}
                    className="text-stone-400 hover:text-orange-400 transition-colors"
                    title="Copy to clipboard"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                      <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
