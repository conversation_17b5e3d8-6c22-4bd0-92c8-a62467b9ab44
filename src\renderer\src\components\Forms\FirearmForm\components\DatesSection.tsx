import React from 'react';
import { FormField } from '../../../FormComponents';
import { DashboardIcons } from '../../../icons/DashboardIcons';
import { Firearm } from '../../../../types/firearm';

interface DatesSectionProps {
  formData: Partial<Firearm>;
  loanId?: string;
  depositAmount: number | null;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onCreditChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onWheel: (e: React.WheelEvent<HTMLInputElement>) => void;
}

export const DatesSection: React.FC<DatesSectionProps> = ({
  formData,
  loanId,
  depositAmount,
  onChange,
  onCreditChange,
  onWheel
}) => {
  return (
    <div>
      <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
        <DashboardIcons.History className="w-3.5 h-3.5 text-orange-400" />
        Dates
      </h2>
      <div className="grid grid-cols-2 gap-2">
        <div>
          <FormField
            label="Sign In Date"
            name="date_signed_in"
            value={formData.date_signed_in || ''}
            onChange={onChange}
            type="date"
            required
            inputClassName="premium-field"
          />
          {formData.date_signed_in && new Date(formData.date_signed_in) < new Date() && (
            <div className="text-xs text-amber-400 mt-1">
              <p>Backdated date - R7.50 daily charge will apply from this date</p>
              {formData.storage_type === 'Owner' ? (
                <p className="mt-0.5">Wallet will be charged for days between sign-in date and today</p>
              ) : formData.storage_type === 'Dealer' ? (
                <p className="mt-0.5">
                  {new Date(formData.date_signed_in).getTime() + (365 * 24 * 60 * 60 * 1000) < new Date().getTime()
                    ? 'Free period has expired - wallet will be charged for days after free period'
                    : 'Within 12-month free period - no charges will apply'}
                </p>
              ) : null}
            </div>
          )}
        </div>

        {/* Credit field for Owner storage type - hide when in Loans */}
        {formData.storage_type === 'Owner' && !loanId && (
          <FormField
            label="Initial Credit (R)"
            name="credit_amount"
            value={depositAmount !== null ? depositAmount.toString() : ''}
            onChange={onCreditChange}
            type="number"
            min="0"
            step="0.01"
            placeholder="Enter initial credit amount"
            inputClassName="premium-field"
            onWheel={onWheel}
          />
        )}
      </div>
    </div>
  );
};
