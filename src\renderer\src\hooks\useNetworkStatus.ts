import { useState, useEffect, useCallback } from 'react';

export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOfflineDialog, setShowOfflineDialog] = useState(!navigator.onLine);

  const checkConnection = useCallback(() => {
    // Force check of connection
    const isCurrentlyOnline = navigator.onLine;
    setIsOnline(isCurrentlyOnline);
    setShowOfflineDialog(!isCurrentlyOnline);
  }, []);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineDialog(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineDialog(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check initial state
    if (!navigator.onLine) {
      handleOffline();
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return { isOnline, showOfflineDialog, checkConnection };
}; 