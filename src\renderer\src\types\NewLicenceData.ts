import { FormData } from './FormData'

// Define the interface for a firearm in possession
export interface FirearmInPossession {
  type: string
  calibre: string
  make: string
  barrelSerialNo: string
  frameSerialNo: string
  licenceNo: string
}

// Define the interface for New Licence form data
export interface NewLicenceData extends FormData {
  before90DaysYes: any
  before90DaysNo: any
  before90DaysReason: string
  afterDueBeforeExpiryYes: any
  afterDueBeforeExpiryNo: any
  afterDueBeforeExpiryReason: string
  additionalOriginalLicences: any
  tradingAsName: string
  farNumber: string
  postalAddress: string
  businessTelNumber: string
  // Additional properties for licence form
  mainHF: boolean
  addHF: boolean

  // Licence type
  s13: boolean // Self-defence
  s15: boolean // Occasional hunting/sport
  s16: boolean // Dedicated hunting/sport
  s20: boolean // Business - hunting
  s20a: boolean // Business - other
  s20b: boolean // Business - security
  s20c: boolean // Business - training

  // Firearm type
  rifle?: boolean // Rifle
  shotgun?: boolean // Shotgun
  pistol?: boolean // Pistol
  comb: boolean // Combination
  otherDesign: boolean
  otherDesignE: string

  // SAP 350 (A) DETAILS - Firearm received from details
  sap350aName?: string // Name
  sap350aIdFar?: string // Identification number or FAR number
  sap350aAddress?: string // Address
  sap350aPostal?: string // Postal code
  sap350aDate?: string // Date received

  // Firearm details
  semi: boolean // Semi-automatic
  auto: boolean // Automatic
  man: boolean // Manual
  otherAction: boolean // Other action type
  otherF: string // Other action description
  engg: string // Names and Addresses engraved in metal
  pafk: string // Physical address where firearm(s) is kept
  pafkPostal: string // Postal Code for firearm address
  flap: string // Name and surname of current owner/authorized person
  flid: string // Identification number of current owner/authorized person
  designation: string // Designation
  apDate: string // Date
  apPlace: string // Place

  // Serial numbers
  bsn: string // Barrel serial number
  fsn: string // Frame serial number
  rsn: string // Receiver serial number

  // Component makes
  bsnm: string // Barrel make
  fsnm: string // Frame make
  rsnm: string // Receiver make

  // Current owner type
  pcoa: boolean // Private owner
  pcob: boolean // Firearm dealer
  pcoc: boolean // Company
  pcoe: boolean // Estate

  // Firearm dealer details
  fdrcn?: string // Registered company name
  fdtas?: string // Trading as
  fdfarn?: string // FAR number
  fdbadre?: string // Business address
  fdpostal?: string // Postal code
  fdbcall?: string // Business telephone
  fdbmail?: string // Email
  fdrpns?: string // Responsible person
  fdrpidsa?: boolean // ID type - SA
  fdrpidno?: boolean // ID type - Non-SA
  fdrpid?: string // ID number
  fdrpcall?: string // Cell number
  fdrpadd?: string // Physical address
  fdrpostal?: string // Postal code

  // Company details
  crcn?: string // Registered company name
  ctn?: string // Trading as
  cfarn?: string // FAR number
  cpadd?: string // Postal address & Business address
  cpostal?: string // Postal code
  cbtn?: string // Business telephone
  ccem?: string // Email
  crpns?: string // Responsible person
  crpidsa?: boolean // ID type - SA
  crpidno?: boolean // ID type - Non-SA
  crpid?: string // ID number
  crpcall?: string // Cell number
  crpadd?: string // Physical address
  crpostal?: string // Postal code

  // Estate details
  executor?: boolean
  administrator?: boolean
  curatorship?: boolean
  trust?: boolean
  deLast?: string // Last name
  deFullName?: string // Full name
  deInitials?: string // Initials
  idOf?: string // ID of owner
  deEName?: string // Executor name
  deEIdNo?: string // Executor ID
  deEIdSa?: boolean // Executor ID type - SA
  deEId?: string // Executor ID
  deEAdd?: string // Executor address
  deEPostal?: string // Executor postal code
  deECell?: string // Executor cell
  deEEmail?: string // Executor email

  // Natural person's details
  npsaid: boolean // South African citizen
  npidno: boolean // Non-South African citizen
  npid: string // ID number
  npname: string // First name
  nplname: string // Last name
  npinitials: string // Initials
  npdateb: string // Date of birth
  npage: string // Age
  npsexmale: boolean // Male
  npsexfemale: boolean // Female
  npaddress: string // Physical address
  nppostal: string // Postal code
  nptypeofres: string // Type of residence
  npprof: string // Profession
  npecname: string // Company name
  npecadd: string // Company address
  npecpostal: string // Company postal code
  npcell: string // Cellphone number
  npworkc: string // Work number
  npemail: string // Email address

  // Private owner additional fields
  cell: string // Cell phone number
  workC: string // Work phone number
  address: string // Physical address
  passport: string // Passport number
  afhy: boolean // Additional firearm licence holders - Yes
  afhn: boolean // Additional firearm licence holders - No

  // Spouse details
  spouseFullName: string

  // Competency certificate
  tradeFirearm?: boolean // Trade in firearm
  possessFirearm?: boolean // Possess firearm
  compHandgun?: boolean // Competency for Handgun
  compRifle?: boolean // Competency for Rifle
  compShotgun?: boolean // Competency for Shotgun
  compSelfLoading?: boolean // Competency for Self-Loading
  f2ab?: string // Certificate number
  f3?: string // Issue date
  f4?: string // Expiry date

  // Firearms in Possession
  firearmsInPossession?: FirearmInPossession[]

  // Training Institution
  nameInst?: string // Training Institution Name
  serialCert?: string // Serial Certificate
  certIssueDate?: string // Date Certificate Issued
  certExpiryDate?: string // Date Certificate Expires

  // Member of association
  f5a: boolean // Yes
  f5b: boolean // No
  f6: string // Association name
  assFarn: string // Association FAR number
  f7: string // Membership number
  f8: string // Date joined
  assExpire: string // Membership expiry date

  // Juristic person's details
  jpname?: string // Registered company name
  jptn?: string // Trading as
  jpfarn?: string // FAR number
  jpadd?: string // Business address
  jppostal?: string // Postal code
  jpbtn?: string // Business telephone
  jpem?: string // Email
  jpfrb?: string // Firearm registered to business
  jpfrbno?: string // Number of persons
  jprpns?: string // Responsible person
  jprpidsa?: boolean // ID type - SA
  jprpidno?: boolean // ID type - Non-SA
  jprpid?: string // ID number
  jprpcall?: string // Cell number
  jprpadd?: string // Physical address
  jprpostal?: string // Postal code

  // Safe information
  safeYes: boolean
  safeNo: boolean
  safeH: boolean // Handgun safe
  safeR: boolean // Rifle safe
  safeS: boolean // Strongroom
  safeSe: string // Strongroom info
  safeD: boolean // Device
  safeDInfo: string // Device info
  safeMountYes: boolean
  safeMountNo: boolean
  safeWall: boolean
  safeFloor: boolean

  // Criminal history
  h5a: boolean // Convicted of offense - Yes
  h5b: boolean // Convicted of offense - No
  h51: string // Police station
  h52: string // Case number
  h53: string // Charge
  h54: string // Outcome/verdict

  h6a: boolean // Pending cases - Yes
  h6b: boolean // Pending cases - No
  h61: string // Police station
  h62: string // Case number
  h6a3: string // Offense

  h7a: boolean // Firearms lost/stolen - Yes
  h7b: boolean // Firearms lost/stolen - No
  h71: string // Police station
  h72: string // Case number
  h73: string // Circumstances
  h74: string // Details of firearm

  h8a: boolean // Case of negligence - Yes
  h8b: boolean // Case of negligence - No
  h81: string // Police station
  h82: string // Case number
  h83: string // Charge
  h84: string // Outcome/verdict

  h9a: boolean // Declared unfit - Yes
  h9b: boolean // Declared unfit - No
  h91: string // Police station
  h92: string // Case number
  h93: string // Charge
  h94: string // Date from which unfit
  h95: string // Period of unfitness

  h10a: boolean // Firearm confiscated - Yes
  h10b: boolean // Firearm confiscated - No
  h101: string // Police station
  h102: string // Case number
  h103: string // Circumstances
  h104: string // Outcome/verdict
}

// Initial form data with all the placeholders
export const initialNewLicenceData: NewLicenceData = {
  // These are standard FormData properties
  fullName: '',
  firstName: '',
  lastName: '',
  idNumber: '',
  phoneNumber: '',
  email: '',
  physicalAddress: '',
  documentProcessed: false,
  companyName: '',
  tradeProfession: '',
  workAddress: '',
  workPostalCode: '',
  workNumber: '',
  saId: false,
  fId: false,
  permRes: false,
  sexM: false,
  sexF: false,
  singles: false,
  married: false,
  divorced: false,
  widower: false,
  widow: false,
  citizenType: '',
  maritalStatus: '',
  spouseIdType: 'none',

  // Additional properties
  mainHF: true,
  addHF: false,

  // Licence type
  s13: false,
  s15: false,
  s16: false,
  s20: false,
  s20a: false,
  s20b: false,
  s20c: false,

  // Firearm type
  comb: false,
  otherDesign: false,
  otherDesignE: '',

  // SAP 350 (A) DETAILS - Firearm received from details
  sap350aName: '',
  sap350aIdFar: '',
  sap350aAddress: '',
  sap350aPostal: '',
  sap350aDate: '',

  // Firearm details
  semi: false,
  auto: false,
  man: false,
  otherAction: false,
  otherF: '',
  engg: '',
  pafk: '',
  pafkPostal: '',
  flap: '',
  flid: '',
  designation: '',
  apDate: '',
  apPlace: '',

  // Serial numbers
  bsn: '',
  fsn: '',
  rsn: '',

  // Component makes
  bsnm: '',
  fsnm: '',
  rsnm: '',

  // Current owner type
  pcoa: false,
  pcob: false,
  pcoc: false,
  pcoe: false,

  // Firearm dealer details
  fdrcn: '',
  fdtas: '',
  fdfarn: '',
  fdbadre: '',
  fdpostal: '',
  fdbcall: '',
  fdbmail: '',
  fdrpns: '',
  fdrpidsa: false,
  fdrpidno: false,
  fdrpid: '',
  fdrpcall: '',
  fdrpadd: '',
  fdrpostal: '',

  // Company details
  crcn: '',
  ctn: '',
  cfarn: '',
  cpadd: '',
  cpostal: '',
  cbtn: '',
  ccem: '',
  crpns: '',
  crpidsa: false,
  crpidno: false,
  crpid: '',
  crpcall: '',
  crpadd: '',
  crpostal: '',

  // Estate details
  executor: false,
  administrator: false,
  curatorship: false,
  trust: false,
  deLast: '',
  deFullName: '',
  deInitials: '',
  idOf: '',
  deEName: '',
  deEIdNo: '',
  deEIdSa: false,
  deEId: '',
  deEAdd: '',
  deEPostal: '',
  deECell: '',
  deEEmail: '',

  // Natural person's details
  npsaid: false,
  npidno: false,
  npid: '',
  npname: '',
  nplname: '',
  npinitials: '',
  npdateb: '',
  npage: '',
  npsexmale: false,
  npsexfemale: false,
  npaddress: '',
  nppostal: '',
  nptypeofres: '',
  npprof: '',
  npecname: '',
  npecadd: '',
  npecpostal: '',
  npcell: '',
  npworkc: '',
  npemail: '',

  // Private owner additional fields
  cell: '',
  workC: '',
  address: '',
  passport: '',
  afhy: false,
  afhn: false,

  // Spouse details
  spouseFullName: '',

  // Competency certificate
  tradeFirearm: false,
  possessFirearm: false,
  compHandgun: false,
  compRifle: false,
  compShotgun: false,
  compSelfLoading: false,
  f2ab: '',
  f3: '',
  f4: '',

  // Firearms in Possession
  firearmsInPossession: [],

  // Training Institution
  nameInst: '',
  serialCert: '',
  certIssueDate: '',
  certExpiryDate: '',

  // Member of association
  f5a: false,
  f5b: false,
  f6: '',
  assFarn: '',
  f7: '',
  f8: '',
  assExpire: '',

  // Juristic person's details
  jpname: '',
  jptn: '',
  jpfarn: '',
  jpadd: '',
  jppostal: '',
  jpbtn: '',
  jpem: '',
  jpfrb: '',
  jpfrbno: '',
  jprpns: '',
  jprpidsa: false,
  jprpidno: false,
  jprpid: '',
  jprpcall: '',
  jprpadd: '',
  jprpostal: '',

  // Safe information
  safeYes: false,
  safeNo: false,
  safeH: false,
  safeR: false,
  safeS: false,
  safeSe: '',
  safeD: false,
  safeDInfo: '',
  safeMountYes: false,
  safeMountNo: false,
  safeWall: false,
  safeFloor: false,

  // Criminal history - Set all 'No' options to true by default
  h5a: false,
  h5b: true, // Default to No
  h51: '',
  h52: '',
  h53: '',
  h54: '',

  h6a: false,
  h6b: true, // Default to No
  h61: '',
  h62: '',
  h6a3: '',

  h7a: false,
  h7b: true, // Default to No
  h71: '',
  h72: '',
  h73: '',
  h74: '',

  h8a: false,
  h8b: true, // Default to No
  h81: '',
  h82: '',
  h83: '',
  h84: '',

  h9a: false,
  h9b: true, // Default to No
  h91: '',
  h92: '',
  h93: '',
  h94: '',
  h95: '',

  h10a: false,
  h10b: true, // Default to No
  h101: '',
  h102: '',
  h103: '',
  h104: '',
  before90DaysYes: undefined,
  before90DaysNo: undefined,
  before90DaysReason: '',
  afterDueBeforeExpiryYes: undefined,
  afterDueBeforeExpiryNo: undefined,
  afterDueBeforeExpiryReason: '',
  additionalOriginalLicences: undefined,
  tradingAsName: '',
  farNumber: '',
  postalAddress: '',
  businessTelNumber: ''
}
