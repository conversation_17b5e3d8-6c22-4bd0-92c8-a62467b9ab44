// Define the update channel type
export type UpdateChannel = 'release' | 'prerelease'

// Define settings sections
export type SettingsSection = 'account' | 'update' | 'system' | 'whatsapp' | 'whatsapp-notifications' | 'logs'

// Define user data type
export type UserData = {
  username: string
  email: string
  role: string
  lastLogin: string | null
  avatarUrl?: string
  displayName?: string
}

// Define WhatsApp session status type
export type WhatsAppSessionStatus = 'CONNECTED' | 'DISCONNECTED' | 'CONNECTING' | 'FAILED' | 'UNKNOWN' | 'NOT_FOUND' | 'TERMINATED'

// Define server status type
export type ServerStatus = {
  alive: boolean
  message?: string
  error?: string
  lastChecked?: Date
  responseTime?: number
} | null

// Define WhatsApp session info type
export type WhatsAppSessionInfo = {
  id: string
  status?: WhatsAppSessionStatus
  lastActive?: Date
  isDefault?: boolean
}

// Define password form type
export type PasswordForm = {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// Define test message form type
export type TestMessageForm = {
  recipient: string
  message: string
}

// Define real-time connection status type
export type RealtimeConnectionStatus = {
  connected: boolean
  lastPing?: number
  reconnectAttempts?: number
  message?: string
} | null
