import React from 'react'
import { FormSection, FormField } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionWithAddressProps } from '../utils/types'

/**
 * Professional Information section component
 */
const ProfessionalInfo: React.FC<SectionWithAddressProps> = ({
  formData,
  updateFormData,
  handleAddressChange,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Professional Information</h3>

      <FormSection title="Company Details" subtitle="Your employment information">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormField
            label="Company Name"
            name="companyName"
            value={formData.companyName || ''}
            onChange={handleChange}
            placeholder="Enter your company name"
            required={false}
          />

          <FormField
            label="Trade/Profession"
            name="tradeProfession"
            value={formData.tradeProfession || ''}
            onChange={handleChange}
            placeholder="e.g. Engineer, Accountant, etc."
            required={false}
          />
        </div>
      </FormSection>

      <FormSection title="Work Address" subtitle="Your work location">
        <AddressInput
          label="Work Address"
          value={formData.workAddress || ''}
          postalCode={formData.workPostalCode || ''}
          onChange={(address, postalCode) =>
            handleAddressChange(address, postalCode, undefined, true)
          }
          placeholder="Enter your work address"
          isTextarea={false}
          required={false}
          postalCodeRequired={false}
        />
        <p className="text-xs text-stone-400 mt-1 mb-3">
          Enter your work address and postal code.
        </p>

        <div className="grid grid-cols-2 gap-3 mt-2">
          <FormField
            label="Work Postal Code"
            name="workPostalCode"
            value={formData.workPostalCode || ''}
            onChange={(e) => {
              const newPostalCode = e.target.value.replace(/\D/g, '').slice(0, 4)
              handleAddressChange(
                formData.workAddress,
                newPostalCode,
                undefined,  // houseNumber parameter
                true
              )
              handleChange({
                ...e,
                target: {
                  ...e.target,
                  value: newPostalCode
                }
              } as React.ChangeEvent<HTMLInputElement>)
            }}
            placeholder="4-digit postal code"
            required={false}
            maxLength={4}
            pattern="[0-9]{4}"
          />

          <FormField
            label="Work Contact"
            name="workNumber"
            value={formData.workNumber || ''}
            onChange={handleChange}
            placeholder="e.g. ************"
            required={false}
            type="tel"
          />
        </div>
      </FormSection>
    </div>
  )
}

export default ProfessionalInfo
