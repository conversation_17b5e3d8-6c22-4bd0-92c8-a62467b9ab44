import { FormData } from '../../../../types/FormData'

/**
 * Validates the entire form data
 * @param formData - The form data to validate
 * @returns Error message or null if valid
 */
export const validateFormData = (): string | null => {
  // All fields are now optional, so validation always passes
  return null
}

/**
 * Validates the personal information section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validatePersonalInfo = (_formData: FormData): boolean => {
  // All fields are now optional, so validation always passes
  return true
}

/**
 * Validates the professional information section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateProfessionalInfo = (_formData: FormData): boolean => {
  // All fields are now optional, so validation always passes
  return true
}

/**
 * Validates the firearm types section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateFirearmTypes = (_formData: FormData): boolean => {
  // All fields are now optional, so validation always passes
  return true
}

/**
 * Validates the training institution section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateTrainingInstitution = (_formData: FormData): boolean => {
  // All fields are now optional, so validation always passes
  return true
}

/**
 * Validates the criminal history section
 * @param _formData - The form data to validate
 * @returns Boolean indicating if the section is valid
 */
export const validateCriminalHistory = (_formData: FormData): boolean => {
  // All fields are now optional, so validation always passes
  return true
}
