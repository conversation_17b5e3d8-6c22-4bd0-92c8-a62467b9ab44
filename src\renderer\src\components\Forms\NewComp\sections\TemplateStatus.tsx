import React from 'react'
import { FormSection } from '../../../FormComponents'
import { TemplateStatus as TemplateStatusType } from '../utils/types'

interface TemplateStatusProps {
  status: TemplateStatusType
  templateName: string
  error: string | null
}

/**
 * Template Status component
 */
const TemplateStatus: React.FC<TemplateStatusProps> = ({ status, templateName, error }) => {
  return (
    <FormSection title="Template Status" className="mb-6">
      {status === 'loading' ? (
        <div className="flex items-center text-stone-300">
          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500 mr-2"></div>
          <span>Checking template availability...</span>
        </div>
      ) : status === 'error' ? (
        <div className="text-red-400 text-sm py-2">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>{error || 'Error accessing template'}</span>
          </div>
        </div>
      ) : (
        <div className="flex items-center text-green-400">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          <span>Template ready: {templateName}</span>
        </div>
      )}
    </FormSection>
  )
}

export default TemplateStatus
