import React from 'react'
import { FormSection, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

const PreviousCompetency: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Previous Competency Details</h3>

      <FormSection
        title="Previous Competency Certificate"
        subtitle="Please provide details of your previous competency certificate"
      >
        <div className="space-y-3">
          {/* Certificate Type and Number side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Type of Competency Certificate</label>
                <input
                  type="text"
                  name="competencyCertificateType"
                  value={formData.competencyCertificateType || ''}
                  onChange={handleChange}
                  placeholder="Enter competency certificate type"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Competency Certificate Number</label>
                <input
                  type="text"
                  name="competencyCertificateNumber"
                  value={formData.competencyCertificateNumber || ''}
                  onChange={handleChange}
                  placeholder="Enter certificate number"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          {/* Issue Date and Expiry Date side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Date of Issue</label>
                <input
                  type="date"
                  name="competencyIssueDate"
                  value={formData.competencyIssueDate || ''}
                  onChange={handleChange}
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Expiry Date</label>
                <input
                  type="date"
                  name="competencyExpiryDate"
                  value={formData.competencyExpiryDate || ''}
                  onChange={handleChange}
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </FormSection>

      <FormSection
        title="Association Membership"
        subtitle="Information about your membership in hunting, sport-shooting, or collectors associations"
      >
        <div className="bg-stone-800 rounded-lg p-4 mb-3">
          <RadioGroup
            name="isMemberOfAssociation"
            value={formData.isMemberOfAssociation ? 'yes' : 'no'}
            onChange={(value) => {
              const isYes = value === 'yes';
              updateFormData({
                isMemberOfAssociation: isYes,
                // Set the corresponding placeholder fields
                f5a: isYes,
                f5b: !isYes
              });
            }}
            options={[
              { value: 'yes', label: 'Yes' },
              { value: 'no', label: 'No' }
            ]}
          />
          <p className="text-sm text-stone-400 mt-2">
            Are you a member of any hunting, sport-shooting, collectors association or a game farmer
            or professional hunter?
          </p>
        </div>

        {formData.isMemberOfAssociation && (
          <div className="space-y-3">
            {/* Association Name and Membership Number side by side */}
            <div className="flex flex-row gap-3 overflow-x-auto pb-2">
              <div className="flex-1 min-w-[150px]">
                <div className="h-full flex flex-col justify-between">
                  <label className="block text-sm font-medium text-stone-300 mb-1">Name of Association</label>
                  <input
                    type="text"
                    name="associationName"
                    value={formData.associationName || ''}
                    onChange={(e) => {
                      handleChange(e);
                      // Also update the f6 field for the template
                      updateFormData({ f6: e.target.value });
                    }}
                    placeholder="Enter association name"
                    required={formData.isMemberOfAssociation}
                    className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex-1 min-w-[150px]">
                <div className="h-full flex flex-col justify-between">
                  <label className="block text-sm font-medium text-stone-300 mb-1">Membership Number</label>
                  <input
                    type="text"
                    name="associationMembershipNumber"
                    value={formData.associationMembershipNumber || ''}
                    onChange={(e) => {
                      handleChange(e);
                      // Also update the f7 field for the template
                      updateFormData({ f7: e.target.value });
                    }}
                    placeholder="Enter membership number"
                    required={formData.isMemberOfAssociation}
                    className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Date Joined */}
            <div className="flex flex-row gap-3 overflow-x-auto pb-2">
              <div className="flex-1 min-w-[150px]">
                <div className="h-full flex flex-col justify-between">
                  <label className="block text-sm font-medium text-stone-300 mb-1">Date Joined</label>
                  <input
                    type="date"
                    name="associationDateJoined"
                    value={formData.associationDateJoined || ''}
                    onChange={(e) => {
                      handleChange(e);
                      // Also update the f8 field for the template
                      updateFormData({ f8: e.target.value });
                    }}
                    required={formData.isMemberOfAssociation}
                    className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </FormSection>
    </div>
  )
}

export default PreviousCompetency
