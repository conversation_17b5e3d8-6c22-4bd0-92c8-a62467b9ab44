import { License as LicenseType, Client } from '../../types'

// Extend the imported type with additional properties used in this component
export interface GunLicense extends Omit<LicenseType, 'last_notification_date'> {
  toggle_notifications: boolean
  issues_date: string
  client: Client
  last_notification_date?: string | null
}

export interface RenewalFormData {
  issue_date: string
  expiry_date: string
  lic_number: string
  section: string
}

export interface NotificationStatus {
  loading: boolean
  error?: string
  success?: string
}

export interface FilterState {
  notificationFilter: 'all' | 'active' | 'paused'
}

export interface PaginationState {
  currentPage: number
  itemsPerPage: number
  totalLicenses: number
}
