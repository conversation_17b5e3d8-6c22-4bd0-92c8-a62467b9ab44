import React from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'
import { PaginationState } from './types'

interface PaginationProps {
  page: PaginationState['page']
  setPage: (page: number) => void
  totalItems: number
  itemsPerPage: PaginationState['perPage']
}

export const Pagination: React.FC<PaginationProps> = ({
  page,
  setPage,
  totalItems,
  itemsPerPage
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage)

  return (
    <div className="flex justify-between items-center mt-4">
      <button
        onClick={() => setPage(Math.max(1, page - 1))}
        disabled={page === 1}
        className="p-2 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-stone-700
          rounded-lg transition-colors"
      >
        <DashboardIcons.PrevPage className="w-5 h-5 text-white" />
      </button>

      <span className="text-white">
        Page {page} of {totalPages}
      </span>

      <button
        onClick={() => setPage(page + 1)}
        disabled={page >= totalPages}
        className="p-2 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-stone-700
          rounded-lg transition-colors"
      >
        <DashboardIcons.NextPage className="w-5 h-5 text-white" />
      </button>
    </div>
  )
}
