import React from 'react'

interface LoanCardFirearmInfoProps {
  gunLicence?: any
  firearmInfo?: any
  assignmentInfo?: any
  highlightMatch: (text: string) => JSX.Element | string
}

const LoanCardFirearmInfo: React.FC<LoanCardFirearmInfoProps> = ({
  gunLicence,
  firearmInfo,
  assignmentInfo,
  highlightMatch
}) => {
  if (!gunLicence && !firearmInfo) {
    return null
  }

  return (
    <div className="text-center mb-2 flex flex-col items-center">
      {gunLicence && (
        <span className="text-xs text-stone-300">
          {highlightMatch(gunLicence.make || 'No firearm')}
        </span>
      )}
      
      {!gunLicence && firearmInfo && (
        <>
          <span className="text-xs text-stone-300">
            {highlightMatch(firearmInfo.make || '')} {firearmInfo.model ? highlightMatch(firearmInfo.model) : ''}
          </span>
          {assignmentInfo && (
            <div className="flex items-center mt-1">
              <span className="text-xs text-green-400 font-medium px-2 py-0.5 bg-green-500/20 rounded-full border border-green-500/30">
                {assignmentInfo.return_date ? 'Was Assigned' : 'Assigned to Client'}
              </span>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default React.memo(LoanCardFirearmInfo)
