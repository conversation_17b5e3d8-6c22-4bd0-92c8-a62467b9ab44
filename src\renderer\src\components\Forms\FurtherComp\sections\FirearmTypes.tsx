import React from 'react'
import { FormSection, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

const FirearmTypes: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target

    // Update the form data with the checkbox value
    updateFormData({ [name]: checked })

    // If selfLoading is checked, set selfLoadingT to 'Self-Loading'
    if (name === 'selfLoading') {
      updateFormData({ selfLoadingT: checked ? 'Self-Loading' : '' })
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Firearm Types</h3>

      <FormSection title="Competency Type" subtitle="Select the type of competency you are applying for">
        <div className="bg-stone-800 rounded-lg p-4">
          <RadioGroup
            name="competencyType"
            value={formData.competencyType || ''}
            onChange={(value) => {
              const competencyType = value as 'trade' | 'possess' | '';
              updateFormData({
                competencyType,
                // Set the corresponding placeholder fields
                tradeFirearm: competencyType === 'trade',
                possessFirearm: competencyType === 'possess'
              });
            }}
            options={[
              { value: 'trade', label: 'Trade in Firearms' },
              { value: 'possess', label: 'To Possess Firearm' }
            ]}
            required
          />
          <p className="text-xs text-stone-400 mt-2">
            Select the type of competency certificate you are applying for
          </p>
        </div>
      </FormSection>

      <FormSection title="Firearm Types" subtitle="Select all firearm types that apply">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {[
            { name: 'pistol', label: 'Pistol', checked: formData.pistol || false },
            { name: 'rifle', label: 'Rifle', checked: formData.rifle || false },
            { name: 'shotgun', label: 'Shotgun', checked: formData.shotgun || false },
            { name: 'revolver', label: 'Revolver', checked: formData.revolver || false },
            { name: 'selfLoading', label: 'Self-Loading', checked: formData.selfLoading || false }
          ].map((option) => (
            <div key={option.name} className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id={option.name}
                name={option.name}
                checked={option.checked}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor={option.name} className="ml-2 block text-sm text-stone-300">
                {option.label}
              </label>
            </div>
          ))}
        </div>
        <p className="text-xs text-stone-400 mt-2">
          Select all firearm types for which you are applying for competency
        </p>
      </FormSection>
    </div>
  )
}

export default FirearmTypes
