import React, { useCallback, useState, useEffect, useRef } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import electronLogo from '../assets/logo.png'
import { Menu, X, LogOut, Check, Settings } from 'lucide-react'
import { handleUserLogout } from '../utils/authUtils'
import LogoutOverlay from './LogoutOverlay'
import { useLogout } from '../contexts/LogoutContext'

function TitleBar(): React.JSX.Element {
  const navigate = useNavigate()
  const location = useLocation()
  const [windowWidth, setWindowWidth] = useState<number>(window.innerWidth)
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false)
  const [isUpdateMenuOpen, setIsUpdateMenuOpen] = useState<boolean>(false)
  const [, setIsCheckingUpdate] = useState<boolean>(false)
  const [downloadProgress, setDownloadProgress] = useState<number>(0)
  const [isDownloading, setIsDownloading] = useState<boolean>(false)
  const [isUpdateReady, setIsUpdateReady] = useState<boolean>(false)
  const { isLoggingOut, setIsLoggingOut } = useLogout()
  const dropdownRef = useRef<HTMLDivElement>(null)
  const updateDropdownRef = useRef<HTMLDivElement>(null)

  // Responsive breakpoint - when to show hamburger menu
  const breakpoint = 768

  // Update window width on resize
  useEffect(() => {
    const handleResize = (): void => {
      setWindowWidth(window.innerWidth)
      if (window.innerWidth > breakpoint) {
        setIsMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent): void => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false)
      }
      if (updateDropdownRef.current && !updateDropdownRef.current.contains(event.target as Node)) {
        setIsUpdateMenuOpen(false)
      }
    }

    if (isMenuOpen || isUpdateMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isMenuOpen, isUpdateMenuOpen])

  // Toggle menu
  const toggleMenu = (): void => {
    setIsMenuOpen(!isMenuOpen)
  }

  // Memoize the logout handler
  const handleLogout = useCallback(async (): Promise<void> => {
    try {
      console.log('Logout process started');
      // Set logging out state to show spinner
      setIsLoggingOut(true)

      // First set window to compact size to ensure it happens before navigation
      console.log('Setting window to compact size');
      window.electronAPI?.setCompactWindowSize()

      // Add a small delay to ensure the window resize completes before navigation
      await new Promise(resolve => setTimeout(resolve, 300))

      // Use our centralized logout function
      console.log('Calling handleUserLogout');
      await handleUserLogout()

      // Notify main process that user is logged out
      console.log('Sending user-logged-out event to main process');
      if (window.electron?.ipcRenderer?.send) {
        // Cast to any to bypass type checking for custom event
        ;(window.electron.ipcRenderer as any).send('user-logged-out')
      } else {
        console.error('window.electron.ipcRenderer.send is not available');
      }

      // Add another small delay before navigation
      await new Promise(resolve => setTimeout(resolve, 300))

      // Navigate to the login page
      console.log('Navigating to login page');
      navigate('/')

      // Reset the logging out state after a short delay to ensure the navigation has completed
      setTimeout(() => {
        console.log('Resetting logout state');
        setIsLoggingOut(false)
      }, 500)
    } catch (error) {
      console.error('Error logging out:', error)
      // Reset logging out state if there's an error
      setIsLoggingOut(false)
      // You could add a toast notification here to inform the user
    }
  }, [navigate])

  // Handle logo click to open external URL
  const handleLogoClick = useCallback((): void => {
    const width = window.screen.width
    const height = window.screen.height
    window.open(
      'https://sheldonbakker.pages.dev/',
      '_blank',
      `toolbar=yes,scrollbars=yes,resizable=yes,top=0,left=0,width=${width},height=${height}`
    )
  }, [])

  // Handle install update
  const handleInstallUpdate = (): void => {
    const electron = window.electron as any
    if (electron?.ipcRenderer?.send) {
      electron.ipcRenderer.send('confirm-install')
      setIsUpdateReady(false)
    }
  }

  // Memoize the navigation handlers
  const navigateTo = useCallback(
    (path: string): (() => void) =>
      () => {
        navigate(path)
        setIsMenuOpen(false) // Close menu after navigation
      },
    [navigate]
  )

  // Memoize the active path check
  const isActive = useCallback(
    (path: string): boolean => location.pathname === path,
    [location.pathname]
  )

  // Memoize the link component to prevent unnecessary re-renders
  const NavLink = useCallback(
    ({ path, label, gradient }: { path: string; label: string; gradient: string }) => (
      <button
        onClick={navigateTo(path)}
        className={`relative px-3 py-1 rounded-md transition-all duration-200 group ${
          isActive(path)
            ? 'text-white bg-stone-800/60'
            : 'text-stone-300 hover:text-white hover:bg-stone-800/40'
        }`}
      >
        {label}
        <span
          className={`absolute inset-x-0 -bottom-[1px] h-[2px] ${gradient} transform transition-transform duration-200 ${
            isActive(path) ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100'
          }`}
        />
      </button>
    ),
    [navigateTo, isActive]
  )

  // Dropdown version of NavLink for mobile menu
  const DropdownNavLink = useCallback(
    ({ path, label, gradient }: { path: string; label: string; gradient: string }) => (
      <button
        onClick={navigateTo(path)}
        className={`relative w-full text-left px-4 py-2 transition-all duration-200 ${
          isActive(path)
            ? 'text-white bg-stone-800/60'
            : 'text-stone-300 hover:text-white hover:bg-stone-800/40'
        }`}
      >
        {label}
        <span
          className={`absolute left-0 h-full w-[3px] ${gradient} transform transition-opacity duration-200 ${
            isActive(path) ? 'opacity-100' : 'opacity-0'
          }`}
        />
      </button>
    ),
    [navigateTo, isActive]
  )

  // Navigation links data
  const navLinks = [
    {
      path: '/dashboard',
      label: 'Clients',
      gradient: 'bg-gradient-to-r from-orange-500 to-orange-600'
    },
    {
      path: '/licenses',
      label: 'Licenses',
      gradient: 'bg-gradient-to-r from-orange-500 to-orange-600'
    },
    {
      path: '/expired',
      label: 'Expired Licenses',
      gradient: 'bg-gradient-to-r from-red-500 to-red-600'
    },
    {
      path: '/docscript',
      label: 'Forms Templates',
      gradient: 'bg-gradient-to-r from-orange-500 to-orange-600'
    },
    { path: '/loans', label: 'Loans', gradient: 'bg-gradient-to-r from-orange-500 to-orange-600' },
    { path: '/firearm-storage', label: 'Firearm Storage', gradient: 'bg-gradient-to-r from-orange-500 to-orange-600' },
    { path: '/pipedrive', label: 'Pipedrive', gradient: 'bg-gradient-to-r from-orange-500 to-orange-600' }
  ]

  // Window control handlers
  const handleMinimize = (): void => {
    window.electronAPI?.windowMinimize()
  }

  const handleMaximize = (): void => {
    window.electronAPI?.windowMaximize()
  }

  const handleClose = (): void => {
    window.electronAPI?.windowClose()
  }

  // Add update status handler
  useEffect(() => {
    const handleUpdateStatus = (status: string, data?: unknown): void => {
      console.log('Update status received:', status, data)

      switch (status) {
        case 'update-not-available':
          setIsCheckingUpdate(false)
          setIsDownloading(false)
          break
        case 'update-error':
          console.error('Update check failed:', data)
          setIsCheckingUpdate(false)
          setIsDownloading(false)
          break
        case 'checking-for-update':
          setIsCheckingUpdate(true)
          setIsDownloading(false)
          setIsUpdateReady(false)
          break
        case 'update-available':
          setIsCheckingUpdate(false)
          setIsDownloading(true)
          setDownloadProgress(0)
          break
        case 'download-progress':
          setIsDownloading(true)
          if (data && typeof data === 'object' && 'percent' in data) {
            setDownloadProgress(Number((data as { percent: number }).percent.toFixed(0)))
          }
          break
        case 'update-downloaded':
          console.log('Update downloaded, ready to install')
          setIsCheckingUpdate(false)
          setIsDownloading(false)
          setIsUpdateReady(true)
          break
        default:
          console.log('Unknown update status:', status)
          setIsCheckingUpdate(false)
          setIsDownloading(false)
      }
    }

    const electron = window.electron as any
    if (electron?.ipcRenderer?.on) {
      electron.ipcRenderer.on('update-status', handleUpdateStatus)
    }

    return () => {
      if (electron?.ipcRenderer?.removeListener) {
        electron.ipcRenderer.removeListener('update-status', handleUpdateStatus)
      }
    }
  }, [])

  // Update button component
  const UpdateButton = (): React.JSX.Element => {
    // Render different button states based on update status
    if (isUpdateReady) {
      return (
        <div className="relative mr-2">
          <button
            onClick={handleInstallUpdate}
            className="flex items-center gap-1 px-3 py-1 bg-green-700 text-white hover:bg-green-600 rounded-md text-xs transition-colors"
            title="Install Update"
          >
            <Check className="text-white transition-colors" size={14} />
            <span>Install Update</span>
          </button>
        </div>
      )
    }

    if (isDownloading) {
      return (
        <div className="relative mr-2">
          <div className="flex items-center gap-1 px-3 py-1 bg-stone-800 text-stone-300 rounded-md text-xs">
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-stone-300 mr-2"></div>
            <span>Downloading: {downloadProgress}%</span>
          </div>
        </div>
      )
    }

    return (
      <div className="relative mr-2" ref={updateDropdownRef}>
        <button
          onClick={() => navigate('/settings')}
          className="flex items-center gap-1 px-3 py-1 bg-stone-800 text-stone-300 hover:text-white hover:bg-stone-700 rounded-md text-xs transition-colors"
          title="Settings"
        >
          <Settings className="text-stone-300 transition-colors" size={14} />
          <span>Settings</span>
        </button>
      </div>
    )
  }

  return (
    <>
      {/* Full-screen overlay when logging out */}
      <LogoutOverlay isVisible={isLoggingOut} />

      <div
        className="flex items-center justify-between w-full h-10 bg-stone-900 border-b border-stone-700/30 select-none app-drag-region app-titlebar"
        style={{ display: 'flex', position: 'fixed', top: 0, left: 0, right: 0, zIndex: 9999 }}
      >
      {/* Empty spacer div for drag handle - limited to the title bar height */}
      <div
        className="absolute inset-0 app-drag-region"
        style={{ height: '100%', pointerEvents: 'auto', bottom: 'auto' }}
      ></div>

      {/* Logo and navigation section */}
      <div className="flex-1 h-full flex items-center z-10">
        {/* Logo */}
        <div className="flex items-center gap-2 ml-3 mr-4 app-no-drag">
          <img
            src={electronLogo}
            alt="Firearm Studio Logo"
            className="w-6 h-6 cursor-pointer hover:opacity-80 transition-opacity"
            loading="lazy"
            onClick={handleLogoClick}
            onDragStart={(e) => e.preventDefault()}
            title="Play a Game"
          />
        </div>

        {/* Responsive Navigation - Hamburger for small screens */}
        {windowWidth <= breakpoint ? (
          <div className="relative app-no-drag" ref={dropdownRef}>
            <button
              onClick={toggleMenu}
              className="flex items-center justify-center p-1 text-stone-300 hover:text-white hover:bg-stone-800/40 rounded-md transition-colors"
              aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
            >
              {isMenuOpen ? <X size={20} /> : <Menu size={20} />}
            </button>

            {/* Dropdown Menu */}
            {isMenuOpen && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-stone-900 border border-stone-700/30 rounded-md shadow-lg z-50 overflow-hidden">
                <div className="py-1">
                  {navLinks.map((link, index) => (
                    <DropdownNavLink
                      key={index}
                      path={link.path}
                      label={link.label}
                      gradient={link.gradient}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          /* Standard Navigation Links for larger screens */
          <div className="flex items-center space-x-1 app-no-drag">
            {navLinks.map((link, index) => (
              <NavLink key={index} path={link.path} label={link.label} gradient={link.gradient} />
            ))}
          </div>
        )}
      </div>

      {/* Right section with update settings, logout and window controls */}
      <div className="flex items-center h-full app-no-drag z-10">
        {/* Update button */}
        <UpdateButton />

        {/* Logout button */}
        <button
          onClick={handleLogout}
          disabled={isLoggingOut}
          className={`flex items-center gap-1 px-3 py-1 mr-3 rounded-md text-xs transition-colors ${isLoggingOut
            ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
            : 'bg-stone-800 text-stone-300 hover:text-white hover:bg-stone-700'}`}
          title={isLoggingOut ? 'Logging out...' : 'Logout'}
        >
          {isLoggingOut ? (
            <>
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-stone-400 mr-1"></div>
              <span>Logging out...</span>
            </>
          ) : (
            <>
              <LogOut className="text-stone-300 transition-colors" size={14} />
              <span>Logout</span>
            </>
          )}
        </button>

        {/* Window controls */}
        <div className="flex items-center">
          <button
            onClick={handleMinimize}
            className="flex items-center justify-center w-10 h-10 hover:bg-stone-800 transition-colors"
            aria-label="Minimize"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
              <rect x="1" y="6" width="10" height="1" fill="white" />
            </svg>
          </button>
          <button
            onClick={handleMaximize}
            className="flex items-center justify-center w-10 h-10 hover:bg-stone-800 transition-colors"
            aria-label="Maximize"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
              <rect x="1" y="1" width="10" height="10" stroke="white" fill="none" />
            </svg>
          </button>
          <button
            onClick={handleClose}
            className="flex items-center justify-center w-10 h-10 hover:bg-red-600 transition-colors"
            aria-label="Close"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">
              <line x1="1" y1="1" x2="11" y2="11" stroke="white" strokeWidth="1" />
              <line x1="1" y1="11" x2="11" y2="1" stroke="white" strokeWidth="1" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    </>
  )
}

export default React.memo(TitleBar)
