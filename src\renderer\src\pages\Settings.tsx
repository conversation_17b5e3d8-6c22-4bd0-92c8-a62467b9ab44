import { useState, useEffect } from 'react'
import { Download, Info, User, FileText, MessageSquare } from 'lucide-react'
import {
  AccountSettings,
  UpdateSettings,
  SystemSettings,
  WhatsAppSettings,
  WhatsAppNotificationSettings,
  LogsSettings,
  SettingsSection
} from '../components/Settings'

function Settings(): React.JSX.Element {
  const [activeSection, setActiveSection] = useState<SettingsSection>('account')

  // Navigation item component
  const NavItem = ({
    id,
    label,
    icon,
    isActive
  }: {
    id: SettingsSection
    label: string
    icon: React.ReactNode
    isActive: boolean
  }) => (
    <button
      onClick={() => setActiveSection(id)}
      className={`flex items-center gap-2 w-full px-2 py-1.5 text-left rounded-md transition-colors text-sm ${
        isActive
          ? 'bg-stone-700 text-white'
          : 'text-stone-400 hover:text-white hover:bg-stone-800/60'
      }`}
    >
      {icon}
      <span>{label}</span>
    </button>
  )

  // Render content based on active section
  const renderContent = () => {
    switch (activeSection) {
      case 'update':
        return <UpdateSettings />
      case 'system':
        return <SystemSettings />
      case 'account':
        return <AccountSettings />
      case 'whatsapp':
        return <WhatsAppSettings activeSection={activeSection} />
      case 'whatsapp-notifications':
        return <WhatsAppNotificationSettings activeSection={activeSection} />
      case 'logs':
        return <LogsSettings />
      default:
        return <AccountSettings />
    }
  }

  // Add a useEffect to disable scrolling on the main content area and body
  useEffect(() => {
    // Get the main content area
    const mainContent = document.querySelector(
      '#root > div > div:nth-child(2)'
    ) as HTMLElement | null

    // Save original styles
    const originalBodyOverflow = document.body.style.overflow
    const originalHtmlOverflow = document.documentElement.style.overflow

    // Disable scrolling on body and html
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    if (mainContent) {
      // Save the original style
      const originalOverflow = mainContent.style.overflowY

      // Disable scrolling
      mainContent.style.overflowY = 'hidden'

      // Restore original styles when component unmounts
      return () => {
        mainContent.style.overflowY = originalOverflow
        document.body.style.overflow = originalBodyOverflow
        document.documentElement.style.overflow = originalHtmlOverflow
      }
    }

    // Restore original styles when component unmounts (if mainContent not found)
    return () => {
      document.body.style.overflow = originalBodyOverflow
      document.documentElement.style.overflow = originalHtmlOverflow
    }
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-b from-stone-900 to-stone-800 p-6">
      {/* Page title */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white">Settings</h1>
      </div>

      {/* Two-column layout - always side by side */}
      <div className="flex flex-row h-[calc(100vh-180px)]">
        {/* Left column: Navigation */}
        <div className="w-80 bg-stone-800/60 rounded-lg shadow-lg p-4 flex-shrink-0 flex flex-col">
          <div className="space-y-1">
            <NavItem
              id="account"
              label="Account Settings"
              icon={<User size={16} />}
              isActive={activeSection === 'account'}
            />
            <NavItem
              id="update"
              label="Update Settings"
              icon={<Download size={16} />}
              isActive={activeSection === 'update'}
            />
            <NavItem
              id="system"
              label="System Settings"
              icon={<Info size={16} />}
              isActive={activeSection === 'system'}
            />
            <NavItem
              id="whatsapp"
              label="WhatsApp Settings"
              icon={
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 21l1.65-3.8a9 9 0 1 1 3.4 2.9L3 21" />
                  <path d="M9 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
                  <path d="M14 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
                  <path d="M12 17a5 5 0 0 0 5-5v-1a5 5 0 0 0-10 0v1a5 5 0 0 0 5 5Z" />
                </svg>
              }
              isActive={activeSection === 'whatsapp'}
            />
            <NavItem
              id="whatsapp-notifications"
              label="WhatsApp Notifications"
              icon={<MessageSquare size={16} />}
              isActive={activeSection === 'whatsapp-notifications'}
            />
            <NavItem
              id="logs"
              label="Logs"
              icon={<FileText size={16} />}
              isActive={activeSection === 'logs'}
            />
          </div>
        </div>

        {/* Right column: Content */}
        <div className="flex-1 ml-6 flex flex-col">
          <div className="bg-stone-800/30 rounded-lg p-4 flex flex-col h-full">
            <div className="overflow-y-auto flex-1 pr-2 custom-scrollbar">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
