import React, { useEffect } from 'react'
import { Loan } from '../../types'
import LoanCard from './LoanCard'

interface FocusedLoanProps {
  loan: Loan | null
  onClose: () => void
  handleCreatePayment: (loan: Loan) => void
  handleDeleteLoan?: (loanId: string) => void
  handleCancelLoan?: (loanId: string) => Promise<boolean>
  handleAddLicense?: (clientId: string, loanId: string) => void
  onUpdateInvoiceNumber?: (loanId: string, invoiceNumber: string) => Promise<{ success: boolean; error?: string }>
}

function FocusedLoan({
  loan,
  onClose,
  handleCreatePayment,
  handleDeleteLoan,
  handleCancelLoan,
  handleAddLicense,
  onUpdateInvoiceNumber
}: FocusedLoanProps): React.ReactElement | null {
  // Add body overflow class when focus mode is active
  useEffect(() => {
    document.body.classList.add('focus-mode-active')

    return () => {
      document.body.classList.remove('focus-mode-active')
    }
  }, [])

  // Add a useEffect to disable scrolling on the main content area and body
  useEffect(() => {
    const mainContent = document.querySelector(
      '#root > div > div:nth-child(2)'
    ) as HTMLElement | null

    const originalBodyOverflow = document.body.style.overflow
    const originalHtmlOverflow = document.documentElement.style.overflow

    if (mainContent) {
      mainContent.style.overflow = 'hidden'
    }
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    return () => {
      if (mainContent) {
        mainContent.style.overflow = ''
      }
      document.body.style.overflow = originalBodyOverflow
      document.documentElement.style.overflow = originalHtmlOverflow
    }
  }, [])

  if (!loan) {
    return null
  }

  return (
    <LoanCard
      loan={loan}
      onAddPayment={handleCreatePayment}
      onDeleteLoan={handleDeleteLoan ? 
        (async (loanId: string) => { await handleDeleteLoan(loanId) }) : 
        (async () => Promise.resolve())}
      onCancelLoan={handleCancelLoan || (async () => false)}
      onAddLicense={handleAddLicense || (() => {})}
      onUpdateInvoiceNumber={onUpdateInvoiceNumber || (async () => ({ success: false, error: 'Not implemented' }))}
      isFocused
      onFocusToggle={onClose}
    />
  )
}

export default FocusedLoan
