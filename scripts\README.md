# Automatic Daily Deduction System

This directory contains scripts for automating tasks in the FLM System.

## daily_charges.py

This script automatically deducts R7.50 per day from the credit balance of all assigned firearms, while respecting the free storage period for Dealer type firearms. It is designed to handle 5,000+ updates per day efficiently.

### How It Works

1. The script runs daily via a GitHub workflow (see `.github/workflows/daily-charges.yml`)
2. It connects to the Supabase database using environment variables
3. It retrieves all active firearm assignments (where return_date is null) using pagination
4. It processes assignments in batches using parallel execution:
   - Checks if the firearm is in its free storage period (for Dealer type)
   - Calculates the number of days since the last charge
   - Applies a charge of R7.50 per day
   - Batches transaction records for efficient insertion
   - Batches assignment updates for efficient database operations

### Performance Features

1. **Batch Processing**: Processes assignments in configurable batches (default: 100)
2. **Parallel Execution**: Uses multiple threads to process batches concurrently
3. **Connection Pooling**: Maintains persistent connections to reduce overhead
4. **Caching**: Caches firearm and client details to reduce duplicate queries
5. **Rate Limiting**: Implements intelligent rate limiting to avoid API throttling
6. **Pagination**: Uses pagination to handle large datasets efficiently
7. **Retry Logic**: Automatically retries failed operations with exponential backoff
8. **Performance Metrics**: Reports processing rate and estimated daily capacity

### Payment Rules

- Owner type: R7.50 per day
- Private type: R7.50 per day
- Dealer type: R7.50 per day but gets 12 months free storage from Assigned Date

### Setup

To run this script, you need to set the following environment variables:

- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_KEY`: Your Supabase service role API key (not the anon key)

In GitHub, these are set as repository secrets.

### Manual Execution

You can manually trigger the workflow in GitHub Actions by going to the "Actions" tab, selecting the "Daily Firearm Storage Charges" workflow, and clicking "Run workflow".

For local testing, you can run the script directly:

```bash
# Set environment variables
export SUPABASE_URL="your-supabase-url"
export SUPABASE_SERVICE_KEY="your-service-key"

# Run the script
python scripts/daily_charges.py
```

### Error Handling

The script includes robust error handling:

- Retries failed API requests up to 3 times
- Logs detailed information about each operation
- Continues processing other assignments even if one fails
- Validates date formats and falls back to defaults if needed
- Exits with a non-zero status code if the overall process fails
