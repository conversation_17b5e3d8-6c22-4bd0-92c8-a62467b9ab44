import React, { useCallback } from 'react'
import { FormSection, FormField } from '../../FormComponents'
import { LoanFormState } from './types'

interface DetailsStepProps {
  formData: LoanFormState
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void
  errors: Record<string, string>
}

const DetailsStep: React.FC<DetailsStepProps> = ({
  formData,
  onChange,
  errors
}) => {
  // Prevent scroll wheel from changing number input values
  const preventWheelChange = useCallback((e: React.WheelEvent<HTMLInputElement>) => {
    // Prevent the default scroll behavior on number inputs
    e.currentTarget.blur();
  }, []);
  return (
    <FormSection title="Loan Details">
      {/* Basic Information Group */}
      <div className="mb-6">
        <h3 className="text-white text-sm font-medium mb-3 border-b border-stone-700 pb-2">Basic Information</h3>
        <div className="flex flex-row space-x-4" style={{ display: 'flex', flexDirection: 'row' }}>
          <div style={{ flex: '1' }}>
            <FormField
              label="Start Date"
              name="start_date"
              type="date"
              value={formData.start_date || ''}
              onChange={onChange}
              error={errors.start_date}
            />
          </div>

          <div style={{ flex: '1' }}>
            <FormField
              label="Quote Number"
              name="invoice_number"
              value={formData.invoice_number || ''}
              onChange={onChange}
              placeholder="INV/QUO-12345"
              error={errors.invoice_number}
            />
          </div>
        </div>
      </div>

      {/* Financial Information Group */}
      <div className="mb-6">
        <h3 className="text-white text-sm font-medium mb-3 border-b border-stone-700 pb-2">Financial Details</h3>
        <div className="flex flex-row space-x-4" style={{ display: 'flex', flexDirection: 'row' }}>
          <div style={{ flex: '1' }}>
            <FormField
              label="Firearm Cost"
              name="weapon_cost"
              type="number"
              value={formData.weapon_cost?.toString() || '0'}
              onChange={onChange}
              min="0"
              step="0.01"
              error={errors.weapon_cost}
              onWheel={preventWheelChange}
            />
          </div>

          <div style={{ flex: '1' }}>
            <FormField
              label="Deposit Amount"
              name="initial_payment"
              type="number"
              value={formData.initial_payment?.toString() || '0'}
              onChange={onChange}
              min="0"
              step="0.01"
              error={errors.initial_payment}
              onWheel={preventWheelChange}
            />
          </div>

          <div style={{ flex: '1' }}>
            <FormField
              label="Loan Amount"
              name="loan_amount"
              type="number"
              value={formData.loan_amount?.toString() || '0'}
              onChange={onChange}
              min="0"
              step="0.01"
              error={errors.loan_amount}
              disabled={true}
              onWheel={preventWheelChange}
            />
          </div>
        </div>
      </div>

      {/* Payment Breakdown Summary */}
      {formData.weapon_cost !== undefined && formData.weapon_cost > 0 && (
        <div className="mt-4 p-4 bg-stone-700/30 rounded-lg border border-stone-600/50">
          <h3 className="text-white font-medium mb-2">Payment Breakdown</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="text-stone-300">Total Firearm Cost:</div>
            <div className="text-white text-right">{(formData.weapon_cost || 0).toFixed(2)}</div>

            <div className="text-stone-300">Initial Payment:</div>
            <div className="text-white text-right">- {(formData.initial_payment || 0).toFixed(2)}</div>

            <div className="text-stone-300 font-medium pt-2 border-t border-stone-600">
              Remaining Balance (Loan Amount):
            </div>
            <div className="text-white font-medium text-right pt-2 border-t border-stone-600">
              {(formData.loan_amount || 0).toFixed(2)}
            </div>
          </div>
        </div>
      )}
    </FormSection>
  )
}

export default DetailsStep
