import React from 'react'
import { FormSection, FormField } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Training Institution section component
 */
const TrainingInstitution: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Training Institution Information</h3>

      <FormSection title="Training Details" subtitle="Information about your firearms training">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormField
            label="Training Institution Name"
            name="nameInst"
            value={formData.nameInst || ''}
            onChange={handleChange}
            placeholder="Name of training institution"
            required={false}
          />

          <FormField
            label="Certificate Serial Number"
            name="serialCert"
            value={formData.serialCert || ''}
            onChange={handleChange}
            placeholder="Training certificate serial number"
            required={false}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
          <FormField
            label="Date Certificate Issued"
            name="certIssueDate"
            value={formData.certIssueDate || ''}
            onChange={handleChange}
            type="date"
            required={false}
          />
          
          <FormField
            label="Date Certificate Expires"
            name="certExpiryDate"
            value={formData.certExpiryDate || ''}
            onChange={handleChange}
            type="date"
            required={false}
          />
        </div>
      </FormSection>
    </div>
  )
}

export default TrainingInstitution
