import { RenewLicenceData } from '../../types/RenewLicenceData'
import RenewLicenceForm from './RenewLicence/index'

// Types
interface RenewLicenceFormProps {
  onSubmit: (data: RenewLicenceData) => void
  templateName?: string
}

/**
 * RenewLicence - Firearm Licence Renewal Application Form Component
 *
 * This is a wrapper component that uses the refactored RenewLicenceForm component
 */
export default function RenewLicence({ onSubmit, templateName }: RenewLicenceFormProps): JSX.Element {
  return (
    <RenewLicenceForm onSubmit={onSubmit} templateName={templateName} />
  )
}
