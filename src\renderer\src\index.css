@import "tailwindcss";
@import "./styles/z-index.css";

/* Custom scrollbar styles */
.custom-scrollbar {
  scroll-behavior: smooth;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(28, 25, 23, 0.6);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(249, 115, 22), rgb(251, 146, 60));
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(249, 115, 22), rgb(234, 88, 12));
}

/* Hidden scrollbar style */
.hidden-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hidden-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}