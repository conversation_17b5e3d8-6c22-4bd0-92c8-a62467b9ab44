/**
 * Safely converts version data to a string
 * @param versionData - The version data to convert
 * @returns The version as a string
 */
export const safeVersionToString = (versionData: any): string => {
  if (versionData === null || versionData === undefined) {
    return 'Unknown'
  }
  if (typeof versionData === 'string') {
    return versionData
  }
  if (typeof versionData === 'object') {
    // If it's an object with a version property, use that
    if (versionData.version) {
      return typeof versionData.version === 'string'
        ? versionData.version
        : JSON.stringify(versionData.version)
    }
    // Otherwise stringify the whole object
    return JSON.stringify(versionData)
  }
  // For any other type, convert to string
  return String(versionData)
}

/**
 * Formats a phone number for WhatsApp
 * @param phone - The phone number to format
 * @returns The formatted phone number
 */
export const formatPhoneForWhatsApp = (phone: string): string => {
  // Remove any non-digit characters
  let cleanNumber = phone.replace(/\D/g, '')

  // If number starts with '0', replace with country code
  if (cleanNumber.startsWith('0')) {
    cleanNumber = '27' + cleanNumber.substring(1)
  }

  // If number doesn't start with country code, add it
  if (!cleanNumber.startsWith('27')) {
    cleanNumber = '27' + cleanNumber
  }

  // Include the @c.us suffix as required by the API
  return `${cleanNumber}@c.us`
}

/**
 * Helper function to make WhatsApp API calls
 * @param method - The method to call
 * @param args - The arguments to pass to the method
 * @returns The result of the API call
 */
export const callWhatsAppApi = async (
  method: string,
  ...args: any[]
): Promise<any> => {
  let result

  // Try using electronAPI first, fallback to electron.ipcRenderer
  if (window.electronAPI && 'whatsapp' in (window.electronAPI as any)) {
    const api = window.electronAPI as any

    if (typeof api.whatsapp[method] !== 'function') {
      // Fall back to using ipcRenderer directly
      if (window.electron?.ipcRenderer) {
        const ipcRenderer = window.electron.ipcRenderer as any
        const channel = `whatsapp-${method.replace(/([A-Z])/g, '-$1').toLowerCase()}`
        result = await ipcRenderer.invoke(channel, ...args)
      } else {
        throw new Error(`Method ${method} not found in WhatsApp API and no fallback available`)
      }
    } else {
      result = await api.whatsapp[method](...args)
    }
  } else if (window.electron?.ipcRenderer) {
    const ipcRenderer = window.electron.ipcRenderer as any
    const channel = `whatsapp-${method.replace(/([A-Z])/g, '-$1').toLowerCase()}`
    result = await ipcRenderer.invoke(channel, ...args)
  } else {
    throw new Error('WhatsApp API not available. Please restart the application.')
  }

  if (!result.success) {
    // Check if the error contains HTML (indicating 404 or similar server error)
    if (
      result.error &&
      typeof result.error === 'string' &&
      result.error.includes('<!DOCTYPE html>')
    ) {
      throw new Error(
        'API endpoint not found. The WhatsApp API may have changed or is unavailable.'
      )
    }

    // Special handling for getAllSessions method
    if (method === 'getAllSessions') {
      // Don't throw for this method, just return the result with success: false
      return result
    }

    throw new Error(result.error || `Failed to ${method}`)
  }

  return result
}
