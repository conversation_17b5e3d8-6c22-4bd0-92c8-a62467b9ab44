import { useState, useCallback, useEffect } from 'react'
import SearchBar, { SearchBarProps } from './SearchBar'
import { DashboardIcons } from './icons/DashboardIcons'

export interface SearchContainerProps extends Omit<SearchBarProps, 'onSearch' | 'isLoading'> {
  onSearch: (query: string) => void
  isLoading?: boolean
  title?: string
  showTitle?: boolean
  showRefreshButton?: boolean
  onRefresh?: () => void
  showCreateButton?: boolean
  createButtonText?: string
  onCreateClick?: () => void
  searchHintsLoader?: () => Promise<string[]>
  filterComponent?: React.ReactNode
  searchTipsContent?: React.ReactNode
  className?: string
  maxSearchHistory?: number
  initialValue?: string
}

function SearchContainer({
  onSearch,
  isLoading = false,
  title = 'Search',
  showTitle = false,
  showRefreshButton = true,
  onRefresh,
  showCreateButton = false,
  createButtonText = 'Create New',
  onCreateClick,
  searchHintsLoader,
  filterComponent,
  searchTipsContent,
  className = '',
  maxSearchHistory = 5,
  initialValue = '',
  ...searchBarProps
}: SearchContainerProps): React.JSX.Element {
  const [searchHints, setSearchHints] = useState<string[]>([])
  const [loadingHints, setLoadingHints] = useState(false)

  // Load search hints if a loader is provided
  const loadSearchHints = useCallback(async () => {
    if (searchHintsLoader) {
      try {
        setLoadingHints(true)
        const hints = await searchHintsLoader()
        setSearchHints(hints)
      } catch (error) {
        console.error('Error loading search hints:', error)
      } finally {
        setLoadingHints(false)
      }
    }
  }, [searchHintsLoader])

  useEffect(() => {
    loadSearchHints()
  }, [loadSearchHints])

  // Handle search with optional debounce
  const handleSearch = useCallback((query: string) => {
    onSearch(query)
  }, [onSearch])

  // Handle refresh button click
  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh()
    }
    loadSearchHints()
  }, [onRefresh, loadSearchHints])

  return (
    <div className={`w-full ${className}`}>
      <div className="flex items-center justify-between gap-2 mb-4">
        {/* Left side - Title */}
        {showTitle && (
          <h2 className="text-xl font-bold text-white shrink-0">{title}</h2>
        )}
        
        {/* Center - Search bar with inline buttons */}
        <div className="flex items-center gap-2 w-full">
          <div className="relative flex-grow">
            <SearchBar
              onSearch={handleSearch}
              isLoading={isLoading || loadingHints}
              searchHints={searchHints}
              maxWidth=""
              maxSearchHistory={maxSearchHistory}
              initialValue={initialValue}
              className="w-full"
              {...searchBarProps}
            />
          </div>
          
          {/* Inline action buttons */}
          {showRefreshButton && (
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-2 rounded-lg 
                transition-colors flex items-center gap-1 disabled:opacity-50 shrink-0 h-10"
              aria-label="Refresh search results"
            >
              <DashboardIcons.Refresh className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">{isLoading ? 'Refreshing...' : 'Refresh'}</span>
            </button>
          )}
          
          {showCreateButton && onCreateClick && (
            <button
              onClick={onCreateClick}
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 
                text-white px-3 py-2 rounded-lg shadow-md shadow-orange-500/20 
                transition-all duration-200 flex items-center gap-2 shrink-0 h-10 whitespace-nowrap"
              aria-label={createButtonText}
            >
              <DashboardIcons.Add className="w-4 h-4" />
              <span>{createButtonText}</span>
            </button>
          )}
          
          {/* Search Tips Info Button */}
          {searchTipsContent && (
            <div className="relative shrink-0 group">
              <button 
                className="w-8 h-8 rounded-full bg-stone-700 hover:bg-stone-600 text-stone-300 
                  flex items-center justify-center transition-colors"
                aria-label="Search tips"
              >
                <span className="text-sm font-semibold">?</span>
              </button>
              
              {/* Tooltip with search tips */}
              <div className="absolute z-50 right-0 mt-2 w-64 p-3 bg-stone-800 
                rounded-lg shadow-xl border border-stone-700 opacity-0 invisible 
                group-hover:opacity-100 group-hover:visible transition-all duration-200 
                text-sm text-stone-300">
                <div className="absolute -top-2 right-2 w-3 h-3 bg-stone-800 border-t border-l 
                  border-stone-700 transform rotate-45"></div>
                <div className="space-y-2">
                  {searchTipsContent}
                  <p className="pt-1 border-t border-stone-700 text-xs">
                    <kbd className="px-2 py-0.5 bg-stone-700 rounded">Alt+↓</kbd> Show search history
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Filter component if provided */}
      {filterComponent && (
        <div className="mb-4">
          {filterComponent}
        </div>
      )}
    </div>
  )
}

export default SearchContainer 