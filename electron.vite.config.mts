import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react-swc'
import { loadEnv } from 'vite'
import { visualizer } from 'rollup-plugin-visualizer'
import fs from 'fs'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isProd = mode === 'production'
  const isAnalyze = process.env.ELECTRON_BUILDER_ANALYZE === 'true'
  const isOptimize = process.env.ELECTRON_VITE_OPTIMIZE === 'true'

  // Before building, ensure env-key.js file exists and copy it to main directory
  const utilsKeyPath = resolve(__dirname, 'src/utils/env-key.js')
  const mainKeyPath = resolve(__dirname, 'src/main/env-key.js')

  // Make sure we have the key file before building
  if (isProd && fs.existsSync(utilsKeyPath)) {
    // If it doesn't exist in main directory, copy it
    if (!fs.existsSync(mainKeyPath)) {
      try {
        console.log('Copying encryption key to main directory for bundling...')
        fs.copyFileSync(utilsKeyPath, mainKeyPath)
        console.log('✅ Encryption key file copied successfully')
      } catch (err) {
        console.error('❌ Failed to copy encryption key file:', err)
      }
    }

    // Verify the key file has content
    try {
      const keyContent = fs.readFileSync(mainKeyPath, 'utf8')
      if (!keyContent.includes('ENV_ENCRYPTION_KEY')) {
        console.error('❌ Key file exists but does not contain the expected key')
      } else {
        console.log('✅ Key file verified to contain encryption key')
      }
    } catch (err) {
      console.error('❌ Failed to read key file for verification:', err)
    }
  }

  // Function to replace HTML placeholders with environment variables
  const htmlPlugin = () => {
    return {
      name: 'html-transform',
      transformIndexHtml(html) {
        // Replace WhatsApp API URL placeholders with environment values
        return html.replace(/%WHATSAPP_API_URL%/g, env.WHATSAPP_API_URL || 'https://studio.gunlicence.co.za');
      }
    }
  }

  return {
    main: {
      plugins: [
        externalizeDepsPlugin(),
        isAnalyze && visualizer({ filename: 'dist/stats-main.html', gzipSize: true, brotliSize: true })
      ].filter(Boolean),
      build: {
        outDir: 'dist/main',
        minify: isProd ? 'terser' : false,
        sourcemap: !isProd,
        terserOptions: isProd ? {
          compress: {
            drop_console: false, // Keep console logs for debugging encryption issues
            drop_debugger: true,
            pure_funcs: ['console.debug'],
            passes: isOptimize ? 3 : 2,
            booleans_as_integers: isOptimize,
            toplevel: isOptimize,
            unsafe: isOptimize,
            unsafe_comps: isOptimize,
            unsafe_math: isOptimize,
            unsafe_methods: isOptimize,
            unsafe_proto: isOptimize,
            unsafe_regexp: isOptimize,
            unsafe_undefined: isOptimize
          },
          mangle: {
            toplevel: isOptimize
          },
          output: {
            comments: false
          }
        } : undefined,
        rollupOptions: {
          input: {
            index: resolve(__dirname, 'src/main/index.ts'),
            // No longer needed as we're using a JS file directly
            // 'env-key': resolve(__dirname, 'src/main/env-key.ts')
          },
          external: ['dotenv', 'crypto', 'electron', 'path', 'fs'],
          output: {
            format: 'cjs',
            compact: isProd,
            minifyInternalExports: isProd
          }
        }
      },
      resolve: {
        alias: {
          '@': resolve('src')
        }
      },
      define: {
        'process.env.NODE_ENV': JSON.stringify(mode)
      }
    },
    preload: {
      plugins: [
        externalizeDepsPlugin(),
        isAnalyze && visualizer({ filename: 'dist/stats-preload.html', gzipSize: true, brotliSize: true })
      ].filter(Boolean),
      build: {
        outDir: 'dist/preload',
        minify: isProd ? 'terser' : false,
        sourcemap: !isProd,
        terserOptions: isProd ? {
          compress: {
            drop_console: true,
            drop_debugger: true,
            passes: isOptimize ? 3 : 2,
            booleans_as_integers: isOptimize,
            toplevel: isOptimize,
            unsafe: isOptimize,
            unsafe_comps: isOptimize,
            unsafe_math: isOptimize,
            unsafe_methods: isOptimize,
            unsafe_proto: isOptimize,
            unsafe_regexp: isOptimize,
            unsafe_undefined: isOptimize
          },
          mangle: {
            toplevel: isOptimize
          },
          output: {
            comments: false
          }
        } : undefined,
        rollupOptions: {
          external: ['path', 'fs', 'crypto', 'electron'],
          output: {
            format: 'cjs',
            compact: isProd,
            minifyInternalExports: isProd
          }
        }
      }
    },
    renderer: {
      build: {
        outDir: 'dist/renderer',
        minify: isProd ? 'terser' : false,
        sourcemap: !isProd,
        chunkSizeWarningLimit: 1000,
        cssMinify: isProd,
        cssCodeSplit: true,
        terserOptions: isProd ? {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info', 'console.debug'],
            passes: isOptimize ? 3 : 2,
            booleans_as_integers: isOptimize,
            toplevel: isOptimize,
            unsafe: isOptimize,
            unsafe_comps: isOptimize,
            unsafe_math: isOptimize,
            unsafe_methods: isOptimize,
            unsafe_proto: isOptimize,
            unsafe_regexp: isOptimize,
            unsafe_undefined: isOptimize
          },
          mangle: {
            toplevel: isOptimize
          },
          output: {
            comments: false
          },
          safari10: false
        } : undefined,
        rollupOptions: {
          output: {
            compact: isProd,
            minifyInternalExports: isProd,
            manualChunks: {
              vendor: ['react', 'react-dom', 'react-router-dom'],
              ui: ['lucide-react'],
              supabase: ['@supabase/supabase-js'],
              docx: ['docxtemplater', 'pizzip'],
              utils: ['lodash'],
              // Add new chunks for better code splitting
              forms: [], // Removed react-hook-form and yup as they're not used in the project
              encryption: [] // Removed crypto-js as it's not installed
            }
          }
        }
      },
      resolve: {
        alias: {
          '@renderer': resolve('src/renderer/src')
        }
      },
      input: {
        index: resolve(__dirname, 'src/renderer/index.html')
      },
      css: {
        postcss: resolve(__dirname, 'postcss.config.cjs')
      },
      plugins: [
        react(),
        htmlPlugin(), // Add custom HTML transform plugin
        isAnalyze && visualizer({ filename: 'dist/stats-renderer.html', gzipSize: true, brotliSize: true })
      ].filter(Boolean),
      define: {
        'process.env.NODE_ENV': JSON.stringify(mode),
        'process.platform': JSON.stringify('win32'),
        // Make environment variables available to the renderer process
        'process.env.WHATSAPP_API_URL': JSON.stringify(env.WHATSAPP_API_URL || '')
      },
      optimizeDeps: {
        include: ['react', 'react-dom', 'react-router-dom'],
        exclude: ['electron']
      }
    }
  }
})
