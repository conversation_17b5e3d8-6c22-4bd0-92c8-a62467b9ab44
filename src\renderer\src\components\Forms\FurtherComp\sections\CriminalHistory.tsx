import React from 'react'
import { FormField, FormSection, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

const CriminalHistory: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  // Helper function to create radio group with template field updates
  const createRadioGroup = (
    name: string,
    yesField: keyof typeof formData,
    noField: keyof typeof formData,
    templateYesField: keyof typeof formData,
    templateNoField: keyof typeof formData,
    label: string
  ) => (
    <div className="bg-stone-800 rounded-lg p-4 mb-3">
      <RadioGroup
        name={name}
        value={formData[yesField] ? 'yes' : 'no'}
        onChange={(value) => {
          const isYes = value === 'yes'
          updateFormData({
            [yesField]: isYes,
            [noField]: !isYes,
            // Set the corresponding placeholder fields
            [templateYesField]: isYes,
            [templateNoField]: !isYes
          })
        }}
        options={[
          { value: 'yes', label: 'Yes' },
          { value: 'no', label: 'No' }
        ]}
      />
      <p className="text-sm text-stone-400 mt-2">{label}</p>
    </div>
  )

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Criminal History</h3>

      <FormSection title="Offense History" subtitle="Information about past offenses">
        {createRadioGroup(
          'offenseHistory',
          'offenseYes',
          'offenseNo',
          'h5a',
          'h5b',
          'Have you been found guilty of an offense for which you\'ve been sentenced to imprisonment without the option of a fine?'
        )}

        {formData.offenseYes && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              label="Name of Police Station"
              name="policeStation"
              value={formData.policeStation || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h51 field for the template
                updateFormData({ h51: e.target.value });
              }}
              placeholder="Enter police station name"
            />

            <FormField
              label="Case Number"
              name="caseNumber"
              value={formData.caseNumber || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h52 field for the template
                updateFormData({ h52: e.target.value });
              }}
              placeholder="Enter case number"
            />

            <FormField
              label="Charge/Offense"
              name="charge"
              value={formData.charge || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h53 field for the template
                updateFormData({ h53: e.target.value });
              }}
              placeholder="Enter charge/offense"
              type="textarea"
              rows={2}
            />

            <FormField
              label="Outcome/Verdict"
              name="outcomeVerdict"
              value={formData.outcomeVerdict || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h54 field for the template
                updateFormData({ h54: e.target.value });
              }}
              placeholder="Enter outcome/verdict"
              type="textarea"
              rows={2}
            />
          </div>
        )}
      </FormSection>

      <FormSection title="Pending Cases" subtitle="Information about pending court cases">
        {createRadioGroup(
          'pendingCase',
          'pendingCaseYes',
          'pendingCaseNo',
          'h6a',
          'h6b',
          'Do you have a case pending before court for an offense related to violence, dishonesty, recklessness, or instability?'
        )}

        {formData.pendingCaseYes && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              label="Name of Police Station"
              name="pendingCasePoliceStation"
              value={formData.pendingCasePoliceStation || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h61 field for the template
                updateFormData({ h61: e.target.value });
              }}
              placeholder="Enter police station name"
            />

            <FormField
              label="Case Number"
              name="pendingCaseCaseNumber"
              value={formData.pendingCaseCaseNumber || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h62 field for the template
                updateFormData({ h62: e.target.value });
              }}
              placeholder="Enter case number"
            />

            <FormField
              label="Nature of Offense"
              name="pendingCaseOffence"
              value={formData.pendingCaseOffence || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h6a3 field for the template
                updateFormData({ h6a3: e.target.value });
              }}
              placeholder="Describe the nature of the offense"
              type="textarea"
              rows={2}
            />
          </div>
        )}
      </FormSection>

      <FormSection title="Lost/Stolen Firearms" subtitle="Information about lost or stolen firearms">
        {createRadioGroup(
          'lostStolenFirearms',
          'lostStolenYes',
          'lostStolenNo',
          'h7a',
          'h7b',
          'Have any of your Firearms been lost or stolen?'
        )}

        {formData.lostStolenYes && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              label="Name of Police Station"
              name="lostStolenPoliceStation"
              value={formData.lostStolenPoliceStation || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h71 field for the template
                updateFormData({ h71: e.target.value });
              }}
              placeholder="Enter police station name"
            />

            <FormField
              label="Case Number"
              name="lostStolenCaseNumber"
              value={formData.lostStolenCaseNumber || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h72 field for the template
                updateFormData({ h72: e.target.value });
              }}
              placeholder="Enter case number"
            />

            <FormField
              label="Circumstances"
              name="lostStolenCircumstances"
              value={formData.lostStolenCircumstances || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h73 field for the template
                updateFormData({ h73: e.target.value });
              }}
              placeholder="Describe the circumstances"
              type="textarea"
              rows={2}
            />

            <FormField
              label="Details of Firearm"
              name="lostStolenFirearmDetails"
              value={formData.lostStolenFirearmDetails || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h74 field for the template
                updateFormData({ h74: e.target.value });
              }}
              placeholder="Enter firearm details"
              type="textarea"
              rows={2}
            />
          </div>
        )}
      </FormSection>

      <FormSection title="Investigation of Negligence" subtitle="Information about negligence investigations">
        {createRadioGroup(
          'investigation',
          'investigationYes',
          'investigationNo',
          'h8a',
          'h8b',
          'Was a case of negligence opened and investigated regarding the stolen/lost firearm?'
        )}

        {formData.investigationYes && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              label="Name of Police Station"
              name="investigationPoliceStation"
              value={formData.investigationPoliceStation || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h81 field for the template
                updateFormData({ h81: e.target.value });
              }}
              placeholder="Enter police station name"
            />

            <FormField
              label="Case Number"
              name="investigationCaseNumber"
              value={formData.investigationCaseNumber || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h82 field for the template
                updateFormData({ h82: e.target.value });
              }}
              placeholder="Enter case number"
            />

            <FormField
              label="Charge"
              name="investigationCharge"
              value={formData.investigationCharge || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h83 field for the template
                updateFormData({ h83: e.target.value });
              }}
              placeholder="Enter charge"
              type="textarea"
              rows={2}
            />

            <FormField
              label="Outcome/Verdict"
              name="investigationOutcome"
              value={formData.investigationOutcome || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h84 field for the template
                updateFormData({ h84: e.target.value });
              }}
              placeholder="Enter outcome/verdict"
              type="textarea"
              rows={2}
            />
          </div>
        )}
      </FormSection>

      <FormSection title="Declared Unfit to Possess a Firearm" subtitle="Information about unfitness declarations">
        {createRadioGroup(
          'declaredUnfit',
          'declaredUnfitYes',
          'declaredUnfitNo',
          'h9a',
          'h9b',
          'Have you ever been declared unfit to possess a firearm?'
        )}

        {formData.declaredUnfitYes && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              label="Name of Police Station"
              name="declaredUnfitPoliceStation"
              value={formData.declaredUnfitPoliceStation || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h91 field for the template
                updateFormData({ h91: e.target.value });
              }}
              placeholder="Enter police station name"
            />

            <FormField
              label="Case Number"
              name="declaredUnfitCaseNumber"
              value={formData.declaredUnfitCaseNumber || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h92 field for the template
                updateFormData({ h92: e.target.value });
              }}
              placeholder="Enter case number"
            />

            <FormField
              label="Charge"
              name="declaredUnfitCharge"
              value={formData.declaredUnfitCharge || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h93 field for the template
                updateFormData({ h93: e.target.value });
              }}
              placeholder="Enter charge"
              type="textarea"
              rows={2}
            />

            <FormField
              label="Date from which unfit"
              name="declaredUnfitDate"
              value={formData.declaredUnfitDate || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h94 field for the template
                updateFormData({ h94: e.target.value });
              }}
              placeholder="YYYY-MM-DD"
              type="date"
            />

            <FormField
              label="Period of Unfitness"
              name="declaredUnfitPeriod"
              value={formData.declaredUnfitPeriod || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h95 field for the template
                updateFormData({ h95: e.target.value });
              }}
              placeholder="Enter period of unfitness"
            />
          </div>
        )}
      </FormSection>

      <FormSection title="Firearm Confiscation" subtitle="Information about confiscated firearms">
        {createRadioGroup(
          'confiscated',
          'confiscatedYes',
          'confiscatedNo',
          'h10a',
          'h10b',
          'Has a Firearm Ever been confiscated?'
        )}

        {formData.confiscatedYes && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              label="Name of Police Station"
              name="confiscatedPoliceStation"
              value={formData.confiscatedPoliceStation || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h101 field for the template
                updateFormData({ h101: e.target.value });
              }}
              placeholder="Enter police station name"
            />

            <FormField
              label="Case Number"
              name="confiscatedCaseNumber"
              value={formData.confiscatedCaseNumber || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h102 field for the template
                updateFormData({ h102: e.target.value });
              }}
              placeholder="Enter case number"
            />

            <FormField
              label="Circumstances"
              name="confiscatedCircumstances"
              value={formData.confiscatedCircumstances || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h103 field for the template
                updateFormData({ h103: e.target.value });
              }}
              placeholder="Describe the circumstances"
              type="textarea"
              rows={2}
            />

            <FormField
              label="Outcome/Verdict"
              name="confiscatedOutcome"
              value={formData.confiscatedOutcome || ''}
              onChange={(e) => {
                handleChange(e);
                // Also update the h104 field for the template
                updateFormData({ h104: e.target.value });
              }}
              placeholder="Enter outcome/verdict"
              type="textarea"
              rows={2}
            />
          </div>
        )}
      </FormSection>
    </div>
  )
}

export default CriminalHistory
