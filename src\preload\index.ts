/* eslint-disable @typescript-eslint/no-unused-vars */
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import * as path from 'path'
import * as fs from 'fs'

// Define the electron API interface
interface IElectronAPI {
  onLoadingStateChange: (callback: (loading: boolean) => void) => () => void;
  showOpenDialog: (options: Electron.OpenDialogOptions) => Promise<Electron.OpenDialogReturnValue>;
  showSaveDialog: (options: Electron.SaveDialogOptions) => Promise<Electron.SaveDialogReturnValue>;
  saveFile: (filePath: string, data: ArrayBuffer) => Promise<{success: boolean, filePath?: string, error?: string}>;
  path: typeof path;
  fs: typeof fs;
  getCredentials: () => Promise<{
    supabaseUrl: string;
    supabaseAnonKey: string;
    supabaseServiceKey: string;
    whatsappApiUrl: string;
    whatsappApiToken: string;
  }>;
  getAppVersion: () => Promise<{ version: string }>;
  windowMinimize: () => void;
  windowMaximize: () => void;
  windowClose: () => void;
  setCompactWindowSize: () => void;
  setFullWindowSize: () => void;
  // Logs API methods
  getApplicationLogs: (options: {
    filter?: {
      level?: string;
      source?: string;
      startDate?: string;
      endDate?: string;
      searchTerm?: string;
    };
    page?: number;
    pageSize?: number;
  }) => Promise<{
    success: boolean;
    logs: any[];
    total: number;
    sources?: string[];
    error?: string;
  }>;
  // WhatsApp API methods
  whatsapp: {
    getQrCode: (sessionId: string) => Promise<any>;
    checkStatus: (sessionId: string) => Promise<any>;
    restartSession: (sessionId: string) => Promise<any>;
    sendMessage: (sessionId: string, phone: string, message: string) => Promise<any>;
    checkAlive: () => Promise<any>;
    startSession: (sessionId: string) => Promise<any>;
    getAllSessions: () => Promise<any>;
    terminateSession: (sessionId: string) => Promise<any>;
    // Real-time connection methods
    connectRealtime: (sessionId: string) => Promise<any>;
    disconnectRealtime: (sessionId: string) => Promise<any>;
    getRealtimeStatus: (sessionId: string) => Promise<any>;
    onSessionConnected: (callback: (data: any) => void) => () => void;
    onSessionDisconnected: (callback: (data: any) => void) => () => void;
    onSessionUpdate: (callback: (data: any) => void) => () => void;
    onSessionError: (callback: (data: any) => void) => () => void;
    onSessionReconnectFailed: (callback: (data: any) => void) => () => void;
  };
}

// Define the saveFile function once to ensure consistency
const saveFileFunction = (filePath: string, data: ArrayBuffer) => {
  console.log(`saveFile called with path: ${filePath}, data size: ${data.byteLength} bytes`);
  return ipcRenderer.invoke('save-file', filePath, data);
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  onLoadingStateChange: (callback: (loading: boolean) => void): (() => void) => {
    ipcRenderer.on('loading-state', (_event, isLoading) => callback(isLoading))
    return () => {
      ipcRenderer.removeAllListeners('loading-state')
    }
  },
  showOpenDialog: (options: Electron.OpenDialogOptions) =>
    ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options: Electron.SaveDialogOptions) =>
    ipcRenderer.invoke('show-save-dialog', options),
  saveFile: saveFileFunction,
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  path: require('path'),
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  fs: require('fs'),
  getCredentials: () => ipcRenderer.invoke('get-credentials'),
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  // Window control methods
  windowMinimize: () => ipcRenderer.send('window-minimize'),
  windowMaximize: () => ipcRenderer.send('window-maximize'),
  windowClose: () => ipcRenderer.send('window-close'),
  // Window size control methods
  setCompactWindowSize: () => ipcRenderer.send('set-compact-size'),
  setFullWindowSize: () => ipcRenderer.send('set-full-size'),
  // Logs API methods
  getApplicationLogs: (options) => ipcRenderer.invoke('get-application-logs', options),
  // WhatsApp API methods
  whatsapp: {
    getQrCode: (sessionId: string) => ipcRenderer.invoke('whatsapp-get-qr-code', sessionId),
    checkStatus: (sessionId: string) => ipcRenderer.invoke('whatsapp-check-status', sessionId),
    restartSession: (sessionId: string) => ipcRenderer.invoke('whatsapp-restart-session', sessionId),
    sendMessage: (sessionId: string, phone: string, message: string) =>
      ipcRenderer.invoke('whatsapp-send-message', sessionId, phone, message),
    checkAlive: () => ipcRenderer.invoke('whatsapp-check-alive'),
    startSession: (sessionId: string) => ipcRenderer.invoke('whatsapp-start-session', sessionId),
    getAllSessions: () => ipcRenderer.invoke('whatsapp-get-all-sessions'),
    terminateSession: (sessionId: string) => ipcRenderer.invoke('whatsapp-terminate-session', sessionId),
    // Real-time connection methods
    connectRealtime: (sessionId: string) => ipcRenderer.invoke('whatsapp-connect-realtime', sessionId),
    disconnectRealtime: (sessionId: string) => ipcRenderer.invoke('whatsapp-disconnect-realtime', sessionId),
    getRealtimeStatus: (sessionId: string) => ipcRenderer.invoke('whatsapp-get-realtime-status', sessionId),
    // Event listeners for real-time updates
    onSessionConnected: (callback: (data: any) => void) => {
      const listener = (_event: any, data: any) => callback(data);
      ipcRenderer.on('whatsapp-session-connected', listener);
      return () => {
        ipcRenderer.removeListener('whatsapp-session-connected', listener);
      };
    },
    onSessionDisconnected: (callback: (data: any) => void) => {
      const listener = (_event: any, data: any) => callback(data);
      ipcRenderer.on('whatsapp-session-disconnected', listener);
      return () => {
        ipcRenderer.removeListener('whatsapp-session-disconnected', listener);
      };
    },
    onSessionUpdate: (callback: (data: any) => void) => {
      const listener = (_event: any, data: any) => callback(data);
      ipcRenderer.on('whatsapp-session-update', listener);
      return () => {
        ipcRenderer.removeListener('whatsapp-session-update', listener);
      };
    },
    onSessionError: (callback: (data: any) => void) => {
      const listener = (_event: any, data: any) => callback(data);
      ipcRenderer.on('whatsapp-session-error', listener);
      return () => {
        ipcRenderer.removeListener('whatsapp-session-error', listener);
      };
    },
    onSessionReconnectFailed: (callback: (data: any) => void) => {
      const listener = (_event: any, data: any) => callback(data);
      ipcRenderer.on('whatsapp-session-reconnect-failed', listener);
      return () => {
        ipcRenderer.removeListener('whatsapp-session-reconnect-failed', listener);
      };
    }
  }
} as IElectronAPI)

// Set up and expose electron ipcRenderer for update handling
const setupElectronBridge = async () => {
  try {
    const appVersionInfo = await ipcRenderer.invoke('get-app-version');

    contextBridge.exposeInMainWorld('electron', {
      ipcRenderer: {
        on: (
          channel:
            | 'update-message'
            | 'update-data'
            | 'update-available'
            | 'update-not-available'
            | 'update-downloaded'
            | 'update-error'
            | 'update-status'
            | 'update-channel-changed',
          func: (...args: unknown[]) => void
        ): void => {
          ipcRenderer.on(channel, (_event, ...args) => func(...args))
        },
        send: (
          channel: 'confirm-download' | 'confirm-install' | 'update-data' | 'check-for-updates' | 'user-logged-in' | 'user-logged-out' | 'change-update-channel',
          data?: unknown
        ): void => {
          console.log(`ipcRenderer.send called with channel: ${channel}`, data ? data : '');
          if (channel === 'user-logged-out') {
            console.log('user-logged-out event detected in preload script');
            console.trace('Stack trace for user-logged-out event:');
          }
          if (data) {
            ipcRenderer.send(channel, data)
          } else {
            ipcRenderer.send(channel)
          }
        },
        invoke: (
          channel: string,
          ...args: unknown[]
        ): Promise<unknown> => {
          return ipcRenderer.invoke(channel, ...args)
        },
        showSaveDialog: (options: Electron.SaveDialogOptions) =>
          ipcRenderer.invoke('show-save-dialog', options),
        saveFile: saveFileFunction,
        removeListener: (
          channel:
            | 'update-message'
            | 'update-data'
            | 'update-available'
            | 'update-not-available'
            | 'update-downloaded'
            | 'update-error'
            | 'update-status'
            | 'update-channel-changed',
          func: (...args: unknown[]) => void
        ): void => {
          ipcRenderer.removeListener(channel, (_event, ...args) => func(...args))
        }
      },
      process: {
        versions: process.versions
      },
      appVersion: appVersionInfo
    })

    console.log('Electron bridge initialized successfully with version info:', appVersionInfo);
  } catch (error) {
    console.error('Failed to initialize electron bridge:', error);

    // Fallback without version info
    contextBridge.exposeInMainWorld('electron', {
      ipcRenderer: {
        on: (channel: string, func: (...args: unknown[]) => void): void => {
          ipcRenderer.on(channel, (_event, ...args) => func(...args))
        },
        send: (channel: string, data?: unknown): void => {
          console.log(`ipcRenderer.send called with channel: ${channel}`, data ? data : '');
          if (channel === 'user-logged-out') {
            console.log('user-logged-out event detected in preload script (fallback)');
            console.trace('Stack trace for user-logged-out event:');
          }
          if (data) {
            ipcRenderer.send(channel, data)
          } else {
            ipcRenderer.send(channel)
          }
        },
        invoke: (channel: string, ...args: unknown[]): Promise<unknown> => {
          return ipcRenderer.invoke(channel, ...args)
        },
        showSaveDialog: (options: Electron.SaveDialogOptions) =>
          ipcRenderer.invoke('show-save-dialog', options),
        saveFile: saveFileFunction,
        removeListener: (channel: string, func: (...args: unknown[]) => void): void => {
          ipcRenderer.removeListener(channel, (_event, ...args) => func(...args))
        }
      },
      process: {
        versions: process.versions
      },
      appVersion: { version: '0.0.0' }
    })
  }
}

// Initialize the electron bridge
setupElectronBridge();

// Log the exposed APIs for debugging
setTimeout(() => {
  console.log('Exposed electronAPI methods:', Object.keys(window.electronAPI || {}));
  console.log('Exposed electron.ipcRenderer methods:', Object.keys(window.electron?.ipcRenderer || {}));

  // Check if saveFile is properly exposed
  if (window.electronAPI?.saveFile) {
    console.log('saveFile is properly exposed in electronAPI');
  } else {
    console.error('saveFile is NOT exposed in electronAPI');
  }

  if (window.electron?.ipcRenderer?.saveFile) {
    console.log('saveFile is properly exposed in electron.ipcRenderer');
  } else {
    console.error('saveFile is NOT exposed in electron.ipcRenderer');
  }
}, 1000);
