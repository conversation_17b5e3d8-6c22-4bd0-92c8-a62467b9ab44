import { useState, useEffect, useCallback } from 'react'
import { getOrInitSupabase } from '../lib/supabase'

export const useDatabaseStatus = () => {
  // Initialize with null to indicate we don't know the status yet
  const [isDatabaseOnline, setIsDatabaseOnline] = useState<boolean | null>(null)
  // Start with isChecking true to indicate we're checking on initial load
  const [isChecking, setIsChecking] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const checkDatabaseConnection = useCallback(async () => {
    setIsChecking(true)
    setError(null)

    try {
      const supabase = await getOrInitSupabase()

      // Make a simple query to check if the database is online
      // We're just checking the connection, so we limit to 1 row
      const { error } = await supabase
        .from('clients')
        .select('id', { count: 'exact', head: true })
        .limit(1)

      if (error) {
        console.error('Database connection check failed:', error)
        setIsDatabaseOnline(false)
        setError(error.message)
      } else {
        setIsDatabaseOnline(true)
      }
    } catch (err) {
      console.error('Error checking database connection:', err)
      setIsDatabaseOnline(false)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsChecking(false)
    }
  }, [])

  // Check database connection on initial load
  useEffect(() => {
    checkDatabaseConnection()

    // Set up an interval to check the connection periodically (every 15 seconds)
    const intervalId = setInterval(() => {
      checkDatabaseConnection()
    }, 15000)

    return () => clearInterval(intervalId)
  }, [checkDatabaseConnection])

  return {
    isDatabaseOnline,
    isChecking,
    error,
    checkDatabaseConnection
  }
}
