import React from 'react'
import { LoanPayment } from '../../../types'
import { DashboardIcons } from '../../icons/DashboardIcons'
import { formatCurrency } from '../../../utils/formatters'

interface LoanCardPaymentScheduleProps {
  paymentMonths: Date[]
  daysActive: number
  getMonthStatus: (month: Date) => string
  getPaymentForMonth: (month: Date) => LoanPayment | undefined
  getMonthlyPaymentAmount: () => number
  getCurrentMonthPaymentStatus: () => {
    isPaidInFull: boolean
  }
  formatDate: (date: string) => string
}

const LoanCardPaymentSchedule: React.FC<LoanCardPaymentScheduleProps> = ({
  paymentMonths,
  daysActive,
  getMonthStatus,
  getPaymentForMonth,
  getMonthlyPaymentAmount,
  getCurrentMonthPaymentStatus,
  formatDate
}) => {
  return (
    <div className="mb-4 bg-stone-800/50 p-3 rounded-lg border border-stone-700/50">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-semibold text-white flex items-center gap-1.5">
          <DashboardIcons.Payment className="w-3.5 h-3.5 text-orange-400" />
          <span>Payment Schedule</span>
        </h3>
        <div className="flex items-center gap-2 px-3 py-1 bg-stone-700/80 rounded-lg">
          <span className="text-stone-300">Days active:</span>
          <span className="font-semibold text-orange-400 text-base">{daysActive}</span>
        </div>
      </div>

      <div className="flex flex-wrap gap-1.5">
        {paymentMonths.map((month, index) => {
          const status = getMonthStatus(month)
          const payment = getPaymentForMonth(month)
          const monthName = month.toLocaleString('default', { month: 'short' })

          // Determine the status color based on our system
          let colorClass = ''
          let bgClass = ''
          let borderClass = ''

          const startDate = new Date()
          startDate.setDate(1)
          startDate.setMonth(startDate.getMonth() - 1)
          
          const isStartMonth = status === 'start'

          const now = new Date()
          const isCurrentMonth =
            month.getMonth() === now.getMonth() && month.getFullYear() === now.getFullYear()

          if (status === 'paid') {
            // Fully paid month
            colorClass = 'text-white'
            bgClass = 'bg-green-500'
            borderClass = 'border-green-600'
          } else if (isStartMonth) {
            // Grace period - always green
            colorClass = 'text-white'
            bgClass = 'bg-green-600/70'
            borderClass = 'border-green-700'
          } else if (status === 'missed') {
            // Missed payment
            colorClass = 'text-white'
            bgClass = 'bg-red-500'
            borderClass = 'border-red-600'
          } else if (isCurrentMonth) {
            // Current month - check if it's paid in full
            const monthStatus = getCurrentMonthPaymentStatus()
            if (monthStatus.isPaidInFull) {
              // Show green if current month is paid in full
              colorClass = 'text-white'
              bgClass = 'bg-green-500'
              borderClass = 'border-green-600'
            } else {
              // Show orange for current month not fully paid
              colorClass = 'text-white'
              bgClass = 'bg-orange-500'
              borderClass = 'border-orange-600'
            }
          } else if (payment && payment.amount < getMonthlyPaymentAmount()) {
            // Partial payment - show yellow/orange
            colorClass = 'text-white'
            bgClass = 'bg-yellow-500'
            borderClass = 'border-yellow-600'
          } else if (status === 'future') {
            // Future months
            colorClass = 'text-stone-300'
            bgClass = 'bg-stone-700'
            borderClass = 'border-stone-600'
          } else {
            // Default
            colorClass = 'text-stone-400'
            bgClass = 'bg-stone-800'
            borderClass = 'border-stone-700'
          }

          // Create tooltip content
          let tooltipContent = `${month.toLocaleString('default', { month: 'long' })} ${month.getFullYear()}`

          if (payment) {
            tooltipContent += `\nPaid: ${formatCurrency(payment.amount)}`
            if (payment.penalties_paid > 0) {
              tooltipContent += `\nPenalties: ${formatCurrency(payment.penalties_paid)}`
            }
            tooltipContent += `\nDate: ${formatDate(payment.payment_date)}`
            if (payment.method) {
              tooltipContent += `\nMethod: ${payment.method.replace('_', ' ')}`
            }
          } else if (isStartMonth) {
            tooltipContent += '\nGrace period - no payment due'
          } else if (status === 'missed') {
            tooltipContent += '\nMissed payment'
            tooltipContent += `\nAmount Due: ${formatCurrency(getMonthlyPaymentAmount())}`
            tooltipContent += '\nThis amount is included in your next payment'
          } else if (isCurrentMonth) {
            tooltipContent += '\nCurrent month'
          } else {
            tooltipContent += '\nUpcoming'
          }

          return (
            <div
              key={index}
              className={`rounded-md px-2 py-1 text-xs font-medium border ${colorClass} ${bgClass} ${borderClass} transition-all duration-200 hover:shadow-md cursor-default flex items-center justify-center min-w-[36px]`}
              title={tooltipContent}
            >
              {monthName}
              {payment && <span className="ml-1 w-1.5 h-1.5 bg-white rounded-full"></span>}
            </div>
          )
        })}
      </div>

      {/* Simplified Legend */}
      <div className="flex flex-wrap gap-2 mt-3 pt-2 border-t border-stone-700/50 text-xs">
        <div className="flex items-center">
          <div className="w-2.5 h-2.5 rounded-full bg-green-500 mr-1.5"></div>
          <span className="text-stone-300">Paid</span>
        </div>
        <div className="flex items-center">
          <div className="w-2.5 h-2.5 rounded-full bg-red-500 mr-1.5"></div>
          <span className="text-stone-300">Missed</span>
        </div>
        <div className="flex items-center">
          <div className="w-2.5 h-2.5 rounded-full bg-orange-500 mr-1.5"></div>
          <span className="text-stone-300">Current</span>
        </div>
        <div className="flex items-center">
          <div className="w-2.5 h-2.5 rounded-full bg-stone-700 mr-1.5"></div>
          <span className="text-stone-300">Upcoming</span>
        </div>
      </div>
    </div>
  )
}

export default React.memo(LoanCardPaymentSchedule)
