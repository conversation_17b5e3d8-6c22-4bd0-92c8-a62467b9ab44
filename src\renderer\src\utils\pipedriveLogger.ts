import { getOrInitSupabase } from '../lib/supabase';
import { v4 as uuidv4 } from 'uuid';

// Define the entity types
export type PipedriveEntityType = 'pipeline' | 'stage' | 'deal' | 'document';

// Define the action types
export type PipedriveActionType =
  | 'create'
  | 'update'
  | 'delete'
  | 'move'
  | 'upload'
  | 'download';

// Define the log entry interface
export interface PipedriveLogEntry {
  id: string;
  action_type: PipedriveActionType;
  entity_type: PipedriveEntityType;
  entity_id: string;
  user_id?: string;
  user_name?: string;
  client_id?: string;
  client_name?: string;
  details?: any;
  status?: 'success' | 'error' | 'warning';
  related_entities?: {
    type: PipedriveEntityType;
    id: string;
    name?: string;
  }[];
  created_at: string;
}

/**
 * Log a Pipedrive action to the database
 *
 * @param actionType The type of action performed
 * @param entityType The type of entity the action was performed on
 * @param entityId The ID of the entity
 * @param options Additional options for the log entry
 * @returns Promise that resolves when the log is created
 */
export const logPipedriveAction = async (
  actionType: PipedriveActionType,
  entityType: PipedriveEntityType,
  entityId: string,
  options?: {
    details?: any;
    userId?: string;
    userName?: string;
    clientId?: string;
    clientName?: string;
    status?: 'success' | 'error' | 'warning';
    relatedEntities?: Array<{
      type: PipedriveEntityType;
      id: string;
      name?: string;
    }>;
  }
): Promise<void> => {
  try {
    const supabase = await getOrInitSupabase();

    // Verify authentication status first
    const { data: authData, error: authError } = await supabase.auth.getSession();
    if (authError || !authData.session) {
      console.error('Authentication required for logging Pipedrive actions:', authError);
      // Try to refresh the session
      const { error: refreshError } = await supabase.auth.refreshSession();
      if (refreshError) {
        console.error('Failed to refresh authentication session:', refreshError);
        return; // Exit early if we can't authenticate
      }
    }

    // Get current user information if not provided
    let userId = options?.userId;
    let userName = options?.userName;

    if (!userId) {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) {
        console.error('Failed to get current user:', userError);
        return; // Exit early if we can't get the user
      }

      if (user) {
        userId = user.id;
        // Try to get user profile information from the clients table
        try {
          const { data: clientProfile, error: profileError } = await supabase
            .from('clients')
            .select('first_name, last_name')
            .eq('auth_user_id', user.id)
            .single();

          if (profileError) {
            console.warn('Could not fetch client profile, using email instead:', profileError);
            userName = user.email || 'Unknown User';
          } else if (clientProfile) {
            userName = `${clientProfile.first_name} ${clientProfile.last_name}`.trim();
          } else {
            userName = user.email || 'Unknown User';
          }
        } catch (profileError) {
          console.warn('Error fetching client profile:', profileError);
          userName = user.email || 'Unknown User';
        }
      }
    }

    // Get client information if client ID is provided but name is not
    let clientName = options?.clientName;
    if (options?.clientId && !clientName) {
      try {
        const { data: client, error: clientError } = await supabase
          .from('clients')
          .select('first_name, last_name')
          .eq('id', options.clientId)
          .single();

        if (clientError) {
          console.warn('Could not fetch client information:', clientError);
        } else if (client) {
          clientName = `${client.first_name} ${client.last_name}`.trim();
        }
      } catch (clientError) {
        console.warn('Error fetching client information:', clientError);
      }
    }

    // Create the log entry
    const { error: insertError } = await supabase
      .from('pipedrive_logs')
      .insert({
        id: uuidv4(),
        action_type: actionType,
        entity_type: entityType,
        entity_id: entityId,
        user_id: userId,
        user_name: userName,
        client_id: options?.clientId,
        client_name: clientName,
        status: options?.status || 'success',
        details: options?.details ? JSON.stringify(options.details) : null,
        related_entities: options?.relatedEntities ? JSON.stringify(options.relatedEntities) : null
      });

    if (insertError) {
      console.error('Failed to insert Pipedrive log entry:', insertError);
      return;
    }

    // Logging is handled by the database entry
  } catch (error) {
    // Don't let logging errors affect the main application flow
    console.error('Failed to log Pipedrive action:', error);
  }
};

/**
 * Fetch Pipedrive logs from the database
 *
 * @param filter Filter options for the logs
 * @param page Page number for pagination
 * @param pageSize Number of logs per page
 * @returns Promise that resolves with the logs and total count
 */
export const fetchPipedriveLogs = async (
  filter: {
    actionType?: PipedriveActionType;
    entityType?: PipedriveEntityType;
    entityId?: string;
    userId?: string;
    clientId?: string;
    status?: 'success' | 'error' | 'warning';
    startDate?: string;
    endDate?: string;
    searchTerm?: string;
  } = {},
  page = 1,
  pageSize = 50
): Promise<{
  logs: PipedriveLogEntry[];
  total: number;
  userOptions: Array<{ id: string; name: string }>;
  clientOptions: Array<{ id: string; name: string }>;
  statusOptions: string[];
}> => {
  try {
    const supabase = await getOrInitSupabase();

    // Build the query
    let query = supabase
      .from('pipedrive_logs')
      .select('*', { count: 'exact' });

    // Apply filters
    if (filter.actionType) {
      query = query.eq('action_type', filter.actionType);
    }

    if (filter.entityType) {
      query = query.eq('entity_type', filter.entityType);
    }

    if (filter.entityId) {
      query = query.eq('entity_id', filter.entityId);
    }

    if (filter.userId) {
      query = query.eq('user_id', filter.userId);
    }

    if (filter.clientId) {
      query = query.eq('client_id', filter.clientId);
    }

    if (filter.status) {
      query = query.eq('status', filter.status);
    }

    if (filter.startDate) {
      query = query.gte('created_at', filter.startDate);
    }

    if (filter.endDate) {
      query = query.lte('created_at', filter.endDate);
    }

    if (filter.searchTerm) {
      // Search in multiple fields
      query = query.or(`details.ilike.%${filter.searchTerm}%,user_name.ilike.%${filter.searchTerm}%,client_name.ilike.%${filter.searchTerm}%`);
    }

    // Apply pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    // Execute the query
    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    // Get unique users for filtering
    const { data: userData } = await supabase
      .from('pipedrive_logs')
      .select('user_id, user_name')
      .not('user_id', 'is', null)
      .order('user_name', { ascending: true });

    const userOptions = userData
      ? Array.from(new Map(userData
          .filter(item => item.user_id && item.user_name)
          .map(item => [item.user_id, { id: item.user_id, name: item.user_name }])
        ).values())
      : [];

    // Get unique clients for filtering
    const { data: clientData } = await supabase
      .from('pipedrive_logs')
      .select('client_id, client_name')
      .not('client_id', 'is', null)
      .order('client_name', { ascending: true });

    const clientOptions = clientData
      ? Array.from(new Map(clientData
          .filter(item => item.client_id && item.client_name)
          .map(item => [item.client_id, { id: item.client_id, name: item.client_name }])
        ).values())
      : [];

    // Get unique statuses for filtering
    const { data: statusData } = await supabase
      .from('pipedrive_logs')
      .select('status')
      .not('status', 'is', null);

    const statusOptions = statusData
      ? Array.from(new Set(statusData.map(item => item.status)))
      : [];

    return {
      logs: data as PipedriveLogEntry[],
      total: count || 0,
      userOptions,
      clientOptions,
      statusOptions
    };
  } catch (error) {
    console.error('Failed to fetch Pipedrive logs:', error);
    return {
      logs: [],
      total: 0,
      userOptions: [],
      clientOptions: [],
      statusOptions: []
    };
  }
};
