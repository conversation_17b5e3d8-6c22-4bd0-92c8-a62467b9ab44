import { createClient, SupabaseClient } from '@supabase/supabase-js'

// Global configuration for Supabase clients
const STORAGE_KEY = 'flm-system-client-auth'

// Track both the client instances and initialization promises
let supabaseInstance: SupabaseClient | null = null
let serviceRoleInstance: SupabaseClient | null = null
let initializationPromise: Promise<SupabaseClient> | null = null
let serviceRolePromise: Promise<SupabaseClient> | null = null

const getSupabaseCredentials = async (): Promise<{
  supabaseUrl: string
  supabaseAnonKey: string
  supabaseServiceKey?: string
}> => {
  try {
    // Get credentials from the Electron main process
    if (window.electronAPI) {
      const credentials = await window.electronAPI.getCredentials()
      if (credentials) {
        return {
          supabaseUrl: credentials.supabaseUrl,
          supabaseAnonKey: credentials.supabaseAnonKey,
          supabaseServiceKey: credentials.supabaseServiceKey
        }
      }
    }

    throw new Error('No credentials available')
  } catch (error) {
    console.error('Failed to get credentials:', error)
    throw new Error('Database authentication failed')
  }
}

/**
 * Initialize the Supabase client singleton or return the existing instance.
 * Uses a Promise cache to prevent race conditions during initialization.
 */
export const initializeSupabase = async (): Promise<SupabaseClient> => {
  // If client already exists, return it immediately
  if (supabaseInstance) return supabaseInstance

  // If there's an ongoing initialization, return that promise to prevent concurrent init
  if (initializationPromise) return initializationPromise

  // Create a new initialization promise
  initializationPromise = (async () => {
    try {
      const { supabaseUrl, supabaseAnonKey } = await getSupabaseCredentials()

      if (!supabaseUrl || !supabaseAnonKey) {
        throw new Error('Database credentials required')
      }

      // Create the client with explicit storage options to prevent duplicate GoTrueClient instances
      supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
        auth: {
          storageKey: STORAGE_KEY,
          persistSession: true,
          autoRefreshToken: true
        }
      })

      return supabaseInstance
    } catch (error) {
      // Clear the promise on error so future calls can try again
      initializationPromise = null
      throw error
    }
  })()

  return initializationPromise
}

/**
 * Get the existing Supabase client instance or throw if not initialized.
 * For components that expect the instance to already exist.
 */
export const getSupabase = (): SupabaseClient => {
  if (!supabaseInstance) {
    throw new Error('Database not initialized. Call initializeSupabase() first.')
  }
  return supabaseInstance
}

/**
 * Safe getter that initializes the client if needed.
 * Use this instead of separate initializeSupabase() + getSupabase() calls.
 */
export const getOrInitSupabase = async (): Promise<SupabaseClient> => {
  if (supabaseInstance) return supabaseInstance
  return initializeSupabase()
}

/**
 * Create a service role client for admin operations.
 * Uses a singleton pattern to prevent multiple instances.
 */
export const createServiceRoleClient = async (): Promise<SupabaseClient> => {
  // If service role client already exists, return it immediately
  if (serviceRoleInstance) return serviceRoleInstance

  // If there's an ongoing initialization, return that promise to prevent concurrent init
  if (serviceRolePromise) return serviceRolePromise

  // Create a new initialization promise
  serviceRolePromise = (async () => {
    try {
      const { supabaseUrl, supabaseServiceKey } = await getSupabaseCredentials()

      if (!supabaseServiceKey) {
        throw new Error('Service role key not available')
      }

      // Create the client with explicit storage options to prevent duplicate GoTrueClient instances
      // Use a completely different storage key for service role client
      const SERVICE_ROLE_STORAGE_KEY = `${STORAGE_KEY}-service-role`

      serviceRoleInstance = createClient(supabaseUrl, supabaseServiceKey, {
        auth: {
          storageKey: SERVICE_ROLE_STORAGE_KEY,
          autoRefreshToken: false,
          persistSession: false
        }
      })

      return serviceRoleInstance
    } catch (error) {
      // Clear the promise on error so future calls can try again
      serviceRolePromise = null
      throw error
    }
  })()

  return serviceRolePromise
}

/**
 * Check if the Supabase database is online and accessible
 * @returns {Promise<boolean>} True if database is online, false otherwise
 */
export const isDatabaseOnline = async (): Promise<boolean> => {
  try {
    // Initialize Supabase if not already initialized
    const supabase = await getOrInitSupabase()

    // Try to make a simple query to check connectivity
    // Using a simple table query that should always be accessible
    const { error } = await supabase.from('clients').select('id', { count: 'exact', head: true }).limit(1)

    // If there's no error, the database is online
    return !error
  } catch (error) {
    console.error('Database connectivity check failed:', error)
    return false
  }
}

/**
 * Clear all Supabase instances and promises.
 * Call this when logging out or when you need to reset the Supabase state.
 */
export const clearSupabaseInstances = (): void => {
  supabaseInstance = null
  serviceRoleInstance = null
  initializationPromise = null
  serviceRolePromise = null
  console.log('Cleared all Supabase instances')
}
