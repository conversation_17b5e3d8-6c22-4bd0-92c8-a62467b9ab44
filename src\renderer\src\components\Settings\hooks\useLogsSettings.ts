import { useState, useEffect, useCallback } from 'react'
import { getOrInitSupabase } from '../../../lib/supabase'

// Define log entry type
export interface LogEntry {
  id: string
  timestamp: string
  level: string
  message: string
  details?: string
  source?: string
  user_id?: string
}

// Define audit log entry type
export interface AuditLogEntry {
  id: string
  timestamp: string
  action: string
  table_name: string
  record_id: string
  user_id?: string
  old_data?: any
  new_data?: any
  ip_address?: string
  user_agent?: string
  client_info?: {
    first_name?: string
    last_name?: string
    email?: string
  }
}

// Define log filter options
export interface LogFilter {
  level?: string
  source?: string
  startDate?: string
  endDate?: string
  searchTerm?: string
}

// Define audit log filter options
export interface AuditLogFilter {
  action?: string
  table_name?: string
  user_id?: string
  startDate?: string
  endDate?: string
  searchTerm?: string
}

// Define log type
export type LogType = 'application' | 'audit' | 'payment' | 'credit' | 'storage' | 'pipedrive'

// Define Pipedrive log entry type
export interface PipedriveLogEntry {
  id: string
  action_type: string
  entity_type: string
  entity_id: string
  user_id?: string
  user_name?: string
  client_id?: string
  client_name?: string
  details?: any
  status?: 'success' | 'error' | 'warning'
  related_entities?: Array<{
    type: string
    id: string
    name?: string
  }>
  created_at: string
}

// Define Pipedrive log filter options
export interface PipedriveLogFilter {
  actionType?: string
  entityType?: string
  userId?: string
  clientId?: string
  status?: string
  startDate?: string
  endDate?: string
  searchTerm?: string
}

export const useLogsSettings = () => {
  // Common state
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState<number>(1)
  const [pageSize, setPageSize] = useState<number>(50)
  const [activeLogType, setActiveLogType] = useState<LogType>('application')

  // Logs state based on active type
  const [logs, setLogs] = useState<LogEntry[] | AuditLogEntry[] | any[]>([])
  const [filter, setFilter] = useState<LogFilter | AuditLogFilter | any>({})
  const [logSources, setLogSources] = useState<string[]>([])
  const [logLevels] = useState<string[]>(['info', 'error', 'warn', 'debug'])
  const [totalLogs, setTotalLogs] = useState<number>(0)

  // Available tables for audit logs
  const [auditLogTables, setAuditLogTables] = useState<string[]>([])

  // Available actions for audit logs
  const [auditLogActions, setAuditLogActions] = useState<string[]>(['insert', 'update', 'delete'])

  // Available transaction types
  const [transactionTypes, setTransactionTypes] = useState<string[]>([])

  // Pipedrive log states
  const [pipedriveActionTypes, setPipedriveActionTypes] = useState<string[]>([
    'create', 'update', 'delete', 'move', 'upload', 'download'
  ])
  const [pipedriveEntityTypes, setPipedriveEntityTypes] = useState<string[]>([
    'pipeline', 'stage', 'deal', 'document'
  ])
  const [pipedriveUsers, setPipedriveUsers] = useState<Array<{ id: string, name: string }>>([])
  const [pipedriveClients, setPipedriveClients] = useState<Array<{ id: string, name: string }>>([])
  const [pipedriveStatuses, setPipedriveStatuses] = useState<string[]>(['success', 'error', 'warning'])

  // Function to fetch application logs from electron-log files
  const fetchElectronLogs = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Use IPC to request logs from the main process
      const electron = window.electron as any
      if (electron?.ipcRenderer?.invoke) {
        const result = await electron.ipcRenderer.invoke('get-application-logs', {
          filter,
          page,
          pageSize
        })

        if (result.success) {
          setLogs(result.logs)
          setTotalLogs(result.total)
          setLogSources(result.sources || [])
        } else {
          setError(result.error || 'Failed to fetch logs')
        }
      } else {
        setError('IPC renderer not available')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error fetching logs')
    } finally {
      setIsLoading(false)
    }
  }, [filter, page, pageSize])



  // Function to fetch audit logs from the database
  const fetchAuditLogs = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const supabase = await getOrInitSupabase()

      // Build query based on filters
      let query = supabase
        .from('audit_logs')
        .select('*', { count: 'exact' })

      // Apply filters
      if (filter.action) {
        query = query.eq('operation', filter.action)
      }

      if (filter.table_name) {
        query = query.eq('table_name', filter.table_name)
      }

      if (filter.user_id) {
        query = query.eq('changed_by', filter.user_id)
      }

      if (filter.startDate) {
        query = query.gte('change_time', filter.startDate)
      }

      if (filter.endDate) {
        query = query.lte('change_time', filter.endDate)
      }

      if (filter.searchTerm) {
        // Search in new_data or old_data fields
        query = query.or(`new_data.ilike.%${filter.searchTerm}%,old_data.ilike.%${filter.searchTerm}%`)
      }

      // Apply pagination
      const from = (page - 1) * pageSize
      const to = from + pageSize - 1

      const { data, error: queryError, count } = await query
        .order('change_time', { ascending: false })
        .range(from, to)

      if (queryError) {
        throw queryError
      }

      if (data) {
        // Transform data to match AuditLogEntry interface
        const transformedData = data.map(log => ({
          id: log.id.toString(),
          timestamp: log.change_time,
          action: log.operation,
          table_name: log.table_name,
          record_id: '',
          user_id: log.changed_by,
          old_data: log.old_data,
          new_data: log.new_data,
          role: log.role
        }))

        setLogs(transformedData)
        setTotalLogs(count || 0)

        // Get unique table names and operations
        const tables = [...new Set(data.map(log => log.table_name).filter(Boolean))]
        setAuditLogTables(tables as string[])

        const operations = [...new Set(data.map(log => log.operation).filter(Boolean))]
        setAuditLogActions(operations as string[])
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error)
      setError('Failed to fetch audit logs')
    } finally {
      setIsLoading(false)
    }
  }, [filter, page, pageSize])

  // Function to fetch payment history logs
  const fetchPaymentLogs = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const supabase = await getOrInitSupabase()

      // Build query based on filters
      let query = supabase
        .from('payment_history')
        .select('*, loans(client_id, clients(first_name, last_name))', { count: 'exact' })

      // Apply filters
      if (filter.payment_type) {
        query = query.eq('payment_type', filter.payment_type)
      }

      if (filter.payment_status) {
        query = query.eq('payment_status', filter.payment_status)
      }

      if (filter.startDate) {
        query = query.gte('payment_date', filter.startDate)
      }

      if (filter.endDate) {
        query = query.lte('payment_date', filter.endDate)
      }

      if (filter.searchTerm) {
        // Search in transaction_id
        query = query.ilike('transaction_id', `%${filter.searchTerm}%`)
      }

      // Apply pagination
      const from = (page - 1) * pageSize
      const to = from + pageSize - 1

      const { data, error: queryError, count } = await query
        .order('payment_date', { ascending: false })
        .range(from, to)

      if (queryError) {
        throw queryError
      }

      if (data) {
        setLogs(data)
        setTotalLogs(count || 0)

        // Get unique payment types and statuses
        const types = [...new Set(data.map(log => log.payment_type).filter(Boolean))]
        const statuses = [...new Set(data.map(log => log.payment_status).filter(Boolean))]

        setTransactionTypes([...types, ...statuses])
      }
    } catch (error) {
      console.error('Error fetching payment logs:', error)
      setError('Failed to fetch payment logs')
    } finally {
      setIsLoading(false)
    }
  }, [filter, page, pageSize])

  // Function to fetch credit transaction logs
  const fetchCreditLogs = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const supabase = await getOrInitSupabase()

      // Build query based on filters
      let query = supabase
        .from('credit_transactions')
        .select('*, client_credit_wallets(client_id, clients(first_name, last_name))', { count: 'exact' })

      // Apply filters
      if (filter.transaction_type) {
        query = query.eq('transaction_type', filter.transaction_type)
      }

      if (filter.reference_type) {
        query = query.eq('reference_type', filter.reference_type)
      }

      if (filter.startDate) {
        query = query.gte('transaction_date', filter.startDate)
      }

      if (filter.endDate) {
        query = query.lte('transaction_date', filter.endDate)
      }

      if (filter.searchTerm) {
        query = query.ilike('description', `%${filter.searchTerm}%`)
      }

      // Apply pagination
      const from = (page - 1) * pageSize
      const to = from + pageSize - 1

      const { data, error: queryError, count } = await query
        .order('transaction_date', { ascending: false })
        .range(from, to)

      if (queryError) {
        throw queryError
      }

      if (data) {
        setLogs(data)
        setTotalLogs(count || 0)

        // Get unique transaction types and reference types
        const transTypes = [...new Set(data.map(log => log.transaction_type).filter(Boolean))]
        const refTypes = [...new Set(data.map(log => log.reference_type).filter(Boolean))]

        setTransactionTypes([...transTypes, ...refTypes])
      }
    } catch (error) {
      console.error('Error fetching credit transaction logs:', error)
      setError('Failed to fetch credit transaction logs')
    } finally {
      setIsLoading(false)
    }
  }, [filter, page, pageSize])

  // Function to fetch firearm storage transaction logs
  const fetchStorageLogs = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const supabase = await getOrInitSupabase()

      // First check if the table exists
      const { error: countError } = await supabase
        .from('firearm_storage_transactions')
        .select('*', { head: true })

      if (countError) {
        console.error('Error checking storage transactions table:', countError)
        // If there's an error with the table, set empty logs but don't show an error
        setLogs([])
        setTotalLogs(0)
        setIsLoading(false)
        return
      }

      // Build query based on filters
      let query = supabase
        .from('firearm_storage_transactions')
        .select('*, clients(first_name, last_name), firearms(make, model, serial)', { count: 'exact' })

      // Apply filters
      if (filter.startDate) {
        query = query.gte('transaction_date', filter.startDate)
      }

      if (filter.endDate) {
        query = query.lte('transaction_date', filter.endDate)
      }

      if (filter.searchTerm) {
        query = query.ilike('notes', `%${filter.searchTerm}%`)
      }

      // Apply pagination
      const from = (page - 1) * pageSize
      const to = from + pageSize - 1

      const { data, error: queryError, count } = await query
        .order('transaction_date', { ascending: false })
        .range(from, to)

      if (queryError) {
        throw queryError
      }

      // Set logs data
      setLogs(data || [])
      setTotalLogs(count || 0)
    } catch (error) {
      console.error('Error fetching storage transaction logs:', error)
      // Set empty logs but don't show an error if it's just that there's no data
      setLogs([])
      setTotalLogs(0)
    } finally {
      setIsLoading(false)
    }
  }, [filter, page, pageSize])

  // Function to fetch Pipedrive logs
  const fetchPipedriveLogs = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const supabase = await getOrInitSupabase()

      // Import the fetchPipedriveLogs function from the utility
      const { fetchPipedriveLogs } = await import('../../../utils/pipedriveLogger')

      // Use the utility function to fetch logs with all the filters
      const result = await fetchPipedriveLogs(
        {
          actionType: filter.actionType,
          entityType: filter.entityType,
          userId: filter.userId,
          clientId: filter.clientId,
          status: filter.status as any,
          startDate: filter.startDate,
          endDate: filter.endDate,
          searchTerm: filter.searchTerm
        },
        page,
        pageSize
      )

      // Update state with the results
      setLogs(result.logs)
      setTotalLogs(result.total)

      // Update filter options
      if (result.userOptions.length > 0) {
        setPipedriveUsers(result.userOptions)
      }

      if (result.clientOptions.length > 0) {
        setPipedriveClients(result.clientOptions)
      }

      if (result.statusOptions.length > 0) {
        setPipedriveStatuses(result.statusOptions)
      }

      // Get unique action types and entity types from the logs
      const actionTypes = [...new Set(result.logs.map(log => log.action_type).filter(Boolean))]
      const entityTypes = [...new Set(result.logs.map(log => log.entity_type).filter(Boolean))]

      if (actionTypes.length > 0) {
        setPipedriveActionTypes(actionTypes)
      }

      if (entityTypes.length > 0) {
        setPipedriveEntityTypes(entityTypes)
      }
    } catch (error) {
      console.error('Error fetching Pipedrive logs:', error)
      setError('Failed to fetch Pipedrive logs')
    } finally {
      setIsLoading(false)
    }
  }, [filter, page, pageSize])

  // Function to fetch logs based on active log type
  const fetchLogs = useCallback(async () => {
    try {
      switch (activeLogType) {
        case 'application':
          await fetchElectronLogs()
          break
        case 'audit':
          await fetchAuditLogs()
          break
        case 'payment':
          await fetchPaymentLogs()
          break
        case 'credit':
          await fetchCreditLogs()
          break
        case 'storage':
          await fetchStorageLogs()
          break
        case 'pipedrive':
          await fetchPipedriveLogs()
          break
        default:
          await fetchElectronLogs()
      }
    } catch (error) {
      console.error('Error fetching logs:', error)
      setError('Failed to fetch logs')
    }
  }, [activeLogType, fetchElectronLogs, fetchAuditLogs, fetchPaymentLogs, fetchCreditLogs, fetchStorageLogs, fetchPipedriveLogs])

  // Load logs on component mount and when filters or active log type changes
  useEffect(() => {
    fetchLogs()
  }, [fetchLogs, activeLogType])

  // Function to clear filters
  const clearFilters = useCallback(() => {
    setFilter({})
    setPage(1)
  }, [])

  // Function to change log type
  const changeLogType = useCallback((type: LogType) => {
    setActiveLogType(type)
    setFilter({})
    setPage(1)
  }, [])

  // Function to export logs as CSV
  const exportLogs = useCallback(async () => {
    try {
      let csvContent = '';

      // Create different CSV formats based on log type
      switch (activeLogType) {
        case 'application':
          csvContent = [
            // CSV header
            ['Timestamp', 'Level', 'Source', 'Message', 'Details'].join(','),
            // CSV rows
            ...logs.map((log: any) => [
              log.timestamp,
              log.level,
              log.source || '',
              `"${log.message?.replace(/"/g, '""') || ''}"`,
              `"${log.details?.replace(/"/g, '""') || ''}"`
            ].join(','))
          ].join('\n');
          break;

        case 'audit':
          // For audit logs, create a more readable format that shows changes
          csvContent = [
            // CSV header
            ['Timestamp', 'Action', 'Table', 'Role', 'User ID', 'Field', 'Old Value', 'New Value'].join(','),
            // CSV rows with expanded changes
            ...logs.flatMap((log: any) => {
              const oldData = log.old_data || {};
              const newData = log.new_data || {};

              // Get all keys from both objects
              const allKeys = [...new Set([...Object.keys(oldData), ...Object.keys(newData)])].sort();

              // If there are no changes, return a single row with basic info
              if (allKeys.length === 0) {
                return [[
                  log.timestamp,
                  log.action,
                  log.table_name,
                  log.role || '',
                  log.user_id || '',
                  '',
                  '',
                  ''
                ].join(',')];
              }

              // Otherwise, return a row for each changed field
              return allKeys.map(key => {
                const oldValue = oldData[key];
                const newValue = newData[key];

                // Format values for CSV
                const formatValue = (value: any) => {
                  if (value === undefined || value === null) return '';
                  return `"${String(value).replace(/"/g, '""')}"`;
                };

                return [
                  log.timestamp,
                  log.action,
                  log.table_name,
                  log.role || '',
                  log.user_id || '',
                  key,
                  formatValue(oldValue),
                  formatValue(newValue)
                ].join(',');
              });
            })
          ].join('\n');
          break;

        case 'payment':
          csvContent = [
            // CSV header
            ['Date', 'Amount', 'Type', 'Status', 'Transaction ID', 'Client'].join(','),
            // CSV rows
            ...logs.map((log: any) => [
              log.payment_date || log.created_at,
              log.payment_amount,
              log.payment_type || '',
              log.payment_status || '',
              log.transaction_id || '',
              `"${log.loans?.clients?.first_name || ''} ${log.loans?.clients?.last_name || ''}"`
            ].join(','))
          ].join('\n');
          break;

        case 'credit':
          csvContent = [
            // CSV header
            ['Date', 'Amount', 'Type', 'Reference Type', 'Description', 'Client'].join(','),
            // CSV rows
            ...logs.map((log: any) => [
              log.transaction_date || log.created_at,
              log.amount,
              log.transaction_type || '',
              log.reference_type || '',
              `"${(log.description || '').replace(/"/g, '""')}"`,
              `"${log.client_credit_wallets?.clients?.first_name || ''} ${log.client_credit_wallets?.clients?.last_name || ''}"`
            ].join(','))
          ].join('\n');
          break;

        case 'storage':
          csvContent = [
            // CSV header
            ['Date', 'Amount', 'Days Charged', 'Daily Rate', 'Firearm', 'Client', 'Notes'].join(','),
            // CSV rows
            ...logs.map((log: any) => [
              log.transaction_date || log.created_at,
              log.amount,
              log.days_charged || '',
              log.daily_rate || '',
              `"${log.firearms?.make || ''} ${log.firearms?.model || ''} (${log.firearms?.serial || ''})"`,
              `"${log.clients?.first_name || ''} ${log.clients?.last_name || ''}"`,
              `"${(log.notes || '').replace(/"/g, '""')}"`
            ].join(','))
          ].join('\n');
          break;

        case 'pipedrive':
          csvContent = [
            // CSV header
            ['Timestamp', 'Action Type', 'Entity Type', 'Entity ID', 'User ID', 'Details'].join(','),
            // CSV rows
            ...logs.map((log: any) => {
              // Format the details JSON for CSV
              let detailsStr = '';
              try {
                if (log.details) {
                  detailsStr = JSON.stringify(log.details);
                }
              } catch (e) {
                detailsStr = String(log.details || '');
              }

              return [
                log.created_at,
                log.action_type,
                log.entity_type,
                log.entity_id,
                log.user_id || '',
                `"${detailsStr.replace(/"/g, '""')}"`
              ].join(',');
            })
          ].join('\n');
          break;
      }

      // Create blob and download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.setAttribute('href', url)
      link.setAttribute('download', `${activeLogType}-logs-export-${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Error exporting logs:', error)
      setError('Failed to export logs')
    }
  }, [logs, activeLogType])

  return {
    // Log data
    logs,
    isLoading,
    error,
    filter,
    setFilter,
    logSources,
    logLevels,
    totalLogs,
    page,
    setPage,
    pageSize,
    setPageSize,

    // Log type management
    activeLogType,
    changeLogType,

    // Audit log specific data
    auditLogTables,
    auditLogActions,

    // Transaction log specific data
    transactionTypes,

    // Pipedrive log specific data
    pipedriveActionTypes,
    pipedriveEntityTypes,
    pipedriveUsers,
    pipedriveClients,
    pipedriveStatuses,

    // Actions
    clearFilters,
    exportLogs,
    refreshLogs: fetchLogs
  }
}
