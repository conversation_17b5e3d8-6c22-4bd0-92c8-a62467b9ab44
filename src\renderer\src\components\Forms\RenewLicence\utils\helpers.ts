import { RenewLicenceData } from '../../../../types/RenewLicenceData'

/**
 * Format a date string to a more readable format
 * @param dateString Date string in YYYY-MM-DD format
 * @returns Formatted date string in DD Month YYYY format
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-ZA', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  } catch (error) {
    console.error('Error formatting date:', error)
    return dateString
  }
}

/**
 * Validate a date is not in the future
 * @param dateString Date string to validate
 * @returns Boolean indicating if the date is valid (not in the future)
 */
export const isValidPastDate = (dateString: string): boolean => {
  if (!dateString) return false
  
  try {
    const date = new Date(dateString)
    const today = new Date()
    
    // Set today to end of day for comparison
    today.setHours(23, 59, 59, 999)
    
    return date <= today
  } catch (error) {
    console.error('Error validating date:', error)
    return false
  }
}

/**
 * Validate a licence number format
 * @param licenceNumber Licence number to validate
 * @returns Boolean indicating if the licence number is valid
 */
export const isValidLicenceNumber = (licenceNumber: string): boolean => {
  // Basic validation - can be expanded based on specific requirements
  return licenceNumber.length >= 5
}

/**
 * Validate an ID number format
 * @param idNumber ID number to validate
 * @returns Boolean indicating if the ID number is valid
 */
export const isValidIdNumber = (idNumber: string): boolean => {
  // Basic validation for South African ID number (13 digits)
  return /^\d{13}$/.test(idNumber)
}

/**
 * Validate a passport number format
 * @param passportNumber Passport number to validate
 * @returns Boolean indicating if the passport number is valid
 */
export const isValidPassportNumber = (passportNumber: string): boolean => {
  // Basic validation - can be expanded based on specific requirements
  return passportNumber.length >= 5
}

/**
 * Validate an email address format
 * @param email Email address to validate
 * @returns Boolean indicating if the email address is valid
 */
export const isValidEmail = (email: string): boolean => {
  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate a phone number format
 * @param phoneNumber Phone number to validate
 * @returns Boolean indicating if the phone number is valid
 */
export const isValidPhoneNumber = (phoneNumber: string): boolean => {
  // Basic validation for South African phone numbers
  return /^0\d{9}$/.test(phoneNumber.replace(/\s/g, ''))
}

/**
 * Check if a business licence type is selected
 * @param formData The form data
 * @returns Boolean indicating if a business licence type is selected
 */
export const isBusinessLicence = (formData: RenewLicenceData): boolean => {
  return formData.s20 || formData.s20a || formData.s20b
}
