import { ElectronAPI } from '@electron-toolkit/preload'

declare global {
  interface Window {
    electron: {
      ipcRenderer: {
        on: (
          channel:
            | 'update-message'
            | 'update-data'
            | 'update-available'
            | 'update-downloaded'
            | 'update-not-available'
            | 'download-progress'
            | 'update-error'
            | 'update-status'
            | 'update-channel-changed',
          func: (...args: unknown[]) => void
        ) => void
        send: (
          channel: 'confirm-download' | 'confirm-install' | 'update-data' | 'check-for-updates' | 'change-update-channel' | 'user-logged-in' | 'user-logged-out',
          data?: unknown
        ) => void
        invoke: (
          channel: string,
          ...args: unknown[]
        ) => Promise<unknown>
        removeListener: (
          channel:
            | 'update-message'
            | 'update-data'
            | 'update-available'
            | 'update-downloaded'
            | 'update-not-available'
            | 'download-progress'
            | 'update-error'
            | 'update-status'
            | 'update-channel-changed',
          func: (...args: unknown[]) => void
        ) => void
      }
      process: {
        versions: NodeJS.ProcessVersions
      }
      appVersion: string
      path: typeof import('path')
    }
    api: unknown
    electronAPI?: {
      getCredentials(): Promise<{
        supabaseUrl: string
        supabaseAnonKey: string
        supabaseServiceKey: string
      }>
      getAppVersion(): Promise<{ version: string; channel: string }>
      onLoadingStateChange: (callback: (loading: boolean) => void) => () => void
      showOpenDialog: (
        options: Electron.OpenDialogOptions
      ) => Promise<Electron.OpenDialogReturnValue>
      showSaveDialog: (
        options: Electron.SaveDialogOptions
      ) => Promise<Electron.SaveDialogReturnValue>
      path: typeof import('path')
      fs: typeof import('fs')
      windowMinimize: () => void
      windowMaximize: () => void
      windowClose: () => void
    }
    getCredentials: () => Promise<{
      supabaseAnonKey: string
      supabaseUrl: string
      email: string
      password: string
    }>
  }
}

interface ElectronAPI {
  ipcRenderer: {
    on: <T>(channel: string, listener: (event: Electron.IpcRendererEvent, args: T) => void) => void
    off: <T>(channel: string, listener: (event: Electron.IpcRendererEvent, args: T) => void) => void
    send: <T>(channel: string, args?: T) => void
  }
}

export {}
