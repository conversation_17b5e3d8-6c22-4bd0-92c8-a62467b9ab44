export type FirearmStorageType = 'Owner' | 'Private' | 'Dealer'

export interface Firearm {
  id: string
  stock_number: string
  dealer_id_number: string
  full_name: string
  make: string
  model: string
  serial: string
  date_signed_in: string
  date_signed_out: string | null
  amount_due: number
  notes: string | null
  created_at: string
  updated_at: string
  storage_type: FirearmStorageType
  // Virtual fields for UI
  is_assigned?: boolean
  assigned_to?: string
  client_id?: string
  assignment_id?: string // Added to track the assignment ID
  assignment_notes?: string | null
  assigned_date?: string // Added to track when the firearm was assigned
  free_storage_until?: string | null // This is now from the assignment, not the firearm itself
  credit_balance?: number // Added to track credit balance (legacy - will be replaced by wallet_balance)
  wallet_id?: string // Added to track client wallet ID
  wallet_balance?: number // Added to track client wallet balance
  assigned_client_details?: {
    id: string
    first_name: string
    last_name: string
    email: string
    phone: string
    id_number: string
  }
  // Virtual field for payment status
  payment_status?: 'free' | 'paid' | 'due'
  next_payment_date?: string
}

export interface FirearmAssignment {
  id: string
  firearm_id: string
  client_id: string
  assigned_date: string
  return_date: string | null
  notes: string | null
  free_storage_until: string | null
  deposit_amount?: number // Will be repurposed as credit_balance
  credit_balance?: number // New field for tracking credit
  last_charge_date?: string | null // New field for tracking when charges were last calculated
  created_at: string
  updated_at: string
  // Joined fields
  client?: {
    id: string
    first_name: string
    last_name: string
    email: string
    phone: string
    id_number?: string
  }
  firearm?: Firearm
}
