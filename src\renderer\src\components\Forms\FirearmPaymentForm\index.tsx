import React, { useState, useEffect } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { getSupabase } from '../../../lib/supabase'
import { FormField } from '../../FormComponents'
import { DashboardIcons } from '../../icons/DashboardIcons'
import { formatCurrency } from '../../../utils/formatters'
import { ClientCreditWallet } from '../../../types/clientWallet'
import { sendFirearmStoragePaymentNotification } from '../../../utils/whatsAppNotificationService'

interface FirearmPaymentFormProps {
  firearmId: string
  clientId: string
  clientName: string
  assignmentId: string
  currentBalance: number
  storageType?: string
  onClose: () => void
  onSuccess: () => void
}

const FirearmPaymentForm: React.FC<FirearmPaymentFormProps> = ({
  firearmId,
  clientId,
  clientName,
  onClose,
  onSuccess
}) => {
  const [amount, setAmount] = useState<number>(0)
  const [notes, setNotes] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [wallet, setWallet] = useState<ClientCreditWallet | null>(null)

  // Fetch client wallet on component mount
  useEffect(() => {
    const fetchWallet = async () => {
      if (!clientId) return

      try {
        const supabase = getSupabase()
        const { data, error } = await supabase
          .from('client_credit_wallets')
          .select('*')
          .eq('client_id', clientId)
          .single()

        if (error) {
          console.error('Error fetching wallet:', error)
          return
        }

        if (data) {
          setWallet(data)
        }
      } catch (err) {
        console.error('Error in fetchWallet:', err)
      }
    }

    fetchWallet()
  }, [clientId])

  // Helper function to get client phone number
  const getClientPhone = async (clientId: string): Promise<string> => {
    try {
      const supabase = getSupabase()
      const { data, error } = await supabase
        .from('clients')
        .select('phone')
        .eq('id', clientId)
        .single()

      if (error) {
        console.error('Error fetching client phone:', error)
        return ''
      }

      return data?.phone || ''
    } catch (err) {
      console.error('Error in getClientPhone:', err)
      return ''
    }
  }

  // Helper function to get firearm details
  const getFirearmDetails = async (firearmId: string): Promise<{ stockNumber: string, details: string, startDate: string, paymentRates: string }> => {
    try {
      const supabase = getSupabase()
      const { data, error } = await supabase
        .from('firearms')
        .select('stock_number, make, model, serial, date_signed_in')
        .eq('id', firearmId)
        .single()

      if (error) {
        console.error('Error fetching firearm details:', error)
        return { stockNumber: '', details: '', startDate: '', paymentRates: '' }
      }

      const stockNumber = data?.stock_number || ''
      const details = `${data?.make || ''} ${data?.model || ''} (SN: ${data?.serial || ''})`
      const startDate = data?.date_signed_in || new Date().toISOString()
      const paymentRates = 'R225 per month / R7.50 per day'

      return { stockNumber, details, startDate, paymentRates }
    } catch (err) {
      console.error('Error in getFirearmDetails:', err)
      return { stockNumber: '', details: '', startDate: '', paymentRates: '' }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (amount <= 0) {
      setError('Please enter a valid amount')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const supabase = getSupabase()
      const now = new Date().toISOString()

      // If we don't have a wallet yet, we need to create one
      let walletId = wallet?.id

      if (!walletId) {
        // Check if wallet exists
        const { data: existingWallet, error: walletCheckError } = await supabase
          .from('client_credit_wallets')
          .select('id')
          .eq('client_id', clientId)
          .single()

        if (walletCheckError && walletCheckError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error('Error checking for wallet:', walletCheckError)
          throw new Error('Could not check for wallet. Please try again.')
        }

        if (existingWallet) {
          walletId = existingWallet.id
        } else {
          // Create a new wallet
          const newWalletId = uuidv4()
          const { error: createWalletError } = await supabase
            .from('client_credit_wallets')
            .insert({
              id: newWalletId,
              client_id: clientId,
              balance: 0, // Initial balance is 0, will be updated by trigger
              created_at: now,
              updated_at: now
            })

          if (createWalletError) {
            console.error('Error creating wallet:', createWalletError)
            throw new Error('Could not create wallet. Please try again.')
          }

          walletId = newWalletId
        }
      }

      // Create transaction record
      const transactionId = uuidv4()
      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          id: transactionId,
          wallet_id: walletId,
          amount: amount,
          transaction_type: 'payment',
          reference_type: 'firearm',
          reference_id: firearmId,
          description: notes || 'Credit payment',
          transaction_date: now,
          created_at: now,
          updated_at: now
        })

      if (transactionError) {
        console.error('Error creating transaction:', transactionError)
        throw transactionError
      }

      // Create payment record
      const { error: paymentError } = await supabase
        .from('firearm_storage_payments')
        .insert({
          id: uuidv4(),
          firearm_id: firearmId,
          client_id: clientId,
          wallet_id: walletId,
          transaction_id: transactionId,
          payment_date: now,
          amount: amount,
          payment_type: 'credit',
          notes: notes || 'Credit payment',
          created_at: now,
          updated_at: now
        })

      if (paymentError) {
        console.error('Error creating payment record:', paymentError)
        throw paymentError
      }

      // Fetch updated wallet balance
      const { data: updatedWallet, error: fetchError } = await supabase
        .from('client_credit_wallets')
        .select('*')
        .eq('id', walletId)
        .single()

      if (fetchError) {
        console.error('Error fetching updated wallet:', fetchError)
      } else if (updatedWallet) {
        setWallet(updatedWallet)
      }

      // Send WhatsApp notification for firearm storage payment
      try {
        const clientPhone = await getClientPhone(clientId)
        const firearmInfo = await getFirearmDetails(firearmId)

        if (clientPhone) {
          await sendFirearmStoragePaymentNotification(
            clientName,
            clientPhone,
            firearmInfo.stockNumber,
            firearmInfo.details,
            amount,
            firearmInfo.startDate,
            updatedWallet?.balance || 0,
            firearmInfo.paymentRates
          )
        }
      } catch (notificationError) {
        console.error('Error sending WhatsApp notification:', notificationError)
        // Don't throw error - we don't want to fail payment if notification fails
      }

      onSuccess()
    } catch (err: any) {
      console.error('Error adding credit:', err)
      setError(err.message || 'An error occurred while processing payment')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-stone-800 rounded-xl shadow-xl w-full max-w-md overflow-hidden border border-orange-500/30">
        <div className="p-6">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            <DashboardIcons.Payment className="w-5 h-5 text-orange-400" />
            Add Credit for {clientName}
          </h2>

          {error && (
            <div className="mb-4 p-3 bg-red-900/30 border border-red-700 rounded-lg text-red-400">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-4 p-3 bg-stone-700/50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-stone-300">Current Wallet Balance:</span>
                <span className={`font-medium ${(wallet?.balance || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatCurrency(wallet?.balance || 0)}
                </span>
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-stone-300">New Balance:</span>
                <span className={`font-medium ${((wallet?.balance || 0) + amount) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatCurrency((wallet?.balance || 0) + amount)}
                </span>
              </div>
            </div>

            <div className="space-y-4">
              <FormField
                name="amount"
                label="Amount (R)"
                type="number"
                value={amount.toString()}
                onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                min="0"
                step="0.01"
                required
              />

              <FormField
                name="notes"
                label="Notes"
                type="text"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Optional payment notes"
              />

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-md"
                >
                  Cancel
                </button>

                <button
                  type="submit"
                  disabled={loading || amount <= 0}
                  className="px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-600 hover:from-orange-600 hover:to-amber-700
                    text-white rounded-md flex items-center gap-1.5 disabled:opacity-70"
                >
                  {loading ? (
                    <>
                      <DashboardIcons.Spinner className="w-4 h-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <DashboardIcons.Payment className="w-4 h-4" />
                      Add Credit
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default FirearmPaymentForm
