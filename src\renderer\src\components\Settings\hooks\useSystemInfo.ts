import { useCallback } from 'react'
import { safeVersionToString } from '../utils'

export const useSystemInfo = (currentVersion: string) => {
  const getSystemInfo = useCallback(() => {
    return {
      appName: 'Firearm Studio',
      version: safeVersionToString(currentVersion),
      electronVersion: safeVersionToString(window?.electron?.process?.versions?.electron),
      nodeVersion: safeVersionToString(window?.electron?.process?.versions?.node),
      platform: navigator.platform
    }
  }, [currentVersion])

  return {
    getSystemInfo,
    safeVersionToString
  }
}
