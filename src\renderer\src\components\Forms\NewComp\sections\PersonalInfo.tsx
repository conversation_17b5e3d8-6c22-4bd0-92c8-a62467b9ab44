import React from 'react'
import { FormSection, FormField, RadioGroup } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionWithAddressProps } from '../utils/types'
import { extractFromIdNumber, generateInitials } from '../utils/helpers'

/**
 * Personal Information section component
 */
const PersonalInfo: React.FC<SectionWithAddressProps> = ({
  formData,
  updateFormData,
  handleAddressChange,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement

    const updatedData: any = {}

    if (type === 'checkbox') {
      updatedData[name] = (e.target as HTMLInputElement).checked
    } else {
      updatedData[name] = value
    }

    // If firstName is changed, extract initials
    if (name === 'firstName' && value) {
      updatedData.initials = generateInitials(value)
    }

    // If ID number field is changed and has 13 digits, extract all possible information
    if (name === 'idNumber' && value.length === 13) {
      const extractedData = extractFromIdNumber(value)
      if (extractedData) {
        updatedData.birthDate = extractedData.birthDate
        updatedData.age = extractedData.age
        updatedData.sexM = extractedData.sexM
        updatedData.sexF = extractedData.sexF
      }
    }

    updateFormData(updatedData)
  }

  const handleCitizenTypeChange = (value: string) => {
    updateFormData({
      citizenType: value as 'saId' | 'fId' | '',
      saId: value === 'saId',
      fId: value === 'fId'
    })
  }

  const handleMaritalStatusChange = (value: string) => {
    updateFormData({
      maritalStatus: value as 'single' | 'married' | 'divorced' | 'widower' | 'widow' | '',
      singles: value === 'single',
      married: value === 'married',
      divorced: value === 'divorced',
      widower: value === 'widower',
      widow: value === 'widow'
    })
  }

  const citizenTypeOptions = [
    { value: 'saId', label: 'SA Citizen' },
    { value: 'fId', label: 'Foreign' }
  ]

  const maritalStatusOptions = [
    { value: 'single', label: 'Single' },
    { value: 'married', label: 'Married' },
    { value: 'divorced', label: 'Divorced' },
    { value: 'widower', label: 'Widower' },
    { value: 'widow', label: 'Widow' }
  ]

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Personal Information</h3>

      <FormSection title="Basic Information" subtitle="Your personal details">
        <div className="space-y-3">
          {/* First Names and Last Name side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">First Names</label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName || ''}
                  onChange={handleChange}
                  placeholder="Enter first name"
                  required={false}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Last Name</label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName || ''}
                  onChange={handleChange}
                  placeholder="Enter last name"
                  required={false}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          {/* ID Number and Citizenship side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col">
                <label className="block text-sm font-medium text-stone-300 mb-1">ID Number</label>
                <input
                  type="text"
                  name="idNumber"
                  value={formData.idNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. 8001015009087"
                  required={false}
                  pattern="[0-9]{13}"
                  maxLength={13}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col">
                <label className="block text-sm font-medium text-stone-300 mb-1">Citizenship</label>
                <div className="bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 h-[42px] flex items-center">
                  {citizenTypeOptions.map((option) => (
                    <label key={option.value} className="inline-flex items-center mr-4">
                      <input
                        type="radio"
                        name="citizenType"
                        value={option.value}
                        checked={formData.citizenType === option.value}
                        onChange={() => handleCitizenTypeChange(option.value)}
                        className="h-4 w-4 text-orange-500 border-stone-600 focus:ring-orange-500/50"
                      />
                      <span className="ml-2 text-sm text-stone-300">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Note about ID Number auto-calculation */}
          <p className="text-xs text-stone-400 mt-1">
            ID Number auto-calculates age, birth date, and gender (override options below)
          </p>
        </div>

        {/* Fields for initials, age, birth date, and gender that can be overridden */}
        <div className="mt-4 border-t border-stone-700 pt-4 overflow-hidden">
          <div className="flex justify-between items-center mb-2 overflow-x-auto whitespace-nowrap">
            <h4 className="text-sm font-medium text-orange-400 mr-2">Override Auto-Calculated Fields</h4>
            <p className="text-xs text-stone-400 flex-shrink-0">
              Auto-calculated from ID number
            </p>
          </div>

          <div className="flex flex-row gap-3 items-end overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <FormField
                label="Initials"
                name="initials"
                value={formData.initials || ''}
                onChange={handleChange}
                placeholder="e.g. J.D."
                required={false}
              />
            </div>

            <div className="flex-1 min-w-[150px]">
              <FormField
                label="Age"
                name="age"
                value={formData.age || ''}
                onChange={handleChange}
                placeholder="e.g. 35"
                type="number"
                min="18"
                required={false}
              />
            </div>

            <div className="flex-1 min-w-[150px]">
              <FormField
                label="Birth Date"
                name="birthDate"
                value={formData.birthDate || ''}
                onChange={handleChange}
                type="date"
                required={false}
              />
            </div>

            <div className="flex-1 min-w-[150px]">
              <label className="block text-sm font-medium text-stone-300 mb-1">Gender</label>
              <div className="flex space-x-4 bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 h-[42px] items-center">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="gender"
                    checked={formData.sexM || false}
                    onChange={() => updateFormData({ sexM: true, sexF: false })}
                    className="h-4 w-4 text-orange-500 border-stone-600 focus:ring-orange-500/50"
                  />
                  <span className="ml-2 text-sm text-stone-300">Male</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="gender"
                    checked={formData.sexF || false}
                    onChange={() => updateFormData({ sexM: false, sexF: true })}
                    className="h-4 w-4 text-orange-500 border-stone-600 focus:ring-orange-500/50"
                  />
                  <span className="ml-2 text-sm text-stone-300">Female</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </FormSection>

      <FormSection title="Contact Information" subtitle="Your contact details">
        <div className="space-y-3">
          {/* Phone Number and Email Address side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Phone Number</label>
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. ************"
                  required={false}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Email Address</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email || ''}
                  onChange={handleChange}
                  placeholder="e.g. <EMAIL>"
                  required={false}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          {/* Citizenship moved to be with ID Number */}
        </div>
      </FormSection>

      <FormSection title="Physical Address" subtitle="Your residential address">
        <AddressInput
          label="Physical Address"
          value={formData.physicalAddress || ''}
          postalCode={formData.postalCode || ''}
          onChange={(address, postalCode) => {
            handleAddressChange(address, postalCode);
          }}
          placeholder="Enter your physical address"
          isTextarea={false}
          required={false}
          postalCodeRequired={false}
        />
        <p className="text-xs text-stone-400 mt-1 mb-3">
          Enter your full physical address and postal code.
        </p>


        <div className="mt-2">
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">House Type</label>
                <select
                  name="houseType"
                  value={formData.houseType || ''}
                  onChange={handleChange as any}
                  required={false}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                >
                  <option value="">Select house type</option>
                  <option value="Shack">Shack</option>
                  <option value="Flat">Flat</option>
                  <option value="Town House">Town House</option>
                  <option value="Caravan">Caravan</option>
                  <option value="Cottage">Cottage</option>
                  <option value="House">House</option>
                  <option value="Hostel">Hostel</option>
                  <option value="Homeless">Homeless</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </FormSection>

      <FormSection title="Marital Status" subtitle="Your marital status">
        <RadioGroup
          name="maritalStatus"
          value={formData.maritalStatus || ''}
          onChange={handleMaritalStatusChange}
          options={maritalStatusOptions}
          required={false}
        />
      </FormSection>
    </div>
  )
}

export default PersonalInfo
