import type { OpenDialogOptions, OpenDialogReturnValue, SaveDialogOptions, SaveDialogReturnValue } from 'electron'

declare global {
  interface Window {
    electronAPI: {
      onLoadingStateChange: (callback: (loading: boolean) => void) => () => void;
      showOpenDialog: (options: OpenDialogOptions) => Promise<OpenDialogReturnValue>;
      showSaveDialog: (options: SaveDialogOptions) => Promise<SaveDialogReturnValue>;
      path: any;
      fs: any;
      getCredentials: () => Promise<{
        supabaseUrl: string;
        supabaseAnonKey: string;
        supabaseServiceKey: string;
        googleMapsApiKey: string;
      }>;
      windowMinimize: () => void;
      windowMaximize: () => void;
      windowClose: () => void;
      setCompactWindowSize: () => void;
      setFullWindowSize: () => void;
    } | undefined;
    electron: {
      ipcRenderer: {
        on: (
          channel:
            | 'update-message'
            | 'update-data'
            | 'update-available'
            | 'update-not-available'
            | 'update-downloaded'
            | 'update-error'
            | 'update-status',
          func: (...args: unknown[]) => void
        ) => void;
        send: (
          channel: 'confirm-download' | 'confirm-install' | 'update-data' | 'check-for-updates',
          data?: unknown
        ) => void;
        showSaveDialog: (options: SaveDialogOptions) => Promise<SaveDialogReturnValue>;
        removeListener: (
          channel:
            | 'update-message'
            | 'update-data'
            | 'update-available'
            | 'update-not-available'
            | 'update-downloaded'
            | 'update-error'
            | 'update-status',
          func: (...args: unknown[]) => void
        ) => void;
      };
      process: {
        env: {
          NODE_ENV: { versions: Record<string, string>; } | undefined; versions: Record<string, string>; 
} | undefined;
        versions: Record<string, string>;
      };
      appVersion?: string;
    } | undefined;
  }
}

export {} 