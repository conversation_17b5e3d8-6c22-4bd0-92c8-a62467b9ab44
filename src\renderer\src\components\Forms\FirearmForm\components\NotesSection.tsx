import React from 'react';
import { FormField } from '../../../FormComponents';
import { DashboardIcons } from '../../../icons/DashboardIcons';
import { Firearm } from '../../../../types/firearm';

interface NotesSectionProps {
  formData: Partial<Firearm>;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

export const NotesSection: React.FC<NotesSectionProps> = ({
  formData,
  onChange
}) => {
  return (
    <div>
      <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
        <DashboardIcons.Edit className="w-3.5 h-3.5 text-orange-400" />
        Notes
      </h2>
      <FormField
        label="Notes"
        name="notes"
        value={formData.notes || ''}
        onChange={onChange}
        type="textarea"
        rows={1}
        inputClassName="premium-field"
      />
    </div>
  );
};
