import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Action Type section component for SAPS Inspection Report form
 */
const ActionType: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    updateFormData({ [name]: checked })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Action Type</h3>

      <FormSection title="Firearm Action Type" subtitle="Select the action type of the firearm">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="manual"
              name="manual"
              checked={formData.manual || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="manual" className="ml-2 block text-sm text-stone-300">
              Manual
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="semiAutomatic"
              name="semiAutomatic"
              checked={formData.semiAutomatic || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="semiAutomatic" className="ml-2 block text-sm text-stone-300">
              Semi-Automatic
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="automatic"
              name="automatic"
              checked={formData.automatic || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="automatic" className="ml-2 block text-sm text-stone-300">
              Automatic
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="bolt"
              name="bolt"
              checked={formData.bolt || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="bolt" className="ml-2 block text-sm text-stone-300">
              Bolt
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="breakneck"
              name="breakneck"
              checked={formData.breakneck || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="breakneck" className="ml-2 block text-sm text-stone-300">
              Breakneck
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="pump"
              name="pump"
              checked={formData.pump || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="pump" className="ml-2 block text-sm text-stone-300">
              Pump
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="lever"
              name="lever"
              checked={formData.lever || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="lever" className="ml-2 block text-sm text-stone-300">
              Lever
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="cylinder"
              name="cylinder"
              checked={formData.cylinder || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="cylinder" className="ml-2 block text-sm text-stone-300">
              Cylinder
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="fallingBlock"
              name="fallingBlock"
              checked={formData.fallingBlock || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="fallingBlock" className="ml-2 block text-sm text-stone-300">
              Falling Block
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="cappingBreechLoader"
              name="cappingBreechLoader"
              checked={formData.cappingBreechLoader || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="cappingBreechLoader" className="ml-2 block text-sm text-stone-300">
              Capping Breech Loader
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="otherActionTypeChecked"
              name="otherActionTypeChecked"
              checked={formData.otherActionTypeChecked || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="otherActionTypeChecked" className="ml-2 block text-sm text-stone-300">
              Other
            </label>
          </div>
        </div>

        {formData.otherActionTypeChecked && (
          <div className="mt-2">
            <label className="block text-sm font-medium text-stone-300 mb-1">
              Other Action Type Details
            </label>
            <input
              type="text"
              name="otherActionTypeDetails"
              value={formData.otherActionTypeDetails || ''}
              onChange={handleChange}
              placeholder="Specify other action type"
              required={true}
              className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
            />
          </div>
        )}
      </FormSection>

      <FormSection title="Engraved Information" subtitle="Enter details about engravings on the firearm">
        <div className="space-y-3">
          <div className="flex flex-col">
            <label className="block text-sm font-medium text-stone-300 mb-1">Engraved Make</label>
            <input
              type="text"
              name="engravedMake"
              value={formData.engravedMake || ''}
              onChange={handleChange}
              placeholder="e.g. Glock"
              className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
            />
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default ActionType
