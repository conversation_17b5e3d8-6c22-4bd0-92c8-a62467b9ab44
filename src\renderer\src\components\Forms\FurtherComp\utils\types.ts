// types.ts - Type definitions for the Further Competency form

import { FurtherCompetencyData } from '../../../../types/FormData'

// Form validation status
export type ValidationStatus = 'idle' | 'processing' | 'success' | 'error'

// Template loading status
export type TemplateStatus = 'loading' | 'ready' | 'error'

// Section props interface
export interface SectionProps {
  formData: FurtherCompetencyData
  updateFormData: (data: Partial<FurtherCompetencyData>) => void
  className?: string
}

// Form props interface
export interface FurtherCompFormProps {
  onSubmit: (data: FurtherCompetencyData) => void
}
