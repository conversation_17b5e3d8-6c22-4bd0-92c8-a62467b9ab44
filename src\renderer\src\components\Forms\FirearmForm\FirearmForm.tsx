import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { DashboardIcons } from '../../icons/DashboardIcons';
import { Firearm } from '../../../types/firearm';
import { useFirearmForm, useClientSearch } from './hooks';
import {
  IdentificationSection,
  OwnerInfoSection,
  FirearmDetailsSection,
  DatesSection,
  NotesSection
} from './components';

interface FirearmFormProps {
  firearm?: Firearm | null;
  onClose: () => void;
  onSuccess: (firearmId?: string, assignmentId?: string) => void;
  loanId?: string;
  clientId?: string;
  loan?: any;
}

const FirearmForm: React.FC<FirearmFormProps> = (props) => {
  const {
    formData,
    depositAmount,
    loading,
    error,
    selectedClient,
    isEditing,
    handleChange,
    handleCreditChange,
    handleStorageTypeChange,
    handleClientSelect,
    handleClientRemove,
    handleSubmit,
    preventWheelChange
  } = useFirearmForm(props);

  const {
    clients,
    clientSearchTerm,
    isLoadingClients,
    handleClientSearchChange
  } = useClientSearch(error => {
    // This is a callback to set error state from the client search hook
    if (error) console.error(error);
  });

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-black/80 via-stone-900/90 to-stone-800/90 flex items-center justify-center z-50 p-3 overflow-y-auto">
      <div className="bg-stone-800/95 rounded-2xl shadow-2xl border border-orange-400/10 w-full max-w-xl relative max-h-[90vh] backdrop-blur-xl">
        <button
          onClick={props.onClose}
          className="absolute top-2 right-2 text-stone-400 hover:text-white focus:outline-none focus:text-white transition-colors z-10"
          aria-label="Close form"
        >
          <DashboardIcons.Close className="w-4 h-4" />
        </button>

        <div className="p-4 overflow-y-auto max-h-[90vh] flex flex-col gap-3">
          <h1 className="text-xl font-extrabold text-white mb-2 flex items-center gap-2 tracking-tight drop-shadow-lg">
            <span className="inline-flex items-center justify-center bg-gradient-to-tr from-orange-500/80 to-orange-400/70 rounded-full p-1.5 shadow-md">
              <DashboardIcons.Firearm className="w-5 h-5 text-white" />
            </span>
            {isEditing ? 'Edit Firearm' : 'Add Firearm'}
          </h1>

          <form onSubmit={handleSubmit} className="overflow-y-auto">
            {error && (
              <div className="flex items-center gap-1.5 bg-red-600/20 text-red-200 border border-red-500/40 p-1.5 rounded-lg mb-1.5 text-xs animate-fade-in shadow">
                <AlertTriangle className="w-3.5 h-3.5 text-red-300 animate-shake" />
                <span>{error}</span>
              </div>
            )}

            {/* Show client assignment info when adding from loans */}
            {props.loanId && props.clientId && selectedClient && (
              <div className="flex items-center gap-1.5 bg-green-600/20 text-green-200 border border-green-500/40 p-2 rounded-lg mb-3 text-xs animate-fade-in shadow">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-300">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
                <div>
                  <span className="font-medium">This firearm will be automatically:</span>
                  <ul className="list-disc list-inside mt-1 ml-1">
                    <li>Added with Dealer storage type</li>
                    <li>Assigned to client: <span className="font-medium text-white">{selectedClient.first_name} {selectedClient.last_name}</span></li>
                    <li>Given 12 months free storage</li>
                    <li>Linked to the loan (using firearm storage only, no license record)</li>
                  </ul>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 gap-3">
              {/* Form Sections */}
              <IdentificationSection
                formData={formData}
                loanId={props.loanId}
                onStorageTypeChange={handleStorageTypeChange}
                onChange={handleChange}
              />

              <OwnerInfoSection
                formData={formData}
                clientSearchTerm={clientSearchTerm}
                isLoadingClients={isLoadingClients}
                clients={clients}
                selectedClient={selectedClient}
                onClientSearchChange={handleClientSearchChange}
                onClientSelect={handleClientSelect}
                onClientRemove={handleClientRemove}
                onChange={handleChange}
              />

              <FirearmDetailsSection
                formData={formData}
                onChange={handleChange}
              />

              <DatesSection
                formData={formData}
                loanId={props.loanId}
                depositAmount={depositAmount}
                onChange={handleChange}
                onCreditChange={handleCreditChange}
                onWheel={preventWheelChange}
              />

              {/* Notes - Hidden when adding from Loans */}
              {!props.loanId && (
                <NotesSection
                  formData={formData}
                  onChange={handleChange}
                />
              )}

              {/* Form Actions */}
              <div className="flex justify-end gap-2 mt-1">
                <button
                  type="button"
                  onClick={props.onClose}
                  className="px-3 py-1.5 bg-stone-700 hover:bg-stone-600 text-white rounded-lg text-xs font-semibold shadow transition-all duration-150 active:scale-95 focus:ring-2 focus:ring-orange-400/50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-3 py-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-lg flex items-center gap-2 text-xs font-semibold shadow-lg transition-all duration-150 active:scale-95 focus:ring-2 focus:ring-orange-400/50 disabled:opacity-60 disabled:cursor-not-allowed"
                >
                  {loading && <DashboardIcons.Spinner className="w-3.5 h-3.5 animate-spin" />}
                  {isEditing ? 'Update Firearm' : 'Add Firearm'}
                </button>
              </div>
            </div>
          </form>

          {/* Premium input field style override */}
          <style>{`
            .premium-field {
              background-color: rgba(68, 64, 60, 0.8);
              border: 1px solid rgba(251, 146, 60, 0.2);
              border-radius: 0.5rem;
              padding: 0.5rem 0.75rem;
              color: white;
              font-size: 0.875rem;
              box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
              transition-property: all;
              transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
              transition-duration: 150ms;
            }
            .premium-field:focus {
              outline: none;
              --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
              --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) rgba(251, 146, 60, 0.6);
              box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
              border-color: rgb(251, 146, 60);
            }
          `}</style>
        </div>
      </div>
    </div>
  );
};

export default FirearmForm;
