import React, { useMemo, memo } from 'react'
import FirearmCard from './FirearmCard'
import { Firearm } from '../../types/firearm'
import { SkeletonDashboard } from '../SkeletonLoading'
import { useVirtualization } from '../../hooks/useVirtualization'

interface FirearmCardListProps {
  firearms: Firearm[]
  onEditFirearm: (firearm: Firearm) => void
  onDeleteFirearm: (firearmId: string) => void
  onAssignFirearm: (firearm: Firearm) => void
  onFocusToggle: (firearmId: string) => void
  onSignOutFirearm: (firearmId: string) => void
  focusedFirearmId: string | null
  onAddCredit?: (firearm: Firearm) => void // Optional callback for when credit is added
}

// Number of items to load initially and in each batch
const INITIAL_BATCH_SIZE = 5
const BATCH_INCREMENT = 5

const FirearmCardList: React.FC<FirearmCardListProps> = memo(({
  firearms,
  onEditFirearm,
  onDeleteFirearm,
  onAssignFirearm,
  onFocusToggle,
  onSignOutFirearm,
  focusedFirearmId,
  onAddCredit
}) => {
  // If a firearm is focused, only show that one
  const filteredFirearms = useMemo(() => {
    return focusedFirearmId
      ? firearms.filter(firearm => firearm.id === focusedFirearmId)
      : firearms
  }, [firearms, focusedFirearmId])

  // Use the virtualization hook
  const {
    items: visibleFirearms,
    total,
    isLoading: isLoadingMore,
    handleScroll,
    handleLoadMore
  } = useVirtualization<Firearm>(filteredFirearms, INITIAL_BATCH_SIZE, BATCH_INCREMENT)

  return (
    <div className="space-y-2 overflow-y-auto h-full" onScroll={handleScroll}>
      {visibleFirearms.map(firearm => (
        <div key={firearm.id}>
          <FirearmCard
            firearm={firearm}
            onEdit={onEditFirearm}
            onDelete={onDeleteFirearm}
            onAssign={onAssignFirearm}
            onSignOut={onSignOutFirearm}
            onFocusToggle={onFocusToggle}
            isFocused={firearm.id === focusedFirearmId}
            isOtherCardFocused={!!focusedFirearmId && firearm.id !== focusedFirearmId}
            onAddCredit={onAddCredit}
          />
        </div>
      ))}
      {isLoadingMore && (
        <div className="py-2">
          <SkeletonDashboard />
        </div>
      )}
      {total > visibleFirearms.length && !isLoadingMore && (
        <div className="text-center py-2">
          <button
            className="text-orange-500 hover:text-orange-400 text-sm"
            onClick={handleLoadMore}
          >
            Load more firearms ({total - visibleFirearms.length} remaining)
          </button>
        </div>
      )}
    </div>
  )
})

export default FirearmCardList
