import React from 'react';
import { debounce } from 'lodash';

interface AddressInputProps {
  value: string;
  onChange: (address: string, postalCode?: string) => void;
  placeholder?: string;
  isTextarea?: boolean;
  rows?: number;
  required?: boolean;
  className?: string;
  label?: string;
  postalCodeRequired?: boolean;
  postalCode?: string;
}

const AddressInput: React.FC<AddressInputProps> = ({
  value,
  onChange,
  placeholder = 'Enter an address',
  isTextarea = false,
  rows = 3,
  required = false,
  className = '',
  label,
  postalCodeRequired = true,
  postalCode = '',
}) => {
  const [addressValue, setAddressValue] = React.useState<string>(value);
  const [extractedPostalCode, setExtractedPostalCode] = React.useState<string>(postalCode);
  const debouncedUpdate = React.useRef(
    debounce((address: string, postalCode: string) => {
      onChange(address, postalCode);
    }, 300)
  ).current;

  // Handle address input change
  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newAddress = e.target.value;
    setAddressValue(newAddress);
    debouncedUpdate(newAddress, extractedPostalCode);
  };

  // Handle postal code change
  const handlePostalCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow numbers and limit to 4 digits
    const newPostalCode = e.target.value.replace(/\\D/g, '').slice(0, 4);
    setExtractedPostalCode(newPostalCode);
    debouncedUpdate(addressValue, newPostalCode);
  };

  // Match the styling of the other inputs in the app
  const baseClasses = "w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white";
  const focusClasses = "focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none";
  const inputClasses = `${baseClasses} ${focusClasses} ${className}`;

  return (
    <div className="space-y-3">
      {label && (
        <label className="block text-sm font-medium text-stone-300 mb-1">
          {label} {required && <span className="text-orange-500">*</span>}
        </label>
      )}

      <div>
        {isTextarea ? (
          <textarea
            value={addressValue}
            onChange={handleAddressChange}
            placeholder={placeholder}
            rows={rows}
            required={required}
            className={inputClasses}
          />
        ) : (
          <input
            type="text"
            value={addressValue}
            onChange={handleAddressChange}
            placeholder={placeholder}
            required={required}
            className={inputClasses}
          />
        )}
      </div>

      {/* Always show Postal Code field */}
      <div className="mt-2">
        <label className="block text-sm font-medium text-stone-300 mb-1">
          Postal Code {postalCodeRequired && <span className="text-orange-500">*</span>}
        </label>
        <input
          type="text"
          value={extractedPostalCode}
          onChange={handlePostalCodeChange}
          placeholder="4-digit postal code"
          required={postalCodeRequired}
          maxLength={4}
          pattern="[0-9]{4}"
          className={inputClasses}
        />
        <p className="text-xs text-stone-400 mt-1">
          South African postal code (4 digits)
        </p>
      </div>
    </div>
  );
};

// Export a memoized version of the component to prevent unnecessary re-renders
export default React.memo(AddressInput);
