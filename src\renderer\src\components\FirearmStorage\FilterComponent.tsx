import React, { useState, useRef, useEffect } from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'

interface FilterOption {
  value: string
  label: string
}

interface FilterComponentProps {
  activeFilter: string
  setActiveFilter: (filter: any) => void
  options: FilterOption[]
}

const FilterComponent: React.FC<FilterComponentProps> = ({
  activeFilter,
  setActiveFilter,
  options
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Find the active option label
  const activeOptionLabel = options.find(option => option.value === activeFilter)?.label || 'All'

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 bg-stone-800/50 hover:bg-stone-700/50 text-stone-300 hover:text-white px-3 py-2 rounded-lg transition-colors h-10"
      >
        <DashboardIcons.Filter className="w-4 h-4" />
        <span>{activeOptionLabel}</span>
        <DashboardIcons.ChevronDown className="w-4 h-4" />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-stone-800 border border-stone-700 rounded-lg shadow-lg z-10">
          <div className="py-1">
            {options.map((option) => (
              <button
                key={option.value}
                onClick={() => {
                  setActiveFilter(option.value)
                  setIsOpen(false)
                }}
                className={`w-full text-left px-4 py-2 text-sm ${
                  activeFilter === option.value
                    ? 'bg-stone-700 text-white'
                    : 'text-stone-300 hover:bg-stone-700/50 hover:text-white'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default FilterComponent
