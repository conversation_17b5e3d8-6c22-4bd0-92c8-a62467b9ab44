import React from 'react'
import { Loan } from '../../../types'
import { formatCurrency, formatDate } from '../../../utils/formatters'

interface LoanCardDetailsInfoProps {
  loan: Loan
  firearmInfo: any
  assignmentInfo: any
  highlightMatch: (text: string) => JSX.Element | string
  onAddLicense: (clientId: string, loanId: string) => void
}

const LoanCardDetailsInfo: React.FC<LoanCardDetailsInfoProps> = ({
  loan,
  firearmInfo,
  assignmentInfo,
  highlightMatch,
  onAddLicense
}) => {
  return (
    <div className="overflow-hidden">
      <div className="p-3 space-y-4">
        {/* Firearm Details */}
        <div>
          <h3 className="text-sm font-semibold text-white mb-2 flex items-center">
            <span className="inline-block mr-2 w-1 h-4 bg-orange-500 rounded-full" />
            Firearm Details
          </h3>

          {loan.gun_licences ? (
            <div className="grid grid-cols-2 gap-x-3 gap-y-1 text-xs">
              <p>
                <span className="text-orange-400 font-medium">Make & Model:</span>{' '}
                <span className="text-white">
                  {highlightMatch(
                    loan.gun_licences.make
                      ? loan.gun_licences.make.toUpperCase()
                      : 'N/A'
                  )}
                </span>
              </p>
              <p>
                <span className="text-orange-400 font-medium">Serial:</span>{' '}
                <span className="text-white">
                  {highlightMatch(
                    loan.gun_licences.serial_number
                      ? loan.gun_licences.serial_number.toUpperCase()
                      : 'N/A'
                  )}
                </span>
              </p>
              <p>
                <span className="text-orange-400 font-medium">Caliber:</span>{' '}
                <span className="text-white">
                  {highlightMatch(
                    loan.gun_licences.caliber
                      ? loan.gun_licences.caliber.toUpperCase()
                      : 'N/A'
                  )}
                </span>
              </p>
              <p>
                <span className="text-orange-400 font-medium">Stock Code:</span>{' '}
                <span className="text-white">
                  {highlightMatch(
                    loan.gun_licences.stock_code
                      ? loan.gun_licences.stock_code.toUpperCase()
                      : 'N/A'
                  )}
                </span>
              </p>
            </div>
          ) : firearmInfo ? (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-x-3 gap-y-1 text-xs">
                <p>
                  <span className="text-orange-400 font-medium">Make & Model:</span>{' '}
                  <span className="text-white">
                    {highlightMatch(
                      firearmInfo.make
                        ? firearmInfo.make.toUpperCase() + (firearmInfo.model ? ' ' + firearmInfo.model.toUpperCase() : '')
                        : 'N/A'
                    )}
                  </span>
                </p>
                <p>
                  <span className="text-orange-400 font-medium">Serial:</span>{' '}
                  <span className="text-white">
                    {highlightMatch(
                      firearmInfo.serial
                        ? firearmInfo.serial.toUpperCase()
                        : 'N/A'
                    )}
                  </span>
                </p>
                <p>
                  <span className="text-orange-400 font-medium">Stock Number:</span>{' '}
                  <span className="text-white">
                    {highlightMatch(
                      firearmInfo.stock_number
                        ? firearmInfo.stock_number.toUpperCase()
                        : 'N/A'
                    )}
                  </span>
                </p>
                <p>
                  <span className="text-green-400 font-medium">Storage Type:</span>{' '}
                  <span className="text-white">
                    {highlightMatch(
                      firearmInfo.storage_type
                        ? firearmInfo.storage_type.toUpperCase()
                        : 'N/A'
                    )}
                  </span>
                </p>
              </div>

              {/* Assignment Information */}
              {assignmentInfo && (
                <div className="mt-2 bg-stone-800/50 p-2 rounded-md border border-stone-700/50">
                  <h4 className="text-xs font-semibold text-green-400 mb-1.5 flex items-center">
                    <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5"></span>
                    {assignmentInfo.return_date ? 'Was Assigned To' : 'Currently Assigned To'}
                  </h4>
                  <div className="grid grid-cols-2 gap-x-3 gap-y-1 text-xs">
                    {assignmentInfo.clients && (
                      <>
                        <p>
                          <span className="text-stone-400">Client:</span>{' '}
                          <span className="text-white">
                            {highlightMatch(`${assignmentInfo.clients.first_name} ${assignmentInfo.clients.last_name}`.toUpperCase())}
                          </span>
                        </p>
                        <p>
                          <span className="text-stone-400">Assigned:</span>{' '}
                          <span className="text-white">
                            {formatDate(assignmentInfo.assigned_date)}
                          </span>
                        </p>
                        {assignmentInfo.return_date && (
                          <p>
                            <span className="text-stone-400">Returned:</span>{' '}
                            <span className="text-white">
                              {formatDate(assignmentInfo.return_date)}
                            </span>
                          </p>
                        )}
                        {assignmentInfo.notes && (
                          <p className={assignmentInfo.return_date ? "col-span-2" : ""}>
                            <span className="text-stone-400">Notes:</span>{' '}
                            <span className="text-white">
                              {highlightMatch(assignmentInfo.notes)}
                            </span>
                          </p>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center py-2">
              <span className="text-stone-400 text-xs mr-2">
                No firearm information
              </span>
              {/* Only show Add Firearm button if no firearm is assigned through any method */}
              {loan.clients && !loan.license_id && !loan.firearm_id && !loan.assignment_id && !loan.firearm && !loan.assignment && (
                <button
                  onClick={() => onAddLicense(loan.clients!.id, loan.id)}
                  className="text-xs text-orange-500 hover:text-orange-400 transition-colors"
                >
                  Add Firearm
                </button>
              )}
            </div>
          )}
        </div>

        {/* Financial Details */}
        <div>
          <h3 className="text-sm font-semibold text-white mb-2 flex items-center">
            <span className="inline-block mr-2 w-1 h-4 bg-orange-500 rounded-full" />
            Financial Details
          </h3>
          <div className="grid grid-cols-2 gap-x-3 gap-y-1 text-xs">
            <p>
              <span className="text-orange-400 font-medium">Firearm Cost:</span>{' '}
              <span className="text-white font-medium">
                {formatCurrency(loan.weapon_cost)}
              </span>
            </p>
            <p>
              <span className="text-orange-400 font-medium">Deposit Payment:</span>{' '}
              <span className="text-white font-medium">
                {formatCurrency(loan.initial_payment)}
              </span>
            </p>
            <p>
              <span className="text-orange-400 font-medium">Interest Rate:</span>{' '}
              <span className="text-white font-medium">{loan.interest_rate}%</span>
            </p>
            <p>
              <span className="text-orange-400 font-medium">Penalties:</span>{' '}
              <span
                className={`font-medium ${(loan.penalties || 0) > 0 ? 'text-red-400' : 'text-white'}`}
              >
                {formatCurrency(loan.penalties || 0)}
              </span>
            </p>
          </div>
        </div>

        {/* Notes Section */}
        {loan.notes && (
          <div>
            <h3 className="text-sm font-semibold text-white mb-2 flex items-center">
              <span className="inline-block mr-2 w-1 h-4 bg-orange-500 rounded-full" />
              Notes
            </h3>
            <div className="bg-stone-700/30 rounded-lg p-3 text-xs text-white whitespace-pre-wrap max-h-[100px] overflow-y-auto custom-scrollbar">
              {loan.notes}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default React.memo(LoanCardDetailsInfo)
