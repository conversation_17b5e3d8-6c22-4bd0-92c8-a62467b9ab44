import React from 'react'

interface SelectOption {
  value: string
  label: string
}

interface SelectFieldProps {
  label: string
  name: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void
  options: SelectOption[]
  required?: boolean
  placeholder?: string
  className?: string
  error?: string
  disabled?: boolean
}

const SelectField = ({ 
  label, 
  name, 
  value, 
  onChange, 
  options,
  required = false,
  placeholder = 'Select an option',
  className = '',
  error,
  disabled = false
}: SelectFieldProps) => (
  <div className={`space-y-1 ${className}`}>
    <label htmlFor={name} className="block text-sm font-medium text-stone-300">
      {label} {required && <span className="text-orange-500">*</span>}
    </label>
    <select
      id={name}
      name={name}
      value={value || ''}
      onChange={onChange}
      required={required}
      disabled={disabled}
      className={`w-full bg-stone-700/50 border ${
        error ? 'border-red-500' : 'border-stone-600/50'
      } rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none ${
        disabled ? 'opacity-60 cursor-not-allowed' : ''
      }`}
    >
      <option value="">{placeholder}</option>
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
    {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
  </div>
);

export default SelectField; 