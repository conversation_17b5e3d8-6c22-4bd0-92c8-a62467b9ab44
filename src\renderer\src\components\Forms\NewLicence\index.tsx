import React, { useState, useEffect, useMemo } from 'react'
import { NewLicenceData, initialNewLicenceData } from '../../../types/NewLicenceData'
import {
  FormLayout,
  TemplateStatus,
  FormSectionType
} from '../../FormComponents'

// Import test data and toast
import { testPrivateOwnerData, testFirearmDealerData, testCompanyData, testEstateData } from './utils/testData'
import { showToast } from './utils/ToastManager'

// Import section components
import ApplicationType from './sections/ApplicationType'
import PersonalInfo from './sections/PersonalInfo'
import FirearmType from './sections/FirearmType'
import SAP350ADetails from './sections/SAP350ADetails'
import FirearmDetails from './sections/FirearmDetails'
import CriminalHistory from './sections/CriminalHistory'
import CurrentOwner from './sections/CurrentOwner'
import CompetencyCertificate from './sections/CompetencyCertificate'
import AssociationMembership from './sections/AssociationMembership'
import <PERSON>rist<PERSON><PERSON><PERSON> from './sections/JuristicPerson'
import SafeStorage from './sections/SafeStorage'
import FirearmsInPossession from './sections/FirearmsInPossession'

// Import types
import { ValidationStatus, TemplateStatus as TemplateStatusType } from './utils/types'

// Constants
const TEMPLATE_URL =
  'https://app.gunlicence.co.za/storage/v1/object/public/templates/Licence/271_SAPS_Form.docx'
const TEMPLATE_NAME = 'New Licence.docx'

// Types
interface NewLicenceFormProps {
  onSubmit: (data: NewLicenceData) => void
  templateName?: string
}

/**
 * NewLicence - New Firearm Licence Application Form Component
 *
 * A multi-step form for processing new firearm licence applications
 * that passes data to DocScript for document generation.
 */
export default function NewLicence({ onSubmit, templateName }: NewLicenceFormProps): JSX.Element {
  // Form state
  const [formData, setFormData] = useState<NewLicenceData>(initialNewLicenceData)
  const [currentStep, setCurrentStep] = useState<number>(1)

  // Template state
  const [templateStatus, setTemplateStatus] = useState<TemplateStatusType>('loading')
  const [templateError, setTemplateError] = useState<string | null>(null)

  // Submission state
  const [submissionStatus, setSubmissionStatus] = useState<ValidationStatus>('idle')
  const [submissionMessage, setSubmissionMessage] = useState<string | null>(null)

  // Form sections definition
  const sections: FormSectionType[] = useMemo(
    () => {
      // Define sections that are always included
      const alwaysIncludedSections = [
        { id: 'application', title: 'Application Type' },
        { id: 'currentOwner', title: 'Current Owner' }, // Moved to be the second step
        { id: 'firearmType', title: 'Firearm Type' },
        { id: 'sap350a', title: 'SAP 350 (A) DETAILS' },
        { id: 'firearm', title: 'Firearm Details' },
        { id: 'criminal', title: 'Criminal History' },
        { id: 'competency', title: 'Competency Certificate' },
        { id: 'association', title: 'Association Membership' }
      ]

      // Create the base sections array
      let baseSections = [...alwaysIncludedSections]

      // Include Personal Information section only if Current Owner type is NOT Company
      // For Company owners, personal information is not relevant as the license is for the company
      // and the company details are captured in the Juristic Person's Details section
      if (!formData.pcoc) {
        // Insert Personal Information after Current Owner (which is now the second step)
        baseSections.splice(2, 0, { id: 'personal', title: 'Personal Information' })
      }

      // Only include Juristic Person's Details if Current Owner type is Company
      const finalSections = [
        ...baseSections,
        ...(formData.pcoc ? [{ id: 'juristic', title: "Juristic Person's Details" }] : []),
        { id: 'firearmsInPossession', title: 'Firearms in Possession' },
        { id: 'storage', title: 'Safe Storage' }
      ]

      return finalSections
    },
    [formData.pcoc] // Depend on pcoc value so sections update when it changes
  )

  // Effect to check template availability on component mount
  useEffect(() => {
    const abortController = new AbortController()

    const checkTemplate = async () => {
      setTemplateStatus('loading')
      setTemplateError(null)

      try {
        const response = await fetch(TEMPLATE_URL, {
          method: 'HEAD',
          signal: abortController.signal
        })

        if (!response.ok) {
          throw new Error(`Template not accessible: ${response.statusText}`)
        }

        setTemplateStatus('ready')
      } catch (err) {
        if (!abortController.signal.aborted) {
          console.error('Error checking template:', err)
          setTemplateStatus('error')
          setTemplateError('Could not access the template file. Please try again later.')
        }
      }
    }

    checkTemplate()
    return () => abortController.abort()
  }, [])

  // Effect to adjust current step when sections change due to pcoc toggle
  useEffect(() => {
    // Get the current section ID
    const currentSectionId = sections[currentStep - 1]?.id

    // If current step is beyond the available sections, adjust it
    if (currentStep > sections.length) {
      setCurrentStep(sections.length)
    }

    // If the current section is not found in the new sections array
    // (which can happen when switching to Company and the Personal Information section is removed)
    // then adjust the current step to the first section
    if (!currentSectionId) {
      setCurrentStep(1)
    }
  }, [sections, currentStep])

  // Validate the current step

  // Helper function to update submission state
  const updateSubmissionState = (status: ValidationStatus, message: string | null) => {
    setSubmissionStatus(status)
    setSubmissionMessage(message)
  }

  // Handle form field changes
  const updateFormData = (updatedData: Partial<NewLicenceData>) => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      ...updatedData
    }))
  }

  // Handle address field changes
  const handleAddressChange = (
    address: string,
    postalCode?: string,
    houseNumber?: string,
    isWorkAddress = false
  ) => {
    if (isWorkAddress) {
      setFormData((prev) => ({
        ...prev,
        workAddress: address,
        npecadd: address,
        workPostalCode: postalCode || '',
        npecpostal: postalCode || '',
        houseUnitNumber: houseNumber || ''
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        physicalAddress: address,
        npaddress: address,
        postalCode: postalCode || '',
        nppostal: postalCode || '',
        houseUnitNumber: houseNumber || ''
      }))
    }
  }

  // Handle house/unit number changes
  const handleHouseNumberChange = (houseNumber: string, isWorkAddress = false) => {
    if (isWorkAddress) {
      setFormData((prev) => ({
        ...prev,
        workHouseNumber: houseNumber
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        houseNumber
      }))
    }
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Ensure this is a manual submission, not an auto-submission
    // Check if the event target is a button with type="button" or if it's a direct call from the Submit button
    const isManualSubmission = e.type === 'click' || (e.target && (e.target as HTMLElement).tagName === 'BUTTON')

    // If it's not a manual submission and we're on the last step, don't proceed
    if (!isManualSubmission && currentStep === sections.length) {
      console.log('Preventing auto-submission on last step')
      return
    }

    // All fields are optional, so no validation is needed
    // Just proceed with submission

    // Update submission status
    updateSubmissionState('processing', 'Preparing your application...')

    try {
      // Create a separate object for placeholders to avoid type conflicts
      const placeholders = {
        // Current Owner Types
        PCOA: formData.pcoa ? 'X' : '',
        pcoa: formData.pcoa ? 'X' : '',
        PCOB: formData.pcob ? 'X' : '',
        pcob: formData.pcob ? 'X' : '',
        PCOC: formData.pcoc ? 'X' : '',
        pcoc: formData.pcoc ? 'X' : '',
        PCOE: formData.pcoe ? 'X' : '',
        pcoe: formData.pcoe ? 'X' : '',
        // Application Type
        MainHF: formData.mainHF ? 'X' : '',
        MAINHF: formData.mainHF ? 'X' : '',
        mainHF: formData.mainHF ? 'X' : '',
        mainhf: formData.mainHF ? 'X' : '',
        AddHF: formData.addHF ? 'X' : '',
        ADDHF: formData.addHF ? 'X' : '',
        addHF: formData.addHF ? 'X' : '',
        addhf: formData.addHF ? 'X' : '',

        // License Types
        S13: formData.s13 ? 'X' : '',
        s13: formData.s13 ? 'X' : '',
        S15: formData.s15 ? 'X' : '',
        s15: formData.s15 ? 'X' : '',
        S16: formData.s16 ? 'X' : '',
        s16: formData.s16 ? 'X' : '',
        S20: formData.s20 ? 'X' : '',
        s20: formData.s20 ? 'X' : '',
        S20A: formData.s20a ? 'X' : '',
        s20a: formData.s20a ? 'X' : '',
        S20B: formData.s20b ? 'X' : '',
        s20b: formData.s20b ? 'X' : '',
        S20C: formData.s20c ? 'X' : '',
        s20c: formData.s20c ? 'X' : '',

        // Firearm Types
        Rifle: formData.rifle ? 'X' : '',
        RIFLE: formData.rifle ? 'X' : '',
        rifle: formData.rifle ? 'X' : '',
        Shotgun: formData.shotgun ? 'X' : '',
        SHOTGUN: formData.shotgun ? 'X' : '',
        shotgun: formData.shotgun ? 'X' : '',
        Pistol: formData.pistol ? 'X' : '',
        PISTOL: formData.pistol ? 'X' : '',
        pistol: formData.pistol ? 'X' : '',
        Comb: formData.comb ? 'X' : '',
        COMB: formData.comb ? 'X' : '',
        comb: formData.comb ? 'X' : '',
        OtherDesign: formData.otherDesign ? 'X' : '',
        OTHERDESIGN: formData.otherDesign ? 'X' : '',
        otherDesign: formData.otherDesign ? 'X' : '',
        otherdesign: formData.otherDesign ? 'X' : '',
        OtherDesignE: formData.otherDesignE || '',
        OTHERDESIGNE: formData.otherDesignE || '',
        otherDesignE: formData.otherDesignE || '',
        otherdesigne: formData.otherDesignE || '',

        // Firearm Action Types
        Semi: formData.semi ? 'X' : '',
        SEMI: formData.semi ? 'X' : '',
        semi: formData.semi ? 'X' : '',
        Auto: formData.auto ? 'X' : '',
        AUTO: formData.auto ? 'X' : '',
        auto: formData.auto ? 'X' : '',
        Man: formData.man ? 'X' : '',
        MAN: formData.man ? 'X' : '',
        man: formData.man ? 'X' : '',
        OtherF: formData.otherF || '',
        OTHERF: formData.otherF || '',
        otherF: formData.otherF || '',
        otherf: formData.otherF || '',

        // Personal Information
        FULLNAME: formData.fullName || `${formData.firstName || ''} ${formData.lastName || ''}`,
        Fullname: formData.fullName || `${formData.firstName || ''} ${formData.lastName || ''}`,
        fullName: formData.fullName || `${formData.firstName || ''} ${formData.lastName || ''}`,
        fullname: formData.fullName || `${formData.firstName || ''} ${formData.lastName || ''}`,
        FIRSTNAME: formData.firstName || '',
        FirstName: formData.firstName || '',
        firstName: formData.firstName || '',
        firstname: formData.firstName || '',
        LASTNAME: formData.lastName || '',
        LastName: formData.lastName || '',
        lastName: formData.lastName || '',
        lastname: formData.lastName || '',
        IDNUMBER: formData.idNumber || '',
        IdNumber: formData.idNumber || '',
        idNumber: formData.idNumber || '',
        idnumber: formData.idNumber || '',
        ID: formData.idNumber || '',
        Id: formData.idNumber || '',
        id: formData.idNumber || '',
        PHONENUMBER: formData.phoneNumber || '',
        PhoneNumber: formData.phoneNumber || '',
        phoneNumber: formData.phoneNumber || '',
        phonenumber: formData.phoneNumber || '',
        Cell: formData.phoneNumber || '',
        CELL: formData.phoneNumber || '',
        cell: formData.phoneNumber || '',
        EMAIL: formData.email || '',
        Email: formData.email || '',
        email: formData.email || '',
        PHYSICALADDRESS: formData.physicalAddress || '',
        PhysicalAddress: formData.physicalAddress || '',
        physicalAddress: formData.physicalAddress || '',
        physicaladdress: formData.physicalAddress || '',
        Address: formData.physicalAddress || '',
        ADDRESS: formData.physicalAddress || '',
        address: formData.physicalAddress || '',
        POSTALCODE: formData.postalCode || '',
        PostalCode: formData.postalCode || '',
        postalCode: formData.postalCode || '',
        postalcode: formData.postalCode || '',

        // Professional Information
        COMPANYNAME: formData.companyName || '',
        CompanyName: formData.companyName || '',
        companyName: formData.companyName || '',
        companyname: formData.companyName || '',
        TRADEPROFESSION: formData.tradeProfession || '',
        TradeProfession: formData.tradeProfession || '',
        tradeProfession: formData.tradeProfession || '',
        tradeprofession: formData.tradeProfession || '',
        WORKADDRESS: formData.workAddress || '',
        WorkAddress: formData.workAddress || '',
        workAddress: formData.workAddress || '',
        workaddress: formData.workAddress || '',
        WORKNUMBER: formData.workNumber || '',
        WorkNumber: formData.workNumber || '',
        workNumber: formData.workNumber || '',
        worknumber: formData.workNumber || '',
        WORKPOSTALCODE: formData.workPostalCode || '',
        WorkPostalCode: formData.workPostalCode || '',
        workPostalCode: formData.workPostalCode || '',
        workpostalcode: formData.workPostalCode || '',

        // Firearm Details
        Make: formData.make || '',
        MAKE: formData.make || '',
        make: formData.make || '',
        Model: formData.model || '',
        MODEL: formData.model || '',
        model: formData.model || '',
        Caliber: formData.caliber || '',
        CALIBER: formData.caliber || '',
        caliber: formData.caliber || '',
        ENGG: formData.engg || '',
        Engg: formData.engg || '',
        engg: formData.engg || '',

        // Firearm component type
        BSN: formData.bsn || '',
        Bsn: formData.bsn || '',
        bsn: formData.bsn || '',
        FSN: formData.fsn || '',
        Fsn: formData.fsn || '',
        fsn: formData.fsn || '',
        RSN: formData.rsn || '',
        Rsn: formData.rsn || '',
        rsn: formData.rsn || '',

        // Firearm component make
        BSNM: formData.bsnm || '',
        Bsnm: formData.bsnm || '',
        bsnm: formData.bsnm || '',
        FSNM: formData.fsnm || '',
        Fsnm: formData.fsnm || '',
        fsnm: formData.fsnm || '',
        RSNM: formData.rsnm || '',
        Rsnm: formData.rsnm || '',
        rsnm: formData.rsnm || '',

        // SAP 350 (A) DETAILS
        SAP350A_NAME: formData.sap350aName || '',
        Sap350a_Name: formData.sap350aName || '',
        sap350aName: formData.sap350aName || '',
        sap350a_name: formData.sap350aName || '',
        SAP350A_ID_FAR: formData.sap350aIdFar || '',
        Sap350a_Id_Far: formData.sap350aIdFar || '',
        sap350aIdFar: formData.sap350aIdFar || '',
        sap350a_id_far: formData.sap350aIdFar || '',
        SAP350A_ADDRESS: formData.sap350aAddress || '',
        Sap350a_Address: formData.sap350aAddress || '',
        sap350aAddress: formData.sap350aAddress || '',
        sap350a_address: formData.sap350aAddress || '',
        SAP350A_POSTAL: formData.sap350aPostal || '',
        Sap350a_Postal: formData.sap350aPostal || '',
        sap350aPostal: formData.sap350aPostal || '',
        sap350a_postal: formData.sap350aPostal || '',
        SAP350A_DATE: formData.sap350aDate || '',
        Sap350a_Date: formData.sap350aDate || '',
        sap350aDate: formData.sap350aDate || '',
        sap350a_date: formData.sap350aDate || '',

        // Safe Storage
        SAFEYES: formData.safeYes ? 'X' : '',
        SafeYes: formData.safeYes ? 'X' : '',
        safeYes: formData.safeYes ? 'X' : '',
        safeyes: formData.safeYes ? 'X' : '',
        SAFENO: formData.safeNo ? 'X' : '',
        SafeNo: formData.safeNo ? 'X' : '',
        safeNo: formData.safeNo ? 'X' : '',
        safeno: formData.safeNo ? 'X' : '',
        SAFEH: formData.safeH ? 'X' : '',
        SafeH: formData.safeH ? 'X' : '',
        safeH: formData.safeH ? 'X' : '',
        safeh: formData.safeH ? 'X' : '',
        SAFER: formData.safeR ? 'X' : '',
        SafeR: formData.safeR ? 'X' : '',
        safeR: formData.safeR ? 'X' : '',
        safer: formData.safeR ? 'X' : '',
        SAFES: formData.safeS ? 'X' : '',
        SafeS: formData.safeS ? 'X' : '',
        safeS: formData.safeS ? 'X' : '',
        safes: formData.safeS ? 'X' : '',
        SAFED: formData.safeD ? 'X' : '',
        SafeD: formData.safeD ? 'X' : '',
        safeD: formData.safeD ? 'X' : '',
        safed: formData.safeD ? 'X' : '',
        SAFESE: formData.safeSe || '',
        SafeSe: formData.safeSe || '',
        safeSe: formData.safeSe || '',
        safese: formData.safeSe || '',
        SAFEDINFO: formData.safeDInfo || '',
        SafeDInfo: formData.safeDInfo || '',
        safeDInfo: formData.safeDInfo || '',
        safedinfo: formData.safeDInfo || '',
        SAFEMOUNTYES: formData.safeMountYes ? 'X' : '',
        SafeMountYes: formData.safeMountYes ? 'X' : '',
        safeMountYes: formData.safeMountYes ? 'X' : '',
        safemountyes: formData.safeMountYes ? 'X' : '',
        SAFEMOUNTNO: formData.safeMountNo ? 'X' : '',
        SafeMountNo: formData.safeMountNo ? 'X' : '',
        safeMountNo: formData.safeMountNo ? 'X' : '',
        safemountno: formData.safeMountNo ? 'X' : '',
        SAFEWALL: formData.safeWall ? 'X' : '',
        SafeWall: formData.safeWall ? 'X' : '',
        safeWall: formData.safeWall ? 'X' : '',
        safewall: formData.safeWall ? 'X' : '',
        SAFEFLOOR: formData.safeFloor ? 'X' : '',
        SafeFloor: formData.safeFloor ? 'X' : '',
        safeFloor: formData.safeFloor ? 'X' : '',
        safefloor: formData.safeFloor ? 'X' : '',

        // Competency Certificate
        TradeFirearm: formData.tradeFirearm ? 'X' : '',
        TRADEFIREARM: formData.tradeFirearm ? 'X' : '',
        tradeFirearm: formData.tradeFirearm ? 'X' : '',
        tradefirearm: formData.tradeFirearm ? 'X' : '',
        PossessFirearm: formData.possessFirearm ? 'X' : '',
        POSSESSFIREARM: formData.possessFirearm ? 'X' : '',
        possessFirearm: formData.possessFirearm ? 'X' : '',
        possessfirearm: formData.possessFirearm ? 'X' : '',
        F2AB: formData.f2ab || '',
        f2ab: formData.f2ab || '',
        F3: formData.f3 || '',
        f3: formData.f3 || '',
        F4: formData.f4 || '',
        f4: formData.f4 || '',

        // Criminal History
        H5A: formData.h5a ? 'X' : '',
        h5a: formData.h5a ? 'X' : '',
        H5B: formData.h5b ? 'X' : '',
        h5b: formData.h5b ? 'X' : '',
        'H5.1': formData.h51 || '',
        'h5.1': formData.h51 || '',
        H51: formData.h51 || '',
        h51: formData.h51 || '',
        'H5.2': formData.h52 || '',
        'h5.2': formData.h52 || '',
        H52: formData.h52 || '',
        h52: formData.h52 || '',
        'H5.3': formData.h53 || '',
        'h5.3': formData.h53 || '',
        H53: formData.h53 || '',
        h53: formData.h53 || '',
        'H5.4': formData.h54 || '',
        'h5.4': formData.h54 || '',
        H54: formData.h54 || '',
        h54: formData.h54 || '',

        // Pending Cases
        H6A: formData.h6a ? 'X' : '',
        h6a: formData.h6a ? 'X' : '',
        H6B: formData.h6b ? 'X' : '',
        h6b: formData.h6b ? 'X' : '',
        'H6.1': formData.h61 || '',
        'h6.1': formData.h61 || '',
        H61: formData.h61 || '',
        h61: formData.h61 || '',
        'H6.2': formData.h62 || '',
        'h6.2': formData.h62 || '',
        H62: formData.h62 || '',
        h62: formData.h62 || '',
        'H6.A3': formData.h6a3 || '',
        'h6.a3': formData.h6a3 || '',
        H6A3: formData.h6a3 || '',
        h6a3: formData.h6a3 || '',
      }

      // Add placeholders for Other Information section using the same format as in the 518A form
      placeholders['15A'] = formData.before90DaysYes ? 'X' : '';
      placeholders['15a'] = formData.before90DaysYes ? 'X' : '';
      placeholders['15B'] = formData.before90DaysNo ? 'X' : '';
      placeholders['15b'] = formData.before90DaysNo ? 'X' : '';
      placeholders['15C'] = formData.before90DaysReason || '';
      placeholders['15c'] = formData.before90DaysReason || '';
      placeholders['Before90DaysReason'] = formData.before90DaysReason || '';
      placeholders['BEFORE90DAYSREASON'] = formData.before90DaysReason || '';
      placeholders['16A'] = formData.afterExpiryYes ? 'X' : '';
      placeholders['16a'] = formData.afterExpiryYes ? 'X' : '';
      placeholders['16B'] = formData.afterExpiryNo ? 'X' : '';
      placeholders['16b'] = formData.afterExpiryNo ? 'X' : '';
      placeholders['16C'] = formData.afterExpiryReason || '';
      placeholders['16c'] = formData.afterExpiryReason || '';
      placeholders['AfterExpiryReason'] = formData.afterExpiryReason || '';
      placeholders['AFTEREXPIRYREASON'] = formData.afterExpiryReason || '';

      // Additional placeholders for after due date but before expiry
      placeholders['17A'] = formData.afterDueBeforeExpiryYes ? 'X' : '';
      placeholders['17a'] = formData.afterDueBeforeExpiryYes ? 'X' : '';
      placeholders['17B'] = formData.afterDueBeforeExpiryNo ? 'X' : '';
      placeholders['17b'] = formData.afterDueBeforeExpiryNo ? 'X' : '';
      placeholders['17C'] = formData.afterDueBeforeExpiryReason || '';
      placeholders['17c'] = formData.afterDueBeforeExpiryReason || '';
      placeholders['AfterDueBeforeExpiryReason'] = formData.afterDueBeforeExpiryReason || '';
      placeholders['AFTERDUEBEFOREEXPIRYREASON'] = formData.afterDueBeforeExpiryReason || '';

      // Add placeholders for additional original licences
      placeholders['OrigLicNum2'] = formData.additionalOriginalLicences?.[0]?.licenceNumber || '';
      placeholders['OrigLicNum3'] = formData.additionalOriginalLicences?.[1]?.licenceNumber || '';
      placeholders['OrigLicNum4'] = formData.additionalOriginalLicences?.[2]?.licenceNumber || '';
      placeholders['OrigLicNum5'] = formData.additionalOriginalLicences?.[3]?.licenceNumber || '';

      // Add placeholders for Juristic Person's Details (only for business licence types)
      if (formData.s20 || formData.s20a || formData.s20b) {
        placeholders['CompanyName'] = formData.companyName || '';
        placeholders['COMPANYNAME'] = formData.companyName || '';
        placeholders['TradingAs'] = formData.tradingAsName || '';
        placeholders['TRADINGAS'] = formData.tradingAsName || '';
        placeholders['FARNumber'] = formData.farNumber || '';
        placeholders['FARNUMBER'] = formData.farNumber || '';
        placeholders['PostalAddress'] = formData.postalAddress || '';
        placeholders['POSTALADDRESS'] = formData.postalAddress || '';
        placeholders['JuristicPostalCode'] = formData.workPostalCode || '';
        placeholders['JURISTICPOSTALCODE'] = formData.workPostalCode || '';
        placeholders['BusinessTelNumber'] = formData.businessTelNumber || '';
        placeholders['BUSINESSTELNUMBER'] = formData.businessTelNumber || '';
        placeholders['JuristicWorkNumber'] = formData.workNumber || '';
        placeholders['JURISTICWORKNUMBER'] = formData.workNumber || '';
      }

      // Add placeholders for Competency Certificate Types with specific placeholders for certificate number, issue date, and expiry date
      // Handgun Type
      placeholders['D1.6A1'] = formData.handgunType || '';
      placeholders['D1.6B1'] = formData.certNumber || '';
      placeholders['D1.6C1'] = formData.handgunCertIssueDate || '';
      placeholders['D1.6D1'] = formData.handgunCertExpiryDate || '';

      // Handgun and Rifle Type
      placeholders['D1.6A2'] = formData.handgunRifleType || '';
      placeholders['D1.6B2'] = formData.handgunRifleCertNumber || '';
      placeholders['D1.6C2'] = formData.handgunRifleCertIssueDate || '';
      placeholders['D1.6D2'] = formData.handgunRifleCertExpiryDate || '';

      // Rifle Type
      placeholders['D1.6A3'] = formData.rifleType || '';
      placeholders['D1.6B3'] = formData.rifleCertNumber || '';
      placeholders['D1.6C3'] = formData.rifleCertIssueDate || '';
      placeholders['D1.6D3'] = formData.rifleCertExpiryDate || '';

      // Shotgun Type
      placeholders['D1.6A4'] = formData.shotgunType || '';
      placeholders['D1.6B4'] = formData.shotgunCertNumber || '';
      placeholders['D1.6C4'] = formData.shotgunCertIssueDate || '';
      placeholders['D1.6D4'] = formData.shotgunCertExpiryDate || '';

      // Create a copy of the form data with the template information
      const submissionData: NewLicenceData = {
        ...formData,
        templateName: templateName || TEMPLATE_NAME,
        templateUrl: TEMPLATE_URL,
        documentProcessed: false // Set to false so DocScript knows it needs to process it
      }

      // Create a separate object for placeholders that won't be type-checked as NewLicenceData
      const placeholdersData = {
        ...placeholders
      }

      // Log the template URL to confirm it's using the correct one
      console.log('New Licence using template URL:', TEMPLATE_URL)

      // Log that we're submitting the form with placeholders for document processing
      console.log('Submitting New Licence form with placeholders for document processing')
      console.log('Document will be processed with documentProcessed flag set to:', submissionData.documentProcessed)

      // Pass the data to the parent component (DocScript) for processing
      // Use type assertion to allow the combined object to be passed to onSubmit
      onSubmit({ ...submissionData, ...placeholdersData } as unknown as NewLicenceData)

      // Update submission status
      updateSubmissionState('success', 'Application submitted to DocScript for processing!')
    } catch (error) {
      console.error('Error submitting form:', error)
      updateSubmissionState('error', `Error submitting form: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Render the current step based on section ID
  const renderCurrentStep = () => {
    const sectionId = sections[currentStep - 1]?.id

    switch (sectionId) {
      case 'application':
        return (
          <ApplicationType
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'personal':
        return (
          <PersonalInfo
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
            handleHouseNumberChange={handleHouseNumberChange}
          />
        )
      case 'firearmType':
        return (
          <FirearmType
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'sap350a':
        return (
          <SAP350ADetails
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
            handleHouseNumberChange={handleHouseNumberChange}
          />
        )
      case 'firearm':
        return (
          <FirearmDetails
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'criminal':
        return (
          <CriminalHistory
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'currentOwner':
        return (
          <CurrentOwner
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
            handleHouseNumberChange={handleHouseNumberChange}
          />
        )
      case 'competency':
        return (
          <CompetencyCertificate
            formData={formData}
            updateFormData={updateFormData}
          />
        )

      case 'association':
        return (
          <AssociationMembership
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'juristic':
        return (
          <JuristicPerson
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
            handleHouseNumberChange={handleHouseNumberChange}
          />
        )
      case 'firearmsInPossession':
        return (
          <FirearmsInPossession
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'storage':
        return (
          <SafeStorage
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      default:
        return null
    }
  }

  // Render template status component
  const renderTemplateStatus = () => (
    <TemplateStatus status={templateStatus} templateName={TEMPLATE_NAME} error={templateError} />
  )

  // Handle cancel submission
  const onCancelSubmission = () => {
    setSubmissionStatus('idle')
  }

  // Define test data sets
  const testDataSets = {
    'private-owner': { data: testPrivateOwnerData, name: 'Private Owner' },
    'firearm-dealer': { data: testFirearmDealerData, name: 'Firearm Dealer' },
    'company': { data: testCompanyData, name: 'Company' },
    'estate': { data: testEstateData, name: 'Estate' }
  }

  // Create options for the dropdown
  const testDataOptions = [
    { name: 'Private Owner', value: 'private-owner' },
    { name: 'Firearm Dealer', value: 'firearm-dealer' },
    { name: 'Company', value: 'company' },
    { name: 'Estate', value: 'estate' }
  ]

  // Fill form with test data
  const fillWithTestData = (dataType: string) => {
    // Get the selected test data set
    const { data, name } = testDataSets[dataType]

    // Create a copy of the data to modify
    const updatedData = { ...data }

    // If Private Owner is selected, ensure other owner types are cleared
    if (dataType === 'private-owner') {
      // Clear Firearm Dealer fields
      updatedData.pcob = false
      updatedData.fdrcn = ''
      updatedData.fdtas = ''
      updatedData.fdfarn = ''
      updatedData.fdbadre = ''
      updatedData.fdpostal = ''
      updatedData.fdbcall = ''
      updatedData.fdbmail = ''
      updatedData.fdrpns = ''
      updatedData.fdrpidsa = false
      updatedData.fdrpidno = false
      updatedData.fdrpid = ''
      updatedData.fdrpcall = ''
      updatedData.fdrpadd = ''
      updatedData.fdrpostal = ''

      // Reset SAP 350 (A) DETAILS to Private Owner values
      updatedData.sap350aName = 'John Smith'
      updatedData.sap350aIdFar = '8001015009087'
      updatedData.sap350aAddress = '123 Main Street, Pretoria'
      updatedData.sap350aPostal = '0001'
      updatedData.sap350aDate = '2023-01-15'

      // Clear Company fields
      updatedData.pcoc = false
      updatedData.crcn = ''
      updatedData.ctn = ''
      updatedData.cfarn = ''
      updatedData.cpadd = ''
      updatedData.cpostal = ''
      updatedData.cbtn = ''
      updatedData.ccem = ''
      updatedData.crpns = ''
      updatedData.crpidsa = false
      updatedData.crpidno = false
      updatedData.crpid = ''
      updatedData.crpcall = ''
      updatedData.crpadd = ''
      updatedData.crpostal = ''

      // Clear Estate fields
      updatedData.pcoe = false
      updatedData.executor = false
      updatedData.administrator = false
      updatedData.curatorship = false
      updatedData.trust = false
      updatedData.deLast = ''
      updatedData.deFullName = ''
      updatedData.deInitials = ''
      updatedData.idOf = ''
      updatedData.deEName = ''
      updatedData.deEIdSa = false
      updatedData.deEIdNo = ''
      updatedData.deEId = ''
      updatedData.deEAdd = ''
      updatedData.deEPostal = ''
      updatedData.deECell = ''
      updatedData.deEEmail = ''
    }

    // If Firearm Dealer is selected
    else if (dataType === 'firearm-dealer') {
      // Reset SAP 350 (A) DETAILS to Firearm Dealer values
      updatedData.sap350aName = 'Firearm Dealer Supply Co.'
      updatedData.sap350aIdFar = 'FD98765'
      updatedData.sap350aAddress = '456 Supplier Street, Cape Town'
      updatedData.sap350aPostal = '8001'
      updatedData.sap350aDate = '2023-02-20'
    }

    // If Company is selected
    else if (dataType === 'company') {
      // Reset SAP 350 (A) DETAILS to Company values
      updatedData.sap350aName = 'Corporate Firearms Ltd'
      updatedData.sap350aIdFar = 'CF54321'
      updatedData.sap350aAddress = '789 Business Park, Johannesburg'
      updatedData.sap350aPostal = '2000'
      updatedData.sap350aDate = '2023-03-15'
    }

    // If Estate is selected
    else if (dataType === 'estate') {
      // Reset SAP 350 (A) DETAILS to Estate values
      updatedData.sap350aName = 'Estate of James Wilson'
      updatedData.sap350aIdFar = '6501015009087'
      updatedData.sap350aAddress = '321 Legacy Road, Durban'
      updatedData.sap350aPostal = '4001'
      updatedData.sap350aDate = '2023-04-10'
    }

    // Update the form data
    setFormData(updatedData)

    // Show a toast notification
    showToast({
      message: `Form filled with ${name} test data`,
      type: 'info',
      duration: 3000
    })

    console.log(`Form filled with ${name} test data`)
  }

  // No longer need to get the next test data type since we're using a dropdown

  // Main component render
  return (
    <FormLayout
      title="New Firearm Licence Application"
      sections={sections}
      currentStep={currentStep}
      setCurrentStep={setCurrentStep}
      onSubmit={handleSubmit}
      submissionStatus={submissionStatus}
      submissionMessage={submissionMessage}
      onCancelSubmission={onCancelSubmission}
      onFillTestData={fillWithTestData}
      testDataOptions={testDataOptions}
      isDocScriptForm={true}
    >
      {templateStatus === 'error' && renderTemplateStatus()}
      {renderCurrentStep()}
    </FormLayout>
  )
}
