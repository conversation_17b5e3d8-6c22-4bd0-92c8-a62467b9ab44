import React from 'react'
import { Loan } from '../../../types'
import { DashboardIcons } from '../../icons/DashboardIcons'

interface LoanCardHeaderProps {
  loan: Loan
  clientName: string
  statusClass: string
  isFocused: boolean
  handleFocusToggle: () => void
  highlightMatch: (text: string) => JSX.Element | string
  formatDate: (date: string) => string
}

const LoanCardHeader: React.FC<LoanCardHeaderProps> = ({
  loan,
  clientName,
  statusClass,
  isFocused,
  handleFocusToggle,
  highlightMatch,
  formatDate
}) => {
  return (
    <div className={`flex flex-col gap-3 mb-4 ${isFocused ? 'gap-4' : ''}`}>
      {/* Client info, invoice and status row - simplified layout */}
      <div className="flex justify-between items-start">
        {/* Client info */}
        <div className="flex items-start gap-2">
          <div className="mt-1">
            <span
              className={`inline-block w-1.5 h-6 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full ${isFocused ? 'w-2 h-8' : ''}`}
            />
          </div>
          <div>
            <h2
              className={`font-semibold text-xl text-white ${isFocused ? 'text-2xl' : ''}`}
            >
              {highlightMatch(clientName)}
            </h2>
          </div>
        </div>

        {/* Quote number with maximise button - updated positioning */}
        <div className="flex-1 flex justify-center items-center">
          <div className="px-4 py-1.5 bg-gradient-to-r from-stone-800/70 via-stone-700/40 to-stone-800/70 border border-stone-500/20 rounded-md shadow-sm flex items-center">
            <div className="w-3 h-px bg-orange-400/50 mr-2"></div>
            <span className="text-lg text-white font-medium tracking-wide">
              {loan.invoice_number && `QUOTE ${highlightMatch(loan.invoice_number)}`}
            </span>
            <div className="w-3 h-px bg-orange-400/50 ml-2"></div>

            {/* Focus button - relocated next to quote number and only shown when not focused */}
            {!isFocused && (
              <button
                onClick={handleFocusToggle}
                className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-stone-600/80 hover:text-white"
                title="Focus on this loan"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                </svg>
              </button>
            )}

            {/* Close button - shown only when focused */}
            {isFocused && (
              <button
                onClick={handleFocusToggle}
                className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-red-500/80 hover:text-white"
                title="Exit focus mode"
              >
                <DashboardIcons.Close className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Status */}
        <div className="flex flex-col items-end gap-1">
          <div className={`px-2 py-0.5 rounded-full text-xs font-medium ${statusClass}`}>
            {loan.status.charAt(0).toUpperCase() + loan.status.slice(1)}
          </div>
          <div className="text-xs text-stone-300">
            Started: {formatDate(loan.start_date)}
          </div>
        </div>
      </div>
    </div>
  )
}

export default React.memo(LoanCardHeader)
