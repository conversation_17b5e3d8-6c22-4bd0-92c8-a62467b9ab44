import React from 'react'
import { Download, Zap, RefreshCw, Check, AlertCircle } from 'lucide-react'
import { useUpdateSettings } from './hooks/useUpdateSettings'

const UpdateSettings: React.FC = () => {
  const {
    currentChannel,
    isChangingChannel,
    isCheckingUpdate,
    isUpdateAvailable,
    updateVersion,
    currentVersion,
    updateStatus,
    handleCheckForUpdates,
    handleChangeChannel,
    handleInstallUpdate,
    safeVersionToString
  } = useUpdateSettings()

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-white border-b border-stone-700/50 pb-1 mb-4">
        Update Settings
      </h2>

      {/* Current Version */}
      <div className="flex flex-col space-y-1 mb-4">
        <label className="text-stone-300 font-medium text-sm">Current Version</label>
        <div className="flex items-center gap-2">
          <span className="px-2 py-1 bg-stone-700 text-white rounded-md text-sm">
            {safeVersionToString(currentVersion)}
          </span>

          {isUpdateAvailable && (
            <span className="px-2 py-1 bg-green-700/20 text-green-400 border border-green-700/30 rounded-md flex items-center gap-1 text-sm">
              <Check size={14} />
              Update available: {safeVersionToString(updateVersion)}
            </span>
          )}
        </div>
      </div>

      {/* Update Channel */}
      <div className="flex flex-col space-y-1 mb-4">
        <label className="text-stone-300 font-medium text-sm">Update Channel</label>
        <p className="text-stone-400 text-xs">
          Select which update channel to use for the application.
        </p>

        <div className="flex flex-col items-center gap-2 mt-1">
          {/* Stable Release Channel */}
          <button
            onClick={() => handleChangeChannel('release')}
            disabled={isChangingChannel || currentChannel === 'release'}
            className={`flex items-center p-2 rounded-lg border w-full ${
              currentChannel === 'release'
                ? 'bg-stone-700/50 border-orange-500/50 shadow-md shadow-orange-500/10'
                : 'bg-stone-700/30 border-stone-600 hover:border-stone-500'
            } transition-all duration-200`}
          >
            <div
              className={`w-3 h-3 rounded-full mr-2 ${
                currentChannel === 'release' ? 'bg-green-500' : 'bg-stone-600'
              }`}
            ></div>
            <div className="flex-1 text-center">
              <h3 className="font-medium text-white text-sm">Stable Release</h3>
              <p className="text-stone-400 text-xs">Recommended for most users</p>
            </div>
          </button>

          {/* Pre-release Channel */}
          <button
            onClick={() => handleChangeChannel('prerelease')}
            disabled={isChangingChannel || currentChannel === 'prerelease'}
            className={`flex items-center p-2 rounded-lg border w-full ${
              currentChannel === 'prerelease'
                ? 'bg-stone-700/50 border-yellow-500/50 shadow-md shadow-yellow-500/10'
                : 'bg-stone-700/30 border-stone-600 hover:border-stone-500'
            } transition-all duration-200`}
          >
            <div
              className={`w-3 h-3 rounded-full mr-2 ${
                currentChannel === 'prerelease' ? 'bg-yellow-500' : 'bg-stone-600'
              }`}
            ></div>
            <div className="flex-1 text-center">
              <div className="flex items-center justify-center gap-1">
                <h3 className="font-medium text-white text-sm">Pre-release</h3>
                <Zap className="text-yellow-500" size={12} />
              </div>
              <p className="text-stone-400 text-xs">Early access to new features</p>
            </div>
          </button>
        </div>

        {/* Channel changing indicator */}
        {isChangingChannel && (
          <div className="mt-1 px-2 py-1 bg-stone-700/30 border border-stone-600 rounded-md text-stone-300 flex items-center gap-1 text-xs">
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-stone-300"></div>
            <span>Switching update channel...</span>
          </div>
        )}
      </div>

      {/* Check for updates */}
      <div className="flex flex-col space-y-1 mb-4">
        <label className="text-stone-300 font-medium text-sm">Check for Updates</label>
        <div className="flex flex-wrap items-center gap-2">
          <button
            onClick={handleCheckForUpdates}
            disabled={isCheckingUpdate}
            className={`px-3 py-1 rounded-md text-sm ${
              isCheckingUpdate
                ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
                : 'bg-orange-600 hover:bg-orange-500 text-white'
            } transition-colors flex items-center gap-1`}
          >
            {isCheckingUpdate ? (
              <>
                <RefreshCw className="animate-spin" size={14} />
                <span>Checking...</span>
              </>
            ) : (
              <>
                <Download size={14} />
                <span>Check Now</span>
              </>
            )}
          </button>

          {isUpdateAvailable && (
            <button
              onClick={handleInstallUpdate}
              className="px-3 py-1 rounded-md bg-green-600 hover:bg-green-500 text-white transition-colors flex items-center gap-1 text-sm"
            >
              <Check size={14} />
              <span>Install Update</span>
            </button>
          )}

          {updateStatus === 'update-error' && (
            <div className="px-2 py-1 bg-red-500/20 border border-red-500/30 rounded-md text-red-400 flex items-center gap-1 text-xs">
              <AlertCircle size={14} />
              <span>Error checking for updates</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default UpdateSettings
