import React from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'
import { FilterState } from './types'

interface EmptyStateProps {
  searchQuery: string
  clientFilter: FilterState['clientFilter']
  onCreateClient: () => void
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  searchQuery,
  clientFilter,
  onCreateClient
}) => {
  return (
    <div className="flex flex-col items-center justify-center h-64 text-stone-400">
      <DashboardIcons.Search className="w-12 h-12 mb-4" />
      {searchQuery ? (
        <>
          <p className="text-lg">No results found for "{searchQuery}"</p>
          <p className="text-sm mt-2">Try searching by name, ID, email, or phone number</p>
        </>
      ) : clientFilter !== 'all' ? (
        <>
          <p className="text-lg">No clients found with the selected filter</p>
          <p className="text-sm mt-2">Try a different filter or create a new client</p>
        </>
      ) : (
        <>
          <p className="text-lg">No clients found</p>
          <p className="text-sm mt-2">Create your first client to get started</p>
        </>
      )}
      <button
        onClick={onCreateClient}
        className="mt-4 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg
        inline-flex items-center gap-2 transition-colors"
      >
        <DashboardIcons.Add className="w-4 h-4" />
        Create New Client
      </button>
    </div>
  )
}
