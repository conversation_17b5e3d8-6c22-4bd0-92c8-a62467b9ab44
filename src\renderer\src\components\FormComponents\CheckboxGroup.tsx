import React from 'react'

interface CheckboxOption {
  name: string
  label: string
  checked: boolean
}

interface CheckboxGroupProps {
  options: CheckboxOption[]
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  title?: string
  className?: string
  itemClassName?: string
  required?: boolean
  error?: string
  disabled?: boolean
  columns?: 2 | 3 | 4
}

const CheckboxGroup = ({
  options,
  onChange,
  title,
  className = '',
  itemClassName = '',
  required = false,
  error,
  disabled = false,
  columns = 3
}: CheckboxGroupProps) => (
  <div className={`space-y-2 ${className}`}>
    {title && (
      <div className="block text-sm font-medium text-stone-300">
        {title} {required && <span className="text-orange-500">*</span>}
      </div>
    )}
    <div
      className={`grid ${
        columns === 2
          ? 'grid-cols-2'
          : columns === 3
            ? 'grid-cols-3'
            : columns === 4
              ? 'grid-cols-4'
              : 'grid-cols-3'
      } gap-2`}
    >
      {options.map((option) => (
        <label
          key={option.name}
          className={`flex items-center p-1.5 bg-stone-800/50 rounded-lg ${
            disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer hover:bg-stone-700/50'
          } ${itemClassName}`}
        >
          <input
            type="checkbox"
            name={option.name}
            checked={option.checked}
            onChange={onChange}
            disabled={disabled}
            className="mr-1.5 h-4 w-4 text-orange-500 border-stone-600 rounded focus:ring-1 focus:ring-orange-500/30 flex-shrink-0"
          />
          <span className="text-stone-300 text-sm truncate">{option.label}</span>
        </label>
      ))}
    </div>
    {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
  </div>
)

export default CheckboxGroup
