import { Firearm } from '../types/firearm'

/**
 * This function previously sent WhatsApp notifications for firearm assignments.
 * It has been disabled as per requirements to remove WhatsApp notification functionality.
 *
 * The function now returns a success response without sending any notifications.
 */
export const sendFirearmAssignmentMessage = async (
  clientFirstName: string,
  clientLastName: string,
  clientPhone: string,
  firearm: Firearm,
  assignedDate: string,
  paidAmount: number,
  currentWalletBalance: number
): Promise<{ success: boolean; error?: string }> => {
  // WhatsApp notification functionality has been removed
  // Return success without sending any notification
  return { success: true };
}
