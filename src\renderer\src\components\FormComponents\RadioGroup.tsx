interface RadioGroupProps {
  label?: string
  name: string
  value: string
  onChange: (value: string) => void
  options: { value: string; label: string }[]
  className?: string
  required?: boolean
  error?: string
  disabled?: boolean
}

const RadioGroup = ({
  label,
  name,
  value,
  onChange,
  options,
  className = '',
  required = false,
  error,
  disabled = false
}: RadioGroupProps) => (
  <div className={`space-y-2 ${className}`}>
    {label && (
      <div className="block text-sm font-medium text-stone-300">
        {label} {required && <span className="text-orange-500">*</span>}
      </div>
    )}

    <div className="flex flex-wrap gap-4">
      {options.map((option) => (
        <label
          key={option.value}
          className={`flex items-center ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}`}
        >
          <input
            type="radio"
            name={name}
            checked={value === option.value}
            onChange={() => onChange(option.value)}
            required={required}
            disabled={disabled}
            className="mr-2 text-orange-500 border-stone-600 rounded focus:ring-1 focus:ring-orange-500/30"
          />
          <span className="text-stone-300">{option.label}</span>
        </label>
      ))}
    </div>

    {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
  </div>
)

export default RadioGroup
