import { useState, useEffect, useCallback, useRef } from 'react'
import { WhatsAppSessionStatus, ServerStatus, TestMessageForm, WhatsAppSessionInfo } from '../types'
import { formatPhoneForWhatsApp, callWhatsAppApi } from '../utils'

export const useWhatsAppSettings = (activeSection: string) => {
  // WhatsApp states
  const [whatsappSessionId, setWhatsappSessionId] = useState<string>('')
  const [whatsappQrCode, setWhatsappQrCode] = useState<string | null>(null)
  const [whatsappStatus, setWhatsappStatus] = useState<WhatsAppSessionStatus>('UNKNOWN')
  const [isLoadingQrCode, setIsLoadingQrCode] = useState<boolean>(false)
  const [isRestartingSession, setIsRestartingSession] = useState<boolean>(false)
  const [isSendingTestMessage, setIsSendingTestMessage] = useState<boolean>(false)
  const [whatsappError, setWhatsappError] = useState<string>('')
  const [testMessageForm, setTestMessageForm] = useState<TestMessageForm>({
    recipient: '',
    message: ''
  })
  const [whatsappSuccess, setWhatsappSuccess] = useState<string>('')
  const [isCheckingServer, setIsCheckingServer] = useState<boolean>(false)
  const [serverStatus, setServerStatus] = useState<ServerStatus>(null)
  const [autoCheckEnabled, setAutoCheckEnabled] = useState<boolean>(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false)
  const [confirmAction, setConfirmAction] = useState<() => Promise<void> | void>(() => Promise.resolve())
  const [confirmMessage, setConfirmMessage] = useState<string>('')

  // State for session management
  const [availableSessions, setAvailableSessions] = useState<WhatsAppSessionInfo[]>([])
  const [availableSessionIds, setAvailableSessionIds] = useState<string[]>([])
  const [isCreatingSession, setIsCreatingSession] = useState<boolean>(false)
  const [newSessionId, setNewSessionId] = useState<string>('')
  const [isLoadingSessions, setIsLoadingSessions] = useState<boolean>(false)
  const [isTerminatingSession, setIsTerminatingSession] = useState<boolean>(false)

  // Refs for interval timers
  const serverCheckIntervalRef = useRef<number | null>(null)

  /**
   * Checks if the WhatsApp API server is alive
   */
  const checkServerAlive = useCallback(async () => {
    setIsCheckingServer(true)
    setWhatsappError('')

    try {
      const startTime = performance.now()
      const result = await callWhatsAppApi('checkAlive')
      const endTime = performance.now()
      const responseTime = Math.round(endTime - startTime)

      console.log('Server status check result:', result)

      if (!result.success || !result.alive) {
        setServerStatus({
          alive: false,
          error: result.error || 'Server appears to be offline',
          lastChecked: new Date(),
          responseTime
        })
      } else {
        setServerStatus({
          alive: true,
          message: result.message || 'Server is online',
          lastChecked: new Date(),
          responseTime
        })
      }
    } catch (error) {
      console.error('Error checking server status:', error)
      setServerStatus({
        alive: false,
        error: error instanceof Error ? error.message : 'Failed to check server status',
        lastChecked: new Date()
      })
    } finally {
      setIsCheckingServer(false)
    }
  }, [])



  /**
   * Gets all WhatsApp sessions
   */
  const getAllWhatsAppSessions = useCallback(async () => {
    setIsLoadingSessions(true)
    setWhatsappError('')

    try {
      // Use the correct method name as defined in the preload script
      console.log('Calling getAllSessions API...')
      const result = await callWhatsAppApi('getAllSessions')

      console.log('Get all sessions result:', result)

      // Handle different possible response formats
      if (result.data) {
        let sessionInfos: WhatsAppSessionInfo[] = []
        let sessionIds: string[] = []

        // Case 1: Array of sessions
        if (Array.isArray(result.data)) {
          sessionInfos = result.data.map((session: any) => {
            // If the session is an object with an id property, use that
            if (typeof session === 'object' && session.id) {
              return {
                id: session.id,
                status: session.status || session.state || 'UNKNOWN',
                lastActive: session.lastActive ? new Date(session.lastActive) : undefined,
                isDefault: session.isDefault || false
              }
            }
            // If the session is a string, create a basic session info
            if (typeof session === 'string') {
              return {
                id: session,
                status: 'UNKNOWN'
              }
            }
            // Otherwise, try to convert to string
            return {
              id: String(session),
              status: 'UNKNOWN'
            }
          })
        }
        // Case 2: Object with sessions property that is an array
        else if (typeof result.data === 'object' && Array.isArray(result.data.sessions)) {
          sessionInfos = result.data.sessions.map((session: any) => {
            if (typeof session === 'object' && session.id) {
              return {
                id: session.id,
                status: session.status || session.state || 'UNKNOWN',
                lastActive: session.lastActive ? new Date(session.lastActive) : undefined,
                isDefault: session.isDefault || false
              }
            }
            return {
              id: String(session),
              status: 'UNKNOWN'
            }
          })
        }
        // Case 3: Object with keys as session IDs
        else if (typeof result.data === 'object') {
          sessionInfos = Object.entries(result.data).map(([id, data]: [string, any]) => {
            return {
              id,
              status: data?.status || data?.state || 'UNKNOWN',
              lastActive: data?.lastActive ? new Date(data.lastActive) : undefined,
              isDefault: data?.isDefault || false
            }
          })
        }

        console.log('Parsed session infos:', sessionInfos)

        // Extract just the IDs for backward compatibility
        sessionIds = sessionInfos.map(session => session.id)

        // If we got an empty array but the API call was successful,
        // it might mean there are no sessions yet or the API returned an unexpected format
        if (sessionInfos.length === 0) {
          console.warn('No sessions found in the API response.')
          // Don't use hardcoded sessions anymore, just keep the empty array
        }

        setAvailableSessions(sessionInfos)
        setAvailableSessionIds(sessionIds)

        // If we have sessions but none is selected, select the first one
        if (sessionIds.length > 0 && !whatsappSessionId) {
          setWhatsappSessionId(sessionIds[0])
        }

        // Clear any previous error since we have sessions now
        setWhatsappError('')
      } else {
        // If the API returns no data, set empty arrays
        console.warn('No sessions data returned from API')
        setAvailableSessions([])
        setAvailableSessionIds([])

        // Clear any selected session since none are available
        if (whatsappSessionId) {
          setWhatsappSessionId('')
        }
      }
    } catch (error) {
      console.error('Error getting WhatsApp sessions:', error)

      // Set empty arrays for sessions
      setAvailableSessions([])
      setAvailableSessionIds([])

      // Clear any selected session since none are available
      if (whatsappSessionId) {
        setWhatsappSessionId('')
      }

      // Show error message
      setWhatsappError('Failed to retrieve sessions from API. Please create a new session.')
    } finally {
      setIsLoadingSessions(false)
    }
  }, [whatsappSessionId, setWhatsappSessionId])

  // Check API availability when WhatsApp tab is opened
  useEffect(() => {
    if (activeSection === 'whatsapp') {
      // Only check if API is available when tab is opened, don't check status automatically
      if (
        !((window.electronAPI && 'whatsapp' in (window.electronAPI as any)) || window.electron?.ipcRenderer)
      ) {
        // Set error if API is not available
        setWhatsappError('WhatsApp API not available. Please restart the application.')
        setWhatsappStatus('FAILED')
      }
    }
  }, [activeSection])

  // Check server status and load sessions when WhatsApp tab is opened
  useEffect(() => {
    if (activeSection === 'whatsapp') {
      // Start automatic server checking
      if (autoCheckEnabled) {
        if (serverCheckIntervalRef.current) {
          window.clearInterval(serverCheckIntervalRef.current)
        }

        // Check immediately
        checkServerAlive()

        // Then check every 30 seconds
        serverCheckIntervalRef.current = window.setInterval(() => {
          checkServerAlive()
        }, 30000) // 30 seconds
      } else {
        // Just check once if auto-check is disabled
        checkServerAlive()
      }

      // Load all sessions
      getAllWhatsAppSessions()
    } else {
      // Clear interval when leaving the WhatsApp tab
      if (serverCheckIntervalRef.current) {
        window.clearInterval(serverCheckIntervalRef.current)
        serverCheckIntervalRef.current = null
      }
    }

    // Cleanup function to clear interval when component unmounts
    return () => {
      if (serverCheckIntervalRef.current) {
        window.clearInterval(serverCheckIntervalRef.current)
        serverCheckIntervalRef.current = null
      }
    }
  }, [activeSection, autoCheckEnabled, checkServerAlive, getAllWhatsAppSessions])

  /**
   * Gets the QR code for a specific WhatsApp session
   * @param sessionId The ID of the session to get the QR code for
   */
  const getQrCodeForSession = useCallback(async (sessionId: string) => {
    if (!sessionId) {
      setWhatsappError('Session ID is required')
      return
    }

    setIsLoadingQrCode(true)
    setWhatsappError('')
    setWhatsappQrCode(null)

    try {
      // First check the session status to see if it's already connected
      const statusResult = await callWhatsAppApi('checkStatus', sessionId)
      const sessionStatus = statusResult.data?.status || statusResult.data?.state || 'UNKNOWN'

      // Set the current session ID to the one we're checking
      setWhatsappSessionId(sessionId)

      // Update the status in the UI
      setWhatsappStatus(sessionStatus as WhatsAppSessionStatus)

      // If the session is already connected, don't try to get a QR code
      if (sessionStatus === 'CONNECTED') {
        console.log(`Session ${sessionId} is already connected, no need for QR code`)
        setWhatsappSuccess('Session is already connected! No need to scan QR code.')
        return
      }

      // If not connected, proceed to get the QR code
      const result = await callWhatsAppApi('getQrCode', sessionId)

      // Create object URL from the returned buffer
      const buffer = result.data
      const blob = new Blob([buffer], { type: 'image/png' })
      const qrCodeUrl = URL.createObjectURL(blob)
      setWhatsappQrCode(qrCodeUrl)
    } catch (error) {
      console.error(`Error getting WhatsApp QR code for session ${sessionId}:`, error)

      // Check if this is the "already connected" error
      const errorMessage = error instanceof Error ? error.message : 'Failed to get QR code'

      if (errorMessage.includes('already connected') ||
          errorMessage.includes('already scanned') ||
          errorMessage.includes('409')) {
        // This is not really an error - the session is already connected
        setWhatsappSuccess('Session is already connected! No need to scan QR code.')

        // Update the status to connected
        setWhatsappStatus('CONNECTED')

        // Check the session status to get the latest info
        checkSessionStatus(sessionId)
      } else {
        // This is a real error
        setWhatsappError(errorMessage)
      }
    } finally {
      setIsLoadingQrCode(false)
    }
  }, [])

  /**
   * Checks the status of a specific WhatsApp session
   * @param sessionId The ID of the session to check
   */
  const checkSessionStatus = useCallback(async (sessionId: string) => {
    if (!sessionId) {
      return
    }

    setWhatsappError('')

    try {
      const result = await callWhatsAppApi('checkStatus', sessionId)

      const data = result.data
      // Handle different response formats - API may return "status" or "state"
      const sessionStatus = data.status || data.state || 'UNKNOWN'
      setWhatsappStatus(sessionStatus as WhatsAppSessionStatus)

      // If connected, we don't need the QR code anymore
      if (sessionStatus === 'CONNECTED') {
        setWhatsappQrCode(null)
        setWhatsappSuccess('WhatsApp connected successfully!')
      }

      console.log(`Session ${sessionId} status data:`, data)
    } catch (error) {
      console.error(`Error checking WhatsApp session ${sessionId} status:`, error)

      // Check if this is a "session not found" error
      const errorMessage = error instanceof Error ? error.message : 'Failed to check session status';

      if (errorMessage.includes('not found')) {
        console.warn(`Session ${sessionId} not found. It may have been deleted.`);
        setWhatsappError(`Session "${sessionId}" not found. Please create a new session.`);
        setWhatsappStatus('NOT_FOUND');

        // Clear the session ID if it matches the current one
        if (sessionId === whatsappSessionId) {
          setWhatsappSessionId('');
        }

        // Refresh the session list to get current valid sessions
        getAllWhatsAppSessions();
      } else {
        setWhatsappError(errorMessage);
        setWhatsappStatus('FAILED');
      }
    }
  }, [whatsappSessionId, getAllWhatsAppSessions])

  /**
   * Gets the QR code for the currently selected WhatsApp session
   * This is a wrapper around getQrCodeForSession that uses the current whatsappSessionId
   */
  const getWhatsappQrCode = useCallback(async () => {
    if (!whatsappSessionId) {
      setWhatsappError('Please select a session ID')
      return
    }

    await getQrCodeForSession(whatsappSessionId)
  }, [whatsappSessionId, getQrCodeForSession])

  /**
   * Checks the status of the currently selected WhatsApp session
   * This is a wrapper around checkSessionStatus that uses the current whatsappSessionId
   */
  const checkWhatsappSessionStatus = useCallback(async () => {
    if (!whatsappSessionId) {
      return
    }

    await checkSessionStatus(whatsappSessionId)
  }, [whatsappSessionId, checkSessionStatus])

  /**
   * Restarts the WhatsApp session
   */
  const restartWhatsappSession = useCallback(async () => {
    if (!whatsappSessionId) {
      setWhatsappError('Please select a session ID')
      return
    }

    setIsRestartingSession(true)
    setWhatsappError('')
    setWhatsappSuccess('')

    try {
      // First check the session status (we'll use this info later)
      await checkSessionStatus(whatsappSessionId)

      // Now restart the session
      await callWhatsAppApi('restartSession', whatsappSessionId)

      setWhatsappStatus('CONNECTING')
      setWhatsappSuccess('Session restart initiated. Please wait 10 seconds...')

      // Get new QR code after 10 second delay
      // Keep the loading state active during this time
      const sessionToRestart = whatsappSessionId // Store the session ID in a local variable

      // Don't set isRestartingSession to false immediately
      // Instead, wait for the full 10 seconds
      setTimeout(() => {
        // If the session was previously connected, it might reconnect automatically
        // without needing a new QR code, so we'll check the status first
        checkSessionStatus(sessionToRestart).then(() => {
          // Only try to get QR code if the session isn't already connected
          if (whatsappStatus !== 'CONNECTED') {
            getQrCodeForSession(sessionToRestart)
          }
        })

        // Only set isRestartingSession to false after the delay
        setIsRestartingSession(false)
      }, 10000)
    } catch (error) {
      console.error('Error restarting WhatsApp session:', error)
      setWhatsappError(error instanceof Error ? error.message : 'Failed to restart session')
      // Clear the status to indicate reconnection is needed
      setWhatsappStatus('FAILED')
      // Make sure to set isRestartingSession to false in case of error
      setIsRestartingSession(false)
    }
    // Remove the finally block since we're handling the state in the setTimeout
  }, [whatsappSessionId, getQrCodeForSession, checkSessionStatus, whatsappStatus])

  /**
   * Sends a test message via WhatsApp
   */
  const sendWhatsappTestMessage = useCallback(async () => {
    if (!whatsappSessionId) {
      setWhatsappError('Please enter a session ID')
      return
    }

    if (!testMessageForm.recipient || !testMessageForm.message) {
      setWhatsappError('Please enter recipient phone number and message')
      return
    }

    setIsSendingTestMessage(true)
    setWhatsappError('')
    setWhatsappSuccess('')

    try {
      // Format the phone number for WhatsApp
      const formattedNumber = formatPhoneForWhatsApp(testMessageForm.recipient)

      await callWhatsAppApi(
        'sendMessage',
        whatsappSessionId,
        formattedNumber,
        testMessageForm.message
      )

      setWhatsappSuccess('Test message sent successfully!')

      // Clear message form
      setTestMessageForm((prev) => ({
        ...prev,
        message: ''
      }))
    } catch (error) {
      console.error('Error sending WhatsApp test message:', error)
      setWhatsappError(error instanceof Error ? error.message : 'Failed to send test message')
    } finally {
      setIsSendingTestMessage(false)
    }
  }, [whatsappSessionId, testMessageForm])




  /**
   * Creates a new WhatsApp session
   */
  const createWhatsAppSession = useCallback(async () => {
    if (!newSessionId || newSessionId.trim() === '') {
      setWhatsappError('Please enter a valid session ID')
      return
    }

    setIsCreatingSession(true)
    setWhatsappError('')
    setWhatsappSuccess('')

    try {
      const result = await callWhatsAppApi('startSession', newSessionId)

      console.log('Create session result:', result)

      // Success message is now set in the QR code section below

      // Update the session list
      await getAllWhatsAppSessions()

      // Set the current session to the newly created one
      setWhatsappSessionId(newSessionId)

      // Clear the new session input
      setNewSessionId('')

      // Get QR code for the new session after 10 seconds
      // This delay gives the server time to initialize the session properly
      const createdSessionId = newSessionId // Store the created session ID in a local variable
      setWhatsappSuccess(`Session "${createdSessionId}" created successfully! QR code will be available in 10 seconds...`)
      setTimeout(() => {
        // Explicitly get QR code for the newly created session, not the currently selected one
        getQrCodeForSession(createdSessionId)
      }, 10000)
    } catch (error) {
      console.error('Error creating WhatsApp session:', error)
      setWhatsappError(error instanceof Error ? error.message : 'Failed to create session')
    } finally {
      setIsCreatingSession(false)
    }
  }, [newSessionId, getWhatsappQrCode, getAllWhatsAppSessions])

  /**
   * Initiates termination of a WhatsApp session with confirmation
   * @param sessionId Optional session ID to terminate. If not provided, uses the currently selected session.
   */
  const initiateTerminateSession = useCallback((sessionId?: string) => {
    // Use provided sessionId or fall back to the currently selected one
    const sessionToTerminate = sessionId || whatsappSessionId

    if (!sessionToTerminate) {
      setWhatsappError('Please select a session ID')
      return
    }

    // Set up confirmation dialog
    setConfirmMessage(`Are you sure you want to terminate session "${sessionToTerminate}"? This action cannot be undone.`)
    setConfirmAction(() => () => terminateWhatsAppSession(sessionToTerminate))
    setShowConfirmDialog(true)
  }, [whatsappSessionId])

  /**
   * Terminates a WhatsApp session
   * @param sessionId Optional session ID to terminate. If not provided, uses the currently selected session.
   */
  const terminateWhatsAppSession = useCallback(async (sessionId?: string) => {
    // Use provided sessionId or fall back to the currently selected one
    const sessionToTerminate = sessionId || whatsappSessionId

    if (!sessionToTerminate) {
      setWhatsappError('Please select a session ID')
      return
    }

    setIsTerminatingSession(true)
    setWhatsappError('')
    setWhatsappSuccess('')
    setShowConfirmDialog(false) // Hide confirmation dialog if it was shown

    try {
      const result = await callWhatsAppApi('terminateSession', sessionToTerminate)

      console.log('Terminate session result:', result)

      setWhatsappSuccess(`Session "${sessionToTerminate}" terminated successfully!`)

      // Clear the current session if it matches the terminated one
      if (sessionToTerminate === whatsappSessionId) {
        setWhatsappSessionId('')

        // Clear the QR code if it exists
        if (whatsappQrCode) {
          setWhatsappQrCode(null)
        }

        // Set status to indicate session is terminated
        setWhatsappStatus('TERMINATED')
      }

      // Update the session list after a short delay
      setTimeout(() => {
        getAllWhatsAppSessions()
      }, 1000)
    } catch (error) {
      console.error('Error terminating WhatsApp session:', error)
      setWhatsappError(error instanceof Error ? error.message : 'Failed to terminate session')
    } finally {
      setIsTerminatingSession(false)
    }
  }, [whatsappSessionId, whatsappQrCode, getAllWhatsAppSessions])



  // Clean up all intervals when component unmounts
  useEffect(() => {
    return () => {
      // Clean up server check interval
      if (serverCheckIntervalRef.current) {
        window.clearInterval(serverCheckIntervalRef.current)
        serverCheckIntervalRef.current = null
      }
    }
  }, [])

  return {
    whatsappSessionId,
    setWhatsappSessionId,
    whatsappQrCode,
    whatsappStatus,
    isLoadingQrCode,
    isRestartingSession,
    isSendingTestMessage,
    whatsappError,
    testMessageForm,
    setTestMessageForm,
    whatsappSuccess,
    isCheckingServer,
    serverStatus,
    availableSessionIds,
    availableSessions,
    getWhatsappQrCode,
    checkWhatsappSessionStatus,
    restartWhatsappSession,
    sendWhatsappTestMessage,
    checkServerAlive,
    // Auto-check server status
    autoCheckEnabled,
    setAutoCheckEnabled,
    // Confirmation dialog
    showConfirmDialog,
    setShowConfirmDialog,
    confirmMessage,
    confirmAction,
    // New session management functions
    newSessionId,
    setNewSessionId,
    isCreatingSession,
    isLoadingSessions,
    isTerminatingSession,
    createWhatsAppSession,
    getAllWhatsAppSessions,
    terminateWhatsAppSession,
    initiateTerminateSession
  }
}
