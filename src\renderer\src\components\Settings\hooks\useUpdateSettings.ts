import { useState, useEffect, useCallback } from 'react'
import { UpdateChannel } from '../types'
import { safeVersionToString } from '../utils'

// Define progress information type
interface ProgressInfo {
  percent: number
  bytesPerSecond: number
  total: number
  transferred: number
  averageSpeed?: number
  estimatedTimeRemaining?: string
  downloadedFormatted?: string
  totalFormatted?: string
  speedFormatted?: string
  elapsedTime?: string
}

// Define update information type
interface UpdateInfo {
  version: string
  downloadSize?: string
  files?: Array<{ url: string, size: number }>
  path?: string
  sha512?: string
  releaseDate?: string
  releaseName?: string
  releaseNotes?: string
  downloadTime?: string
}

// Define error information type
interface ErrorInfo {
  message: string
  stack?: string
  type?: string
  recoverable?: boolean
  suggestion?: string
}

// Define retry information type
interface RetryInfo {
  retryCount: number
  maxRetries: number
  retryDelay: number
  retryAt: string
}

export const useUpdateSettings = () => {
  const [currentChannel, setCurrentChannel] = useState<UpdateChannel>('release')
  const [isChangingChannel, setIsChangingChannel] = useState<boolean>(false)
  const [isCheckingUpdate, setIsCheckingUpdate] = useState<boolean>(false)
  const [isUpdateAvailable, setIsUpdateAvailable] = useState<boolean>(false)
  const [isDownloading, setIsDownloading] = useState<boolean>(false)
  const [updateVersion, setUpdateVersion] = useState<string | null>(null)
  const [currentVersion, setCurrentVersion] = useState<string>('')
  const [updateStatus, setUpdateStatus] = useState<string>('')
  const [progressInfo, setProgressInfo] = useState<ProgressInfo | null>(null)
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null)
  const [errorInfo, setErrorInfo] = useState<ErrorInfo | null>(null)
  const [retryInfo, setRetryInfo] = useState<RetryInfo | null>(null)
  const [showProgressDetails, setShowProgressDetails] = useState<boolean>(false)

  // Fetch current update channel and version info on component mount
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const electron = window.electron as any
        if (electron?.ipcRenderer?.invoke) {
          // Get current update channel
          const result = await electron.ipcRenderer.invoke('get-available-channels')
          if (result && result.currentChannel) {
            setCurrentChannel(result.currentChannel)
          }

          // Get current app version
          const versionResult = await electron.ipcRenderer.invoke('get-app-version')
          if (versionResult && versionResult.version) {
            setCurrentVersion(versionResult.version)
          } else if (electron.appVersion && electron.appVersion.version) {
            setCurrentVersion(electron.appVersion.version)
          }
        }
      } catch (error) {
        console.error('Error fetching initial data:', error)
      }
    }

    fetchInitialData()
  }, [])

  // Listen for update status changes
  useEffect(() => {
    const handleUpdateStatus = (_event: any, status: string, data?: any) => {
      setUpdateStatus(status)

      switch (status) {
        case 'checking-for-update':
          setIsCheckingUpdate(true)
          setIsUpdateAvailable(false)
          setIsDownloading(false)
          setProgressInfo(null)
          setErrorInfo(null)
          setRetryInfo(null)
          break

        case 'update-available':
          setIsCheckingUpdate(false)
          setIsUpdateAvailable(true)

          if (data) {
            // Store update info
            setUpdateInfo(data as UpdateInfo)

            // Ensure we're storing a string value for version
            if (data.version) {
              setUpdateVersion(
                typeof data.version === 'object'
                  ? data.version.version || String(data.version)
                  : String(data.version)
              )
            }
          }
          break

        case 'download-progress':
          // Update is downloading
          setIsDownloading(true)
          setProgressInfo(data as ProgressInfo)
          break

        case 'update-downloaded':
          // Update is ready to install
          setIsCheckingUpdate(false)
          setIsDownloading(false)

          // Update download info if available
          if (data) {
            setUpdateInfo(prev => ({
              ...prev,
              ...data
            } as UpdateInfo))
          }
          break

        case 'update-not-available':
          setIsCheckingUpdate(false)
          setIsUpdateAvailable(false)
          setIsDownloading(false)
          setProgressInfo(null)
          break

        case 'update-error':
          setIsCheckingUpdate(false)
          setIsDownloading(false)
          setErrorInfo(data as ErrorInfo)
          break

        case 'download-retry':
        case 'retry-scheduled':
          setRetryInfo(data as RetryInfo)
          break

        default:
          break
      }
    }

    // Listen for channel change notifications
    const handleChannelChanged = (_event: any, channel: UpdateChannel) => {
      setCurrentChannel(channel)
      setIsChangingChannel(false)

      // Check for updates after channel change
      handleCheckForUpdates()
    }

    // Set up event listeners
    const electron = window.electron as any
    if (electron?.ipcRenderer?.on) {
      electron.ipcRenderer.on('update-status', handleUpdateStatus)
      electron.ipcRenderer.on('update-channel-changed', handleChannelChanged)
    }

    return () => {
      // Clean up event listeners
      if (electron?.ipcRenderer?.removeListener) {
        electron.ipcRenderer.removeListener('update-status', handleUpdateStatus)
        electron.ipcRenderer.removeListener('update-channel-changed', handleChannelChanged)
      }
    }
  }, [])

  // Handle check for updates
  const handleCheckForUpdates = useCallback(() => {
    const electron = window.electron as any
    if (electron?.ipcRenderer?.send) {
      setIsCheckingUpdate(true)
      setIsUpdateAvailable(false) // Reset update available state
      setUpdateVersion(null) // Reset update version
      electron.ipcRenderer.send('check-for-updates', true)
    }
  }, [])

  // Handle change update channel
  const handleChangeChannel = useCallback((channel: UpdateChannel) => {
    if (channel === currentChannel) return

    const electron = window.electron as any
    if (electron?.ipcRenderer?.send) {
      setIsChangingChannel(true)
      electron.ipcRenderer.send('change-update-channel', channel)
    }
  }, [currentChannel])

  // Handle install update
  const handleInstallUpdate = useCallback(() => {
    const electron = window.electron as any
    if (electron?.ipcRenderer?.send) {
      electron.ipcRenderer.send('confirm-install')
    }
  }, [])

  // Toggle progress details visibility
  const toggleProgressDetails = useCallback(() => {
    setShowProgressDetails(prev => !prev)
  }, [])

  return {
    // State
    currentChannel,
    isChangingChannel,
    isCheckingUpdate,
    isUpdateAvailable,
    isDownloading,
    updateVersion,
    currentVersion,
    updateStatus,
    progressInfo,
    updateInfo,
    errorInfo,
    retryInfo,
    showProgressDetails,

    // Actions
    handleCheckForUpdates,
    handleChangeChannel,
    handleInstallUpdate,
    toggleProgressDetails,

    // Utilities
    safeVersionToString
  }
}
