<!doctype html>
<html class="custom-scrollbar">
  <head>
    <meta charset="UTF-8" />
    <title>Firearm Studio</title>
    <!-- Import Montserrat font from Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Content Security Policy -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' %WHATSAPP_API_URL%/ https://app.gunlicence.co.za/ https://dev.gunneryguns.com/ http://*********:3000; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: %WHATSAPP_API_URL%/; connect-src 'self' %WHATSAPP_API_URL%/ https://app.gunlicence.co.za/ https://dev.gunneryguns.com"
    />
    <style>
      /* Ensure scrollbar styling is applied immediately */
      ::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }
      ::-webkit-scrollbar-track {
        background: rgba(28, 25, 23, 0.6);
      }
      ::-webkit-scrollbar-thumb {
        background: rgb(249, 115, 22);
      }
      ::-webkit-scrollbar-thumb:hover {
        background: rgb(234, 88, 12);
      }

      html,
      body {
        margin: 0;
        padding: 0;
        height: 100%;
        overflow: hidden; /* Prevent double scrollbars */
        font-family: 'Montserrat', sans-serif;
      }

      #root {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      /* Critical drag region styles */
      .app-drag-region {
        -webkit-app-region: drag !important;
        -webkit-user-select: none;
        user-select: none;
      }

      .app-no-drag {
        -webkit-app-region: no-drag !important;
      }
    </style>
  </head>

  <body class="custom-scrollbar font-sans">
    <div id="root" class="custom-scrollbar font-sans"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
