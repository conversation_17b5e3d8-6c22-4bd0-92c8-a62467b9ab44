// helpers.ts - Helper functions for the Further Competency form

import { FurtherCompetencyData } from '../../../../types/FormData'

/**
 * Prepares form data for submission by ensuring all placeholder fields are properly set
 * @param formData The current form data
 * @returns Updated form data with all placeholder fields set
 */
export const prepareFormDataForSubmission = (
  formData: FurtherCompetencyData,
  templateUrl: string,
  templateName: string
): FurtherCompetencyData => {
  return {
    ...formData,
    // Competency Form specific fields
    tradeFirearm: formData.competencyType === 'trade',
    possessFirearm: formData.competencyType === 'possess',

    // Set selfLoadingT if selfLoading is true
    selfLoadingT: formData.selfLoading ? 'Self-Loading' : '',

    // Association membership fields
    f5a: formData.isMemberOfAssociation,
    f5b: !formData.isMemberOfAssociation,
    f6: formData.associationName || '',
    f7: formData.associationMembershipNumber || '',
    f8: formData.associationDateJoined || '',

    // Criminal history fields
    h5a: formData.offenseYes,
    h5b: formData.offenseNo,
    h51: formData.policeStation || '',
    h52: formData.caseNumber || '',
    h53: formData.charge || '',
    h54: formData.outcomeVerdict || '',

    h6a: formData.pendingCaseYes,
    h6b: formData.pendingCaseNo,
    h61: formData.pendingCasePoliceStation || '',
    h62: formData.pendingCaseCaseNumber || '',
    h6a3: formData.pendingCaseOffence || '',

    h7a: formData.lostStolenYes,
    h7b: formData.lostStolenNo,
    h71: formData.lostStolenPoliceStation || '',
    h72: formData.lostStolenCaseNumber || '',
    h73: formData.lostStolenCircumstances || '',
    h74: formData.lostStolenFirearmDetails || '',

    h8a: formData.investigationYes,
    h8b: formData.investigationNo,
    h81: formData.investigationPoliceStation || '',
    h82: formData.investigationCaseNumber || '',
    h83: formData.investigationCharge || '',
    h84: formData.investigationOutcome || '',

    h9a: formData.declaredUnfitYes,
    h9b: formData.declaredUnfitNo,
    h91: formData.declaredUnfitPoliceStation || '',
    h92: formData.declaredUnfitCaseNumber || '',
    h93: formData.declaredUnfitCharge || '',
    h94: formData.declaredUnfitDate || '',
    h95: formData.declaredUnfitPeriod || '',

    h10a: formData.confiscatedYes,
    h10b: formData.confiscatedNo,
    h101: formData.confiscatedPoliceStation || '',
    h102: formData.confiscatedCaseNumber || '',
    h103: formData.confiscatedCircumstances || '',
    h104: formData.confiscatedOutcome || '',

    // Add template info
    templateUrl,
    templateName
  }
}
