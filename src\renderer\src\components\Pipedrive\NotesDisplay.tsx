import React from 'react';
import { parseNotes } from '../../utils/noteUtils';

interface NotesDisplayProps {
  notes: string | null;
  maxHeight?: string;
}

const NotesDisplay: React.FC<NotesDisplayProps> = ({ notes, maxHeight = '150px' }) => {
  const parsedNotes = parseNotes(notes);
  
  if (!parsedNotes.length) {
    return (
      <div className="text-stone-500 text-xs italic">
        No notes yet
      </div>
    );
  }
  
  return (
    <div 
      className="bg-stone-700/50 border border-stone-600/50 rounded-md p-2 overflow-y-auto custom-scrollbar"
      style={{ maxHeight }}
    >
      <div className="space-y-2">
        {parsedNotes.map((note, index) => (
          <div key={index} className="text-xs">
            {note.date && (
              <span className="text-orange-400 font-medium">[{note.date}]</span>
            )}
            <span className="text-stone-300 ml-1">{note.content}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NotesDisplay;
