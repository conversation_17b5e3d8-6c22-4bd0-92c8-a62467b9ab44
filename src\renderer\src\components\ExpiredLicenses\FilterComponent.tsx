import React from 'react'
import { GunLicense } from './types'

interface FilterComponentProps {
  notificationFilter: 'all' | 'active' | 'paused'
  setNotificationFilter: (filter: 'all' | 'active' | 'paused') => void
  expiredLicenses: GunLicense[]
}

export const FilterComponent: React.FC<FilterComponentProps> = ({
  notificationFilter,
  setNotificationFilter,
  expiredLicenses
}) => {
  return (
    <div className="bg-stone-800/70 p-4 rounded-lg shadow-lg mb-4">
      <div className="flex flex-col gap-4">
        {/* Total Expiring Licenses */}
        <div className="flex items-center justify-between mb-3 bg-stone-700/20 p-2 rounded-lg border border-stone-600/20">
          <div className="text-white text-sm">Total Expiring:</div>
          <div className="text-white font-bold bg-orange-500/10 px-3 py-1 rounded-full border border-orange-500/30 text-base">
            {expiredLicenses.length}
          </div>
        </div>

        {/* Filter Label */}
        <div className="flex items-center">
          <div className="text-stone-300 font-medium">Filter Licenses:</div>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={() => setNotificationFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between ${
              notificationFilter === 'all'
                ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-md shadow-orange-500/20'
                : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
            }`}
          >
            <span>All Licenses</span>
            <span className="ml-2 px-2 py-0.5 bg-stone-800/40 rounded-full text-xs">
              {expiredLicenses.length}
            </span>
          </button>
          <button
            onClick={() => setNotificationFilter('active')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between ${
              notificationFilter === 'active'
                ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-md shadow-green-500/20'
                : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
            }`}
          >
            <span className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-green-400 mr-2" />
              Active
            </span>
            <span className="ml-2 px-2 py-0.5 bg-stone-800/40 rounded-full text-xs">
              {expiredLicenses.filter((license) => license.toggle_notifications).length}
            </span>
          </button>
          <button
            onClick={() => setNotificationFilter('paused')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between ${
              notificationFilter === 'paused'
                ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-md shadow-orange-500/20'
                : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
            }`}
          >
            <span className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-orange-400 mr-2" />
              Paused
            </span>
            <span className="ml-2 px-2 py-0.5 bg-stone-800/40 rounded-full text-xs">
              {expiredLicenses.filter((license) => !license.toggle_notifications).length}
            </span>
          </button>
        </div>
      </div>
    </div>
  )
}
