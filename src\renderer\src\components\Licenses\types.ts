import { License } from '../../types'

export interface FormState {
  type: 'license' | null
  isOpen: boolean
  selectedLicense: License | null
  selectedClientId: string | null
}

export interface DeleteDialogState {
  isOpen: boolean
  licenseId: string | null
}

export interface ToastState {
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
}

export interface FilterState {
  activeFilter: 'all' | 'expiring' | 'expired'
}

export interface PaginationState {
  page: number
  perPage: number
}

export interface VirtualizationResult<T> {
  items: T[]
  total: number
}

export interface LicenseSearchTips {
  title: string
  items: string[]
}
