import React, { useMemo } from 'react'
import { PipedriveLogEntry } from './hooks/useLogsSettings'
import { Activity, BarChart2, Briefcase, FileText, GitBranch, ListOrdered, User, Users } from 'lucide-react'

interface PipedriveLogVisualizationProps {
  logs: PipedriveLogEntry[]
  onFilterChange: (filter: any) => void
}

const COLORS = {
  create: '#10b981', // green
  update: '#3b82f6', // blue
  delete: '#ef4444', // red
  move: '#f59e0b',   // amber
  upload: '#8b5cf6', // purple
  download: '#06b6d4' // cyan
}

const PipedriveLogVisualization: React.FC<PipedriveLogVisualizationProps> = ({ logs, onFilterChange }) => {
  // Prepare data for action type chart
  const actionTypeData = useMemo(() => {
    const counts: Record<string, number> = {}
    logs.forEach(log => {
      const actionType = log.action_type || 'unknown'
      counts[actionType] = (counts[actionType] || 0) + 1
    })
    return Object.entries(counts)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
  }, [logs])

  // Prepare data for entity type chart
  const entityTypeData = useMemo(() => {
    const counts: Record<string, number> = {}
    logs.forEach(log => {
      const entityType = log.entity_type || 'unknown'
      counts[entityType] = (counts[entityType] || 0) + 1
    })
    return Object.entries(counts)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
  }, [logs])

  // Prepare data for user activity chart
  const userActivityData = useMemo(() => {
    const counts: Record<string, number> = {}
    logs.forEach(log => {
      const userName = log.user_name || log.user_id?.substring(0, 8) || 'unknown'
      counts[userName] = (counts[userName] || 0) + 1
    })
    return Object.entries(counts)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 5) // Top 5 users
  }, [logs])

  // Prepare data for activity timeline
  const timelineData = useMemo(() => {
    const counts: Record<string, Record<string, number>> = {}

    // Get date range (last 7 days)
    const today = new Date()
    const dates = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      dates.push(date.toISOString().split('T')[0])
    }

    // Initialize counts for all dates and action types
    const actionTypes = [...new Set(logs.map(log => log.action_type))]
    dates.forEach(date => {
      counts[date] = {}
      actionTypes.forEach(type => {
        if (type) counts[date][type] = 0
      })
    })

    // Count actions by date
    logs.forEach(log => {
      if (!log.action_type || !log.created_at) return

      const date = new Date(log.created_at).toISOString().split('T')[0]
      if (counts[date]) {
        counts[date][log.action_type] = (counts[date][log.action_type] || 0) + 1
      }
    })

    // Convert to chart data format
    return dates.map(date => {
      const result: Record<string, any> = { date }
      actionTypes.forEach(type => {
        if (type) result[type] = counts[date][type] || 0
      })
      return result
    })
  }, [logs])

  // Calculate total actions
  const totalActions = useMemo(() => {
    return logs.length
  }, [logs])

  // Get entity icon
  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'pipeline':
        return <GitBranch size={16} />
      case 'stage':
        return <ListOrdered size={16} />
      case 'deal':
        return <Briefcase size={16} />
      case 'document':
        return <FileText size={16} />
      default:
        return <Activity size={16} />
    }
  }

  // Format date for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString()
  }

  return (
    <div className="space-y-6 mt-4 w-full max-w-full">
      <h3 className="text-white text-sm font-medium">Pipedrive Activity Visualization</h3>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 w-full">
        {/* Action Type Distribution */}
        <div className="bg-stone-800/60 p-4 rounded-md border border-stone-700/50">
          <h4 className="text-white text-xs font-medium mb-2">Action Type Distribution</h4>
          <div className="space-y-2">
            {actionTypeData.map(item => (
              <div
                key={item.name}
                className="flex items-center cursor-pointer hover:bg-stone-700/30 p-2 rounded"
                onClick={() => onFilterChange({ actionType: item.name })}
              >
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: COLORS[item.name as keyof typeof COLORS] || '#a1a1aa' }}
                />
                <div className="flex-1 text-sm text-white truncate">{item.name}</div>
                <div className="text-sm font-medium text-white">{item.value}</div>
                <div className="text-xs text-stone-400 ml-2">
                  ({((item.value / totalActions) * 100).toFixed(1)}%)
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Entity Type Distribution */}
        <div className="bg-stone-800/60 p-4 rounded-md border border-stone-700/50">
          <h4 className="text-white text-xs font-medium mb-2">Entity Type Distribution</h4>
          <div className="space-y-2">
            {entityTypeData.map(item => (
              <div
                key={item.name}
                className="flex items-center cursor-pointer hover:bg-stone-700/30 p-2 rounded"
                onClick={() => onFilterChange({ entityType: item.name })}
              >
                <div className="mr-2 text-stone-300">
                  {getEntityIcon(item.name)}
                </div>
                <div className="flex-1 text-sm text-white truncate">{item.name}</div>
                <div className="text-sm font-medium text-white">{item.value}</div>
                <div className="text-xs text-stone-400 ml-2">
                  ({((item.value / totalActions) * 100).toFixed(1)}%)
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Users */}
        <div className="bg-stone-800/60 p-4 rounded-md border border-stone-700/50">
          <h4 className="text-white text-xs font-medium mb-2">Top Users by Activity</h4>
          <div className="space-y-2">
            {userActivityData.map(item => (
              <div
                key={item.name}
                className="flex items-center cursor-pointer hover:bg-stone-700/30 p-2 rounded"
                onClick={() => onFilterChange({ userId: item.name })}
              >
                <User size={16} className="mr-2 text-stone-300" />
                <div className="flex-1 text-sm text-white truncate">{item.name}</div>
                <div className="text-sm font-medium text-white">{item.value}</div>
                <div className="text-xs text-stone-400 ml-2">
                  ({((item.value / totalActions) * 100).toFixed(1)}%)
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Activity Timeline */}
        <div className="bg-stone-800/60 p-4 rounded-md border border-stone-700/50">
          <h4 className="text-white text-xs font-medium mb-2">Activity Timeline (Last 7 Days)</h4>
          <div className="space-y-2">
            {timelineData.map(day => {
              const totalForDay = Object.entries(day)
                .filter(([key]) => key !== 'date')
                .reduce((sum, [_, value]) => sum + (value as number), 0)

              return (
                <div key={day.date} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-white">{formatDate(day.date)}</div>
                    <div className="text-xs text-stone-400">{totalForDay} actions</div>
                  </div>
                  <div className="h-6 bg-stone-700/30 rounded-full overflow-hidden flex">
                    {Object.entries(day)
                      .filter(([key]) => key !== 'date' && (day[key] as number) > 0)
                      .map(([type, count]) => (
                        <div
                          key={type}
                          className="h-full flex items-center justify-center text-xs text-white font-medium hover:opacity-80 cursor-pointer"
                          style={{
                            width: `${((count as number) / totalForDay) * 100}%`,
                            backgroundColor: COLORS[type as keyof typeof COLORS] || '#a1a1aa',
                            minWidth: '20px'
                          }}
                          onClick={() => onFilterChange({ actionType: type, startDate: day.date, endDate: day.date })}
                          title={`${type}: ${count}`}
                        >
                          {(count as number) > 1 ? count : ''}
                        </div>
                      ))
                    }
                  </div>
                </div>
              )
            })}
            <div className="flex flex-wrap gap-2 mt-2">
              {Object.entries(COLORS).map(([type, color]) => (
                <div key={type} className="flex items-center text-xs">
                  <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: color }} />
                  <span className="text-white">{type}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PipedriveLogVisualization
