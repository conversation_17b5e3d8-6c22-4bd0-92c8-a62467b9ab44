import React, { useState, useEffect, useMemo } from 'react'
import { FurtherCompetencyData, initialFurtherCompetencyData } from '../../../types/FormData'
import {
  FormLayout,
  FormSectionType
} from '../../FormComponents'

// Import section components
import PersonalInfo from './sections/PersonalInfo'
import ProfessionalInfo from './sections/ProfessionalInfo'
import FirearmTypes from './sections/FirearmTypes'
import PreviousCompetency from './sections/PreviousCompetency'
import CriminalHistory from './sections/CriminalHistory'
import TemplateStatus from './sections/TemplateStatus'

// Import utility functions and types
import { validateStep, validateFormData } from './utils/validation'
import { prepareFormDataForSubmission } from './utils/helpers'
import { TEMPLATE_URL, TEMPLATE_NAME, FORM_SECTIONS } from './utils/constants'
import { ValidationStatus, TemplateStatus as TemplateStatusType, FurtherCompFormProps } from './utils/types'

/**
 * FurtherComp - Further Competency Application Form Component
 *
 * A multi-step form for processing further competency applications
 * that passes data to DocScript for document generation.
 */
export default function FurtherComp({ onSubmit }: FurtherCompFormProps): JSX.Element {
  // Form state
  const [formData, setFormData] = useState<FurtherCompetencyData>(initialFurtherCompetencyData)
  const [currentStep, setCurrentStep] = useState<number>(1)
  const [, setIsFormValid] = useState<boolean>(false)

  // Template state
  const [templateStatus, setTemplateStatus] = useState<TemplateStatusType>('loading')
  const [templateError, setTemplateError] = useState<string | null>(null)

  // Submission state
  const [submissionStatus, setSubmissionStatus] = useState<ValidationStatus>('idle')
  const [submissionMessage, setSubmissionMessage] = useState<string | null>(null)

  // Form sections definition
  const sections: FormSectionType[] = useMemo(() => FORM_SECTIONS, [])

  // Check template availability on component mount
  useEffect(() => {
    const abortController = new AbortController()

    const checkTemplate = async () => {
      setTemplateStatus('loading')
      setTemplateError(null)

      try {
        const response = await fetch(TEMPLATE_URL, {
          method: 'HEAD',
          signal: abortController.signal
        })

        if (!response.ok) {
          throw new Error(`Template not accessible: ${response.statusText}`)
        }

        setTemplateStatus('ready')
      } catch (err) {
        if (!abortController.signal.aborted) {
          console.error('Error checking template:', err)
          setTemplateStatus('error')
          setTemplateError('Could not access the template file. Please try again later.')
        }
      }
    }

    checkTemplate()
    return () => abortController.abort()
  }, [])

  // Validate form when data or step changes
  useEffect(() => {
    const isValid = validateStep(formData, currentStep)
    setIsFormValid(isValid)
  }, [formData, currentStep])

  // Helper function to update form data
  const updateFormData = (newData: Partial<FurtherCompetencyData>) => {
    setFormData(prev => ({ ...prev, ...newData }))

    // Clear any error messages when user makes changes
    if (submissionStatus === 'error') {
      setSubmissionStatus('idle')
      setSubmissionMessage(null)
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    setSubmissionStatus('processing')
    setSubmissionMessage('Validating data...')

    try {
      // Validate form data
      const validationError = validateFormData(formData)
      if (validationError) {
        setSubmissionStatus('error')
        setSubmissionMessage(validationError)
        return
      }

      // Prepare form data for submission
      const updatedFormData = prepareFormDataForSubmission(formData, TEMPLATE_URL, TEMPLATE_NAME)

      // Update status and notify parent
      setSubmissionStatus('success')
      setSubmissionMessage('Form submitted successfully!')

      // Send data to DocScript component for processing
      onSubmit(updatedFormData)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setSubmissionStatus('error')
      setSubmissionMessage(`Error processing form: ${errorMessage}`)
    }
  }

  // Render the current step based on section ID
  const renderCurrentStep = () => {
    const sectionId = sections[currentStep - 1].id

    switch (sectionId) {
      case 'personal':
        return <PersonalInfo formData={formData} updateFormData={updateFormData} />
      case 'professional':
        return <ProfessionalInfo formData={formData} updateFormData={updateFormData} />
      case 'firearms':
        return <FirearmTypes formData={formData} updateFormData={updateFormData} />
      case 'previous':
        return <PreviousCompetency formData={formData} updateFormData={updateFormData} />
      case 'criminal':
        return <CriminalHistory formData={formData} updateFormData={updateFormData} />
      default:
        return null
    }
  }

  // Handle cancel submission
  const onCancelSubmission = () => {
    setSubmissionStatus('idle')
    setSubmissionMessage(null)
  }

  // Main component render
  return (
    <FormLayout
      title="Further Competency Application"
      sections={sections}
      currentStep={currentStep}
      setCurrentStep={setCurrentStep}
      onSubmit={handleSubmit}
      submissionStatus={submissionStatus}
      submissionMessage={submissionMessage}
      onCancelSubmission={onCancelSubmission}
      isDocScriptForm={true}
    >
      {templateStatus === 'error' && (
        <TemplateStatus
          status={templateStatus}
          templateName={TEMPLATE_NAME}
          error={templateError}
        />
      )}
      {renderCurrentStep()}
    </FormLayout>
  )
}
