import { useEffect, useState } from 'react'
import logo from '../assets/logo.png'

interface LoadingScreenProps {
  updateStatus?: {
    status: string
    data?: any
  } | null
}

const LoadingScreen = ({ updateStatus }: LoadingScreenProps = {}): React.JSX.Element => {
  const [statusMessage, setStatusMessage] = useState<string>('Initializing...')
  const [downloadProgress, setDownloadProgress] = useState<number | null>(null)
  const [showLoadingAnimation, setShowLoadingAnimation] = useState(true)
  const [subMessage, setSubMessage] = useState<string>('')

  // Update message based on update status
  useEffect(() => {
    if (!updateStatus) {
      setStatusMessage('Initializing...')
      setSubMessage('Please wait while we check for updates')
      setDownloadProgress(null)
      setShowLoadingAnimation(true)
      return
    }

    switch (updateStatus.status) {
      case 'checking-for-update':
        setStatusMessage('Checking for updates')
        setSubMessage('Connecting to update server...')
        setDownloadProgress(null)
        setShowLoadingAnimation(true)
        break
      case 'update-available':
        setStatusMessage('Update available')
        setSubMessage('Preparing to download...')
        setDownloadProgress(0)
        setShowLoadingAnimation(false)
        break
      case 'download-progress':
        const progressData = updateStatus.data as
          | { percent: number; transferred: number; total: number }
          | undefined
        if (progressData?.percent) {
          const percent = Math.round(progressData.percent)
          const transferred = Math.round((progressData.transferred / 1024 / 1024) * 100) / 100
          const total = Math.round((progressData.total / 1024 / 1024) * 100) / 100
          setStatusMessage(`Downloading update (${percent}%)`)
          setSubMessage(`${transferred} MB / ${total} MB`)
          setDownloadProgress(percent)
        } else {
          setStatusMessage('Downloading update')
          setSubMessage('Calculating download size...')
        }
        setShowLoadingAnimation(false)
        break
      case 'update-downloaded':
        setStatusMessage('Update ready')
        setSubMessage('The application will restart to install the update')
        setDownloadProgress(100)
        setShowLoadingAnimation(false)
        break
      case 'update-not-available':
        setStatusMessage('Loading application')
        setSubMessage('You are using the latest version')
        setDownloadProgress(null)
        setShowLoadingAnimation(true)
        break
      case 'update-error':
        setStatusMessage('Update check failed')
        setSubMessage('Continuing with current version')
        setDownloadProgress(null)
        setShowLoadingAnimation(true)
        break
      default:
        setStatusMessage('Loading')
        setSubMessage('Please wait...')
        setDownloadProgress(null)
        setShowLoadingAnimation(true)
    }
  }, [updateStatus])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-stone-900 via-stone-900 to-stone-950">
      <div className="relative mb-12 group">
        <div className="absolute -inset-1 bg-gradient-to-r from-orange-600 to-orange-900 rounded-full blur opacity-20 group-hover:opacity-30 transition duration-1000 group-hover:duration-200" />
        <img
          src={logo}
          alt="Firearm Studio Logo"
          className={`relative h-32 mx-auto drop-shadow-2xl ${showLoadingAnimation ? 'animate-loading-pulse' : ''}`}
        />
      </div>

      {/* Progress bar */}
      <div className="w-64 h-2 bg-stone-800/50 rounded-full overflow-hidden backdrop-blur-sm ring-1 ring-stone-800/60">
        <div
          className={`h-full rounded-full transition-all duration-300 ease-out shadow-lg ${
            downloadProgress !== null
              ? 'bg-gradient-to-r from-green-500 via-green-400 to-green-500'
              : 'bg-gradient-to-r from-orange-500 via-orange-600 to-orange-500'
          }`}
          style={{
            width:
              downloadProgress !== null
                ? `${downloadProgress}%`
                : showLoadingAnimation
                  ? '100%'
                  : '0%',
            backgroundSize: '200% 100%',
            animation: showLoadingAnimation ? 'shimmer 2s linear infinite' : 'none',
            transition: showLoadingAnimation ? 'none' : 'width 0.3s ease-out'
          }}
        />
      </div>

      {/* Status message */}
      <p className="mt-6 text-stone-400 text-sm font-medium tracking-wide">
        {statusMessage}
        <span className="animate-pulse">...</span>
      </p>

      {/* Sub message */}
      <p className="mt-2 text-stone-500 text-xs">{subMessage}</p>

      {/* Download progress text - only shown during update */}
      {downloadProgress !== null && (
        <div className="mt-2 flex items-center justify-center">
          <div className="text-stone-400 text-xs font-medium">
            {downloadProgress < 100
              ? `Download progress: ${downloadProgress}%`
              : 'Download complete - installing...'}
          </div>
        </div>
      )}

      {/* Version display */}
      <div className="absolute bottom-4 text-stone-500 text-xs flex flex-col items-center">
        <span>Application Version</span>
        {updateStatus?.status === 'update-error' && (
          <span className="mt-1 text-red-400">Error checking for updates</span>
        )}
      </div>
    </div>
  )
}

export default LoadingScreen
