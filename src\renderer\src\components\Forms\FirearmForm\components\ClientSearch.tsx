import React from 'react';
import { FormField } from '../../../FormComponents';
import { DashboardIcons } from '../../../icons/DashboardIcons';
import { SimpleClient } from '../hooks';

interface ClientSearchProps {
  clientSearchTerm: string;
  isLoadingClients: boolean;
  clients: SimpleClient[];
  selectedClient: SimpleClient | null;
  onClientSearchChange: (searchTerm: string) => void;
  onClientSelect: (client: SimpleClient) => void;
  onClientRemove: () => void;
}

export const ClientSearch: React.FC<ClientSearchProps> = ({
  clientSearchTerm,
  isLoadingClients,
  clients,
  selectedClient,
  onClientSearchChange,
  onClientSelect,
  onClientRemove
}) => {
  if (selectedClient) {
    return (
      <div className="mb-2 p-2 bg-orange-500/10 border border-orange-500/20 rounded-lg">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-semibold text-white text-sm">
              {selectedClient.first_name?.toUpperCase() || ''} {selectedClient.last_name?.toUpperCase() || ''}
            </h3>
            <p className="text-stone-400 text-xs">ID: {selectedClient.id_number?.toUpperCase() || 'N/A'}</p>
            <p className="text-stone-400 text-xs">{selectedClient.email?.toUpperCase() || ''}</p>
          </div>
          <button
            type="button"
            onClick={onClientRemove}
            className="text-orange-400 hover:text-orange-300"
          >
            <DashboardIcons.Close className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <FormField
        label="Search Client by Name or ID"
        name="client-search"
        value={clientSearchTerm}
        onChange={(e) => onClientSearchChange(e.target.value)}
        placeholder="Enter name or ID number"
      />

      {isLoadingClients && (
        <div className="flex items-center justify-center p-1 mt-1">
          <DashboardIcons.Spinner className="w-4 h-4 animate-spin text-orange-500" />
          <span className="ml-1 text-stone-400 text-xs">Searching clients...</span>
        </div>
      )}

      {clients.length > 0 && clientSearchTerm && (
        <div className="mt-1 bg-stone-700/50 rounded-lg border border-stone-600/50 max-h-48 overflow-y-auto">
          {clients.map(client => (
            <div
              key={client.id}
              onClick={() => onClientSelect(client)}
              className="p-2 hover:bg-orange-500/20 cursor-pointer border-b border-stone-600/30 last:border-0 transition-colors"
            >
              <p className="text-white font-medium text-xs">
                {client.first_name?.toUpperCase() || ''} {client.last_name?.toUpperCase() || ''}
              </p>
              <p className="text-stone-400 text-xs">ID: {client.id_number?.toUpperCase() || 'N/A'}</p>
            </div>
          ))}
        </div>
      )}

      {clientSearchTerm && clients.length === 0 && !isLoadingClients && (
        <p className="mt-1 text-stone-400 text-xs">No clients found. Try a different search term.</p>
      )}
    </div>
  );
};
