/**
 * Generate initials from a full name
 * @param name Full name to generate initials from
 * @returns Initials in the format "J.D."
 */
export const generateInitials = (name: string): string => {
  if (!name) return ''
  
  return name
    .split(' ')
    .map(part => part.charAt(0).toUpperCase())
    .join('.')
    .concat('.')
}

/**
 * Extract information from South African ID number
 * @param idNumber South African ID number (13 digits)
 * @returns Object containing extracted information
 */
export const extractFromIdNumber = (idNumber: string): {
  birthDate: string
  age: number
  gender: 'male' | 'female'
} => {
  // Default values
  const defaultResult = {
    birthDate: '',
    age: 0,
    gender: 'male' as const
  }

  // Validate ID number
  if (!idNumber || idNumber.length !== 13 || !/^\d+$/.test(idNumber)) {
    return defaultResult
  }

  try {
    // Extract year, month, day
    const year = parseInt(idNumber.substring(0, 2))
    const month = parseInt(idNumber.substring(2, 4))
    const day = parseInt(idNumber.substring(4, 6))
    
    // Determine century
    const currentYear = new Date().getFullYear()
    const century = year > (currentYear % 100) ? 1900 : 2000
    const fullYear = century + year
    
    // Create date string
    const birthDate = `${fullYear}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
    
    // Calculate age
    const today = new Date()
    const birthDateObj = new Date(birthDate)
    let age = today.getFullYear() - birthDateObj.getFullYear()
    const monthDiff = today.getMonth() - birthDateObj.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
      age--
    }
    
    // Determine gender (7th digit >= 5 for male, < 5 for female)
    const genderDigit = parseInt(idNumber.substring(6, 7))
    const gender = genderDigit >= 5 ? 'male' : 'female'
    
    return {
      birthDate,
      age,
      gender
    }
  } catch (error) {
    console.error('Error extracting data from ID number:', error)
    return defaultResult
  }
}
