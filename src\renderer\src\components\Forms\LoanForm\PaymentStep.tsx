import React from 'react'
import { FormSection, FormField } from '../../FormComponents'
import { DashboardIcons } from '../../icons/DashboardIcons'
import { LoanFormState } from './types'

interface PaymentStepProps {
  formData: LoanFormState
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void
  onCheckboxChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  errors: Record<string, string>
}

const PaymentStep: React.FC<PaymentStepProps> = ({
  formData,
  onChange,
  onCheckboxChange,
  errors
}) => {
  return (
    <FormSection title="Payment Terms">
      <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Interest Rate (%)"
          name="interest_rate"
          type="number"
          value={formData.interest_rate?.toString() || '0'}
          onChange={onChange}
          min="0"
          step="0.1"
          error={errors.interest_rate}
        />
        
        <FormField
          label="Payment Due Date"
          name="payment_due_date"
          type="date"
          value={formData.payment_due_date || ''}
          onChange={onChange}
          error={errors.payment_due_date}
        />
        
        <FormField
          label="Loan Term (Months)"
          name="loan_term"
          type="number"
          value={formData.loan_term?.toString() || '12'}
          onChange={onChange}
          min="1"
          step="1"
          error={errors.loan_term}
        />
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-stone-300 mb-2">Status</label>
          <select
            name="status"
            value={formData.status || 'active'}
            onChange={onChange}
            className="w-full bg-stone-700/50 border border-stone-600/50 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
          >
            <option value="active">Active</option>
            <option value="paid">Paid</option>
            <option value="overdue">Overdue</option>
            <option value="defaulted">Defaulted</option>
          </select>
        </div>
        
        <FormField
          label="Remaining Balance"
          name="remaining_balance"
          type="number"
          value={formData.remaining_balance?.toString() || '0'}
          onChange={onChange}
          min="0"
          step="0.01"
        />
        
        {/* Notification toggle checkbox */}
        <div className="col-span-2 mb-4">
          <div className="p-3 rounded-lg bg-stone-700/30 border border-stone-600/50">
            <div className="flex items-center gap-3 mb-2">
              <input
                type="checkbox"
                id="pause_notifications"
                name="pause_notifications"
                checked={formData.pause_notifications || false}
                onChange={onCheckboxChange}
                className="w-5 h-5 rounded text-orange-500 focus:ring-orange-500 bg-stone-700 border-stone-500"
              />
              <label htmlFor="pause_notifications" className="text-white cursor-pointer select-none flex items-center gap-2 font-medium">
                {formData.pause_notifications ? 'Notifications disabled' : 'Notifications enabled'}
                {formData.pause_notifications ? (
                  <DashboardIcons.BellOff className="w-4 h-4 text-red-400" />
                ) : (
                  <DashboardIcons.Bell className="w-4 h-4 text-blue-400" />
                )}
              </label>
            </div>
            
            <div className={`text-xs ml-8 ${formData.pause_notifications ? 'text-red-400/80' : 'text-stone-400'}`}>
              {formData.pause_notifications 
                ? "No automatic Email/WhatsApp notifications will be sent to this client for payment reminders or any other loan-related messages."
                : "Client will receive WhatsApp notifications for payment reminders and important loan-related messages."
              }
            </div>
          </div>
        </div>
        
        {/* Add notes field that spans full width */}
        <div className="col-span-2">
          <FormField
            label="Notes"
            name="notes"
            type="textarea"
            value={formData.notes || ''}
            onChange={onChange}
            placeholder="Enter any additional notes about this loan..."
            rows={4}
          />
        </div>
      </div>
    </FormSection>
  )
}

export default PaymentStep
