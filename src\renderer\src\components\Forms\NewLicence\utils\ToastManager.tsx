import React, { useState, createContext, useContext } from 'react'
import Toast from './Toast'

interface ToastOptions {
  message: string
  type?: 'success' | 'error' | 'info'
  duration?: number
}

interface ToastItem extends ToastOptions {
  id: number
}

interface ToastContextType {
  showToast: (options: ToastOptions) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

/**
 * Toast Manager Provider Component
 */
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastItem[]>([])
  const [nextId, setNextId] = useState(1)

  const showToast = (options: ToastOptions) => {
    const id = nextId
    setToasts((prevToasts) => [...prevToasts, { ...options, id }])
    setNextId(id + 1)
  }

  const removeToast = (id: number) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id))
  }

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            message={toast.message}
            type={toast.type}
            duration={toast.duration}
            onClose={() => removeToast(toast.id)}
          />
        ))}
      </div>
    </ToastContext.Provider>
  )
}

/**
 * Hook to use the toast manager
 */
export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

/**
 * Standalone function to show a toast without using the context
 * This is useful for showing toasts from non-React code
 */
let toastContainer: HTMLDivElement | null = null
let toastCount = 0

export const showToast = (options: ToastOptions): void => {
  // Create toast container if it doesn't exist
  if (!toastContainer) {
    toastContainer = document.createElement('div')
    toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2'
    document.body.appendChild(toastContainer)
  }

  // Create toast element
  const toastId = `toast-${++toastCount}`
  const toastElement = document.createElement('div')
  toastElement.id = toastId
  toastElement.className = `${
    options.type === 'error'
      ? 'bg-red-500'
      : options.type === 'info'
      ? 'bg-purple-500'
      : 'bg-green-500'
  } text-white px-4 py-3 rounded-lg shadow-lg flex items-center space-x-3 animate-fade-in-down`

  // Add icon based on type
  const iconSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
  iconSvg.setAttribute('class', 'w-5 h-5 text-white')
  iconSvg.setAttribute('fill', 'none')
  iconSvg.setAttribute('stroke', 'currentColor')
  iconSvg.setAttribute('viewBox', '0 0 24 24')
  
  const iconPath = document.createElementNS('http://www.w3.org/2000/svg', 'path')
  iconPath.setAttribute('stroke-linecap', 'round')
  iconPath.setAttribute('stroke-linejoin', 'round')
  iconPath.setAttribute('stroke-width', '2')
  
  if (options.type === 'error') {
    iconPath.setAttribute('d', 'M6 18L18 6M6 6l12 12')
  } else if (options.type === 'info') {
    iconPath.setAttribute('d', 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z')
  } else {
    iconPath.setAttribute('d', 'M5 13l4 4L19 7')
  }
  
  iconSvg.appendChild(iconPath)
  
  const iconContainer = document.createElement('div')
  iconContainer.className = 'flex-shrink-0'
  iconContainer.appendChild(iconSvg)
  toastElement.appendChild(iconContainer)

  // Add message
  const messageElement = document.createElement('div')
  messageElement.className = 'flex-1'
  messageElement.textContent = options.message
  toastElement.appendChild(messageElement)

  // Add close button
  const closeButton = document.createElement('button')
  closeButton.className = 'flex-shrink-0 ml-2 text-white hover:text-gray-200 transition-colors'
  closeButton.innerHTML = `
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
    </svg>
  `
  closeButton.onclick = () => {
    toastContainer?.removeChild(toastElement)
  }
  toastElement.appendChild(closeButton)

  // Add to container
  toastContainer.appendChild(toastElement)

  // Auto-remove after duration
  setTimeout(() => {
    if (toastContainer?.contains(toastElement)) {
      toastContainer.removeChild(toastElement)
    }
  }, options.duration || 3000)
}
