import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'
import { isValidSerialNumber } from '../utils/helpers'

/**
 * Firearm Details section component for E350 Information form
 */
const FirearmDetails: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    updateFormData({ [name]: checked })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">FIREARM TYPE</h3>

      <FormSection title="Firearm Type" subtitle="Select the type of firearm">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="pistol"
              name="pistol"
              checked={formData.pistol || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="pistol" className="ml-2 block text-sm text-stone-300">
              HANDGUN
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="rifle"
              name="rifle"
              checked={formData.rifle || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="rifle" className="ml-2 block text-sm text-stone-300">
              RIFLE
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="shotgun"
              name="shotgun"
              checked={formData.shotgun || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="shotgun" className="ml-2 block text-sm text-stone-300">
              SHOTGUN
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="revolver"
              name="revolver"
              checked={formData.revolver || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="revolver" className="ml-2 block text-sm text-stone-300">
              REVOLVER
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="combination"
              name="combination"
              checked={formData.combination || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="combination" className="ml-2 block text-sm text-stone-300">
              COMBINATION
            </label>
          </div>

          <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
            <input
              type="checkbox"
              id="carbine"
              name="carbine"
              checked={formData.carbine || false}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="carbine" className="ml-2 block text-sm text-stone-300">
              CARBINE
            </label>
          </div>
        </div>
      </FormSection>

      <h3 className="text-lg font-semibold text-white mb-2 mt-4">FIREARM DETAILS</h3>
      <FormSection title="Firearm Specifications" subtitle="Enter the firearm's details">
        <div className="space-y-3">
          {/* Make, Model, Serial Number, and Caliber in one row */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Make</label>
                <input
                  type="text"
                  name="make"
                  value={formData.make || ''}
                  onChange={handleChange}
                  placeholder="Firearm Make"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Model</label>
                <input
                  type="text"
                  name="model"
                  value={formData.model || ''}
                  onChange={handleChange}
                  placeholder="Firearm Model"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Serial Number</label>
                <input
                  type="text"
                  name="serialNumber"
                  value={formData.serialNumber || ''}
                  onChange={handleChange}
                  placeholder="Serial Number"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
                {formData.serialNumber && !isValidSerialNumber(formData.serialNumber) && (
                  <p className="text-xs text-red-400 mt-1">
                    Serial number must be at least 4 characters
                  </p>
                )}
              </div>
            </div>

            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Caliber</label>
                <input
                  type="text"
                  name="caliber"
                  value={formData.caliber || ''}
                  onChange={handleChange}
                  placeholder="Caliber"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default FirearmDetails
