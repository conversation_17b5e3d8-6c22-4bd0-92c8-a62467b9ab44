import React, { useState, useRef, memo } from 'react';
import { DashboardIcons } from '../icons/DashboardIcons';

interface DocumentUploadProps {
  dealId: string;
  onUpload: (dealId: string, file: File) => Promise<any>;
  onUploadComplete?: () => void;
}

const DocumentUpload: React.FC<DocumentUploadProps> = memo(({ dealId, onUpload, onUploadComplete }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Allowed file types
  const allowedFileTypes = [
    'application/pdf', // PDF
    'application/msword', // DOC
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
    'application/vnd.ms-excel', // XLS
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
    'image/jpeg', // JPEG/JPG
    'image/png', // PNG
    'image/gif', // GIF
    'image/bmp', // BMP
    'image/tiff', // TIFF
    'application/vnd.ms-powerpoint', // PPT
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
    'text/plain' // TXT
  ];

  // Handle file selection
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Reset the file input so the same file can be uploaded again if needed
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      await uploadFile(file);
    }
  };

  // Handle drag events
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      await uploadFile(files[0]);
    }
  };

  // Validate file before upload
  const validateFile = (file: File): boolean => {
    // Check file type
    if (!allowedFileTypes.includes(file.type)) {
      setError(`File type not allowed: ${file.type}. Please upload a PDF, Word, Excel, or image file.`);
      return false;
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      setError(`File is too large (${(file.size / (1024 * 1024)).toFixed(2)}MB). Maximum size is 10MB.`);
      return false;
    }

    return true;
  };

  // Upload file
  const uploadFile = async (file: File) => {
    // Reset states
    setIsUploading(true);
    setError(null);
    setUploadProgress(0);
    setUploadSuccess(false);

    // Validate file
    if (!validateFile(file)) {
      setIsUploading(false);
      return;
    }

    try {
      // Set initial progress
      setUploadProgress(10);

      // Use a more efficient progress simulation with fewer state updates
      const updateProgress = () => {
        setUploadProgress(prev => {
          const newProgress = prev + (90 - prev) * 0.3; // Gradually approach 90%
          return Math.min(newProgress, 90);
        });
      };

      // Update progress less frequently to reduce render cycles
      const progressInterval = setInterval(updateProgress, 500);

      // Upload the file
      await onUpload(dealId, file);

      // Clear progress interval
      clearInterval(progressInterval);
      setUploadProgress(100);

      setUploadSuccess(true);

      // Call the onUploadComplete callback if provided
      if (onUploadComplete) {
        onUploadComplete();
      }

      // Reset progress after a delay
      setTimeout(() => {
        setUploadProgress(0);
        setUploadSuccess(false);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload file. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="mt-4">
      <h4 className="text-sm font-medium text-stone-300 mb-2">Documents</h4>

      <div
        className={`border border-dashed rounded-md p-4 text-center transition-colors ${
          isDragging
            ? 'border-orange-500 bg-stone-700/20'
            : isUploading
              ? 'border-blue-500/50 bg-stone-700/10'
              : uploadSuccess
                ? 'border-green-500/50 bg-stone-700/10'
                : 'border-stone-600/50 hover:bg-stone-700/10'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="document-upload"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileChange}
          accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.ppt,.pptx,.txt"
        />

        {isUploading ? (
          <div className="py-2">
            <DashboardIcons.Spinner className="h-6 w-6 text-orange-400 mx-auto animate-spin" />
            <p className="mt-2 text-xs text-stone-300">Uploading file...</p>

            {/* Progress bar */}
            <div className="w-full bg-stone-700 rounded-full h-1.5 mt-2">
              <div
                className="bg-orange-500 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
          </div>
        ) : uploadSuccess ? (
          <div className="py-2">
            <DashboardIcons.Check className="h-6 w-6 text-green-400 mx-auto" />
            <p className="mt-2 text-xs text-green-400">File uploaded successfully!</p>
          </div>
        ) : (
          <label
            htmlFor="document-upload"
            className="cursor-pointer flex flex-col items-center py-2"
          >
            <DashboardIcons.Upload className="h-6 w-6 text-stone-400" />
            <p className="mt-2 text-xs text-stone-300">
              <span className="font-semibold">Click to upload</span> or drag and drop
            </p>
            <p className="text-xs text-stone-500 mt-1">
              PDF, Word, Excel, or image files (max 10MB)
            </p>
          </label>
        )}
      </div>

      {error && (
        <div className="mt-2 p-2 bg-red-900/20 border border-red-800/30 rounded-md">
          <p className="text-xs text-red-400">{error}</p>
        </div>
      )}
    </div>
  );
});

export default DocumentUpload;
