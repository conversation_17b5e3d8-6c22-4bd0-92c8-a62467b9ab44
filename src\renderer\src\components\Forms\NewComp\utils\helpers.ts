/**
 * Extract information from a South African ID number
 * @param idNumber - The 13-digit South African ID number
 * @returns Object containing extracted information or null if invalid
 */
export const extractFromIdNumber = (idNumber: string) => {
  if (!/^\d{13}$/.test(idNumber)) {
    return null
  }

  try {
    // Extract date components
    const year = idNumber.substring(0, 2)
    const month = idNumber.substring(2, 4)
    const day = idNumber.substring(4, 6)

    // Extract gender - 7th digit (index 6)
    const genderDigit = parseInt(idNumber.substring(6, 7))
    const isMale = genderDigit >= 5
    const isFemale = genderDigit < 5

    // Determine century
    const currentYear = new Date().getFullYear()
    const currentCentury = Math.floor(currentYear / 100) * 100
    const previousCentury = currentCentury - 100

    // If year is greater than current year's last 2 digits, assume previous century
    const fullYear =
      parseInt(year) > parseInt(currentYear.toString().substring(2, 4))
        ? previousCentury + parseInt(year)
        : currentCentury + parseInt(year)

    // Format date as YYYY-MM-DD
    const birthDate = `${fullYear}-${month}-${day}`

    // Calculate age
    const today = new Date()
    const birthDateTime = new Date(birthDate)
    let age = today.getFullYear() - birthDateTime.getFullYear()

    // Adjust age if birthday hasn't occurred yet this year
    const monthDiff = today.getMonth() - birthDateTime.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateTime.getDate())) {
      age--
    }

    return {
      birthDate,
      age: age.toString(),
      sexM: isMale,
      sexF: isFemale
    }
  } catch (error) {
    console.error('Error parsing ID number:', error)
    return null
  }
}

/**
 * Generate initials from a name
 * @param name - The full name
 * @returns The initials
 */
export const generateInitials = (name: string): string => {
  if (!name) return ''
  
  return name
    .split(' ')
    .map((part) => part.charAt(0).toUpperCase())
    .join(' ')
}
