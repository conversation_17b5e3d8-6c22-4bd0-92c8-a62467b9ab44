import React, { useContext } from 'react'
import { DocScriptFormContext } from './FormLayout'

interface FormSectionProps {
  title?: string
  subtitle?: string
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'highlight' | 'nested' | 'success' | 'error' | 'warning'
  collapsible?: boolean
  initialCollapsed?: boolean
  isDocScriptForm?: boolean
}

const FormSection = ({
  title,
  subtitle,
  children,
  className = '',
  variant = 'default',
  collapsible = false,
  initialCollapsed = false,
  isDocScriptForm: propIsDocScriptForm = false
}: FormSectionProps) => {
  // Get the isDocScriptForm value from context or props
  const { isDocScriptForm: contextIsDocScriptForm } = useContext(DocScriptFormContext);
  const isDocScriptForm = propIsDocScriptForm || contextIsDocScriptForm;
  const [isCollapsed, setIsCollapsed] = React.useState(initialCollapsed);

  const getVariantStyles = () => {
    switch (variant) {
      case 'highlight':
        return 'bg-orange-500/10 border border-orange-500/20'
      case 'nested':
        return 'bg-stone-800/70 border border-stone-700/50'
      case 'success':
        return 'bg-green-500/10 border border-green-500/20'
      case 'error':
        return 'bg-red-500/10 border border-red-500/20'
      case 'warning':
        return 'bg-amber-500/10 border border-amber-500/20'
      default:
        return 'bg-stone-700/30 border border-stone-600/30'
    }
  }

  const getTitleColor = () => {
    switch (variant) {
      case 'highlight':
        return 'text-orange-400'
      case 'success':
        return 'text-green-400'
      case 'error':
        return 'text-red-400'
      case 'warning':
        return 'text-amber-400'
      default:
        return 'text-stone-200'
    }
  }

  return (
    <div className={`${isDocScriptForm ? 'p-2' : 'p-4'} ${getVariantStyles()} rounded-lg ${className} ${isDocScriptForm ? 'mb-2' : 'mb-4'}`}>
      {title && (
        <div className={`flex items-center justify-between ${isDocScriptForm ? 'mb-1.5' : 'mb-3'}`}>
          <div className="flex items-center">
            <h4 className={`${isDocScriptForm ? 'text-sm' : 'text-md'} font-medium ${getTitleColor()}`}>{title}</h4>
            {subtitle && <p className={`${isDocScriptForm ? 'text-xs' : 'text-sm'} text-stone-400 ml-3`}>{subtitle}</p>}
          </div>
          {collapsible && (
            <button
              type="button"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="text-stone-400 hover:text-stone-300 transition-colors"
            >
              {isCollapsed ? (
                <svg className={`${isDocScriptForm ? 'w-4 h-4' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              ) : (
                <svg className={`${isDocScriptForm ? 'w-4 h-4' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
              )}
            </button>
          )}
        </div>
      )}
      <div className={isDocScriptForm ? 'space-y-1.5' : 'space-y-3'}>
        {!isCollapsed && children}
      </div>
    </div>
  )
}

export default FormSection;