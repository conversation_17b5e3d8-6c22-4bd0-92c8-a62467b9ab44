import React from 'react'
import { Check, AlertCircle, LogOut, RefreshCw } from 'lucide-react'
import { useAccountSettings } from './hooks/useAccountSettings'

const AccountSettings: React.FC = () => {
  const {
    userData,
    isLoadingUserData,
    showPasswordModal,
    setShowPasswordModal,
    errorMessage,
    isChangingPassword,
    passwordForm,
    setPasswordForm,
    passwordError,
    passwordSuccess,
    changePassword,
    handleLogout
  } = useAccountSettings()

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-white border-b border-stone-700/50 pb-1 mb-4">
        Account Settings
      </h2>

      {isLoadingUserData ? (
        <div className="flex justify-center items-center py-6">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-stone-300"></div>
        </div>
      ) : (
        <>
          {/* User Profile Information */}
          <div className="flex flex-col space-y-1 mb-4">
            <label className="text-stone-300 font-medium text-sm">User Profile</label>
            <div className="flex flex-col gap-3 bg-stone-900/50 p-3 rounded-lg border border-stone-700/50">
              <div className="flex flex-col items-center gap-1">
                <div className="text-lg font-medium text-white">
                  {userData.role === 'admin'
                    ? 'Administrator'
                    : userData.displayName || userData.username}
                </div>
                <div className="text-stone-400 text-sm">{userData.email}</div>
              </div>

              {/* User details */}
              <div className="pt-2 border-t border-stone-700/30">
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex justify-between items-center py-1 border-b border-stone-700/30">
                    <div className="text-stone-300 text-sm">Display Name</div>
                    <div className="text-white text-sm">
                      {userData.displayName || userData.username}
                    </div>
                  </div>

                  <div className="flex justify-between items-center py-1 border-b border-stone-700/30">
                    <div className="text-stone-300 text-sm">Email</div>
                    <div className="text-white text-sm">{userData.email}</div>
                  </div>

                  <div className="flex justify-between items-center py-1 border-b border-stone-700/30">
                    <div className="text-stone-300 text-sm">Role</div>
                    <div className="text-white text-sm">
                      {userData.role === 'admin' ? 'Administrator' : 'User'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex flex-wrap gap-2 pt-2 border-t border-stone-700/30">
                <button
                  className="px-3 py-1 bg-stone-700 hover:bg-stone-600 text-stone-300 hover:text-white rounded-md transition-colors flex items-center gap-1 text-sm"
                  onClick={() => setShowPasswordModal(true)}
                >
                  <Check size={14} />
                  Change Password
                </button>

                <button
                  className="px-3 py-1 bg-red-700/70 hover:bg-red-600/70 text-white rounded-md transition-colors flex items-center gap-1 text-sm ml-auto"
                  onClick={handleLogout}
                >
                  <LogOut size={14} />
                  Logout
                </button>
              </div>
            </div>
          </div>

          {/* Error message display */}
          {errorMessage && (
            <div className="px-3 py-2 bg-red-500/20 border border-red-500/30 rounded-md text-red-400 flex items-center gap-1 text-sm">
              <AlertCircle size={14} />
              <span>{errorMessage}</span>
            </div>
          )}
        </>
      )}

      {/* Modal for changing password */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-stone-800 rounded-lg p-4 w-full max-w-sm shadow-lg">
            <h3 className="text-lg font-semibold text-white mb-3">Change Password</h3>

            {/* Password change form */}
            <div className="space-y-3">
              {passwordSuccess ? (
                <div className="px-3 py-2 bg-green-500/20 border border-green-500/30 rounded-md text-green-400 flex items-center gap-1 text-sm">
                  <Check size={14} />
                  <span>Password updated successfully!</span>
                </div>
              ) : (
                <>
                  {passwordError && (
                    <div className="px-3 py-2 bg-red-500/20 border border-red-500/30 rounded-md text-red-400 flex items-center gap-1 text-sm">
                      <AlertCircle size={14} />
                      <span>{passwordError}</span>
                    </div>
                  )}

                  <div className="space-y-1">
                    <label className="text-stone-300 text-xs">Current Password</label>
                    <input
                      type="password"
                      value={passwordForm.currentPassword}
                      onChange={(e) =>
                        setPasswordForm((prev) => ({ ...prev, currentPassword: e.target.value }))
                      }
                      className="w-full bg-stone-900 border border-stone-700 rounded-md px-2 py-1.5 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                      placeholder="Enter your current password"
                    />
                  </div>

                  <div className="space-y-1">
                    <label className="text-stone-300 text-xs">New Password</label>
                    <input
                      type="password"
                      value={passwordForm.newPassword}
                      onChange={(e) =>
                        setPasswordForm((prev) => ({ ...prev, newPassword: e.target.value }))
                      }
                      className="w-full bg-stone-900 border border-stone-700 rounded-md px-2 py-1.5 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                      placeholder="Enter new password"
                    />
                    <p className="text-xs text-stone-400">
                      Password must be at least 6 characters long
                    </p>
                  </div>

                  <div className="space-y-1">
                    <label className="text-stone-300 text-xs">Confirm New Password</label>
                    <input
                      type="password"
                      value={passwordForm.confirmPassword}
                      onChange={(e) =>
                        setPasswordForm((prev) => ({ ...prev, confirmPassword: e.target.value }))
                      }
                      className="w-full bg-stone-900 border border-stone-700 rounded-md px-2 py-1.5 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                      placeholder="Confirm new password"
                    />
                  </div>

                  <div className="mt-2 px-3 py-2 bg-yellow-500/10 border border-yellow-500/20 rounded-md text-yellow-400 flex items-center gap-1 text-xs">
                    <AlertCircle size={12} />
                    <span>
                      Warning: If you change your password and forget it, your account won't be able
                      to be recovered.
                    </span>
                  </div>
                </>
              )}

              <div className="mt-3 flex justify-end gap-2">
                <button
                  className="px-2 py-1 bg-stone-700 text-stone-300 rounded-md text-sm"
                  onClick={() => {
                    setShowPasswordModal(false)
                    setPasswordForm({
                      currentPassword: '',
                      newPassword: '',
                      confirmPassword: ''
                    })
                  }}
                >
                  Cancel
                </button>
                {!passwordSuccess && (
                  <button
                    className={`px-2 py-1 rounded-md flex items-center gap-1 text-sm ${
                      isChangingPassword
                        ? 'bg-stone-700 text-stone-400 cursor-not-allowed'
                        : 'bg-orange-600 hover:bg-orange-500 text-white'
                    }`}
                    onClick={changePassword}
                    disabled={
                      isChangingPassword ||
                      !passwordForm.currentPassword ||
                      !passwordForm.newPassword ||
                      !passwordForm.confirmPassword
                    }
                  >
                    {isChangingPassword ? (
                      <>
                        <RefreshCw className="animate-spin" size={12} />
                        <span>Updating...</span>
                      </>
                    ) : (
                      <>
                        <Check size={12} />
                        <span>Update Password</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AccountSettings
