import React from 'react'
import { FormSection, RadioGroup } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionWithAddressProps } from '../utils/types'
import { extractFromIdNumber, generateInitials } from '../utils/helpers'

/**
 * Personal Information section component for Annexure A form
 */
const PersonalInfo: React.FC<SectionWithAddressProps> = ({
  formData,
  updateFormData,
  handleAddressChange,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement

    const updatedData: any = {}

    if (type === 'checkbox') {
      updatedData[name] = (e.target as HTMLInputElement).checked
    } else {
      updatedData[name] = value
    }

    // If firstName is changed, extract initials
    if (name === 'firstName' && value) {
      updatedData.initials = generateInitials(value)
    }

    // If ID number is changed, extract information
    if (name === 'idNumber' && value && value.length === 13) {
      const { birthDate, age, gender } = extractFromIdNumber(value)

      updatedData.birthDate = birthDate
      updatedData.age = age.toString()

      if (gender === 'male') {
        updatedData.sexM = true
        updatedData.sexF = false
      } else {
        updatedData.sexM = false
        updatedData.sexF = true
      }
    }

    updateFormData(updatedData)
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Personal Information</h3>

      <FormSection title="Basic Information" subtitle="Your personal details">
        <div className="space-y-3">
          {/* First Names, Last Name, and Initials side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">First Names</label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName || ''}
                  onChange={handleChange}
                  placeholder="Enter first name"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Last Name</label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName || ''}
                  onChange={handleChange}
                  placeholder="Enter last name"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[100px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Initials</label>
                <input
                  type="text"
                  name="initials"
                  value={formData.initials || ''}
                  onChange={handleChange}
                  placeholder="e.g. J.S."
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          {/* ID Number and Citizenship side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col">
                <label className="block text-sm font-medium text-stone-300 mb-1">Citizenship Status</label>
                <RadioGroup
                  name="citizenType"
                  value={formData.citizenType}
                  onChange={(value) =>
                    updateFormData({ citizenType: value as 'saId' | 'fId' | '' })
                  }
                  options={[
                    { value: 'saId', label: 'SA Citizen' },
                    { value: 'fId', label: 'Foreign Passport' }
                  ]}
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col">
                {formData.citizenType === 'saId' ? (
                  <>
                    <label className="block text-sm font-medium text-stone-300 mb-1">ID Number</label>
                    <input
                      type="text"
                      name="idNumber"
                      value={formData.idNumber || ''}
                      onChange={handleChange}
                      placeholder="e.g. 8001015009087"
                      required={true}
                      pattern="[0-9]{13}"
                      maxLength={13}
                      className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                    />
                  </>
                ) : formData.citizenType === 'fId' ? (
                  <>
                    <label className="block text-sm font-medium text-stone-300 mb-1">Passport Number</label>
                    <input
                      type="text"
                      name="passport"
                      value={formData.passport || ''}
                      onChange={handleChange}
                      placeholder="e.g. AB123456"
                      required={true}
                      className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                    />
                  </>
                ) : null}
              </div>
            </div>
          </div>

          {/* Note about ID Number auto-calculation */}
          {formData.citizenType === 'saId' && (
            <p className="text-xs text-stone-400 mt-1">
              ID Number auto-calculates age, birth date, and gender
            </p>
          )}
        </div>
      </FormSection>

      <FormSection title="Contact Information" subtitle="Your contact details">
        <div className="space-y-3">
          {/* Phone Number, Cell Phone, Work Contact, and Email Address side by side */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Phone Number</label>
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. ************"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Cell Phone</label>
                <input
                  type="tel"
                  name="cell"
                  value={formData.cell || ''}
                  onChange={handleChange}
                  placeholder="e.g. ************"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Work Contact</label>
                <input
                  type="tel"
                  name="workNumber"
                  value={formData.workNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. ************"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Email Address</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email || ''}
                  onChange={handleChange}
                  placeholder="e.g. <EMAIL>"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </FormSection>

      <FormSection title="Physical Address" subtitle="Your residential address">
        <AddressInput
          label="Physical Address"
          value={formData.physicalAddress || ''}
          postalCode={formData.postalCode || ''}
          onChange={(address, postalCode) =>
            handleAddressChange(address, postalCode)
          }
          placeholder="Enter your physical address"
          isTextarea={false}
          required={true}
          postalCodeRequired={true}
        />
        <p className="text-xs text-stone-400 mt-1">
          Enter your full physical address and postal code
        </p>
      </FormSection>
    </div>
  )
}

export default PersonalInfo
