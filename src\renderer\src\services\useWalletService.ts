import { useState } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { getSupabase } from '../lib/supabase'
import { ClientWallet, WalletTransaction } from '../types'

export const useWalletService = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch client wallet
  const getClientWallet = async (clientId: string): Promise<ClientWallet | null> => {
    setLoading(true)
    setError(null)

    try {
      const supabase = getSupabase()
      const { data, error } = await supabase
        .from('client_credit_wallets')
        .select('*')
        .eq('client_id', clientId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') { // No rows returned
          return null
        }
        console.error('Error fetching wallet:', error)
        setError('Failed to fetch wallet information')
        return null
      }

      return data as ClientWallet
    } catch (err) {
      console.error('Error in getClientWallet:', err)
      setError('An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }

  // Create or get client wallet
  const createOrGetWallet = async (clientId: string): Promise<ClientWallet | null> => {
    setLoading(true)
    setError(null)

    try {
      // First check if wallet exists
      const existingWallet = await getClientWallet(clientId)
      if (existingWallet) {
        return existingWallet
      }

      // Create a new wallet
      const supabase = getSupabase()
      const now = new Date().toISOString()
      const newWalletId = uuidv4()

      const { error: createWalletError } = await supabase
        .from('client_credit_wallets')
        .insert({
          id: newWalletId,
          client_id: clientId,
          balance: 0, // Initial balance is 0, will be updated by trigger
          created_at: now,
          updated_at: now
        })

      if (createWalletError) {
        console.error('Error creating wallet:', createWalletError)
        setError('Failed to create wallet')
        return null
      }

      // Fetch the newly created wallet
      const { data: newWallet, error: fetchError } = await supabase
        .from('client_credit_wallets')
        .select('*')
        .eq('id', newWalletId)
        .single()

      if (fetchError) {
        console.error('Error fetching new wallet:', fetchError)
        setError('Wallet created but failed to fetch details')
        return null
      }

      return newWallet as ClientWallet
    } catch (err) {
      console.error('Error in createOrGetWallet:', err)
      setError('An unexpected error occurred')
      return null
    } finally {
      setLoading(false)
    }
  }

  // Add funds to wallet
  const addFunds = async (
    clientId: string,
    amount: number,
    description: string
  ): Promise<{ success: boolean, wallet: ClientWallet | null }> => {
    if (amount <= 0) {
      setError('Amount must be greater than zero')
      return { success: false, wallet: null }
    }

    setLoading(true)
    setError(null)

    try {
      // Get or create wallet
      const wallet = await createOrGetWallet(clientId)
      if (!wallet) {
        setError('Failed to get or create wallet')
        return { success: false, wallet: null }
      }

      const supabase = getSupabase()
      const now = new Date().toISOString()

      // Create transaction record
      const transactionId = uuidv4()
      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          id: transactionId,
          wallet_id: wallet.id,
          amount: amount, // Positive amount for adding funds
          transaction_type: 'payment',
          reference_type: 'manual',
          description: description || 'Manual credit addition',
          transaction_date: now,
          created_at: now,
          updated_at: now
        })

      if (transactionError) {
        console.error('Error creating transaction:', transactionError)
        setError('Failed to process transaction')
        return { success: false, wallet }
      }

      // Fetch updated wallet balance
      const { data: updatedWallet, error: fetchError } = await supabase
        .from('client_credit_wallets')
        .select('*')
        .eq('id', wallet.id)
        .single()

      if (fetchError) {
        console.error('Error fetching updated wallet:', fetchError)
        setError('Transaction processed but failed to fetch updated balance')
        return { success: true, wallet } // Return original wallet as fallback
      }

      return { success: true, wallet: updatedWallet as ClientWallet }
    } catch (err) {
      console.error('Error in addFunds:', err)
      setError('An unexpected error occurred')
      return { success: false, wallet: null }
    } finally {
      setLoading(false)
    }
  }

  // Deduct funds from wallet
  const deductFunds = async (
    clientId: string,
    amount: number,
    description: string
  ): Promise<{ success: boolean, wallet: ClientWallet | null }> => {
    if (amount <= 0) {
      setError('Amount must be greater than zero')
      return { success: false, wallet: null }
    }

    setLoading(true)
    setError(null)

    try {
      // Get or create wallet
      const wallet = await createOrGetWallet(clientId)
      if (!wallet) {
        setError('Failed to get or create wallet')
        return { success: false, wallet: null }
      }

      // Check if wallet has sufficient funds
      if (wallet.balance < amount) {
        setError('Insufficient funds in wallet')
        return { success: false, wallet }
      }

      const supabase = getSupabase()
      const now = new Date().toISOString()

      // Create transaction record
      const transactionId = uuidv4()
      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          id: transactionId,
          wallet_id: wallet.id,
          amount: -amount, // Negative amount for deducting funds
          transaction_type: 'charge',
          reference_type: 'manual',
          description: description || 'Manual deduction',
          transaction_date: now,
          created_at: now,
          updated_at: now
        })

      if (transactionError) {
        console.error('Error creating transaction:', transactionError)
        setError('Failed to process transaction')
        return { success: false, wallet }
      }

      // Fetch updated wallet balance
      const { data: updatedWallet, error: fetchError } = await supabase
        .from('client_credit_wallets')
        .select('*')
        .eq('id', wallet.id)
        .single()

      if (fetchError) {
        console.error('Error fetching updated wallet:', fetchError)
        setError('Transaction processed but failed to fetch updated balance')
        return { success: true, wallet } // Return original wallet as fallback
      }

      return { success: true, wallet: updatedWallet as ClientWallet }
    } catch (err) {
      console.error('Error in deductFunds:', err)
      setError('An unexpected error occurred')
      return { success: false, wallet: null }
    } finally {
      setLoading(false)
    }
  }

  // Get wallet transactions
  const getWalletTransactions = async (walletId: string): Promise<WalletTransaction[]> => {
    setLoading(true)
    setError(null)

    try {
      const supabase = getSupabase()
      const { data, error } = await supabase
        .from('credit_transactions')
        .select('*')
        .eq('wallet_id', walletId)
        .order('transaction_date', { ascending: false })
        .limit(50) // Limit to most recent 50 transactions

      if (error) {
        console.error('Error fetching transactions:', error)
        setError('Failed to fetch transaction history')
        return []
      }

      return data as WalletTransaction[]
    } catch (err) {
      console.error('Error in getWalletTransactions:', err)
      setError('An unexpected error occurred')
      return []
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    error,
    getClientWallet,
    createOrGetWallet,
    addFunds,
    deductFunds,
    getWalletTransactions
  }
}
