import React from 'react'

interface FormFieldProps {
  label: string
  name: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  type?: 'text' | 'textarea' | 'date' | 'email' | 'tel' | 'number' | 'password'
  rows?: number
  required?: boolean
  placeholder?: string
  className?: string
  inputClassName?: string
  maxLength?: number
  pattern?: string
  min?: string | number
  max?: string | number
  step?: string | number
  error?: string
  disabled?: boolean
  additionalContent?: React.ReactNode
  helpText?: string
}

export const FormField = ({
  label,
  name,
  value,
  onChange,
  type = 'text',
  rows = 2,
  required = false,
  placeholder = '',
  className = '',
  inputClassName = '',
  maxLength,
  pattern,
  min,
  max,
  step,
  error,
  disabled = false,
  additionalContent,
  helpText,
  ...rest
}: FormFieldProps & Record<string, any>) => (
  <div className={`space-y-1 ${className}`}>
    <label htmlFor={name} className="block text-sm font-medium text-stone-300">
      {label} {required && <span className="text-orange-500">*</span>}
    </label>
    {type === 'textarea' ? (
      <textarea
        id={name}
        name={name}
        value={value || ''}
        onChange={onChange}
        rows={rows}
        required={required}
        placeholder={placeholder}
        disabled={disabled}
        className={`w-full bg-stone-700/50 border ${
          error ? 'border-red-500' : 'border-stone-600/50'
        } rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none ${
          disabled ? 'opacity-60 cursor-not-allowed' : ''
        } ${inputClassName}`}
      />
    ) : (
      <input
        type={type}
        id={name}
        name={name}
        value={value || ''}
        onChange={onChange}
        required={required}
        placeholder={placeholder}
        maxLength={maxLength}
        pattern={pattern}
        min={min}
        max={max}
        step={step}
        disabled={disabled}
        className={`w-full bg-stone-700/50 border ${
          error ? 'border-red-500' : 'border-stone-600/50'
        } rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none ${
          disabled ? 'opacity-60 cursor-not-allowed' : ''
        } ${inputClassName}`}
        {...rest}
      />
    )}
    {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
    {helpText && <p className="text-xs text-stone-400 mt-1">{helpText}</p>}
    {additionalContent}
  </div>
);

export default FormField;