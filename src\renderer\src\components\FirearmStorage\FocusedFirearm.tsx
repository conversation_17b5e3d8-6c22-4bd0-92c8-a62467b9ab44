import React, { useEffect } from 'react'
import { Firearm } from '../../types/firearm'
import FirearmCard from './FirearmCard'

interface FocusedFirearmProps {
  firearm: Firearm | null
  onClose: () => void
  onEditFirearm: (firearm: Firearm) => void
  onDeleteFirearm: (firearmId: string) => void
  onAssignFirearm: (firearm: Firearm) => void
  onSignOutFirearm: (firearmId: string) => void
  onAddCredit?: (firearm: Firearm) => void // Optional callback for when credit is added
}

function FocusedFirearm({
  firearm,
  onClose,
  onEditFirearm,
  onDeleteFirearm,
  onAssignFirearm,
  onSignOutFirearm,
  onAddCredit
}: FocusedFirearmProps): React.ReactElement | null {
  // Add body overflow class when focus mode is active
  useEffect(() => {
    document.body.classList.add('focus-mode-active')

    return () => {
      document.body.classList.remove('focus-mode-active')
    }
  }, [])

  if (!firearm) return null

  return (
    <FirearmCard
      firearm={firearm}
      onEdit={onEditFirearm}
      onDelete={onDeleteFirearm}
      onAssign={onAssignFirearm}
      onSignOut={onSignOutFirearm}
      isFocused={true}
      isOtherCardFocused={false}
      onFocusToggle={onClose}
      onAddCredit={onAddCredit}
    />
  )
}

export default FocusedFirearm
