import React, { Suspense, lazy } from 'react'
import { DashboardIcons } from '../components/icons/DashboardIcons'
import SearchContainer from '../components/SearchContainer'
import Toast from '../components/Toast'
import {
  FilterComponent,
  Pagination,
  EmptyState,
  useDashboardService,
  ClientCard,
  ClientCardList
} from '../components/Dashboard'
import { SkeletonDashboard } from '../components/SkeletonLoading'
import '../styles/focus-mode.css'

// Lazy load components
const ClientForm = lazy(() => import('../components/Forms/ClientForm'))
const LicenseForm = lazy(() => import('../components/Forms/AddLicence'))

function Dashboard(): React.JSX.Element {
  const {
    // State
    loading,
    clientSearchQuery,
    page,
    perPage,
    formState,
    totalClients,
    toast,
    focusedClientId,
    focusedClient,
    clientFilter,
    filterCounts,
    paginatedClients,
    clientSearchTips,

    // Actions
    setPage,
    setClientFilter,
    handleSearch,
    debouncedFetch,
    handleDeleteLicense,
    handleDeleteClient,
    resetFormState,
    handleClientFocusToggle,
    handleCreateClient,
    setToast,
    formHandlers,
    fetchSearchHints
  } = useDashboardService()

  return (
    <div className="min-h-screen bg-gradient-to-b from-stone-900 to-stone-800 p-6 overflow-hidden">
      {/* Toast notifications */}
      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}

      {/* Page title with pagination */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white flex items-center gap-3">
          Client Management
          <div className="px-3 py-1 bg-stone-700/50 rounded-full text-sm text-stone-300">
            Total Records: {totalClients}
          </div>
        </h1>

        {/* Pagination controls next to page title */}
        {paginatedClients.clients.length > 0 && (
          <Pagination
            page={page}
            setPage={setPage}
            totalItems={paginatedClients.total}
            itemsPerPage={perPage}
          />
        )}
      </div>

      {/* Two-column layout - always side by side */}
      <div className="flex flex-row h-[calc(100vh-180px)] overflow-hidden">
        {/* Left column: Page tools */}
        <div className="w-80 bg-stone-800/60 rounded-lg shadow-lg p-4 flex-shrink-0 overflow-hidden">
          {/* Action Buttons */}
          <div className="flex items-center justify-between mb-4">
            {/* Refresh Button */}
            <button
              onClick={() => debouncedFetch(clientSearchQuery)}
              disabled={loading}
              className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-2 rounded-lg
                transition-colors flex items-center gap-1 disabled:opacity-50 h-10"
            >
              <DashboardIcons.Refresh className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
            </button>

            {/* Create New Client Button */}
            <button
              onClick={handleCreateClient}
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                text-white px-3 py-2 rounded-lg shadow-md shadow-orange-500/20
                transition-all duration-200 flex items-center gap-2 h-10"
            >
              <DashboardIcons.Add className="w-4 h-4" />
              <span>Create New</span>
            </button>
          </div>

          {/* Search Container (without buttons) */}
          <div className="mb-6">
            <SearchContainer
              onSearch={handleSearch}
              isLoading={loading}
              placeholder="Search clients..."
              searchHintsLoader={fetchSearchHints}
              showRefreshButton={false}
              showCreateButton={false}
              searchTipsContent={
                <>
                  <p>{clientSearchTips.title}</p>
                  <ul className="list-disc pl-4 mt-1 space-y-1">
                    {clientSearchTips.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </>
              }
              debounceTime={400}
              className="w-full"
              initialValue={clientSearchQuery}
            />
          </div>

          {/* Filter Component */}
          <FilterComponent
            clientFilter={clientFilter}
            setClientFilter={setClientFilter}
            filterCounts={filterCounts}
          />
        </div>

        {/* Right column: Client cards */}
        <div className="flex-1 ml-6 overflow-y-auto bg-stone-800/30 rounded-lg p-4 h-[calc(100vh-180px)]">
          {/* Search Results Section */}
          {loading ? (
            <div className="h-full">
              <SkeletonDashboard cardCount={5} />
            </div>
          ) : focusedClientId && focusedClient ? (
            <div className="focus-mode-container">
              <div className="focus-mode-card">
                <ClientCard
                  key={focusedClient.id}
                  client={focusedClient}
                  onEditClient={() => formHandlers.onEditClient(focusedClient)}
                  onDeleteClient={() => handleDeleteClient(focusedClient.id)}
                  onAddLicense={() => formHandlers.onAddLicense(focusedClient.id)}
                  onEditLicense={(license) => formHandlers.onEditLicense(license, focusedClient.id)}
                  onDeleteLicense={(licenseId) => handleDeleteLicense(licenseId)}
                  onFocusToggle={handleClientFocusToggle}
                  isFocused={true}
                  isOtherCardFocused={false}
                />
              </div>
            </div>
          ) : paginatedClients.clients.length > 0 ? (
            <ClientCardList
              clients={paginatedClients.clients}
              onEditClient={formHandlers.onEditClient}
              onDeleteClient={handleDeleteClient}
              onAddLicense={formHandlers.onAddLicense}
              onEditLicense={formHandlers.onEditLicense}
              onDeleteLicense={handleDeleteLicense}
              onFocusToggle={handleClientFocusToggle}
              focusedClientId={focusedClientId}
            />
          ) : (
            <EmptyState
              searchQuery={clientSearchQuery}
              clientFilter={clientFilter}
              onCreateClient={handleCreateClient}
            />
          )}
        </div>
      </div>

      {/* Forms */}
      <Suspense fallback={null}>
        {formState.isOpen && formState.type === 'client' && (
          <ClientForm
            client={formState.selectedClient}
            onClose={() => resetFormState()}
            onSuccess={() => {
              debouncedFetch(clientSearchQuery)
              resetFormState()
            }}
          />
        )}

        {formState.isOpen && formState.type === 'license' && (
          <LicenseForm
            license={formState.selectedLicense}
            clientId={formState.selectedClientId!}
            onClose={() => resetFormState()}
            onSuccess={() => {
              debouncedFetch(clientSearchQuery)
              resetFormState()
            }}
          />
        )}
      </Suspense>
    </div>
  )
}

export default Dashboard
