/**
 * Date utility functions for consistent date handling across the application
 */

/**
 * Format a date value for display in the UI
 * @param dateValue - Date value as string or Date object
 * @returns Formatted date string in YYYY-MM-DD format
 */
export const formatDateForDisplay = (dateValue: string | Date | null): string => {
  if (!dateValue) return '';
  
  // Handle both string and Date objects
  const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
  
  // Check if date is valid
  if (isNaN(date.getTime())) return '';
  
  // Format as YYYY-MM-DD
  return date.toISOString().split('T')[0];
};

/**
 * Format a date value for storing in the database
 * @param dateValue - Date value as string or Date object
 * @returns Formatted date string in YYYY-MM-DD format for database storage
 */
export const formatDateForDatabase = (dateValue: string | Date | null): string => {
  if (!dateValue) return '';
  
  // Handle both string and Date objects
  const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
  
  // Check if date is valid
  if (isNaN(date.getTime())) return '';
  
  // Format as YYYY-MM-DD
  return date.toISOString().split('T')[0];
};

/**
 * Parse a date string from any common format
 * @param dateString - Date string in any format
 * @returns Date object or null if invalid
 */
export const parseDate = (dateString: string): Date | null => {
  if (!dateString) return null;
  
  // Try to parse the date
  const date = new Date(dateString);
  
  // Check if date is valid
  if (isNaN(date.getTime())) return null;
  
  return date;
};

/**
 * Check if a date string is valid
 * @param dateString - Date string to validate
 * @returns Boolean indicating if the date is valid
 */
export const isValidDate = (dateString: string): boolean => {
  return parseDate(dateString) !== null;
};

/**
 * Format a date for display in a user-friendly format
 * @param dateString - Date string in any format
 * @returns Formatted date string (e.g., "Jan 1, 2023")
 */
export const formatDateForUser = (dateString: string | Date | null): string => {
  if (!dateString) return 'N/A';
  
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  
  // Check if date is valid
  if (isNaN(date.getTime())) return 'N/A';
  
  return date.toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};
