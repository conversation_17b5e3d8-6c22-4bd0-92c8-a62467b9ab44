import React from 'react'
import { FormSection, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Firearm Details section component for Annexure A form
 */
const FirearmDetails: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    updateFormData({ [name]: checked })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Firearm Details</h3>

      <FormSection title="Basic Firearm Information" subtitle="Enter the firearm's specifications">
        <div className="space-y-3">
          {/* Make, Model, Serial Number, and Caliber in one row */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Make</label>
                <input
                  type="text"
                  name="make"
                  value={formData.make || ''}
                  onChange={handleChange}
                  placeholder="e.g. Glock"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Model</label>
                <input
                  type="text"
                  name="model"
                  value={formData.model || ''}
                  onChange={handleChange}
                  placeholder="e.g. 19"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Serial Number</label>
                <input
                  type="text"
                  name="serialNumber"
                  value={formData.serialNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. ABC123456"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[120px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Caliber</label>
                <input
                  type="text"
                  name="caliber"
                  value={formData.caliber || ''}
                  onChange={handleChange}
                  placeholder="e.g. 9mm"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          {/* Engravings */}
          <div>
            <label className="block text-sm font-medium text-stone-300 mb-1">
              Names and Addresses engraved in metal
            </label>
            <textarea
              name="engravedDetails"
              value={formData.engravedDetails || ''}
              onChange={handleChange}
              placeholder="Enter details of engravings"
              rows={2}
              className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
            />
          </div>
        </div>
      </FormSection>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <FormSection title="Firearm Type" subtitle="Select the type of firearm">
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="pistol"
                name="pistol"
                checked={formData.pistol || false}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="pistol" className="ml-2 block text-sm text-stone-300">
                Pistol
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="rifle"
                name="rifle"
                checked={formData.rifle || false}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="rifle" className="ml-2 block text-sm text-stone-300">
                Rifle
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="shotgun"
                name="shotgun"
                checked={formData.shotgun || false}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="shotgun" className="ml-2 block text-sm text-stone-300">
                Shotgun
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="revolver"
                name="revolver"
                checked={formData.revolver || false}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="revolver" className="ml-2 block text-sm text-stone-300">
                Revolver
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="combination"
                name="combination"
                checked={formData.combination || false}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="combination" className="ml-2 block text-sm text-stone-300">
                Combination
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="carbine"
                name="carbine"
                checked={formData.carbine || false}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="carbine" className="ml-2 block text-sm text-stone-300">
                Carbine
              </label>
            </div>
          </div>
        </FormSection>

        <FormSection title="Firearm Action Type" subtitle="Select the action mechanism">
          <RadioGroup
            name="actionType"
            value={formData.actionType || ''}
            onChange={(value) =>
              updateFormData({
                actionType: value,
                semiAutomatic: value === 'semi',
                automatic: value === 'auto',
                manual: value === 'manual'
              })
            }
            options={[
              { value: 'semi', label: 'Semi-automatic' },
              { value: 'auto', label: 'Automatic' },
              { value: 'manual', label: 'Manual' }
            ]}
          />

          {formData.actionType === 'other' && (
            <div className="mt-2">
              <label className="block text-sm font-medium text-stone-300 mb-1">
                Other Action Type
              </label>
              <input
                type="text"
                name="otherActionType"
                value={formData.otherActionType || ''}
                onChange={handleChange}
                placeholder="Specify other action type"
                className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
              />
            </div>
          )}
        </FormSection>
      </div>

      <FormSection title="Firearm Components" subtitle="Enter component details if applicable">
        <div className="space-y-3">
          {/* Barrel, Frame, and Receiver Serial Numbers */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Barrel Serial Number</label>
                <input
                  type="text"
                  name="barrelSerialNumber"
                  value={formData.barrelSerialNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. BSN123456"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Frame Serial Number</label>
                <input
                  type="text"
                  name="frameSerialNumber"
                  value={formData.frameSerialNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. FSN123456"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Receiver Serial Number</label>
                <input
                  type="text"
                  name="receiverSerialNumber"
                  value={formData.receiverSerialNumber || ''}
                  onChange={handleChange}
                  placeholder="e.g. RSN123456"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          {/* Barrel, Frame, and Receiver Makes */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Barrel Make</label>
                <input
                  type="text"
                  name="barrelMake"
                  value={formData.barrelMake || ''}
                  onChange={handleChange}
                  placeholder="e.g. Glock"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Frame Make</label>
                <input
                  type="text"
                  name="frameMake"
                  value={formData.frameMake || ''}
                  onChange={handleChange}
                  placeholder="e.g. Glock"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Receiver Make</label>
                <input
                  type="text"
                  name="receiverMake"
                  value={formData.receiverMake || ''}
                  onChange={handleChange}
                  placeholder="e.g. Glock"
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default FirearmDetails
