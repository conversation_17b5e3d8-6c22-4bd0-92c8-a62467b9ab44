import React, { memo, useMemo } from 'react';
import { PipelineStage, Deal } from '../../types/pipedrive';
import DealCard from './DealCard';
import { DashboardIcons } from '../icons/DashboardIcons';

interface StageColumnProps {
  stage: PipelineStage;
  deals: Deal[];
  onAddDeal: (stageId: string) => void;
  onEditStage?: (stage: PipelineStage) => void;
  onDeleteStage?: (stage: PipelineStage) => void;
  onEditDeal?: (deal: Deal) => void;
  onDeleteDeal?: (deal: Deal) => void;
  onDragStart: (e: React.DragEvent, dealId: string) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent, stageId: string) => void;
}

const StageColumn: React.FC<StageColumnProps> = memo(({
  stage,
  deals,
  onAddDeal,
  onEditStage,
  onDeleteStage,
  onEditDeal,
  onDeleteDeal,
  onDragStart,
  onDragOver,
  onDrop
}) => {
  // Filter deals for this stage using memoization to prevent unnecessary recalculations
  const stageDeals = useMemo(() =>
    deals.filter(deal => deal.stage_id === stage.id),
    [deals, stage.id]
  );

  return (
    <div
      className="flex-shrink-0 w-72 bg-stone-800/30 rounded-lg overflow-hidden flex flex-col h-full shadow-md border border-stone-700/30 max-h-full"
      onDragOver={onDragOver}
      onDrop={(e) => onDrop(e, stage.id)}
    >
      {/* Stage header */}
      <div className="p-3 bg-stone-700/50 border-b border-stone-600/30">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <h3 className="font-medium text-white text-sm cursor-help" title="Click the edit icon to rename this stage">{stage.name}</h3>
            <div className="flex space-x-1">
              {onEditStage && (
                <button
                  onClick={() => onEditStage(stage)}
                  className="text-stone-400 hover:text-orange-400 transition-colors duration-200"
                  title="Edit Stage Name"
                >
                  <DashboardIcons.Edit className="w-3.5 h-3.5" />
                </button>
              )}
              {onDeleteStage && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteStage(stage);
                  }}
                  className="text-stone-400 hover:text-red-400 transition-colors duration-200"
                  title="Delete Stage"
                >
                  <DashboardIcons.Delete className="w-3.5 h-3.5" />
                </button>
              )}
            </div>
          </div>
          <span className="text-xs text-stone-300 bg-stone-800/50 px-2 py-0.5 rounded-full">
            {stageDeals.length}
          </span>
        </div>
      </div>

      {/* Deals container */}
      <div className="flex-1 p-2 space-y-3 overflow-y-auto overflow-x-hidden custom-scrollbar">
        {stageDeals.map(deal => (
          <div key={deal.id}>
            <DealCard
              deal={deal}
              onDragStart={onDragStart}
              onEditDeal={onEditDeal}
              onDeleteDeal={onDeleteDeal}
            />
          </div>
        ))}

        {stageDeals.length === 0 && (
          <div className="flex items-center justify-center h-20 border border-dashed border-stone-600/30 rounded-md">
            <p className="text-stone-500 text-xs">No deals in this stage</p>
          </div>
        )}
      </div>

      {/* Add deal button */}
      <div className="p-2 border-t border-stone-600/30">
        <button
          onClick={() => onAddDeal(stage.id)}
          className="w-full flex items-center justify-center space-x-1 py-1.5 bg-gradient-to-r from-orange-500/10 to-orange-600/10 text-orange-400 hover:text-orange-300 rounded-md text-xs transition-colors duration-200"
        >
          <DashboardIcons.Add className="w-3 h-3" />
          <span>Add Deal</span>
        </button>
      </div>
    </div>
  );
});

export default StageColumn;
