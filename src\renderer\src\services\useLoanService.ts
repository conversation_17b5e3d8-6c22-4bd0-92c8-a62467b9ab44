import { useState, useEffect, useCallback, useMemo } from 'react'
import { debounce } from 'lodash'
import { getOrInitSupabase } from '../lib/supabase'
import { Loan } from '../types'
import { FilterCounts, FormState, LoanFilterType, PaginatedLoans, SortConfig } from '../components/Loans/types'

export const useLoanService = () => {
  // State declarations
  const [loans, setLoans] = useState<Loan[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [page, setPage] = useState(1)
  const [perPage] = useState(5)
  const [filter, setFilter] = useState<LoanFilterType>('all')
  const [totalRemainingAmount, setTotalRemainingAmount] = useState<number>(0)
  const [filterCounts, setFilterCounts] = useState<FilterCounts>({
    all: 0,
    active: 0,
    paid: 0,
    overdue: 0,
    pending: 0,
    'days-0-30': 0,
    'days-31-60': 0,
    'days-61-90': 0,
    'days-90-plus': 0
  })
  const [formState, setFormState] = useState<FormState>({
    type: null,
    isOpen: false,
    selectedLoan: null,
    selectedClientId: null
  })
  const [totalLoans, setTotalLoans] = useState<number>(0)
  const [cachedTotalLoans, setCachedTotalLoans] = useState<number>(0)
  const [focusedLoanId, setFocusedLoanId] = useState<string | null>(null)
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'start_date',
    direction: 'desc'
  })

  // Update invoice number for a paid loan
  const updateInvoiceNumber = useCallback(async (loanId: string, invoiceNumber: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true)
      const supabase = await getOrInitSupabase()
      
      const { error } = await supabase
        .from('loans')
        .update({ 
          paid_invoice: invoiceNumber,
          updated_at: new Date().toISOString()
        })
        .eq('id', loanId)
        .select()
        .single()

      if (error) throw error

      // Update the local state
      setLoans(prevLoans => 
        prevLoans.map(loan => 
          loan.id === loanId 
            ? { ...loan, paid_invoice: invoiceNumber }
            : loan
        )
      )

      return { success: true }
    } catch (error) {
      console.error('Error updating invoice number:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update invoice number' 
      }
    } finally {
      setLoading(false)
    }
  }, [])

  // Calculate the number of days a loan has been active
  const calculateDaysActive = (loan: any): number => {
    const startDate = new Date(loan.start_date)
    const today = new Date()
    const diffTime = Math.abs(today.getTime() - startDate.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  // Helper function to check if a loan is in its creation month (grace period)
  const isCreationMonth = (loan: Loan): boolean => {
    if (!loan.start_date) {
      return false
    }

    const startDate = new Date(loan.start_date)
    const currentDate = new Date()

    return (
      startDate.getMonth() === currentDate.getMonth() &&
      startDate.getFullYear() === currentDate.getFullYear()
    )
  }

  // Calculate current month due amount for a loan
  const getCurrentMonthDueAmount = async (loan: Loan): Promise<number> => {
    if (loan.status === 'paid' || loan.remaining_balance <= 0) {
      return 0
    }

    // If in grace period (creation month), no payment is due
    if (isCreationMonth(loan)) {
      return 0
    }

    try {
      // Fetch payments for this loan
      const supabase = await getOrInitSupabase()
      const { data: payments } = await supabase
        .from('loan_payments')
        .select('*')
        .eq('loan_id', loan.id)

      // Calculate monthly payment amount (simple approach)
      const totalMonths = loan.loan_term || 12
      const monthlyPayment = Math.ceil(loan.loan_amount / totalMonths)

      // Get current month's payments
      const now = new Date()
      const currentMonth = now.getMonth()
      const currentYear = now.getFullYear()

      // Filter payments made in the current month
      const currentMonthPayments = (payments || []).filter(payment => {
        const paymentDate = new Date(payment.payment_date)
        return paymentDate.getMonth() === currentMonth &&
               paymentDate.getFullYear() === currentYear
      })

      // Sum up all payments made this month
      const paidThisMonth = currentMonthPayments.reduce(
        (total, payment) => total + (payment.amount || 0), 0
      )

      // Calculate remaining amount for this month
      return Math.max(0, monthlyPayment - paidThisMonth)

    } catch (error) {
      return 0
    }
  }

  // Fetch counts for each filter category
  const fetchFilterCounts = useCallback(async () => {
    try {
      const supabase = await getOrInitSupabase()

      // Get all loans for counting
      const { data: allLoans, error } = await supabase.from('loans').select('*')

      if (error) throw error

      if (allLoans) {
        // Calculate days active for non-paid loans
        const activeLoans = allLoans.filter((loan) => loan.status === 'active')
        const daysActiveCounts = {
          'days-0-30': 0,
          'days-31-60': 0,
          'days-61-90': 0,
          'days-90-plus': 0
        }

        // Calculate total remaining amount
        let totalRemaining = 0

        activeLoans.forEach((loan) => {
          const daysActive = calculateDaysActive(loan)

          // Add to the appropriate days active category
          if (daysActive <= 30) {
            daysActiveCounts['days-0-30']++
          } else if (daysActive <= 60) {
            daysActiveCounts['days-31-60']++
          } else if (daysActive <= 90) {
            daysActiveCounts['days-61-90']++
          } else {
            daysActiveCounts['days-90-plus']++
          }

          // Add to total remaining amount
          totalRemaining += loan.remaining_balance || 0
        })

        // Calculate pending count (loans with current month due > 0.01)

        // First, filter out loans in grace period and with no remaining balance
        const potentialPendingLoans = allLoans.filter(
          loan => loan.remaining_balance > 0 && !isCreationMonth(loan)
        )

        // For each loan, calculate current month due amount
        const pendingPromises = potentialPendingLoans.map(async (loan) => {
          const currentMonthDue = await getCurrentMonthDueAmount(loan)
          return currentMonthDue > 0.01
        })

        // Wait for all calculations to complete
        const pendingResults = await Promise.all(pendingPromises)

        // Count loans with current month due > 0.01
        const pendingCount = pendingResults.filter(Boolean).length

        // Set filter counts
        setFilterCounts({
          all: allLoans.length,
          active: allLoans.filter((loan) => loan.status === 'active').length,
          paid: allLoans.filter((loan) => loan.status === 'paid').length,
          overdue: allLoans.filter((loan) => loan.status === 'overdue').length,
          pending: pendingCount,
          ...daysActiveCounts
        })

        // Set total remaining amount
        setTotalRemainingAmount(totalRemaining)
      }
    } catch (error) {
      console.error('Error fetching filter counts:', error);
      // Set default filter counts to prevent UI issues
      setFilterCounts({
        all: 0,
        active: 0,
        paid: 0,
        overdue: 0,
        pending: 0,
        'days-0-30': 0,
        'days-31-60': 0,
        'days-61-90': 0,
        'days-90-plus': 0
      });
    }
  }, [])

  // Create a debounced fetch function
  const debouncedFetch = useMemo(
    () =>
      debounce(async (term: string) => {
        try {
          setLoading(true)
          const supabase = await getOrInitSupabase()

          // Helper function to calculate search relevance score
          const calculateRelevanceScore = (loan: any, searchTerm: string): number => {
            let score = 0;

            // Check client name exact match (highest priority)
            if (loan.clients) {
              const fullName = `${loan.clients.first_name} ${loan.clients.last_name}`.toLowerCase();
              if (fullName === searchTerm) score += 100;
              else if (fullName.includes(searchTerm)) score += 80;

              // Check individual name fields
              if (loan.clients.first_name?.toLowerCase() === searchTerm) score += 90;
              else if (loan.clients.first_name?.toLowerCase().includes(searchTerm)) score += 70;

              if (loan.clients.last_name?.toLowerCase() === searchTerm) score += 90;
              else if (loan.clients.last_name?.toLowerCase().includes(searchTerm)) score += 70;

              // ID number and email exact matches
              if (loan.clients.id_number?.toLowerCase() === searchTerm) score += 95;
              if (loan.clients.email?.toLowerCase() === searchTerm) score += 90;

              // Phone number
              if (loan.clients.phone?.toLowerCase().includes(searchTerm)) score += 85;
            }

            // Check invoice number (important field)
            if (loan.invoice_number?.toLowerCase() === searchTerm) score += 95;
            else if (loan.invoice_number?.toLowerCase().includes(searchTerm)) score += 75;

            // Check for firearm info in notes
            if (loan.notes) {
              const match = loan.notes.match(/Firearm ID: ([a-f0-9-]+)/i);
              if (match && match[1]) {
                // If there's a firearm ID in the notes, give it a high score
                score += 85;
              }
            }

            return score;
          };

          // --- START REPLACEMENT: Use RPC for search, keep client-side filtering/sorting ---
          let fetchedData: any[] = [];
          let fetchError: any = null;
          let fetchCount: number | null = null;

          if (term) {
            try {
              // If there is a search term, use the RPC function
              const trimmedTerm = term.trim().toLowerCase();
              const { data: loanIds, error: rpcError } = await supabase.rpc('search_loans', {
                search_term: trimmedTerm
              });

              if (loanIds && loanIds.length > 0) {
                // The RPC now returns just the loan IDs, so we need to fetch the full loan data
                // Simplified to avoid foreign key relationship errors
                const { data: loansWithClients } = await supabase
                  .from('loans')
                  .select(`
                    *,
                    clients(*)
                  `)
                  .in('id', loanIds);

                fetchedData = loansWithClients || [];
              } else {
                fetchedData = [];
              }

              fetchError = rpcError;
              // RPC doesn't easily return total count matching search, so we use length
              fetchCount = fetchedData.length;
            } catch (error) {
              // Fallback to direct query if RPC fails
              const trimmedTerm = term.trim().toLowerCase();

              // Build a query that searches across the same tables/columns
              // Simplified to avoid foreign key relationship errors
              const { data: fallbackData, count: fallbackCount } = await supabase
                .from('loans')
                .select(`
                  *,
                  clients(*)
                `, { count: 'exact' })
                .or(
                  `notes.ilike.%${trimmedTerm}%,` +
                  `invoice_number.ilike.%${trimmedTerm}%,` +
                  `clients.first_name.ilike.%${trimmedTerm}%,` +
                  `clients.last_name.ilike.%${trimmedTerm}%,` +
                  `clients.email.ilike.%${trimmedTerm}%,` +
                  `clients.phone.ilike.%${trimmedTerm}%,` +
                  `clients.id_number.ilike.%${trimmedTerm}%`
                );

              fetchedData = fallbackData || [];
              fetchCount = fallbackCount || 0;
            }
          } else {
            // If no search term, fetch normally (potentially with filters)
            let query = supabase
              .from('loans')
              .select(`
                *,
                clients(*)
              `, { count: 'exact' })

            // Simplified query to prevent 400 Bad Request error
            // We'll fetch firearm and assignment data separately

            // Prepare to fetch loans

            // Apply filter conditions (only when not searching via RPC)
            if (filter !== 'all') {
              if (filter === 'active') {
                query = query.eq('status', 'active')
              } else if (filter === 'paid') {
                query = query.eq('status', 'paid')
              } else if (filter === 'overdue') {
                query = query.eq('status', 'overdue')
              } else if (filter === 'pending') {
                // For pending filter, we'll fetch all loans with remaining balance
                // and filter them client-side based on current month due amount
                query = query.gt('remaining_balance', 0)
              } else if (filter.startsWith('days-')) {
                // Days active filtering is handled client-side below
                // But ensure we only fetch active loans for this calculation
                query = query.eq('status', 'active')
              }
            }

            const { data: normalData, error: normalError, count: normalCount } = await query;
            fetchedData = normalData || [];
            fetchError = normalError;
            fetchCount = normalCount;
          }

          if (fetchError) throw fetchError;

          let filteredLoans = fetchedData; // Start with data from RPC or normal fetch
          // --- END REPLACEMENT ---

          // Additional filtering for days active categories
          if (filter.startsWith('days-')) {
            filteredLoans = filteredLoans.filter((loan) => {
              const daysActive = calculateDaysActive(loan)

              if (filter === 'days-0-30') {
                return daysActive <= 30
              } else if (filter === 'days-31-60') {
                return daysActive > 30 && daysActive <= 60
              } else if (filter === 'days-61-90') {
                return daysActive > 60 && daysActive <= 90
              } else if (filter === 'days-90-plus') {
                return daysActive > 90
              }

              return true
            })
          }

          // Additional filtering for pending loans (current month due > 0.01)
          if (filter === 'pending') {
            // First, filter out loans in grace period
            const nonGraceLoans = filteredLoans.filter(loan => !isCreationMonth(loan))

            // For each loan, calculate current month due amount
            const loanPromises = nonGraceLoans.map(async (loan) => {
              const currentMonthDue = await getCurrentMonthDueAmount(loan)
              return { loan, currentMonthDue }
            })

            // Wait for all calculations to complete
            const loanResults = await Promise.all(loanPromises)

            // Filter loans with current month due > 0.01
            filteredLoans = loanResults
              .filter(result => result.currentMonthDue > 0.01)
              .map(result => result.loan)
          }

          // Sort the loans
          filteredLoans.sort((a, b) => {
            let aValue: any, bValue: any

            // If searching, prioritize exact matches first
            if (term) {
              const trimmedTerm = term.trim().toLowerCase();

              // Calculate relevance scores
              const aScore = calculateRelevanceScore(a, trimmedTerm);
              const bScore = calculateRelevanceScore(b, trimmedTerm);

              // If scores are different, sort by relevance first
              if (aScore !== bScore) {
                return bScore - aScore; // Higher score first
              }
            }

            // Otherwise, use regular sorting logic
            if (sortConfig.field === 'client') {
              // Special case for client name sorting
              const aName = `${a.clients?.last_name || ''} ${a.clients?.first_name || ''}`.trim().toLowerCase()
              const bName = `${b.clients?.last_name || ''} ${b.clients?.first_name || ''}`.trim().toLowerCase()
              aValue = aName
              bValue = bName
            } else if (sortConfig.field === 'amount') {
              aValue = a.amount || 0
              bValue = b.amount || 0
            } else if (sortConfig.field === 'remaining') {
              aValue = a.remaining_balance || 0
              bValue = b.remaining_balance || 0
            } else if (sortConfig.field === 'start_date') {
              aValue = new Date(a.start_date || 0).getTime()
              bValue = new Date(b.start_date || 0).getTime()
            } else if (sortConfig.field === 'days_active') {
              aValue = calculateDaysActive(a)
              bValue = calculateDaysActive(b)
            } else {
              aValue = a[sortConfig.field as keyof typeof a] || ''
              bValue = b[sortConfig.field as keyof typeof b] || ''
            }

            if (sortConfig.direction === 'asc') {
              return aValue > bValue ? 1 : -1
            } else {
              return aValue < bValue ? 1 : -1
            }
          })

          // Fetch firearm and assignment data separately
          if (filteredLoans.length > 0) {
            // 1. First, fetch firearm data for loans with firearm_id
            const loansWithFirearms = filteredLoans.filter(loan => loan.firearm_id);

            if (loansWithFirearms.length > 0) {
              try {
                const firearmIds = loansWithFirearms.map(loan => loan.firearm_id);

                // Fetch firearms data separately
                const { data: firearmsData, error: firearmsError } = await supabase
                  .from('firearms')
                  .select('*')
                  .in('id', firearmIds);

                if (firearmsError) {
                  console.error('Error fetching firearms data:', firearmsError);
                }

                // Merge firearms data with loans
                if (firearmsData && firearmsData.length > 0) {
                  filteredLoans = filteredLoans.map(loan => {
                    if (loan.firearm_id) {
                      const firearm = firearmsData.find(f => f.id === loan.firearm_id);
                      if (firearm) {
                        return {
                          ...loan,
                          firearm: firearm
                        };
                      }
                    }
                    return loan;
                  });
                }
              } catch (error) {
                console.error('Error fetching firearms data:', error);
              }
            }

            // 2. Then, fetch assignment data for loans with assignment_id
            const loansWithAssignments = filteredLoans.filter(loan => loan.assignment_id);

            if (loansWithAssignments.length > 0) {
              try {
                const assignmentIds = loansWithAssignments.map(loan => loan.assignment_id);

                // Fetch assignment data separately
                const { data: assignmentsData, error: assignmentsError } = await supabase
                  .from('firearm_assignments')
                  .select('*')
                  .in('id', assignmentIds);

                if (assignmentsError) {
                  console.error('Error fetching assignments data:', assignmentsError);
                }

                // Merge assignment data with loans
                if (assignmentsData && assignmentsData.length > 0) {
                  filteredLoans = filteredLoans.map(loan => {
                    if (loan.assignment_id) {
                      const assignment = assignmentsData.find(a => a.id === loan.assignment_id);
                      if (assignment) {
                        return {
                          ...loan,
                          assignment: assignment
                        };
                      }
                    }
                    return loan;
                  });
                }

                // 3. For assignments, fetch related firearms and clients data
                if (assignmentsData && assignmentsData.length > 0) {
                  // Get unique firearm IDs from assignments
                  const assignmentFirearmIds = assignmentsData
                    .filter(a => a.firearm_id)
                    .map(a => a.firearm_id);

                  // Get unique client IDs from assignments
                  const assignmentClientIds = assignmentsData
                    .filter(a => a.client_id)
                    .map(a => a.client_id);

                  // Fetch firearms for assignments
                  if (assignmentFirearmIds.length > 0) {
                    const { data: assignmentFirearmsData } = await supabase
                      .from('firearms')
                      .select('*')
                      .in('id', assignmentFirearmIds);

                    // Fetch clients for assignments
                    const { data: assignmentClientsData } = await supabase
                      .from('clients')
                      .select('*')
                      .in('id', assignmentClientIds);

                    // Merge the data with assignments and then with loans
                    if ((assignmentFirearmsData && assignmentFirearmsData.length > 0) ||
                        (assignmentClientsData && assignmentClientsData.length > 0)) {

                      // First, enhance assignments with firearms and clients
                      const enhancedAssignments = assignmentsData.map(assignment => {
                        const enhancedAssignment = { ...assignment };

                        if (assignment.firearm_id && assignmentFirearmsData) {
                          const firearm = assignmentFirearmsData.find(f => f.id === assignment.firearm_id);
                          if (firearm) {
                            enhancedAssignment.firearms = firearm;
                          }
                        }

                        if (assignment.client_id && assignmentClientsData) {
                          const client = assignmentClientsData.find(c => c.id === assignment.client_id);
                          if (client) {
                            enhancedAssignment.clients = client;
                          }
                        }

                        return enhancedAssignment;
                      });

                      // Then, update loans with enhanced assignments
                      filteredLoans = filteredLoans.map(loan => {
                        if (loan.assignment_id) {
                          const enhancedAssignment = enhancedAssignments.find(a => a.id === loan.assignment_id);
                          if (enhancedAssignment) {
                            return {
                              ...loan,
                              assignment: enhancedAssignment
                            };
                          }
                        }
                        return loan;
                      });
                    }
                  }
                }
              } catch (error) {
                console.error('Error fetching assignment data:', error);
              }
            }
          }

          setLoans(filteredLoans)
          setTotalLoans(fetchCount || 0)

          // Only update the cached total for full listings (no search term)
          if (!term && filter === 'all') {
            setCachedTotalLoans(fetchCount || 0)
          }
        } catch (error) {
          console.error('Error fetching loans:', error);
          // Show a user-friendly error message
          setLoans([]);
          setTotalLoans(0);
        } finally {
          setLoading(false)
        }
      }, 300),
    [filter]
  )

  // Fetch search hints for autocomplete
  const fetchSearchHints = useCallback(async (): Promise<string[]> => {
    try {
      const supabase = await getOrInitSupabase()

      // Get client data for search hints
      const { data: clientsData } = await supabase
        .from('clients')
        .select('first_name, last_name, id_number, email, phone')
        .limit(50)

      // Get firearms data for search hints
      const { data: firearmsData } = await supabase
        .from('firearms')
        .select('serial, make, model, stock_number')
        .limit(20)

      // Get loan invoice numbers
      const { data: loansData } = await supabase
        .from('loans')
        .select('invoice_number')
        .limit(20)

      const hints: string[] = []

      // Add invoice numbers (Quote Numbers)
      loansData?.forEach(loan => {
        if (loan.invoice_number) hints.push(loan.invoice_number)
      })

      // Add client names
      clientsData?.forEach(client => {
        if (client.first_name && client.last_name) {
          hints.push(`${client.first_name} ${client.last_name}`)
        }
      })

      // Add ID numbers
      clientsData?.forEach(client => {
        if (client.id_number) hints.push(client.id_number)
      })

      // Add email addresses
      clientsData?.forEach(client => {
        if (client.email) hints.push(client.email)
      })

      // Add phone numbers
      clientsData?.forEach(client => {
        if (client.phone) hints.push(client.phone)
      })

      // Add firearm serial numbers and make/model
      firearmsData?.forEach(firearm => {
        if (firearm.serial) hints.push(firearm.serial)
        if (firearm.make) hints.push(firearm.make)
        if (firearm.model) hints.push(firearm.model)
        if (firearm.stock_number) hints.push(firearm.stock_number)
      })

      // Remove duplicates and return
      return [...new Set(hints)]
    } catch (error) {
      return []
    }
  }, [])

  // Handle search
  const handleSearch = (query: string): void => {
    const trimmedQuery = query.trim()

    // Clear search and show all records
    if (!trimmedQuery) {
      setSearchQuery('')
      setLoans([]) // Clear current results
      setTotalLoans(cachedTotalLoans) // Use cached value
      debouncedFetch('') // Fetch all loans
      return
    }

    // Don't search if query is too short
    if (trimmedQuery.length < 2) {
      return
    }

    // Don't search if query hasn't changed
    if (trimmedQuery === searchQuery) {
      return
    }

    // Set page back to 1 when searching
    setPage(1)

    setSearchQuery(trimmedQuery)
    debouncedFetch(trimmedQuery)
  }

  // Handle sort
  const handleSort = (field: string): void => {
    setSortConfig(prevConfig => ({
      field,
      direction: prevConfig.field === field && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }))
  }

  // Handle loan focus toggle
  const handleLoanFocusToggle = (loanId: string | null) => {
    setFocusedLoanId(loanId)
  }

  // Handle create loan
  const handleCreateLoan = useCallback(() => {
    setFormState({
      type: 'loan',
      isOpen: true,
      selectedLoan: null,
      selectedClientId: null
    })
  }, [])

  // Handle create payment
  const handleCreatePayment = useCallback((loan: Loan) => {
    setFormState({
      type: 'payment',
      isOpen: true,
      selectedLoan: loan,
      selectedClientId: null
    })
  }, [])

  // Handle cancel loan
  const handleCancelLoan = useCallback(async (loanId: string): Promise<boolean> => {
    try {
      setLoading(true)
      const supabase = await getOrInitSupabase()

      // Update the loan status to 'cancelled'
      const { error: updateError } = await supabase
        .from('loans')
        .update({ 
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', loanId)

      if (updateError) {
        console.error('Failed to cancel loan:', updateError.message)
        throw new Error(`Failed to cancel loan: ${updateError.message}`)
      }

      // Refresh the loans list
      await debouncedFetch(searchQuery)
      return true
    } catch (error: any) {
      console.error('Error cancelling loan:', error)
      throw error // Re-throw to allow error handling in the component
    } finally {
      setLoading(false)
    }
  }, [debouncedFetch, searchQuery, setLoading])

  // Handle delete loan
  const handleDeleteLoan = useCallback(
    async (loanId: string): Promise<void> => {
      try {
        setLoading(true)
        const supabase = await getOrInitSupabase()

        // First, delete all associated loan payments
        const { error: paymentsError } = await supabase
          .from('loan_payments')
          .delete()
          .eq('loan_id', loanId)

        if (paymentsError) {
          throw new Error(`Failed to delete loan payments: ${paymentsError.message}`)
        }

        // Then delete the loan itself
        const { error: loanError } = await supabase
          .from('loans')
          .delete()
          .eq('id', loanId)

        if (loanError) {
          throw new Error(`Failed to delete loan: ${loanError.message}`)
        }

        // Refresh the loans list
        await debouncedFetch(searchQuery)

        // Clear focus if the deleted loan was focused
        if (focusedLoanId === loanId) {
          setFocusedLoanId(null)
        }
      } catch (error: any) {
        console.error('Error deleting loan:', error)
        throw error // Re-throw to allow error handling in the component
      } finally {
        setLoading(false)
      }
    },
    [debouncedFetch, searchQuery, focusedLoanId, setLoading, setFocusedLoanId]
  )

  // Update loan with firearm information
  const updateLoanWithFirearm = useCallback(async (loanId: string, firearmId: string, assignmentId: string) => {
    try {
      setLoading(true)
      if (!loanId || !firearmId || !assignmentId) {
        return
      }

      const supabase = await getOrInitSupabase()

      // Update the loan with the firearm_id and assignment_id directly in their respective columns
      // Note: Do NOT include license_id in the update as it's causing a 400 error
      const { error: updateError } = await supabase
        .from('loans')
        .update({
          firearm_id: firearmId, // Store the firearm ID in the proper column
          assignment_id: assignmentId, // Store the assignment ID in the proper column
          notes: `FIREARM ASSIGNED` // Add a simple note indicating a firearm is assigned
        })
        .eq('id', loanId)
        .select()

      if (updateError) {
        return
      }

      // Verify the update by fetching the loan directly
      await supabase
        .from('loans')
        .select('*')
        .eq('id', loanId)
        .single()

      // Refresh the loan data to show the updated information
      debouncedFetch(searchQuery)
    } catch (error) {
      console.error('Error updating loan with firearm:', error)
      // Error handling for updateLoanWithFirearm
    } finally {
      setLoading(false)
    }
  }, [debouncedFetch, searchQuery])

  // Handle add license to loan
  const handleAddLicense = useCallback((clientId: string, loanId: string) => {
    // Find the loan by ID to get its invoice_number
    const loan = loans.find(loan => loan.id === loanId);

    setFormState({
      type: 'firearm', // Use 'firearm' type instead of 'license'
      isOpen: true,
      selectedLoan: loan || { id: loanId },
      selectedClientId: clientId
    })
  }, [loans])

  // Reset form state
  const resetFormState = useCallback(() => {
    setFormState({
      type: null,
      isOpen: false,
      selectedLoan: null,
      selectedClientId: null
    })
  }, [])

  // Get paginated loans
  const paginatedLoans = useMemo((): PaginatedLoans => {
    const startIndex = (page - 1) * perPage
    const endIndex = startIndex + perPage
    return {
      loans: loans.slice(startIndex, endIndex),
      total: loans.length
    }
  }, [loans, page, perPage])

  // Get focused loan
  const focusedLoan = useMemo(() => {
    return focusedLoanId ? loans.find(loan => loan.id === focusedLoanId) : null
  }, [loans, focusedLoanId])

  // Load initial data
  useEffect(() => {
    debouncedFetch('')
    fetchFilterCounts()
  }, [debouncedFetch, fetchFilterCounts])

  // Custom search tips for loans
  const loanSearchTips = {
    title: 'Search tips:',
    items: [
      'Enter a client\'s name (e.g., "John Smith")',
      'Search by client ID number (e.g., "9803035727088")',
      'Search by client phone number (e.g., "0123456789")',
      'Search by client email (e.g., "<EMAIL>")',
      'Search by quote number (e.g., "INV0042")',
      'Search by firearm make, model, serial number, or stock number',
      'Search by loan notes',
      'Use filters to narrow down results'
    ]
  };

  useEffect(() => {
    if (focusedLoanId) {
      document.body.classList.add('focus-mode-active')
    } else {
      document.body.classList.remove('focus-mode-active')
    }

    return () => {
      document.body.classList.remove('focus-mode-active')
    }
  }, [focusedLoanId])

  return {
    // State
    loans,
    loading,
    searchQuery,
    page,
    perPage,
    filter,
    totalRemainingAmount,
    filterCounts,
    formState,
    totalLoans,
    focusedLoanId,
    focusedLoan,
    sortConfig,
    paginatedLoans,
    loanSearchTips,

    // Actions
    setPage,
    setFilter,
    handleSearch,
    handleSort,
    debouncedFetch,
    fetchFilterCounts,
    handleLoanFocusToggle,
    handleCreateLoan,
    handleCreatePayment,
    handleDeleteLoan,
    handleCancelLoan,
    handleAddLicense,
    resetFormState,
    calculateDaysActive,
    fetchSearchHints,
    isCreationMonth,
    getCurrentMonthDueAmount,
    updateLoanWithFirearm,
    updateInvoiceNumber
  }
}

