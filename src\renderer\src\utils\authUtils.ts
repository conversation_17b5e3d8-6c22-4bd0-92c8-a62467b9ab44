/**
 * Authentication and authorization utility functions
 */
import { createServiceRoleClient, clearSupabaseInstances } from '../lib/supabase';
import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Get the appropriate Supabase client based on the operation
 * - For admin operations that need to bypass RLS, use the service role client
 * - For regular operations, use the standard client
 *
 * @param requiresAdmin - Whether the operation requires admin privileges
 * @returns The appropriate Supabase client
 */
export const getAppropriateClient = async (requiresAdmin: boolean = false): Promise<SupabaseClient> => {
  if (requiresAdmin) {
    try {
      return await createServiceRoleClient();
    } catch (error) {
      console.error('Failed to create service role client:', error);
      throw new Error('Admin privileges required but not available');
    }
  }

  try {
    // Use getOrInitSupabase instead of getSupabase to ensure we have a client
    const supabase = await import('../lib/supabase').then(module => module.getOrInitSupabase());
    return supabase;
  } catch (error) {
    console.error('Failed to get standard client:', error);
    throw new Error('Database connection not available');
  }
};

/**
 * Check if the current user has admin role
 * @returns Boolean indicating if the user is an admin
 */
export const isUserAdmin = async (): Promise<boolean> => {
  try {
    // Get the Supabase client using dynamic import to ensure we have the latest instance
    const supabase = await import('../lib/supabase').then(module => module.getOrInitSupabase());
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return false;

    const { data, error } = await supabase
      .from('clients')
      .select('role')
      .eq('email', user.email)
      .single();

    if (error || !data) return false;

    return data.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

/**
 * Handle user logout properly by signing out and clearing all Supabase instances
 * @returns Promise that resolves when logout is complete
 */
export const handleUserLogout = async (): Promise<void> => {
  try {
    // Get the Supabase client
    const supabase = await import('../lib/supabase').then(module => module.getOrInitSupabase());

    // Sign out the user
    await supabase.auth.signOut();

    // Clear all Supabase instances to prevent issues with multiple clients
    clearSupabaseInstances();

    // Add a small delay to ensure everything is cleaned up
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('User logged out successfully');
  } catch (error) {
    console.error('Error during logout:', error);
    throw error;
  }
};
