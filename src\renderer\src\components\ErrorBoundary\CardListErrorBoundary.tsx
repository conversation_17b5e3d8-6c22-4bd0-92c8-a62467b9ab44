import React, { ErrorInfo, ReactNode } from 'react';
import ErrorBoundary from './ErrorBoundary';

interface CardListErrorBoundaryProps {
  children: ReactNode;
  listName?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  onReset?: () => void;
  resetKeys?: any[];
}

/**
 * An error boundary specifically designed for wrapping card list components.
 * Provides a list-specific error UI and reset functionality.
 */
const CardListErrorBoundary: React.FC<CardListErrorBoundaryProps> = ({ 
  children, 
  listName = 'items',
  onError,
  onReset,
  resetKeys
}) => {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    // Log the error
    console.error(`Error in ${listName} list:`, error, errorInfo);
    
    // Call the onError callback if provided
    if (onError) {
      onError(error, errorInfo);
    }
  };

  const handleReset = () => {
    if (onReset) {
      onReset();
    }
  };

  const ListErrorFallback = () => (
    <div className="p-6 rounded-lg bg-stone-800/50 border border-stone-700 text-white">
      <h3 className="text-lg font-medium text-orange-400 mb-2">Unable to display {listName}</h3>
      <p className="text-stone-300 mb-4">
        We encountered an error while trying to display this content.
        This could be due to a temporary issue or invalid data.
      </p>
      <button
        onClick={handleReset}
        className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors flex items-center"
      >
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Retry
      </button>
    </div>
  );

  return (
    <ErrorBoundary 
      fallback={<ListErrorFallback />} 
      onError={handleError}
      resetKeys={resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

export default CardListErrorBoundary;
