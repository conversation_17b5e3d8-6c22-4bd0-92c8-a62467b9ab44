import { useWindowSize } from '../contexts/WindowSizeContext'
import { useEffect, useRef } from 'react'

interface DialogProps {
  isOpen: boolean
  title: string
  message: React.ReactNode
  onConfirm: () => void
  onCancel: () => void
  onClose?: () => void
  confirmText?: string
  confirmButtonClass?: string
  isConfirming?: boolean
  confirmButtonVariant?: 'danger' | 'primary'
  cancelText?: string
}

export function Dialog({
  isOpen,
  title,
  message,
  onConfirm,
  onCancel,
  onClose,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  isConfirming = false,
  confirmButtonVariant = 'primary'
}: DialogProps): React.JSX.Element | null {
  const dialogRef = useRef<HTMLDivElement>(null)

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
        onClose?.()
      }
    }


    // Handle escape key
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose?.()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  // Use the window size context
  const { calculateResponsiveWidth } = useWindowSize()

  // Calculate responsive width based on window width
  const dialogWidth = Math.min(calculateResponsiveWidth(90), 500) // 90% of window width, max 500px

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm app-dialog">
      <div
        ref={dialogRef}
        className="bg-stone-800 border border-stone-700 rounded-lg shadow-xl p-6"
        style={{ width: `${dialogWidth}px`, marginTop: '20px' }}
      >
        <h3 className="text-xl font-semibold text-white mb-2">{title}</h3>
        <p className="text-stone-300 mb-6">{message}</p>
        <div className="flex justify-end gap-3">
          <button
            onClick={onCancel}
            disabled={isConfirming}
            className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            disabled={isConfirming}
            className={`px-4 py-2 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
              confirmButtonVariant === 'danger' 
                ? 'bg-red-500 hover:bg-red-600' 
                : 'bg-blue-500 hover:bg-blue-600'
            }`}
          >
            {isConfirming ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {confirmText}
              </span>
            ) : confirmText}
          </button>
        </div>
      </div>
    </div>
  )
}
