import { useState, useEffect } from 'react'
import { FormData, initialFormData } from '../../../types/FormData'
import {
  FormLayout,
  FormSectionType
} from '../../FormComponents'

// Import section components
import RepresentativeInfo from './sections/RepresentativeInfo'
import ClientInfo from './sections/ClientInfo'
import FirearmDetails from './sections/FirearmDetails'
import ActionType from './sections/ActionType'
import AdditionalInfo from './sections/AdditionalInfo'
import TemplateStatus from './sections/TemplateStatus'

// Import types
import { ValidationStatus, TemplateStatus as TemplateStatusType } from './utils/types'

// Constants
const TEMPLATE_URL =
  'https://app.gunlicence.co.za/storage/v1/object/public/templates/MISC/SAPS_Inspection_Report.docx'
const TEMPLATE_NAME = 'SAPS_Inspection_Report.docx'

// Types
interface SAPSInspectionReportFormProps {
  onSubmit: (data: FormData) => void
}

/**
 * SAPS Inspection Report Form Component
 *
 * A form for processing SAPS firearm inspection reports
 * that passes data to DocScript for document generation.
 */
export default function SAPSInspectionReport({ onSubmit }: SAPSInspectionReportFormProps): JSX.Element {
  // Form state
  const [formData, setFormData] = useState<FormData>({
    ...initialFormData
  })
  const [currentStep, setCurrentStep] = useState<number>(1)

  // Template state
  const [templateStatus, setTemplateStatus] = useState<TemplateStatusType>('loading')
  const [templateError] = useState<string | null>(null)

  // Submission state
  const [submissionStatus, setSubmissionStatus] = useState<ValidationStatus>('idle')
  const [submissionMessage, setSubmissionMessage] = useState<string>('')

  // Set template URL and name
  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      templateUrl: TEMPLATE_URL,
      templateName: TEMPLATE_NAME
    }))

    // Simulate template loading
    const timer = setTimeout(() => {
      setTemplateStatus('ready')
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Update form data with multiple fields
  const updateFormData = (newData: Partial<FormData>) => {
    setFormData((prev) => ({ ...prev, ...newData }))
  }

  // Define form sections
  const sections: FormSectionType[] = [
    { id: 'representative', title: 'Representative Information' },
    { id: 'client', title: 'Client Information' },
    { id: 'firearm', title: 'Firearm Details' },
    { id: 'action', title: 'Action Type' },
    { id: 'additional', title: 'Additional Information' }
  ]

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setSubmissionStatus('processing')
      setSubmissionMessage('Processing your form...')

      // Set document as processed
      const finalFormData = {
        ...formData,
        documentProcessed: true
      }

      // Submit the form data
      onSubmit(finalFormData)

      // Update submission status
      setSubmissionStatus('success')
      setSubmissionMessage('Form submitted successfully!')
    } catch (error) {
      console.error('Error submitting form:', error)
      setSubmissionStatus('error')
      setSubmissionMessage('Error submitting form. Please try again.')
    }
  }



  // Render the current step based on section ID
  const renderCurrentStep = () => {
    const sectionId = sections[currentStep - 1].id

    switch (sectionId) {
      case 'representative':
        return (
          <RepresentativeInfo
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'client':
        return (
          <ClientInfo
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'firearm':
        return (
          <FirearmDetails
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'action':
        return (
          <ActionType
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'additional':
        return (
          <AdditionalInfo
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      default:
        return null
    }
  }

  // Render template status component
  const renderTemplateStatus = () => (
    <TemplateStatus
      status={templateStatus}
      templateName={TEMPLATE_NAME}
      error={templateError}
    />
  )

  // Handle cancel submission
  const onCancelSubmission = () => {
    setSubmissionStatus('idle')
  }

  // Main component render
  return (
    <FormLayout
      title="SAPS Inspection Report"
      sections={sections}
      currentStep={currentStep}
      setCurrentStep={setCurrentStep}
      onSubmit={handleSubmit}
      submissionStatus={submissionStatus}
      submissionMessage={submissionMessage}
      onCancelSubmission={onCancelSubmission}
      isDocScriptForm={true}
    >
      {templateStatus === 'error' && renderTemplateStatus()}
      {renderCurrentStep()}
    </FormLayout>
  )
}
