# Supabase Storage Guide

This guide provides information on managing Supabase storage buckets for the FLM System.

## Current Storage Buckets

The system uses the following Supabase storage buckets:

1. **deal_documents** - Used for storing documents related to deals in the Pipedrive module
2. **templates** - Used for storing document templates (note: lowercase "templates" is required)

## Accessing Buckets via Supabase Dashboard

1. Log in to your Supabase dashboard at https://app.supabase.com/
2. Select your project
3. Navigate to "Storage" in the left sidebar
4. You should see your buckets listed

## Creating a New Bucket

### Via Supabase Dashboard

1. Go to the Storage section in your Supabase dashboard
2. Click "New Bucket"
3. Enter the bucket name
4. Set the bucket to public or private as needed
5. Click "Create bucket"

### Via API

```typescript
const { data, error } = await supabase.storage.createBucket('bucket_name', {
  public: true // or false for private buckets
});

if (error) {
  console.error('Error creating bucket:', error);
}
```

## Bucket Permissions

Ensure that your service role key has the necessary permissions to:
- List buckets
- Create buckets (if needed)
- Upload files
- Create signed URLs
- Delete files

These permissions are typically available by default with the service role key.

## Troubleshooting

### Common Issues

1. **"Bucket not found" error**
   - Verify the bucket exists in the Supabase dashboard
   - Check for typos in the bucket name
   - Ensure you're using the correct Supabase URL and API key

2. **Permission errors**
   - Make sure you're using the service role key for administrative operations
   - Check bucket policies in the Supabase dashboard

3. **Upload failures**
   - Check file size limits
   - Verify file types are allowed
   - Ensure the path is valid and doesn't contain invalid characters

### Testing Bucket Access

You can use the `test-supabase-storage.html` file in the project root to test bucket access. Simply open it in a browser, enter your Supabase credentials, and use the testing buttons to verify connectivity.

## Best Practices

1. **File Organization**
   - Use consistent path patterns (e.g., `deals/{dealId}/{fileId}-{filename}`)
   - Create folders for different entity types
   - Use UUIDs to prevent filename collisions

2. **Security**
   - Use signed URLs for private files
   - Set appropriate expiration times for signed URLs
   - Validate file types and sizes before upload

3. **Error Handling**
   - Always check for errors in Supabase responses
   - Implement cleanup for failed uploads
   - Log detailed error information for debugging

## Code Examples

### Uploading a File

```typescript
const filePath = `folder/${entityId}/${uuidv4()}-${fileName}`;
const { data, error } = await supabase.storage
  .from('bucket_name')
  .upload(filePath, file, {
    cacheControl: '3600',
    upsert: false
  });
```

### Getting a File URL

```typescript
// Public URL
const { data } = supabase.storage
  .from('bucket_name')
  .getPublicUrl(filePath);

// Signed URL (for private buckets)
const { data, error } = await supabase.storage
  .from('bucket_name')
  .createSignedUrl(filePath, 60 * 60); // 1 hour expiry
```

### Deleting a File

```typescript
const { error } = await supabase.storage
  .from('bucket_name')
  .remove([filePath]);
```
