import React, { useState, useEffect } from 'react';
import { PipelineStage } from '../../../types/pipedrive';

interface StageEditFormProps {
  stage: PipelineStage;
  onSubmit: (formData: { stageId: string; name: string; description: string | null }) => void;
  onCancel: () => void;
}

const StageEditForm: React.FC<StageEditFormProps> = ({ stage, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: ''
  });

  // Initialize form with stage data
  useEffect(() => {
    setFormData({
      name: stage.name
    });
  }, [stage]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      stageId: stage.id,
      name: formData.name,
      description: null
    });
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
        <h2 className="text-lg font-semibold text-white mb-2">Edit Stage Name</h2>
        <p className="text-stone-400 text-sm mb-4">You can rename any stage, including default stages like "Lead" and "Contact Made".</p>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-stone-300 mb-1">
              Stage Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
              required
            />
          </div>

          {/* Description field removed */}

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
            >
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StageEditForm;
