import { useState, useEffect, useCallback, useMemo } from 'react'
import { debounce } from 'lodash'
import { Client, License } from '../types'
import { getAppropriateClient } from '../utils/authUtils'
import {
  FormState,
  ToastState,
  FilterState,
  FilterCounts,
  PaginationState,
  VirtualizationResult,
  ClientSearchTips
} from '../components/Dashboard/types'

export const useDashboardService = () => {
  // State declarations
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [clientSearchQuery, setClientSearchQuery] = useState('')
  const [page, setPage] = useState<PaginationState['page']>(1)
  const [perPage] = useState<PaginationState['perPage']>(5)
  const [formState, setFormState] = useState<FormState>({
    type: null,
    isOpen: false,
    selectedClient: null,
    selectedLicense: null,
    selectedClientId: null
  })
  const [totalClients, setTotalClients] = useState<number>(0)
  const [cachedTotalClients, setCachedTotalClients] = useState<number>(0)
  const [toast, setToast] = useState<ToastState | null>(null)
  const [focusedClientId, setFocusedClientId] = useState<string | null>(null)
  const [clientFilter, setClientFilter] = useState<FilterState['clientFilter']>('all')

  // Filter counts
  const [filterCounts, setFilterCounts] = useState<FilterCounts>({
    all: 0,
    'with-licenses': 0,
    'no-licenses': 0
  })

  // Fetch search hints (client names, emails, etc.)
  const fetchSearchHints = useCallback(async (): Promise<string[]> => {
    try {
      // Use service role client to bypass RLS if needed
      const supabase = await getAppropriateClient(true)

      // Get client data for search hints
      const { data: clientsData } = await supabase
        .from('clients')
        .select('first_name, last_name, email, id_number')
        .limit(50)

      if (clientsData) {
        const hints: string[] = []

        // Add full names
        clientsData.forEach((client) => {
          if (client.first_name && client.last_name) {
            hints.push(`${client.first_name} ${client.last_name}`)
          }
        })

        // Add emails
        clientsData.forEach((client) => {
          if (client.email) {
            hints.push(client.email)
          }
        })

        // Add ID numbers
        clientsData.forEach((client) => {
          if (client.id_number) {
            hints.push(client.id_number)
          }
        })

        // Remove duplicates and return hints
        return [...new Set(hints)]
      }

      return []
    } catch (error) {
      console.error('Error fetching search hints:', error)
      return []
    }
  }, [])

  // Fetch filter counts
  const fetchFilterCounts = useCallback(async (): Promise<void> => {
    try {
      // Use service role client to bypass RLS if needed
      const supabase = await getAppropriateClient(true)

      // Get total count
      const { count: allCount } = await supabase
        .from('clients')
        .select('*', { count: 'exact', head: true })

      // Get clients with licenses count
      const { count: withLicensesCount } = await supabase
        .from('clients')
        .select('*, gun_licences!inner(*)', { count: 'exact', head: true })

      // Get clients without licenses count
      const { count: noLicensesCount } = await supabase
        .from('clients')
        .select('*, gun_licences(*)', { count: 'exact', head: true })
        .is('gun_licences.id', null)

      setFilterCounts({
        all: allCount || 0,
        'with-licenses': withLicensesCount || 0,
        'no-licenses': noLicensesCount || 0
      })
    } catch (error) {
      console.error('Error fetching filter counts:', error)
    }
  }, [])

  // Create a debounced fetch function
  const debouncedFetch = useMemo(
    () =>
      debounce(async (clientTerm: string) => {
        try {
          setLoading(true)
          // Use service role client to bypass RLS if needed
          const supabase = await getAppropriateClient(true)
          let query = supabase
            .from('clients')
            .select('*, gun_licences(*)', { count: 'exact' })
            .order('last_name', { ascending: true })

          const conditions: string[] = []

          // Apply filter conditions
          if (clientFilter !== 'all') {
            if (clientFilter === 'with-licenses') {
              // Clients with licenses
              query = query.not('gun_licences', 'is', null)
            } else if (clientFilter === 'no-licenses') {
              // Clients without licenses
              query = query.is('gun_licences', null)
            }
          }

          // Only add search conditions if there's a term
          if (clientTerm) {
            const trimmedTerm = clientTerm.trim()
            const terms = trimmedTerm.split(' ').filter((t) => t.length > 0)

            // Enhanced search logic
            if (trimmedTerm.includes('@')) {
              // Email search
              conditions.push(`email.ilike.%${trimmedTerm}%`)
            } else if (/^\d+$/.test(trimmedTerm)) {
              // ID number or phone search - partial match for both
              conditions.push(`id_number.ilike.%${trimmedTerm}%`)
              conditions.push(`phone.ilike.%${trimmedTerm}%`)
            } else if (terms.length > 1) {
              // Full name search with multiple terms
              // Try different combinations for first name and last name
              const possibleFirstName = terms.slice(0, -1).join(' ')
              const possibleLastName = terms[terms.length - 1]

              // First name + last name pattern
              conditions.push(
                `and(first_name.ilike.%${possibleFirstName}%,last_name.ilike.%${possibleLastName}%)`
              )

              // Last name + first name pattern
              conditions.push(
                `and(first_name.ilike.%${possibleLastName}%,last_name.ilike.%${possibleFirstName}%)`
              )

              // Also search for the full term in either field (for hyphenated names or special cases)
              conditions.push(
                `or(first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%)`
              )
            } else {
              // Single term search - check all text fields
              conditions.push(
                `or(first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%,email.ilike.%${trimmedTerm}%,phone.ilike.%${trimmedTerm}%,id_number.ilike.%${trimmedTerm}%)`
              )
            }

            if (conditions.length > 0) {
              query = query.or(conditions.join(','))
            }
          }

          const { data, error, count } = await query
          if (error) throw error

          setClients(data || [])
          setTotalClients(count || 0)

          // Only update the cached total for full listings (no search term)
          if (!clientTerm) {
            setCachedTotalClients(count || 0)
          }
        } catch (error) {
          console.error('Error fetching clients:', error)
        } finally {
          setLoading(false)
        }
      }, 400),
    [clientFilter]
  )

  // Effect to handle initial fetch and filter count fetch
  useEffect(() => {
    // Initial fetch when component mounts
    debouncedFetch('')
    fetchFilterCounts()

    // Clean up when unmounting
    return (): void => debouncedFetch.cancel()
  }, [debouncedFetch, fetchFilterCounts])

  // Effect to handle filter changes separately from search
  useEffect(() => {
    // When filter changes, maintain the search
    debouncedFetch(clientSearchQuery)
  }, [clientFilter, debouncedFetch, clientSearchQuery])

  // Apply body overflow class when focus mode is active
  useEffect(() => {
    if (focusedClientId) {
      document.body.classList.add('focus-mode-active')
    } else {
      document.body.classList.remove('focus-mode-active')
    }

    return () => {
      document.body.classList.remove('focus-mode-active')
    }
  }, [focusedClientId])

  // Add a useEffect to disable scrolling on the main content area and body
  useEffect(() => {
    // Get the main content area
    const mainContent = document.querySelector(
      '#root > div > div:nth-child(2)'
    ) as HTMLElement | null

    // Save original styles
    const originalBodyOverflow = document.body.style.overflow
    const originalHtmlOverflow = document.documentElement.style.overflow

    // Disable scrolling on body and html
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    if (mainContent) {
      // Save the original style
      const originalOverflow = mainContent.style.overflowY

      // Disable scrolling
      mainContent.style.overflowY = 'hidden'

      // Restore original styles when component unmounts
      return () => {
        mainContent.style.overflowY = originalOverflow
        document.body.style.overflow = originalBodyOverflow
        document.documentElement.style.overflow = originalHtmlOverflow
      }
    }

    // Restore original styles when component unmounts (if mainContent not found)
    return () => {
      document.body.style.overflow = originalBodyOverflow
      document.documentElement.style.overflow = originalHtmlOverflow
    }
  }, [])

  // Handle client focus toggle
  const handleClientFocusToggle = (clientId: string | null) => {
    setFocusedClientId(clientId)
    // Reset to page 1 when focusing on a client to ensure it's visible
    if (clientId) {
      setPage(1)
    }
  }

  // Get focused client object
  const focusedClient = useMemo(() => {
    if (!focusedClientId) return null
    return clients.find((client) => client.id === focusedClientId) || null
  }, [clients, focusedClientId])

  // Get paginated clients
  const paginatedClients: VirtualizationResult<Client> = useMemo(() => {
    const lastIndex = page * perPage
    const firstIndex = lastIndex - perPage

    return {
      clients: clients.slice(firstIndex, lastIndex),
      total: clients.length
    }
  }, [clients, page, perPage])

  // Handle delete license
  const handleDeleteLicense = useCallback(
    async (licenseId: string): Promise<void> => {
      try {
        // Use service role client for admin operations
        const supabase = await getAppropriateClient(true)
        const { error } = await supabase.from('gun_licences').delete().eq('id', licenseId)

        if (error) throw error
        debouncedFetch(clientSearchQuery)
      } catch (error) {
        console.error('Error deleting license:', error)
      }
    },
    [clientSearchQuery, debouncedFetch]
  )

  // Handle delete client
  const handleDeleteClient = useCallback(
    async (clientId: string): Promise<void> => {
      try {
        // Use service role client for admin operations
        const supabase = await getAppropriateClient(true)

        // First check if client is an admin
        const { data: clientData, error: fetchError } = await supabase
          .from('clients')
          .select('role')
          .eq('id', clientId)
          .single()

        if (fetchError) throw fetchError

        // Prevent deletion if client is an admin
        if (clientData?.role === 'admin') {
          console.error('Cannot delete admin users')
          setToast({
            message: 'Admin users cannot be deleted',
            type: 'error'
          })
          return
        }

        const { error } = await supabase.from('clients').delete().eq('id', clientId)
        if (error) throw error
        debouncedFetch(clientSearchQuery)
      } catch (error) {
        console.error('Error deleting client:', error)
      }
    },
    [clientSearchQuery, debouncedFetch]
  )

  // Reset form state
  const resetFormState = useCallback((): void => {
    setFormState({
      type: null,
      isOpen: false,
      selectedClient: null,
      selectedLicense: null,
      selectedClientId: null
    })
  }, [])

  // Form handlers
  const formHandlers = useMemo<{
    onEditClient: (client: Client) => void
    onAddLicense: (clientId: string) => void
    onEditLicense: (license: License, clientId: string) => void
  }>(
    () => ({
      onEditClient: (client: Client): void => {
        if (client.role === 'admin') {
          setToast({
            message: 'Admin users cannot be edited',
            type: 'error'
          })
          return
        }

        setFormState({
          type: 'client',
          isOpen: true,
          selectedClient: client,
          selectedLicense: null,
          selectedClientId: null
        })
      },
      onAddLicense: (clientId: string): void =>
        setFormState({
          type: 'license',
          isOpen: true,
          selectedClient: null,
          selectedLicense: null,
          selectedClientId: clientId
        }),
      onEditLicense: (license: License, clientId: string): void =>
        setFormState({
          type: 'license',
          isOpen: true,
          selectedClient: null,
          selectedLicense: license,
          selectedClientId: clientId
        })
    }),
    []
  )

  // Handle search
  const handleSearch = (query: string): void => {
    const trimmedQuery = query.trim()

    // Clear search and show all records
    if (!trimmedQuery) {
      setClientSearchQuery('')
      setClients([]) // Clear current results
      setTotalClients(cachedTotalClients) // Use cached value
      debouncedFetch('') // Fetch all clients
      return
    }

    // Don't search if query is too short
    if (trimmedQuery.length < 2) {
      return
    }

    // Don't search if query hasn't changed
    if (trimmedQuery === clientSearchQuery) {
      return
    }

    setClientSearchQuery(trimmedQuery)
    debouncedFetch(trimmedQuery)
  }

  // Handle create client
  const handleCreateClient = () => {
    setFormState({
      ...formState,
      type: 'client',
      isOpen: true,
      selectedClient: null
    })
  }

  // Custom search tips for clients
  const clientSearchTips: ClientSearchTips = {
    title: 'Search tips:',
    items: [
      'Enter a client\'s name (e.g., "John Smith")',
      'Search by ID number (e.g., "9803035727088")',
      'Search by email (e.g., "<EMAIL>")',
      'Search by phone number (e.g., "**********")',
      'Use more specific terms for better results'
    ]
  }

  return {
    // State
    clients,
    loading,
    clientSearchQuery,
    page,
    perPage,
    formState,
    totalClients,
    toast,
    focusedClientId,
    focusedClient,
    clientFilter,
    filterCounts,
    paginatedClients,
    clientSearchTips,

    // Actions
    setPage,
    setClientFilter,
    handleSearch,
    debouncedFetch,
    handleDeleteLicense,
    handleDeleteClient,
    resetFormState,
    handleClientFocusToggle,
    handleCreateClient,
    setToast,
    formHandlers,
    fetchSearchHints
  }
}
