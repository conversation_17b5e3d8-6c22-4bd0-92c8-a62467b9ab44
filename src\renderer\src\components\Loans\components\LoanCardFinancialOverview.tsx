import React from 'react'
import { Loan } from '../../../types'
import { formatCurrency } from '../../../utils/formatters'

interface LoanCardFinancialOverviewProps {
  loan: Loan
  loanTermInfo: {
    formattedTerm: string
  }
  nextPaymentAmount: {
    amount: number
    dueDate: Date | null
  }
  isCreationMonth: boolean
  hasInsufficientPastPayments: boolean
  formatDate: (date: string) => string
}

const LoanCardFinancialOverview: React.FC<LoanCardFinancialOverviewProps> = ({
  loan,
  loanTermInfo,
  nextPaymentAmount,
  isCreationMonth,
  hasInsufficientPastPayments,
  formatDate
}) => {
  return (
    <div className="bg-stone-800/50 rounded-lg p-3 border border-stone-700/50">
      <div className="grid grid-cols-5 gap-3 text-sm">
        <div className="text-center rounded-md p-1.5">
          <div className="text-stone-300 text-xs font-medium mb-1">Firearm Cost</div>
          <div className="text-white font-medium">{formatCurrency(loan.weapon_cost)}</div>
        </div>
        <div className="text-center rounded-md p-1.5">
          <div className="text-stone-300 text-xs font-medium mb-1">Loan Amount</div>
          <div className="text-white font-medium">{formatCurrency(loan.loan_amount)}</div>
        </div>
        <div className="text-center rounded-md p-1.5">
          <div className="text-stone-300 text-xs font-medium mb-1">Remaining</div>
          <div className="text-white font-medium">
            {formatCurrency(loan.remaining_balance)}
          </div>
        </div>
        <div className="text-center rounded-md p-1.5">
          <div className="text-stone-300 text-xs font-medium mb-1">Term</div>
          <div className="text-white font-medium">{loanTermInfo.formattedTerm}</div>
        </div>
        {/* Next Payment Due - With colored background */}
        {loan.status !== 'paid' && loan.remaining_balance > 0 ? (
          <div
            className={`text-center rounded-md p-1.5 ${
              isCreationMonth
                ? 'bg-green-500/20 border border-green-500/30'
                : hasInsufficientPastPayments
                  ? 'bg-red-500/20 border border-red-500/30'
                  : 'bg-orange-500/20 border border-orange-500/30'
            }`}
          >
            <div
              className={`text-xs font-medium mb-1 ${
                isCreationMonth
                  ? 'text-green-400'
                  : hasInsufficientPastPayments
                    ? 'text-red-400'
                    : 'text-orange-400'
              }`}
            >
              Current Due
            </div>
            <div className="text-white font-medium">
              {formatCurrency(nextPaymentAmount.amount)}
            </div>
            {nextPaymentAmount.dueDate && (
              <div className="text-xs text-stone-400 mt-0.5">
                {formatDate(nextPaymentAmount.dueDate?.toISOString() || '')}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center bg-green-500/20 border border-green-500/30 rounded-md p-1.5">
            <div className="text-green-400 text-xs font-medium mb-1">Payment</div>
            <div className="text-green-400 font-medium">Paid in Full</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default React.memo(LoanCardFinancialOverview)
