import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'
import { isValidPastDate } from '../utils/helpers'

/**
 * 350 Information date component for E350 Information form
 */
const PeriodInfo: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">350 INFORMATION</h3>

      <FormSection title="Date Information" subtitle="Enter the period date information">
        <div className="space-y-3">
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Period Date From</label>
                <input
                  type="date"
                  name="periodDateFrom"
                  value={formData.periodDateFrom || ''}
                  onChange={handleChange}
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                  placeholder="Format YYYY-MM-DD"
                />
                {formData.periodDateFrom && !isValidPastDate(formData.periodDateFrom) && (
                  <p className="text-xs text-red-400 mt-1">
                    Date cannot be in the future
                  </p>
                )}
              </div>
            </div>
          </div>

          <p className="text-xs text-stone-400 mt-1">
            Enter the period date for the E350 Information form (Format YYYY-MM-DD)
          </p>
        </div>
      </FormSection>
    </div>
  )
}

export default PeriodInfo
