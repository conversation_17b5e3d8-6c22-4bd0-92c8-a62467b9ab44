import React from 'react'
import { useSystemInfo } from './hooks/useSystemInfo'
import { useUpdateSettings } from './hooks/useUpdateSettings'

const SystemSettings: React.FC = () => {
  const { currentVersion } = useUpdateSettings()
  const { getSystemInfo } = useSystemInfo(currentVersion)
  
  const systemInfo = getSystemInfo()

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-white border-b border-stone-700/50 pb-1 mb-4">
        System Settings
      </h2>

      {/* System Information */}
      <div className="flex flex-col space-y-1 mb-4">
        <label className="text-stone-300 font-medium text-sm">System Information</label>

        <div className="grid grid-cols-1 gap-2 bg-stone-900/50 p-3 rounded-lg border border-stone-700/50">
          <div className="flex justify-between items-center py-1 border-b border-stone-700/30">
            <div className="text-stone-300 text-sm">Application Name</div>
            <div className="text-white text-sm">{systemInfo.appName}</div>
          </div>

          <div className="flex justify-between items-center py-1 border-b border-stone-700/30">
            <div className="text-stone-300 text-sm">Version</div>
            <div className="text-white text-sm">{systemInfo.version}</div>
          </div>

          <div className="flex justify-between items-center py-1 border-b border-stone-700/30">
            <div className="text-stone-300 text-sm">Electron Version</div>
            <div className="text-white text-sm">{systemInfo.electronVersion}</div>
          </div>

          <div className="flex justify-between items-center py-1 border-b border-stone-700/30">
            <div className="text-stone-300 text-sm">Node.js Version</div>
            <div className="text-white text-sm">{systemInfo.nodeVersion}</div>
          </div>

          <div className="flex justify-between items-center py-1">
            <div className="text-stone-300 text-sm">Platform</div>
            <div className="text-white text-sm">{systemInfo.platform}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SystemSettings
