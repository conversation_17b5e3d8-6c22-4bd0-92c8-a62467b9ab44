/* Focus mode styles */
@tailwind utilities;

@layer utilities {
  .scale-102 {
    transform: scale(1.02);
    transition: transform 0.3s ease-in-out;
  }

  .scale-98 {
    transform: scale(0.98);
    transition:
      transform 0.3s ease-in-out,
      opacity 0.3s ease-in-out;
  }

  /* Fixed positioning for focused card */
  .focus-mode-active {
    @apply overflow-hidden;
  }

  .focus-mode-container {
    position: fixed;
    top: 40px; /* Leave space for titlebar */
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    background-color: rgba(28, 25, 23, 0.85);
    backdrop-filter: blur(3px);
    padding: 2rem;
    overflow-y: auto;
    animation: fadeIn 0.35s cubic-bezier(0.16, 1, 0.3, 1);
    transform-origin: center;
  }

  .focus-mode-card {
    max-width: 90vw;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: scaleIn 0.35s cubic-bezier(0.34, 1.56, 0.64, 1); /* Slightly bouncy effect */
    box-shadow:
      0 10px 25px -5px rgba(0, 0, 0, 0.5),
      0 8px 10px -6px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(249, 115, 22, 0.2); /* Subtle orange outline */
    border-radius: 1rem;
    transform-origin: center;
    transform: scale(1);
    padding: 0.5rem;
  }

  /* Exit animation classes - add these with JS before removing the element */
  .focus-mode-exit {
    animation: fadeOut 0.25s ease-in-out forwards;
  }

  .focus-mode-card-exit {
    animation: scaleOut 0.25s ease-in-out forwards;
  }

  /* Animations */
  @keyframes fadeIn {
    0% {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    100% {
      opacity: 1;
      backdrop-filter: blur(3px);
    }
  }

  @keyframes scaleIn {
    0% {
      transform: scale(0.92);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
      backdrop-filter: blur(3px);
    }
    100% {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
  }

  @keyframes scaleOut {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(0.92);
      opacity: 0;
    }
  }
}
