import { useState, useRef, useEffect } from 'react'
import { DashboardIcons } from './icons/DashboardIcons'
import { useDebouncedCallback } from '../hooks/useDebounce'

export interface SearchBarProps {
  onSearch: (query: string) => void
  placeholder?: string
  isLoading?: boolean
  initialValue?: string
  autoFocus?: boolean
  searchHints?: string[]
  showSearchTips?: boolean
  searchTipsContent?: React.ReactNode
  className?: string
  maxWidth?: string
  debounceTime?: number
  onClear?: () => void
  maxSearchHistory?: number
}

function SearchBar({
  onSearch,
  placeholder = 'Search...',
  isLoading = false,
  initialValue = '',
  autoFocus = false,
  searchHints = [],
  className = '',
  maxWidth = 'max-w-md',
  debounceTime = 300,
  onClear,
  maxSearchHistory = 5
}: SearchBarProps): React.JSX.Element {
  const [searchTerm, setSearchTerm] = useState(initialValue)
  const [showHints, setShowHints] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [activeHint, setActiveHint] = useState(-1)
  const [searchHistory, setSearchHistory] = useState<string[]>(() => {
    try {
      const savedHistory = localStorage.getItem('searchHistory')
      return savedHistory ? JSON.parse(savedHistory) : []
    } catch (error) {
      console.error('Error loading search history:', error)
      return []
    }
  })

  const inputRef = useRef<HTMLInputElement>(null)
  const hintsRef = useRef<HTMLDivElement>(null)
  const historyRef = useRef<HTMLDivElement>(null)

  // Filter hints based on current search term
  const filteredHints = searchTerm
    ? searchHints.filter(hint =>
        hint.toLowerCase().includes(searchTerm.toLowerCase())
      ).slice(0, 5)
    : [];

  // Save search history to localStorage
  useEffect(() => {
    try {
      localStorage.setItem('searchHistory', JSON.stringify(searchHistory))
    } catch (error) {
      console.error('Error saving search history:', error)
    }
  }, [searchHistory])

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  useEffect(() => {
    // Close hints and history when clicking outside
    const handleClickOutside = (e: MouseEvent) => {
      if (
        (hintsRef.current && !hintsRef.current.contains(e.target as Node)) &&
        (historyRef.current && !historyRef.current.contains(e.target as Node)) &&
        (inputRef.current && !inputRef.current.contains(e.target as Node))
      ) {
        setShowHints(false)
        setShowHistory(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Add a search term to history
  const addToHistory = (term: string) => {
    if (!term.trim()) return

    setSearchHistory(prev => {
      // Remove if already exists
      const filtered = prev.filter(item => item !== term)
      // Add to beginning and limit to max history items
      return [term, ...filtered].slice(0, maxSearchHistory)
    })
  }

  // Debounced search function using our custom hook
  const debouncedSearch = useDebouncedCallback(
    (value: string) => {
      onSearch(value)
    },
    debounceTime
  )

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)

    if (value) {
      setShowHints(true)
      setShowHistory(false)
    } else {
      setShowHistory(searchHistory.length > 0)
      setShowHints(false)
    }

    setActiveHint(-1)

    // If debounce is enabled, use debounced search
    if (debounceTime > 0) {
      debouncedSearch(value)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Ignore empty searches
    if (!searchTerm.trim()) return

    addToHistory(searchTerm)
    onSearch(searchTerm)
    setShowHints(false)
    setShowHistory(false)
  }

  const handleClear = () => {
    setSearchTerm('')
    setShowHints(false)
    setShowHistory(searchHistory.length > 0)

    onSearch('')

    if (onClear) {
      onClear()
    }

    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  const handleSelectItem = (term: string) => {
    setSearchTerm(term)

    addToHistory(term)
    onSearch(term)
    setShowHints(false)
    setShowHistory(false)
  }

  const handleHistoryClick = (term: string) => {
    handleSelectItem(term)
  }

  const handleRemoveHistoryItem = (e: React.MouseEvent, term: string) => {
    e.stopPropagation()
    setSearchHistory(prev => prev.filter(item => item !== term))
  }

  const handleClearHistory = (e: React.MouseEvent) => {
    e.stopPropagation()
    setSearchHistory([])
    setShowHistory(false)
  }

  const handleHintClick = (hint: string) => {
    handleSelectItem(hint)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Toggle history with alt+down when input is empty
    if (e.key === 'ArrowDown' && e.altKey && !searchTerm) {
      e.preventDefault()
      setShowHistory(prev => !prev)
      setActiveHint(-1)
      return
    }

    // Handle keyboard navigation for hints
    if (showHints && filteredHints.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setActiveHint(prev => (prev < filteredHints.length - 1 ? prev + 1 : prev))
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setActiveHint(prev => (prev > 0 ? prev - 1 : prev))
      } else if (e.key === 'Enter' && activeHint >= 0) {
        e.preventDefault()
        handleHintClick(filteredHints[activeHint])
      } else if (e.key === 'Escape') {
        setShowHints(false)
      }
    }
    // Handle keyboard navigation for history
    else if (showHistory && searchHistory.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setActiveHint(prev => (prev < searchHistory.length - 1 ? prev + 1 : prev))
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setActiveHint(prev => (prev > 0 ? prev - 1 : prev))
      } else if (e.key === 'Enter' && activeHint >= 0) {
        e.preventDefault()
        handleHistoryClick(searchHistory[activeHint])
      } else if (e.key === 'Escape') {
        setShowHistory(false)
      }
    }
  }

  const handleFocus = () => {
    if (!searchTerm && searchHistory.length > 0) {
      setShowHistory(true)
    } else if (searchTerm) {
      setShowHints(true)
    }
  }

  return (
    <div className={`relative w-full ${maxWidth} ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={searchTerm}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="w-full bg-stone-700/50 text-white pl-10 pr-10 py-3 rounded-lg
              focus:ring-2 focus:ring-orange-500 focus:bg-stone-700 outline-none transition-all"
            aria-label="Search"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-stone-400">
            <DashboardIcons.Search className="w-5 h-5" />
          </div>

          {/* Clear button */}
          {searchTerm && (
            <button
              type="button"
              onClick={handleClear}
              className="absolute right-10 top-1/2 transform -translate-y-1/2 text-stone-400
                hover:text-white transition-colors"
              aria-label="Clear search"
            >
              <DashboardIcons.Close className="w-5 h-5" />
            </button>
          )}

          {/* Submit button */}
          <button
            type="submit"
            disabled={isLoading}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-stone-400
              hover:text-white transition-colors disabled:opacity-50"
            aria-label="Submit search"
          >
            {isLoading ? (
              <DashboardIcons.Spinner className="w-5 h-5 animate-spin" />
            ) : (
              <DashboardIcons.Enter className="w-5 h-5" />
            )}
          </button>
        </div>
      </form>

      {/* Search hints dropdown */}
      {showHints && filteredHints.length > 0 && (
        <div
          ref={hintsRef}
          className="absolute z-10 mt-1 w-full bg-stone-800 rounded-lg shadow-lg border border-stone-700 overflow-hidden"
        >
          <div className="p-1 text-xs text-stone-400 border-b border-stone-700">
            Search suggestions:
          </div>
          <ul>
            {filteredHints.map((hint, index) => (
              <li
                key={index}
                onClick={() => handleHintClick(hint)}
                onMouseEnter={() => setActiveHint(index)}
                className={`px-3 py-2 cursor-pointer flex items-center ${
                  index === activeHint
                    ? 'bg-orange-500/20 text-white'
                    : 'text-stone-300 hover:bg-stone-700'
                }`}
              >
                <DashboardIcons.Search className="w-4 h-4 mr-2 text-stone-500" />
                {hint}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Search history dropdown */}
      {showHistory && searchHistory.length > 0 && (
        <div
          ref={historyRef}
          className="absolute z-10 mt-1 w-full bg-stone-800 rounded-lg shadow-lg border border-stone-700 overflow-hidden"
        >
          <div className="p-1 text-xs flex justify-between items-center text-stone-400 border-b border-stone-700">
            <span>Recent searches:</span>
            <button
              onClick={handleClearHistory}
              className="text-stone-500 hover:text-stone-300 text-xs"
            >
              Clear All
            </button>
          </div>
          <ul>
            {searchHistory.map((term, index) => (
              <li
                key={index}
                onClick={() => handleHistoryClick(term)}
                onMouseEnter={() => setActiveHint(index)}
                className={`px-3 py-2 cursor-pointer flex items-center justify-between ${
                  index === activeHint
                    ? 'bg-orange-500/20 text-white'
                    : 'text-stone-300 hover:bg-stone-700'
                }`}
              >
                <div className="flex items-center">
                  <DashboardIcons.History className="w-4 h-4 mr-2 text-stone-500" />
                  {term}
                </div>
                <button
                  onClick={(e) => handleRemoveHistoryItem(e, term)}
                  className="text-stone-500 hover:text-stone-300"
                >
                  <DashboardIcons.Close className="w-3 h-3" />
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

export default SearchBar
