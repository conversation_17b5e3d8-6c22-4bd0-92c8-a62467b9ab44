import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { Deal, DealDocument } from '../../types/pipedrive';
import { DashboardIcons } from '../icons/DashboardIcons';
import { usePipedriveService } from '../../services/usePipedriveService';
import NotesPreview from './NotesPreview';
import MoveDealModal from './MoveDealModal';

interface DealCardProps {
  deal: Deal;
  onDragStart: (e: React.DragEvent, dealId: string) => void;
  onEditDeal?: (deal: Deal) => void;
  onDeleteDeal?: (deal: Deal) => void;
}

const DealCard: React.FC<DealCardProps> = memo(({ deal, onDragStart, onEditDeal, onDeleteDeal }) => {
  const [documents, setDocuments] = useState<DealDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [showMoveModal, setShowMoveModal] = useState(false);
  const { fetchDealDocuments, moveDeal, getAllStages } = usePipedriveService();

  // Store the fetchDealDocuments function in a ref to prevent infinite re-renders
  const fetchDocumentsRef = useRef(fetchDealDocuments);

  // Update the ref when the function changes
  useEffect(() => {
    fetchDocumentsRef.current = fetchDealDocuments;
  }, [fetchDealDocuments]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // Debounced function to manually refresh documents if needed
  const refreshDocuments = useCallback(async () => {
    if (!deal.id) return;

    try {
      setLoading(true);
      // Reset hasLoaded to show loading indicator
      setHasLoaded(false);

      // Add a small delay to show the loading indicator
      await new Promise(resolve => setTimeout(resolve, 300));

      const docs = await fetchDocumentsRef.current(deal.id);
      setDocuments(docs);
      setHasLoaded(true);
    } catch (err) {
      setHasLoaded(true);
    } finally {
      setLoading(false);
    }
  }, [deal.id]);

  // Create a debounced version of the loadDocuments function
  const debouncedLoadDocuments = useCallback(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(async () => {
        if (!deal.id || hasLoaded) return;

        try {
          setLoading(true);
          const docs = await fetchDocumentsRef.current(deal.id);
          setDocuments(docs);
          setHasLoaded(true);
        } catch (err) {
          setHasLoaded(true);
        } finally {
          setLoading(false);
        }
      }, 100); // 100ms debounce time
    };
  }, [deal.id, hasLoaded])();

  // Reset hasLoaded when deal ID changes
  useEffect(() => {
    setHasLoaded(false);
  }, [deal.id]);

  // Fetch documents for this deal - only run once per deal ID
  useEffect(() => {
    // Skip if we've already loaded documents for this deal
    if (hasLoaded) return;

    // Use the debounced function to load documents
    debouncedLoadDocuments();

    // Cleanup function to cancel any pending debounced calls when component unmounts
    return () => {
      // The cleanup is handled inside the debouncedLoadDocuments closure
    };
  }, [hasLoaded, debouncedLoadDocuments]); // Include debouncedLoadDocuments in dependencies
  return (
    <div
      className="bg-gradient-to-br from-stone-800 to-stone-900 rounded-md p-3 cursor-grab shadow-md border border-orange-500/30"
      draggable
      onDragStart={(e) => onDragStart(e, deal.id)}
    >
      {/* Deal Title as prominent header */}
      <div className="flex items-start gap-1.5 mb-2">
        <div className="mt-0.5">
          <span className="inline-block w-1.5 h-4 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
        </div>
        <div className="flex-1">
          <h2 className="font-medium text-sm text-white break-words">
            {deal.title}
          </h2>
        </div>

        {/* Action buttons */}
        <div className="flex space-x-1 ml-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setShowMoveModal(true);
            }}
            className="text-stone-400 hover:text-blue-400 p-1 transition-colors duration-200"
            title="Move to Another Pipeline"
          >
            <DashboardIcons.Move className="w-3 h-3" />
          </button>
          {onEditDeal && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                onEditDeal(deal);
              }}
              className="text-stone-400 hover:text-orange-400 p-1 transition-colors duration-200"
              title="Edit Deal"
            >
              <DashboardIcons.Edit className="w-3 h-3" />
            </button>
          )}
          {onDeleteDeal && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                onDeleteDeal(deal);
              }}
              className="text-stone-400 hover:text-red-400 p-1 transition-colors duration-200"
              title="Delete Deal"
            >
              <DashboardIcons.Delete className="w-3 h-3" />
            </button>
          )}
        </div>
      </div>

      {/* Document count indicator with refresh button */}
      <div className="flex justify-between items-center mb-3">
        {loading ? (
          <div className="flex items-center" title="Loading documents...">
            <DashboardIcons.Spinner className="w-3.5 h-3.5 text-orange-400 animate-spin mr-1" />
            <span className="text-xs text-orange-400 font-medium">...</span>
          </div>
        ) : documents.length > 0 ? (
          <div className="flex items-center gap-2">
            <div className="flex items-center" title={`${documents.length} document${documents.length !== 1 ? 's' : ''} attached`}>
              <DashboardIcons.Document className="w-3.5 h-3.5 text-orange-400 mr-1" />
              <span className="text-xs text-orange-400 font-medium">{documents.length}</span>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                refreshDocuments();
              }}
              className="flex items-center justify-center bg-stone-700 text-stone-300 hover:text-orange-400 rounded-full p-1 transition-colors duration-200"
              title="Refresh documents"
            >
              <DashboardIcons.Refresh className="w-3 h-3" />
            </button>
          </div>
        ) : hasLoaded && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              refreshDocuments();
            }}
            className="flex items-center gap-1 bg-stone-700/50 text-stone-400 hover:text-orange-400 rounded-md px-2 py-1 text-xs transition-colors duration-200"
            title="Check for documents"
          >
            <DashboardIcons.Refresh className="w-3 h-3" />
            <span>Refresh</span>
          </button>
        )}
      </div>

      {/* Client info */}
      {deal.client && (
        <div className="mb-3">
          {/* Client Full Name */}
          <p className="text-white text-sm font-medium mb-1 break-words">
            {deal.client.first_name} {deal.client.last_name}
          </p>

          {/* Contact Number */}
          <div className="flex items-start mb-1">
            <span className="text-stone-400 text-xs w-20">Contact:</span>
            <a
              href={`tel:${deal.client.phone}`}
              className="text-xs text-orange-400 hover:text-orange-300 break-words transition-colors duration-200"
              title={deal.client.phone}
            >
              {deal.client.phone}
            </a>
          </div>

          {/* Email Address */}
          <div className="flex items-start">
            <span className="text-stone-400 text-xs w-20">Email:</span>
            <a
              href={`mailto:${deal.client.email}`}
              className="text-xs text-orange-400 hover:text-orange-300 truncate max-w-[150px] inline-block transition-colors duration-200"
              title={deal.client.email}
            >
              {deal.client.email}
            </a>
          </div>
        </div>
      )}


      {/* Notes Button - Clean design without hover effects */}
      {deal.notes && (
        <div className="mb-3 bg-stone-700/20 p-2.5 rounded-md border border-stone-600/30">
          <NotesPreview notes={deal.notes} />
        </div>
      )}

      {/* Documents */}
      {(documents.length > 0 || loading) && (
        <div>
          <div className="flex items-start">
            <span className="text-stone-400 text-xs w-20">Documents:</span>
            <div className="flex-1 text-stone-300 text-xs max-h-24 overflow-y-auto custom-scrollbar pr-1">
              {loading ? (
                <div className="flex items-center py-1">
                  <DashboardIcons.Spinner className="w-3 h-3 text-orange-400 animate-spin mr-2" />
                  <span className="text-stone-400">Loading documents...</span>
                </div>
              ) : (
                <div className="space-y-1">
                  {documents.map((doc) => (
                    <div key={doc.id} className="flex items-center mb-1">
                      <DashboardIcons.Document className="w-3 h-3 text-stone-400 mr-1 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        {doc.url ? (
                          <a
                            href={doc.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-orange-400 hover:text-orange-300 truncate block transition-colors duration-200"
                            onClick={(e) => e.stopPropagation()}
                            title={doc.file_name}
                          >
                            {doc.file_name}
                          </a>
                        ) : (
                          <span className="text-stone-400 truncate block" title={doc.file_name}>
                            {doc.file_name}
                          </span>
                        )}
                        <span className="text-stone-500 text-xs">
                          {formatFileSize(doc.file_size)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Move Deal Modal */}
      {showMoveModal && (
        <MoveDealModal
          deal={deal}
          onClose={() => setShowMoveModal(false)}
          onMoveDeal={moveDeal}
          getAllStages={getAllStages}
        />
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  // Only re-render if the deal ID changes or if the deal data has changed
  return prevProps.deal.id === nextProps.deal.id &&
         prevProps.deal.updated_at === nextProps.deal.updated_at;
});

export default DealCard;
