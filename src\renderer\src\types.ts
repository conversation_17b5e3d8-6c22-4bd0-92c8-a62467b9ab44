export interface License {
  id: string
  client_id: string
  make: string
  type: string
  caliber: string
  serial_number: string
  section: string
  expiry_date: string
  barrel_serial?: string
  barrel_make?: string
  receiver_serial?: string
  receiver_make?: string
  frame_serial?: string
  frame_make?: string
  last_notification_date?: string
  firearm_type: string
  license_number: string
  issue_date: string
  stock_code: string
  lic_number?: string
  client?: Client
}

export interface Client {
  id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  postal_code: string
  id_number: string
  gun_licences: License[]
  created_at: string
  updated_at: string
  club_provider?: 'PHASA' | 'NATSHOOT' | null
  username?: string
  password?: string
  role?: string
  // Wallet related fields
  wallet_id?: string
  wallet_balance?: number
}

export interface Pagination {
  page: number
  perPage: number
}

export interface PaginatedClients {
  clients: Client[]
  total: number
}

export interface Loan {
  id: string
  client_id: string
  license_id: string
  start_date: string
  invoice_number: string
  weapon_cost: number
  initial_payment: number
  loan_amount: number
  remaining_balance: number
  interest_rate: number
  payment_due_date: string
  status: 'active' | 'paid' | 'overdue' | 'defaulted' | 'cancelled'
  created_at: string
  updated_at: string
  clients?: Client
  gun_licences?: License
  penalties?: number
  loan_term?: number
  notes?: string
  last_reminder_date?: string
  next_payment_date?: string
  pause_notifications?: boolean
  firearm_id?: string
  assignment_id?: string
  paid_invoice?: string
  // Added properties for joined data
  firearm?: any
  assignment?: any
}

export interface LoanPayment {
  id: string
  loan_id: string
  payment_date: string
  amount: number
  penalties_paid: number
  created_at: string
  payment_number: number
  status: 'paid' | 'due' | 'overdue'
  method?: string
  notes?: string
  receipt_number?: string
}

export interface PaginatedLoans {
  loans: Loan[]
  total: number
}

export interface WalletTransaction {
  id: string
  wallet_id: string
  amount: number
  transaction_type: 'payment' | 'charge' | 'adjustment' | 'transfer'
  reference_type: 'firearm' | 'manual' | 'system'
  reference_id?: string
  description?: string
  transaction_date: string
  created_at: string
  updated_at: string
}

export interface ClientWallet {
  id: string
  client_id: string
  balance: number
  created_at: string
  updated_at: string
}
