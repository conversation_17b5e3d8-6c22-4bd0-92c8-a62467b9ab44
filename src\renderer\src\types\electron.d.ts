import type {
  OpenDialogOptions,
  OpenDialogReturnValue,
  SaveDialogOptions,
  SaveDialogReturnValue
} from 'electron'

/**
 * Electron API declaration file
 */

interface ElectronAPI {
  onLoadingStateChange: (callback: (loading: boolean) => void) => () => void;
  showOpenDialog: (options: any) => Promise<any>;
  showSaveDialog: (options: any) => Promise<any>;
  saveFile: (filePath: string, data: ArrayBuffer) => Promise<{success: boolean, filePath?: string, error?: string}>;
  path: any;
  fs: any;
  getCredentials: () => Promise<{
    supabaseUrl: string;
    supabaseAnonKey: string;
    supabaseServiceKey: string;
    googleMapsApiKey: string;
  }>;
  getAppVersion: () => Promise<{ version: string }>;
  windowMinimize: () => void;
  windowMaximize: () => void;
  windowClose: () => void;
  setCompactWindowSize: () => void;
  setFullWindowSize: () => void;
  // WhatsApp API methods
  whatsapp: {
    getQrCode: (sessionId: string) => Promise<{
      success: boolean;
      data?: ArrayBuffer;
      error?: string;
    }>;
    checkStatus: (sessionId: string) => Promise<{
      success: boolean;
      data?: {
        status: string;
        [key: string]: any;
      };
      error?: string;
    }>;
    restartSession: (sessionId: string) => Promise<{
      success: boolean;
      data?: any;
      error?: string;
    }>;
    sendMessage: (sessionId: string, phone: string, message: string) => Promise<{
      success: boolean;
      data?: any;
      error?: string;
    }>;
  };
}

export interface IElectronWindow {
  ipcRenderer: {
    on: (
      channel:
        | 'update-message'
        | 'update-data'
        | 'update-available'
        | 'update-not-available'
        | 'update-downloaded'
        | 'update-error'
        | 'update-status',
      func: (...args: unknown[]) => void
    ) => void;
    send: (
      channel: 'confirm-download' | 'confirm-install' | 'update-data' | 'check-for-updates' | 'user-logged-in' | 'user-logged-out' | 'change-update-channel',
      data?: unknown
    ) => void;
    invoke: (
      channel: string,
      ...args: unknown[]
    ) => Promise<unknown>;
    showSaveDialog: (options: SaveDialogOptions) => Promise<SaveDialogReturnValue>;
    saveFile: (filePath: string, data: ArrayBuffer) => Promise<{success: boolean, filePath?: string, error?: string}>;
    removeListener: (
      channel:
        | 'update-message'
        | 'update-data'
        | 'update-available'
        | 'update-not-available'
        | 'update-downloaded'
        | 'update-error'
        | 'update-status',
      func: (...args: unknown[]) => void
    ) => void;
  };
  process: {
    versions: Record<string, string>;
  };
  appVersion?: {
    version: string;
  };
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    electron: {
      ipcRenderer: {
        on: (channel: string, func: (...args: any[]) => void) => void;
        invoke: (channel: string, ...args: any[]) => Promise<any>;
        send: (channel: string, data?: any) => void;
        removeListener: (channel: string, func: (...args: any[]) => void) => void;
        showSaveDialog: (options: any) => Promise<any>;
        saveFile: (filePath: string, data: ArrayBuffer) => Promise<{success: boolean, filePath?: string, error?: string}>;
      };
      process: {
        versions: Record<string, string>;
      };
      appVersion: {
        version: string;
      };
    };
  }
}

export {}
