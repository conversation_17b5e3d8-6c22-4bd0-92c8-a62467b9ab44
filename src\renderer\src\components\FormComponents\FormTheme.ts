    // FormTheme.ts - Standardized styling for all form components

// Color palette
export const colors = {
  // Primary colors
  primary: {
    main: '#f97316', // orange-500
    light: '#fb923c', // orange-400
    dark: '#ea580c', // orange-600
    hover: '#f97316', // orange-500
    active: '#ea580c', // orange-600
    focus: 'rgba(249, 115, 22, 0.4)', // orange-500 with opacity
  },
  
  // Background colors
  background: {
    main: '#1c1917', // stone-900
    light: '#292524', // stone-800
    lighter: '#44403c', // stone-700
    dark: '#0c0a09', // stone-950
    input: 'rgba(68, 64, 60, 0.5)', // stone-700 with opacity
    section: 'rgba(41, 37, 36, 0.5)', // stone-800 with opacity
    card: 'rgba(41, 37, 36, 0.5)', // stone-800 with opacity
    modal: 'rgba(41, 37, 36, 0.9)', // stone-800 with opacity for modals
  },
  
  // Border colors
  border: {
    main: 'rgba(68, 64, 60, 0.5)', // stone-700 with opacity
    light: 'rgba(120, 113, 108, 0.3)', // stone-500 with opacity
    focus: '#f97316', // orange-500
    error: '#ef4444', // red-500
  },
  
  // Text colors
  text: {
    primary: '#ffffff', // white
    secondary: '#d6d3d1', // stone-300
    tertiary: '#a8a29e', // stone-400
    disabled: '#78716c', // stone-500
    error: '#ef4444', // red-500
    success: '#22c55e', // green-500
  },
  
  // Status colors
  status: {
    success: {
      main: '#22c55e', // green-500
      light: 'rgba(34, 197, 94, 0.1)', // green-500 with opacity
      border: 'rgba(34, 197, 94, 0.3)', // green-500 with opacity
    },
    error: {
      main: '#ef4444', // red-500
      light: 'rgba(239, 68, 68, 0.1)', // red-500 with opacity
      border: 'rgba(239, 68, 68, 0.3)', // red-500 with opacity
    },
    warning: {
      main: '#f59e0b', // amber-500
      light: 'rgba(245, 158, 11, 0.1)', // amber-500 with opacity
      border: 'rgba(245, 158, 11, 0.3)', // amber-500 with opacity
    },
    info: {
      main: '#3b82f6', // blue-500
      light: 'rgba(59, 130, 246, 0.1)', // blue-500 with opacity
      border: 'rgba(59, 130, 246, 0.3)', // blue-500 with opacity
    }
  }
}

// Typography
export const typography = {
  fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  fontSizes: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
  },
  fontWeights: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  }
}

// Spacing
export const spacing = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '1rem',       // 16px
  lg: '1.5rem',     // 24px
  xl: '2rem',       // 32px
  '2xl': '2.5rem',  // 40px
  '3xl': '3rem',    // 48px
}

// Borders
export const borders = {
  radius: {
    sm: '0.25rem',  // 4px
    md: '0.5rem',   // 8px
    lg: '0.75rem',  // 12px
    xl: '1rem',     // 16px
    full: '9999px', // Full rounded (for circles)
  },
  width: {
    thin: '1px',
    default: '2px',
    thick: '3px',
  }
}

// Shadows
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  orange: '0 4px 14px 0 rgba(249, 115, 22, 0.39)',
}

// Transitions
export const transitions = {
  default: 'all 0.3s ease',
  fast: 'all 0.15s ease',
  slow: 'all 0.5s ease',
}

// Form specific styles
export const formStyles = {
  // Styles for common form elements
  field: {
    input: {
      base: `w-full px-4 py-3 rounded-lg bg-[${colors.background.input}] 
             border border-[${colors.border.main}] text-[${colors.text.primary}]
             placeholder-[${colors.text.tertiary}] focus:outline-none focus:ring-2
             focus:ring-[${colors.primary.focus}] focus:border-[${colors.border.focus}] transition-all`,
      error: `border-[${colors.border.error}] focus:ring-[${colors.status.error.border}]`,
      disabled: `opacity-60 cursor-not-allowed`,
    },
    label: {
      base: `block text-[${colors.text.secondary}] mb-1 text-sm font-medium`,
      required: `text-[${colors.primary.main}]`,
      optional: `text-[${colors.text.tertiary}] text-xs`,
    },
    error: `text-xs text-[${colors.text.error}] mt-1`,
  },
  
  button: {
    primary: `bg-gradient-to-r from-[${colors.primary.main}] to-[${colors.primary.light}]
              text-white font-medium shadow-xl shadow-[${colors.primary.focus}] 
              hover:from-[${colors.primary.light}] hover:to-[${colors.primary.main}]
              transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]
              px-6 py-2 rounded-lg`,
    secondary: `bg-[${colors.background.lighter}] text-white font-medium 
                hover:bg-[${colors.background.light}] transition-colors px-6 py-2 rounded-lg`,
    disabled: `opacity-60 cursor-not-allowed`,
  },
  
  section: {
    container: `mb-8 p-6 bg-[${colors.background.section}] rounded-xl border border-[${colors.border.light}]`,
    title: `text-xl font-semibold text-[${colors.text.primary}] mb-4`,
  },
  
  layout: {
    container: `bg-[${colors.background.card}] backdrop-blur-sm rounded-2xl p-8 
                w-full border border-[${colors.border.light}] shadow-2xl`,
    title: `text-2xl font-bold text-[${colors.text.primary}] mb-6`,
    form: `space-y-6`,
  },
  
  grid: {
    cols2: `grid grid-cols-1 md:grid-cols-2 gap-4`,
    cols3: `grid grid-cols-1 md:grid-cols-3 gap-4`,
    cols4: `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4`,
  }
}

// Export default theme object
export default {
  colors,
  typography,
  spacing,
  borders,
  shadows,
  transitions,
  formStyles,
} 