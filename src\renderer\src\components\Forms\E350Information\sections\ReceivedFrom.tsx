import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'
import { isValidPastDate } from '../utils/helpers'

/**
 * Received From section component for E350 Information form
 */
const ReceivedFrom: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">RECEIVED FROM</h3>

      <FormSection title="Source Information" subtitle="Enter details about where the firearm was received from">
        <div className="space-y-3">
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Receiver Name (Dealer or Individual)</label>
                <input
                  type="text"
                  name="receiverName"
                  value={formData.receiverName || ''}
                  onChange={handleChange}
                  placeholder="Receiver Name - Dealer or Individual"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">ID / Dealer Number</label>
                <input
                  type="text"
                  name="receiverId"
                  value={formData.receiverId || ''}
                  onChange={handleChange}
                  placeholder="ID / Dealer Number"
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[200px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Date Received From</label>
                <input
                  type="date"
                  name="dateReceivedFrom"
                  value={formData.dateReceivedFrom || ''}
                  onChange={handleChange}
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
                {formData.dateReceivedFrom && !isValidPastDate(formData.dateReceivedFrom) && (
                  <p className="text-xs text-red-400 mt-1">
                    Date cannot be in the future
                  </p>
                )}
              </div>
            </div>
          </div>

          <p className="text-xs text-stone-400 mt-1">
            This information is required for the E350 Information form to track the firearm's chain of custody
          </p>
        </div>
      </FormSection>
    </div>
  )
}

export default ReceivedFrom
