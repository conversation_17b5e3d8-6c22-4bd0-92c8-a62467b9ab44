import React, { useState } from 'react'
import { FormData } from '../../types/FormData'
import { CompetencyTypeId, getCompetencyTypeById } from '../../config/competencyTypes'
import { useWindowSize } from '../../contexts/WindowSizeContext'

interface FileUploadSectionProps {
  selectedFiles: File[]
  setSelectedFiles: React.Dispatch<React.SetStateAction<File[]>>
  fileError: string | null
  setFileError: React.Dispatch<React.SetStateAction<string | null>>
  formData: FormData | null
  submittedFormType: string | null
  competencyType: 'new' | 'further' | 'renew' | null
  fetchedTemplate: File | null
  documentProcessed: boolean
  isProcessing: boolean
  processingError: string | null
  handleProcessDocument: () => Promise<void>
  setDocumentProcessed: React.Dispatch<React.SetStateAction<boolean>>
  isDragging: boolean
  handleDragOver: (e: React.DragEvent<HTMLDivElement>) => void
  handleDragLeave: (e: React.DragEvent<HTMLDivElement>) => void
  handleDrop: (e: React.DragEvent<HTMLDivElement>) => void
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleClearForm?: () => void
}

const FileUploadSection: React.FC<FileUploadSectionProps> = ({
  selectedFiles,
  setSelectedFiles,
  fileError,
  formData,
  submittedFormType,
  competencyType,
  fetchedTemplate,
  documentProcessed,
  isProcessing,
  processingError,
  handleProcessDocument,
  setDocumentProcessed,
  isDragging,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  handleFileChange,
  handleClearForm
}) => {
  useWindowSize()

  return (
    <div className="bg-stone-800/60 rounded-lg p-3 relative overflow-hidden h-full">
      <div className="relative h-full flex flex-col">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-sm font-semibold text-white flex items-center">
            {submittedFormType === 'deceased' ? 'Deceased Estate' : submittedFormType}
          </h2>
          <button
            onClick={() => {
              if (handleClearForm) {
                handleClearForm()
              } else {
                setSelectedFiles([])
              }
            }}
            className="px-2 py-1 bg-stone-700 hover:bg-stone-600 text-white rounded-md transition-colors text-xs font-medium flex items-center gap-1 h-7"
          >
            <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
            Clear
          </button>
        </div>

        {submittedFormType && documentProcessed && (
          <div className="mb-2 p-1.5 bg-green-500/10 rounded-md border border-green-500/20 flex items-center text-xs">
            <svg
              className="w-3 h-3 mr-1 text-green-400 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            <span className="text-green-400">Document processed and downloaded</span>
          </div>
        )}

        {/* Show auto-fetch status for competency forms */}
        {competencyType && !documentProcessed && (
          <AutoFetchStatus
            competencyType={competencyType}
            fetchedTemplate={fetchedTemplate}
            fileError={fileError}
          />
        )}

        {documentProcessed && (
          <div className="mt-2 flex justify-center space-x-2">
            <button
              onClick={() => setDocumentProcessed(false)}
              className="bg-stone-700 hover:bg-stone-600 text-white px-2 py-1 rounded-md transition-colors flex items-center gap-1 h-7 text-xs font-medium"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                />
              </svg>
              <span>New Template</span>
            </button>
            <button
              onClick={() => {
                // Keep the form data but reset the processed state to allow reprocessing
                setDocumentProcessed(false)
              }}
              className="bg-stone-700 hover:bg-stone-600 text-white px-2 py-1 rounded-md transition-colors flex items-center gap-1 h-7 text-xs font-medium"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>Process Again</span>
            </button>
          </div>
        )}

        {/* Compact manual upload option */}
        {submittedFormType && !documentProcessed && (
          <div className="mt-2 flex-grow">
            <div
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              className={`border border-dashed rounded-md p-2 text-center transition-colors h-[calc(100%-80px)] min-h-[100px] flex flex-col justify-center ${
                isDragging
                  ? 'border-orange-500 bg-stone-700/20'
                  : 'border-stone-600/50 hover:bg-stone-700/10'
              }`}
            >
              <input
                type="file"
                id="file-upload"
                className="hidden"
                onChange={handleFileChange}
                accept=".docx"
                multiple
              />
              <label htmlFor="file-upload" className="cursor-pointer flex flex-col items-center">
                <svg
                  className="h-6 w-6 text-stone-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                  />
                </svg>
                <p className="mt-0.5 text-xs text-stone-300">
                  <span className="font-semibold">
                    {fetchedTemplate &&
                    (submittedFormType === 'new-competency' ||
                      submittedFormType === 'further-competency')
                      ? 'Upload a different template'
                      : 'Click to upload'}
                  </span>{' '}
                  or drag and drop
                </p>
              </label>
              {selectedFiles.length > 0 && (
                <div className="mt-1 space-y-0.5 text-xs">
                  {selectedFiles.map((file, index) => (
                    <p
                      key={index}
                      className="text-stone-300 flex items-center justify-center gap-1"
                    >
                      <svg
                        className="w-3 h-3 text-orange-400 flex-shrink-0"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <span className="truncate max-w-[200px]">{file.name}</span>
                      {fetchedTemplate &&
                        file.name === fetchedTemplate.name &&
                        (submittedFormType === 'new-competency' ||
                          submittedFormType === 'further-competency') && (
                          <span className="text-green-400 text-xs">(Auto)</span>
                        )}
                    </p>
                  ))}
                </div>
              )}
              {fileError && <p className="mt-1 text-xs text-red-400">{fileError}</p>}
            </div>

            {fetchedTemplate &&
              (submittedFormType === 'new-competency' ||
                submittedFormType === 'further-competency') &&
              selectedFiles.length === 0 && (
                <p className="text-xs text-stone-400 text-center mt-0.5">
                  Auto-fetched template will be used unless you upload your own.
                </p>
              )}
          </div>
        )}

        <button
          onClick={handleProcessDocument}
          disabled={(selectedFiles.length === 0 && !fetchedTemplate) || !formData || isProcessing}
          className="w-full mt-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-1.5 rounded-md font-medium shadow-md shadow-orange-500/20 border border-orange-500/30 disabled:opacity-50 disabled:cursor-not-allowed text-xs"
        >
          {isProcessing ? (
            <span className="flex items-center justify-center">
              <svg
                className="animate-spin mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Processing...
            </span>
          ) : documentProcessed ? (
            'Process Again'
          ) : (
            'Process Document'
          )}
        </button>

        {processingError && (
          <p className="text-red-400 text-xs mt-1 text-center">{processingError}</p>
        )}
      </div>
    </div>
  )
}

interface AutoFetchStatusProps {
  competencyType: 'new' | 'further' | 'renew' | null
  fetchedTemplate: File | null
  fileError: string | null
}

const AutoFetchStatus: React.FC<AutoFetchStatusProps> = ({
  competencyType,
  fetchedTemplate,
  fileError
}) => {
  const [isFetchingTemplate] = useState<boolean>(false)

  // This is a simplified component that would normally have its own state
  // For now, we're just showing the UI based on props
  return (
    <div className="mb-3">
      {isFetchingTemplate ? (
        <div className="flex items-center text-stone-300 text-xs p-2 bg-stone-800/30 rounded-md border border-stone-700/50">
          <svg
            className="animate-spin h-3 w-3 mr-2 text-orange-500 flex-shrink-0"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <span>Fetching template automatically...</span>
        </div>
      ) : fetchedTemplate ? (
        <div className="p-2 bg-green-500/10 border border-green-500/20 rounded-md text-green-400 text-xs">
          <div className="flex items-center">
            <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            <div>
              <p className="font-medium">Template fetched automatically</p>
              <p className="text-xs opacity-80">
                {competencyType &&
                  getCompetencyTypeById(competencyType as CompetencyTypeId).fileName}
              </p>
            </div>
          </div>
        </div>
      ) : fileError ? (
        <div className="p-2 bg-red-500/10 border border-red-500/20 rounded-md text-red-400 text-xs">
          <div className="flex items-center">
            <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <p className="font-medium">Error fetching template</p>
              <p className="opacity-80">{fileError}</p>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  )
}

export default FileUploadSection
