import { FormData } from '../../types/FormData'
import RenewCompForm from './RenewComp/index'

/**
 * RenewComp - Competency Renewal Application Form Component
 *
 * A wrapper component that uses the refactored RenewCompForm component
 */
export default function RenewComp({ formType, onSubmit }: { formType: string, onSubmit: (data: FormData) => void }): JSX.Element {
  return (
    <RenewCompForm formType={formType} onSubmit={onSubmit} />
  )
}
