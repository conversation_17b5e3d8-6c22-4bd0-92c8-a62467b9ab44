// validation.ts - Validation functions for the Further Competency form

import { FurtherCompetencyData } from '../../../../types/FormData'

/**
 * Validates the current step of the form
 * @param formData The current form data
 * @param currentStep The current step number
 * @returns Whether the current step is valid
 */
export const validateStep = (formData: FurtherCompetencyData, currentStep: number): boolean => {
  let isValid = false

  switch (currentStep) {
    case 1: // Personal Information
      isValid = Boolean(
        formData.firstName &&
          formData.lastName &&
          formData.phoneNumber &&
          formData.email &&
          formData.physicalAddress &&
          formData.idNumber &&
          formData.citizenType
      )
      break
    case 2: // Professional Information
      isValid = Boolean(
        formData.companyName &&
          formData.tradeProfession &&
          formData.workAddress &&
          formData.workPostalCode &&
          formData.workNumber
      )
      break
    case 3: // Firearm Types
      isValid = Boolean(
        formData.competencyType &&
          (formData.pistol ||
            formData.rifle ||
            formData.shotgun ||
            formData.revolver ||
            formData.selfLoading)
      )
      break
    case 4: // Previous Competency Details
      isValid = Boolean(
        formData.competencyCertificateType &&
          formData.competencyCertificateNumber &&
          formData.competencyIssueDate &&
          formData.competencyExpiryDate
      )
      if (formData.isMemberOfAssociation) {
        isValid =
          isValid &&
          Boolean(
            formData.associationName &&
              formData.associationMembershipNumber &&
              formData.associationDateJoined
          )
      }
      break
    case 5: // Criminal History
      // No specific validation for criminal history
      isValid = true
      break
    default:
      isValid = false
  }

  return isValid
}

/**
 * Validates the entire form data
 * @param formData The current form data
 * @returns An error message if validation fails, null otherwise
 */
export const validateFormData = (formData: FurtherCompetencyData): string | null => {
  if (
    !formData.firstName ||
    !formData.lastName ||
    !formData.idNumber ||
    !formData.phoneNumber ||
    !formData.email ||
    !formData.physicalAddress ||
    !formData.postalCode
  ) {
    return 'Please fill in all required personal information fields.'
  }

  if (
    !formData.companyName ||
    !formData.tradeProfession ||
    !formData.workAddress ||
    !formData.workPostalCode ||
    !formData.workNumber
  ) {
    return 'Please fill in all required work information fields.'
  }

  return null
}
