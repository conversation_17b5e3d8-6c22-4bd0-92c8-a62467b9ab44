export interface Pipeline {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export interface PipelineStage {
  id: string;
  pipeline_id: string;
  name: string;
  description: string | null;
  position: number;
  created_at: string;
  updated_at: string;
}

export interface Deal {
  id: string;
  stage_id: string;
  client_id: string;
  title: string;
  notes: string | null;
  created_at: string;
  updated_at: string;
  // Include comprehensive client information when fetched
  client?: {
    id: string;
    first_name: string;
    last_name: string;
    phone: string;
    email: string;
    address: string;
    city: string;
    state: string;
    postal_code: string;
    id_number: string;
  };
}

export interface DealDocument {
  id: string;
  deal_id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
  created_at: string;
  updated_at: string;
  url?: string | null; // URL for accessing the document
  error?: string; // Error message if document URL is not available
}
