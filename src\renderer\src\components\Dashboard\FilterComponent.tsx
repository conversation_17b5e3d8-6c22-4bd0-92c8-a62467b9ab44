import React from 'react'
import { FilterState, FilterCounts } from './types'

interface FilterComponentProps {
  clientFilter: FilterState['clientFilter']
  setClientFilter: (filter: FilterState['clientFilter']) => void
  filterCounts: FilterCounts
}

export const FilterComponent: React.FC<FilterComponentProps> = ({
  clientFilter,
  setClientFilter,
  filterCounts
}) => {
  return (
    <div className="bg-stone-800/70 p-4 rounded-lg shadow-lg mb-4">
      <div className="flex flex-col gap-4">
        {/* Filter Label */}
        <div className="flex items-center">
          <div className="text-stone-300 font-medium">Filter Clients:</div>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={() => setClientFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between
              ${clientFilter === 'all' ? 'bg-orange-500 text-white' : 'bg-stone-700 text-stone-300 hover:bg-stone-600'}`}
          >
            <span>All Clients</span>
            <span className="ml-2 bg-stone-700/40 px-2 py-0.5 rounded-full text-xs">
              {filterCounts.all}
            </span>
          </button>

          <button
            onClick={() => setClientFilter('with-licenses')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between
              ${clientFilter === 'with-licenses' ? 'bg-orange-500 text-white' : 'bg-stone-700 text-stone-300 hover:bg-stone-600'}`}
          >
            <span>With Licenses</span>
            <span className="ml-2 bg-stone-700/40 px-2 py-0.5 rounded-full text-xs">
              {filterCounts['with-licenses']}
            </span>
          </button>

          <button
            onClick={() => setClientFilter('no-licenses')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between
              ${clientFilter === 'no-licenses' ? 'bg-orange-500 text-white' : 'bg-stone-700 text-stone-300 hover:bg-stone-600'}`}
          >
            <span>No Licenses</span>
            <span className="ml-2 bg-stone-700/40 px-2 py-0.5 rounded-full text-xs">
              {filterCounts['no-licenses']}
            </span>
          </button>
        </div>
      </div>
    </div>
  )
}
