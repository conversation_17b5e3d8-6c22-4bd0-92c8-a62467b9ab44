import React from 'react'
import { TemplateStatus as TemplateStatusType } from '../utils/types'

interface TemplateStatusProps {
  status: TemplateStatusType
  templateName: string
  error: string | null
}

/**
 * Template Status component for SAPS Inspection Report form
 */
const TemplateStatus: React.FC<TemplateStatusProps> = ({
  status,
  templateName,
  error
}) => {
  return (
    <div className="mb-4 p-4 rounded-lg border">
      {status === 'loading' && (
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500 mr-3"></div>
          <span className="text-stone-300">Loading template {templateName}...</span>
        </div>
      )}

      {status === 'ready' && (
        <div className="flex items-center text-green-500">
          <svg
            className="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          <span>Template {templateName} loaded successfully</span>
        </div>
      )}

      {status === 'error' && (
        <div className="flex items-center text-red-500">
          <svg
            className="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
          <span>
            Error loading template {templateName}: {error}
          </span>
        </div>
      )}
    </div>
  )
}

export default TemplateStatus
