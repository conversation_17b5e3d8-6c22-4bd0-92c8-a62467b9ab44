import { useState, useEffect, useRef } from 'react'
import { Setting<PERSON>, Download, Zap, Refresh<PERSON><PERSON>, Al<PERSON><PERSON>riangle, <PERSON><PERSON><PERSON>cle, Clock } from 'lucide-react'

// Define the update channel type
type UpdateChannel = 'release' | 'prerelease'

// Define progress information type
interface ProgressInfo {
  percent: number
  bytesPerSecond: number
  total: number
  transferred: number
  averageSpeed?: number
  estimatedTimeRemaining?: string
  downloadedFormatted?: string
  totalFormatted?: string
  speedFormatted?: string
  elapsedTime?: string
}

// Define update information type
interface UpdateInfo {
  version: string
  downloadSize?: string
  files?: Array<{ url: string, size: number }>
  path?: string
  sha512?: string
  releaseDate?: string
  releaseName?: string
  releaseNotes?: string
}

// Define error information type
interface ErrorInfo {
  message: string
  stack?: string
  type?: string
  recoverable?: boolean
  suggestion?: string
}

// Define retry information type
interface RetryInfo {
  retryCount: number
  maxRetries: number
  retryDelay: number
  retryAt: string
}

function UpdateSettings(): React.JSX.Element {
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [isCheckingUpdate, setIsCheckingUpdate] = useState<boolean>(false)
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false)
  const [currentChannel, setCurrentChannel] = useState<UpdateChannel>('release')
  const [isChangingChannel, setIsChangingChannel] = useState<boolean>(false)
  const [updateStatus, setUpdateStatus] = useState<string>('')
  const [progressInfo, setProgressInfo] = useState<ProgressInfo | null>(null)
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null)
  const [errorInfo, setErrorInfo] = useState<ErrorInfo | null>(null)
  const [retryInfo, setRetryInfo] = useState<RetryInfo | null>(null)
  const [isDownloading, setIsDownloading] = useState<boolean>(false)
  const [showProgressDetails, setShowProgressDetails] = useState<boolean>(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Fetch current channel on component mount
  useEffect(() => {
    const fetchCurrentChannel = async () => {
      try {
        const electron = window.electron as any
        if (electron?.ipcRenderer?.invoke) {
          const result = await electron.ipcRenderer.invoke('get-available-channels')
          if (result && result.currentChannel) {
            setCurrentChannel(result.currentChannel)
          }
        }
      } catch (error) {
        console.error('Error fetching current update channel:', error)
      }
    }

    fetchCurrentChannel()
  }, [])

  useEffect(() => {
    // Set loading to false after a short delay
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 500)

    // Listen for update status changes
    const handleUpdateStatus = (_event: any, status: string, data: any) => {
      setUpdateStatus(status)

      switch (status) {
        case 'checking-for-update':
          setIsCheckingUpdate(true)
          setIsDownloading(false)
          setProgressInfo(null)
          setErrorInfo(null)
          setRetryInfo(null)
          break

        case 'update-available':
          setIsCheckingUpdate(false)
          setUpdateInfo(data as UpdateInfo)
          break

        case 'download-progress':
          setIsDownloading(true)
          setProgressInfo(data as ProgressInfo)
          break

        case 'update-downloaded':
          setIsCheckingUpdate(false)
          setIsDownloading(false)
          // Keep the progress info to show completion
          break

        case 'update-not-available':
          setIsCheckingUpdate(false)
          setIsChangingChannel(false)
          setIsDownloading(false)
          setProgressInfo(null)
          break

        case 'update-error':
          setIsCheckingUpdate(false)
          setIsChangingChannel(false)
          setIsDownloading(false)
          setErrorInfo(data as ErrorInfo)
          break

        case 'download-retry':
          setRetryInfo(data as RetryInfo)
          break

        case 'retry-scheduled':
          setRetryInfo(data as RetryInfo)
          break
      }
    }

    // Listen for channel change notifications
    const handleChannelChanged = (_event: any, channel: UpdateChannel) => {
      setCurrentChannel(channel)
      setIsChangingChannel(false)
    }

    // Type assertion for electron to fix TypeScript errors
    const electron = window.electron as any
    if (electron?.ipcRenderer?.on) {
      electron.ipcRenderer.on('update-status', handleUpdateStatus)
      electron.ipcRenderer.on('update-channel-changed', handleChannelChanged)
    }

    return () => {
      clearTimeout(timer)
      if (electron?.ipcRenderer?.removeListener) {
        electron.ipcRenderer.removeListener('update-status', handleUpdateStatus)
        electron.ipcRenderer.removeListener('update-channel-changed', handleChannelChanged)
      }
    }
  }, [])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent): void => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false)
      }
    }

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isMenuOpen])

  const handleCheckForUpdates = () => {
    const electron = window.electron as any
    if (electron?.ipcRenderer?.send) {
      setIsCheckingUpdate(true)
      electron.ipcRenderer.send('check-for-updates', true)
    }
    setIsMenuOpen(false)
  }

  const handleChangeChannel = (channel: UpdateChannel) => {
    if (channel === currentChannel) return

    const electron = window.electron as any
    if (electron?.ipcRenderer?.send) {
      setIsChangingChannel(true)
      electron.ipcRenderer.send('change-update-channel', channel)
    }
    setIsMenuOpen(false)
  }

  const toggleProgressDetails = () => {
    setShowProgressDetails(!showProgressDetails)
  }

  if (isLoading) {
    return (
      <div className="relative w-8 h-8 flex items-center justify-center">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-stone-300"></div>
      </div>
    )
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        className="flex items-center justify-center w-8 h-8 text-stone-300 hover:text-white hover:bg-stone-800 rounded-md transition-colors"
        title="Update Settings"
      >
        <Settings className="text-stone-300 hover:text-white transition-colors" size={18} />
      </button>

      {isMenuOpen && (
        <div className="absolute right-0 mt-1 w-80 bg-stone-900 border border-stone-700/30 rounded-md shadow-lg z-50 overflow-hidden">
          <div className="py-1">
            <div className="px-3 py-2 text-xs font-medium text-stone-400 uppercase tracking-wider border-b border-stone-700/30">
              Updates
            </div>

            {/* Check for updates button */}
            <button
              onClick={handleCheckForUpdates}
              disabled={isCheckingUpdate || isChangingChannel || isDownloading}
              className="w-full text-left px-4 py-2 text-stone-300 hover:text-white hover:bg-stone-800 transition-colors flex items-center"
            >
              {isCheckingUpdate ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-stone-300 mr-2"></div>
                  <span>Checking for updates</span>
                </>
              ) : (
                <>
                  <Download className="text-stone-300 mr-2" size={14} />
                  <span>Check for Updates</span>
                </>
              )}
            </button>

            {/* Update available info */}
            {updateInfo && !isDownloading && updateStatus === 'update-available' && (
              <div className="px-4 py-2 text-xs text-green-400 flex flex-col">
                <div className="flex items-center mb-1">
                  <CheckCircle className="text-green-500 mr-2" size={14} />
                  <span>Update available: v{updateInfo.version}</span>
                </div>
                {updateInfo.downloadSize && (
                  <div className="ml-5 text-stone-400">
                    Size: {updateInfo.downloadSize}
                  </div>
                )}
              </div>
            )}

            {/* Download progress */}
            {isDownloading && progressInfo && (
              <div className="px-4 py-2 text-xs">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-blue-400">Downloading update...</span>
                  <span className="text-stone-300">{progressInfo.percent.toFixed(1)}%</span>
                </div>

                {/* Progress bar */}
                <div className="w-full h-1.5 bg-stone-700 rounded-full overflow-hidden mb-2">
                  <div
                    className="h-full bg-blue-500 rounded-full"
                    style={{ width: `${progressInfo.percent}%` }}
                  ></div>
                </div>

                {/* Basic info row */}
                <div className="flex justify-between text-stone-400 mb-1">
                  <span>{progressInfo.downloadedFormatted} / {progressInfo.totalFormatted}</span>
                  <span>{progressInfo.speedFormatted}</span>
                </div>

                {/* Toggle details button */}
                <button
                  onClick={toggleProgressDetails}
                  className="text-stone-400 hover:text-white text-xs flex items-center mt-1"
                >
                  <span>{showProgressDetails ? 'Hide details' : 'Show details'}</span>
                  <RefreshCw className="ml-1" size={10} />
                </button>

                {/* Detailed progress info */}
                {showProgressDetails && (
                  <div className="mt-2 text-stone-400 border-t border-stone-700/30 pt-2">
                    <div className="flex justify-between mb-1">
                      <span>Time remaining:</span>
                      <span>{progressInfo.estimatedTimeRemaining}</span>
                    </div>
                    <div className="flex justify-between mb-1">
                      <span>Elapsed time:</span>
                      <span>{progressInfo.elapsedTime}</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Error information */}
            {errorInfo && (
              <div className="px-4 py-2 text-xs text-red-400 flex flex-col">
                <div className="flex items-center mb-1">
                  <AlertTriangle className="text-red-500 mr-2" size={14} />
                  <span>Error: {errorInfo.type || 'Unknown'}</span>
                </div>
                <div className="ml-5 text-stone-400 mb-1">
                  {errorInfo.suggestion || errorInfo.message}
                </div>
                {errorInfo.recoverable && (
                  <button
                    onClick={handleCheckForUpdates}
                    className="ml-5 text-blue-400 hover:text-blue-300 flex items-center mt-1"
                  >
                    <RefreshCw className="mr-1" size={10} />
                    <span>Retry</span>
                  </button>
                )}
              </div>
            )}

            {/* Retry information */}
            {retryInfo && (
              <div className="px-4 py-2 text-xs text-yellow-400 flex items-center">
                <Clock className="text-yellow-500 mr-2" size={14} />
                <span>
                  Retry {retryInfo.retryCount}/{retryInfo.maxRetries} at {retryInfo.retryAt}
                </span>
              </div>
            )}

            {/* Divider */}
            <div className="border-t border-stone-700/30 my-1"></div>

            {/* Channel selector header */}
            <div className="px-3 py-1 text-xs text-stone-400">
              Update Channel
            </div>

            {/* Release channel */}
            <button
              onClick={() => handleChangeChannel('release')}
              disabled={isChangingChannel || currentChannel === 'release' || isDownloading}
              className={`w-full text-left px-4 py-2 flex items-center ${
                currentChannel === 'release'
                  ? 'bg-stone-800 text-white'
                  : 'text-stone-300 hover:text-white hover:bg-stone-800'
              } transition-colors`}
            >
              <div className={`w-3 h-3 rounded-full mr-2 ${
                currentChannel === 'release' ? 'bg-green-500' : 'bg-stone-600'
              }`}></div>
              <span>Stable Release</span>
            </button>

            {/* Pre-release channel */}
            <button
              onClick={() => handleChangeChannel('prerelease')}
              disabled={isChangingChannel || currentChannel === 'prerelease' || isDownloading}
              className={`w-full text-left px-4 py-2 flex items-center ${
                currentChannel === 'prerelease'
                  ? 'bg-stone-800 text-white'
                  : 'text-stone-300 hover:text-white hover:bg-stone-800'
              } transition-colors`}
            >
              <div className={`w-3 h-3 rounded-full mr-2 ${
                currentChannel === 'prerelease' ? 'bg-yellow-500' : 'bg-stone-600'
              }`}></div>
              <span>Pre-release</span>
              <Zap className="text-yellow-500 ml-1" size={12} />
            </button>

            {isChangingChannel && (
              <div className="px-4 py-2 text-xs text-stone-400 flex items-center">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-stone-300 mr-2"></div>
                <span>Switching channel...</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default UpdateSettings