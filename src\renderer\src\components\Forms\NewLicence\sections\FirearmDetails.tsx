import React, { useEffect } from 'react'
import { FormSection, FormField, CheckboxGroup } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionProps } from '../utils/types'

/**
 * Firearm Details section component
 *
 * This component handles the Firearm Details section of the New License form.
 * It collects all the firearm details needed for the license application and
 * maps the form fields to the XML placeholders for document generation.
 *
 * XML Placeholders for Firearm Details:
 *
 * // DETAILS OF FIREARM
 * - {Semi} - Semi-automatic (Mark with X)
 * - {Auto} - Automatic (Mark with X)
 * - {Man} - Manual (Mark with X)
 * - {OtherF} - Other Action (Fill in)
 *
 * - {ENGG} - Names and Addresses engraved in metal (Fill in)
 *
 * - {Caliber} - Caliber (Fill in)
 * - {Make} - Firearm Make (Fill in)
 * - {Model} - Firearm Model (Fill in)
 *
 * // Firearm component type
 * - {BSN} - Barrel serial number (Fill in)
 * - {FSN} - Frame serial number (Fill in)
 * - {RSN} - Receiver serial number (Fill in)
 *
 * // Firearm component make
 * - {BSNM} - Barrel Make (Fill in)
 * - {FSNM} - Frame Make (Fill in)
 * - {RSNM} - Receiver Make (Fill in)
 *
 * // Additional Information
 * - {PAFK} - Physical address where firearm(s) is kept (Fill in)
 * - {PAFKPOSTAL} - Postal Code (Fill in)
 * - {FLAP} - Name and surname of current owner/authorized person (Fill in)
 * - {FLID} - Identification number of current owner/authorized person (Fill in)
 * - {DESIGNATION} - Designation (Fill in)
 * - {APDATE} - Date (Auto-populated with current date)
 * - {APPLACE} - Place (Fill in)
 */
const FirearmDetails: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  // Handle address field changes
  const handleAddressChange = (address: string, postalCode?: string) => {
    updateFormData({
      pafk: address,
      pafkPostal: postalCode || formData.pafkPostal || ''
    })
  }

  // Auto-populate the date field with the current date when the component mounts
  useEffect(() => {
    // Only set the date if it's not already set
    if (!formData.apDate) {
      // Format the current date as YYYY-MM-DD for the date input
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0') // Months are 0-indexed
      const day = String(today.getDate()).padStart(2, '0')
      const formattedDate = `${year}-${month}-${day}`

      // Update the form data with the current date
      updateFormData({
        apDate: formattedDate
      })
    }
  }, [formData.apDate, updateFormData]) // Only run when formData.apDate or updateFormData changes

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Firearm Details</h3>
      <FormSection title="Firearm Details" subtitle="Please provide details about your firearm">
        <div className="space-y-3">
          <div className="border-b border-gray-700 pb-4 mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <FormField
                label="Make"
                name="make"
                value={formData.make || ''}
                onChange={handleChange}
                placeholder="Enter firearm make"
              />

              <FormField
                label="Model"
                name="model"
                value={formData.model || ''}
                onChange={handleChange}
                placeholder="Enter firearm model"
              />

              <FormField
                label="Caliber"
                name="caliber"
                value={formData.caliber || ''}
                onChange={handleChange}
                placeholder="Enter firearm caliber"
              />
            </div>
          </div>

          <div className="border-b border-gray-700 pb-4 mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">Serial Numbers</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <FormField
                label="Barrel Serial Number"
                name="bsn"
                value={formData.bsn}
                onChange={handleChange}
                placeholder="Enter barrel serial number"
              />

              <FormField
                label="Frame Serial Number"
                name="fsn"
                value={formData.fsn}
                onChange={handleChange}
                placeholder="Enter frame serial number"
              />

              <FormField
                label="Receiver Serial Number"
                name="rsn"
                value={formData.rsn}
                onChange={handleChange}
                placeholder="Enter receiver serial number"
              />
            </div>
          </div>

          <div className="border-b border-gray-700 pb-4 mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">Component Makes</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <FormField
                label="Barrel Make"
                name="bsnm"
                value={formData.bsnm}
                onChange={handleChange}
                placeholder="Enter barrel make"
              />

              <FormField
                label="Frame Make"
                name="fsnm"
                value={formData.fsnm}
                onChange={handleChange}
                placeholder="Enter frame make"
              />

              <FormField
                label="Receiver Make"
                name="rsnm"
                value={formData.rsnm}
                onChange={handleChange}
                placeholder="Enter receiver make"
              />
            </div>
          </div>

          <div className="border-b border-gray-700 pb-4 mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">Action Type</h3>
            <CheckboxGroup
              options={[
                { name: 'semi', label: 'Semi-automatic', checked: formData.semi },
                { name: 'auto', label: 'Automatic', checked: formData.auto },
                { name: 'man', label: 'Manual', checked: formData.man },
                { name: 'otherAction', label: 'Other', checked: formData.otherAction }
              ]}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const { name, checked } = e.target
                updateFormData({
                  [name]: checked
                })
              }}
            />

            {formData.otherAction && (
              <FormField
                label="Other Action Information"
                name="otherF"
                value={formData.otherF}
                onChange={handleChange}
                placeholder="Please specify the other action type"
                type="textarea"
                rows={2}
              />
            )}
          </div>

          <FormField
            label="Engravings"
            name="engg"
            value={formData.engg}
            onChange={handleChange}
            placeholder="Enter names and addresses engraved in metal"
            type="textarea"
            rows={2}
          />

          <div className="border-b border-gray-700 pb-4 mb-4 mt-4">
            <h3 className="text-lg font-semibold text-white mb-2">Additional Information</h3>

            {/* Physical address where firearm(s) is kept - {PAFK} and Postal Code - {PAFKPOSTAL} */}
            <AddressInput
              label="Physical address where firearm(s) is kept"
              value={formData.pafk || ''}
              postalCode={formData.pafkPostal || ''}
              onChange={handleAddressChange}
              placeholder="Enter physical address where firearm(s) is kept"
              isTextarea={false}
              required={false}
              postalCodeRequired={false}
            />
            <p className="text-xs text-stone-400 mt-1 mb-3">
              Enter the physical address where the firearm(s) will be kept.
            </p>

            {/* Name and surname of current owner/authorized person - {FLAP} */}
            <FormField
              label="Name and surname of current owner/authorized person"
              name="flap"
              value={formData.flap}
              onChange={handleChange}
              placeholder="Enter name and surname of current owner/authorized person"
            />

            {/* Identification number of current owner/authorized person - {FLID} */}
            <FormField
              label="Identification number of current owner/authorized person"
              name="flid"
              value={formData.flid}
              onChange={handleChange}
              placeholder="Enter identification number of current owner/authorized person"
            />

            {/* Designation - {DESIGNATION} */}
            <FormField
              label="Designation"
              name="designation"
              value={formData.designation}
              onChange={handleChange}
              placeholder="Enter designation"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                {/* Date - {APDATE} - Auto-populated with current date */}
                <FormField
                  label="Date (Auto-populated)"
                  name="apDate"
                  value={formData.apDate}
                  onChange={handleChange}
                  placeholder="Enter date"
                  type="date"
                />
                <p className="text-xs text-stone-400 mt-1">
                  Auto-populated with today's date
                </p>
              </div>

              {/* Place - {APPLACE} */}
              <FormField
                label="Place"
                name="apPlace"
                value={formData.apPlace}
                onChange={handleChange}
                placeholder="Enter place"
              />
            </div>
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default FirearmDetails
