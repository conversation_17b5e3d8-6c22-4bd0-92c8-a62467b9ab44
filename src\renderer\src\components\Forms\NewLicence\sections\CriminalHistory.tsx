import React from 'react'
import { FormSection, FormField, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Criminal History section component
 */
const CriminalHistory: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Criminal History</h3>
      <FormSection
        title="Criminal History"
        subtitle="Please provide information about your criminal history, if any"
      >
        <div className="space-y-3">
          {/* Question 1: Convicted of an offense - Uses H5A and H5B placeholders */}
          <div className="border-b border-gray-700 pb-4 mb-4">
            <RadioGroup
              name="convictedOfOffense"
              value={formData.h5a ? 'yes' : formData.h5b ? 'no' : 'no'} // Default to 'no' if neither is set
              onChange={(value) => {
                // Only update if the value is different from the current value
                const currentValue = formData.h5a ? 'yes' : 'no';
                if (value !== currentValue) {
                  // Explicitly set one to true and the other to false
                  // to ensure only one option gets an X in the document
                  updateFormData({
                    h5a: value === 'yes',
                    h5b: value === 'no'
                  })
                }
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Have you ever been convicted of an offense?"
            />

            {formData.h5a && (
              <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
                <FormField
                  label="Police Station"
                  name="h51"
                  value={formData.h51}
                  onChange={handleChange}
                  placeholder="Enter police station name"
                />

                <FormField
                  label="Case Number"
                  name="h52"
                  value={formData.h52}
                  onChange={handleChange}
                  placeholder="Enter case number"
                />

                <FormField
                  label="Charge"
                  name="h53"
                  value={formData.h53}
                  onChange={handleChange}
                  placeholder="Enter charge details"
                />

                <FormField
                  label="Outcome/Verdict"
                  name="h54"
                  value={formData.h54}
                  onChange={handleChange}
                  placeholder="Enter outcome or verdict"
                />
              </div>
            )}
          </div>

          {/* Question 2: Pending cases - Uses H6A and H6B placeholders */}
          <div className="border-b border-gray-700 pb-4 mb-4">
            <RadioGroup
              name="pendingCases"
              value={formData.h6a ? 'yes' : formData.h6b ? 'no' : 'no'} // Default to 'no' if neither is set
              onChange={(value) => {
                // Only update if the value is different from the current value
                const currentValue = formData.h6a ? 'yes' : 'no';
                if (value !== currentValue) {
                  // Explicitly set one to true and the other to false
                  // to ensure only one option gets an X in the document
                  updateFormData({
                    h6a: value === 'yes',
                    h6b: value === 'no'
                  })
                }
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Do you have any pending cases?"
            />

            {formData.h6a && (
              <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
                <FormField
                  label="Police Station"
                  name="h61"
                  value={formData.h61}
                  onChange={handleChange}
                  placeholder="Enter police station name"
                />

                <FormField
                  label="Case Number"
                  name="h62"
                  value={formData.h62}
                  onChange={handleChange}
                  placeholder="Enter case number"
                />

                <FormField
                  label="Offense"
                  name="h6a3"
                  value={formData.h6a3}
                  onChange={handleChange}
                  placeholder="Enter offense details"
                />
              </div>
            )}
          </div>

          {/* Question 3: Firearms lost/stolen - Uses H7A and H7B placeholders */}
          <div className="border-b border-gray-700 pb-4 mb-4">
            <RadioGroup
              name="firearmsLostStolen"
              value={formData.h7a ? 'yes' : formData.h7b ? 'no' : 'no'} // Default to 'no' if neither is set
              onChange={(value) => {
                // Only update if the value is different from the current value
                const currentValue = formData.h7a ? 'yes' : 'no';
                if (value !== currentValue) {
                  // Explicitly set one to true and the other to false
                  // to ensure only one option gets an X in the document
                  updateFormData({
                    h7a: value === 'yes',
                    h7b: value === 'no'
                  })
                }
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Have any of your Firearms been lost or stolen?"
            />

            {formData.h7a && (
              <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
                <FormField
                  label="Police Station"
                  name="h71"
                  value={formData.h71}
                  onChange={handleChange}
                  placeholder="Enter police station name"
                />

                <FormField
                  label="Case Number"
                  name="h72"
                  value={formData.h72}
                  onChange={handleChange}
                  placeholder="Enter case number"
                />

                <FormField
                  label="Circumstances"
                  name="h73"
                  value={formData.h73}
                  onChange={handleChange}
                  placeholder="Enter circumstances"
                />

                <FormField
                  label="Details of Firearm"
                  name="h74"
                  value={formData.h74}
                  onChange={handleChange}
                  placeholder="Enter firearm details"
                />
              </div>
            )}
          </div>

          {/* Question 4: Case of negligence - Uses H8A and H8B placeholders */}
          <div className="border-b border-gray-700 pb-4 mb-4">
            <RadioGroup
              name="caseOfNegligence"
              value={formData.h8a ? 'yes' : formData.h8b ? 'no' : 'no'} // Default to 'no' if neither is set
              onChange={(value) => {
                // Only update if the value is different from the current value
                const currentValue = formData.h8a ? 'yes' : 'no';
                if (value !== currentValue) {
                  // Explicitly set one to true and the other to false
                  // to ensure only one option gets an X in the document
                  updateFormData({
                    h8a: value === 'yes',
                    h8b: value === 'no'
                  })
                }
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Was a case of negligence opened and investigated regarding the stolen/lost firearm?"
            />

            {formData.h8a && (
              <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
                <FormField
                  label="Police Station"
                  name="h81"
                  value={formData.h81}
                  onChange={handleChange}
                  placeholder="Enter police station name"
                />

                <FormField
                  label="Case Number"
                  name="h82"
                  value={formData.h82}
                  onChange={handleChange}
                  placeholder="Enter case number"
                />

                <FormField
                  label="Charge"
                  name="h83"
                  value={formData.h83}
                  onChange={handleChange}
                  placeholder="Enter charge details"
                />

                <FormField
                  label="Outcome/Verdict"
                  name="h84"
                  value={formData.h84}
                  onChange={handleChange}
                  placeholder="Enter outcome or verdict"
                />
              </div>
            )}
          </div>

          {/* Question 5: Declared unfit - Uses H9A and H9B placeholders */}
          <div className="border-b border-gray-700 pb-4 mb-4">
            <RadioGroup
              name="declaredUnfit"
              value={formData.h9a ? 'yes' : formData.h9b ? 'no' : 'no'} // Default to 'no' if neither is set
              onChange={(value) => {
                // Only update if the value is different from the current value
                const currentValue = formData.h9a ? 'yes' : 'no';
                if (value !== currentValue) {
                  // Explicitly set one to true and the other to false
                  // to ensure only one option gets an X in the document
                  updateFormData({
                    h9a: value === 'yes',
                    h9b: value === 'no'
                  })
                }
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Have you ever been declared unfit to possess a firearm?"
            />

            {formData.h9a && (
              <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
                <FormField
                  label="Police Station"
                  name="h91"
                  value={formData.h91}
                  onChange={handleChange}
                  placeholder="Enter police station name"
                />

                <FormField
                  label="Case Number"
                  name="h92"
                  value={formData.h92}
                  onChange={handleChange}
                  placeholder="Enter case number"
                />

                <FormField
                  label="Charge"
                  name="h93"
                  value={formData.h93}
                  onChange={handleChange}
                  placeholder="Enter charge details"
                />

                <FormField
                  label="Date from which unfit"
                  name="h94"
                  value={formData.h94}
                  onChange={handleChange}
                  placeholder="Enter date"
                />

                <FormField
                  label="Period of Unfitness"
                  name="h95"
                  value={formData.h95}
                  onChange={handleChange}
                  placeholder="Enter period of unfitness"
                />
              </div>
            )}
          </div>

          {/* Question 6: Firearm confiscated - Uses the following placeholders:
           * - {H10A} - Confiscated Yes (Mark with X)
           * - {H10B} - Confiscated No (Mark with X)
           * - {H10.1} - Confiscated Police Station
           * - {H10.2} - Confiscated Case Number
           * - {H10.3} - Confiscated Circumstances
           * - {H10.4} - Confiscated Outcome/Verdict
           */}
          <div className="border-b border-gray-700 pb-4 mb-4">
            <RadioGroup
              name="firearmConfiscated"
              value={formData.h10a ? 'yes' : formData.h10b ? 'no' : 'no'} // Default to 'no' if neither is set
              onChange={(value) => {
                // Only update if the value is different from the current value
                const currentValue = formData.h10a ? 'yes' : 'no';
                if (value !== currentValue) {
                  // Explicitly set one to true and the other to false
                  // to ensure only one option gets an X in the document
                  updateFormData({
                    h10a: value === 'yes',
                    h10b: value === 'no'
                  })
                }
              }}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' }
              ]}
              label="Has a Firearm Ever been confiscated?"
            />

            {formData.h10a && (
              <div className="ml-6 border-l-2 border-orange-500 pl-4 space-y-3 mt-3">
                <FormField
                  label="Police Station"
                  name="h101"
                  value={formData.h101}
                  onChange={handleChange}
                  placeholder="Enter police station name"
                />

                <FormField
                  label="Case Number"
                  name="h102"
                  value={formData.h102}
                  onChange={handleChange}
                  placeholder="Enter case number"
                />

                <FormField
                  label="Circumstances"
                  name="h103"
                  value={formData.h103}
                  onChange={handleChange}
                  placeholder="Enter circumstances"
                />

                <FormField
                  label="Outcome/Verdict"
                  name="h104"
                  value={formData.h104}
                  onChange={handleChange}
                  placeholder="Enter outcome or verdict"
                />
              </div>
            )}
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default CriminalHistory
