/**
 * Format a number as currency (ZAR)
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2
  }).format(amount)
}

/**
 * Format a date string to a readable format
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date)
  } catch (error) {
    // Return the original string if formatting fails
    return dateString
  }
}

/**
 * Calculate days remaining until a date
 * Returns negative number for past dates
 */
export const daysUntil = (dateString: string): number => {
  if (!dateString) return 0

  try {
    const targetDate = new Date(dateString)
    const today = new Date()

    // Reset time to midnight for accurate day calculation
    targetDate.setHours(0, 0, 0, 0)
    today.setHours(0, 0, 0, 0)

    const diffTime = targetDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays
  } catch (error) {
    // Return 0 if calculation fails
    return 0
  }
}

/**
 * Format a relative time (days from now)
 */
export const formatRelativeTime = (days: number): string => {
  if (days === 0) return 'Today'
  if (days === 1) return 'Tomorrow'
  if (days === -1) return 'Yesterday'

  if (days > 0) {
    return `In ${days} days`
  } else {
    return `${Math.abs(days)} days ago`
  }
}

/**
 * Format a phone number
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return ''

  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '')

  // Format based on length
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
  }

  return phone
}

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}