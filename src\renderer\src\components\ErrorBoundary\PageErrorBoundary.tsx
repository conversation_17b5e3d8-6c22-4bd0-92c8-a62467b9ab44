import React, { ErrorInfo, ReactNode } from 'react';
import ErrorBoundary from './ErrorBoundary';
import { DashboardIcons } from '../icons/DashboardIcons';

interface PageErrorBoundaryProps {
  children: ReactNode;
  pageName?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetKeys?: any[];
}

/**
 * An error boundary specifically designed for wrapping entire pages.
 * Provides a more detailed and user-friendly error UI for page-level errors.
 */
const PageErrorBoundary: React.FC<PageErrorBoundaryProps> = ({
  children,
  pageName = 'this page',
  onError,
  resetKeys
}) => {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    // Log the error
    console.error(`Error in ${pageName}:`, error, errorInfo);

    // Call the onError callback if provided
    if (onError) {
      onError(error, errorInfo);
    }
  };

  const PageErrorFallback = () => (
    <div className="flex flex-col items-center justify-center h-full p-8">
      <div className="w-20 h-20 text-red-500 mb-6">
        <DashboardIcons.Error className="w-full h-full" />
      </div>
      <h2 className="text-2xl font-semibold text-white mb-2">Something went wrong</h2>
      <p className="text-stone-400 text-center mb-6 max-w-md">
        We encountered an error while trying to load {pageName}.
        This could be due to a temporary issue or a problem with your connection.
      </p>
      <button
        onClick={() => window.location.reload()}
        className="px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors flex items-center"
      >
        <DashboardIcons.Refresh className="w-4 h-4 mr-2" />
        Reload Page
      </button>
    </div>
  );

  return (
    <ErrorBoundary
      fallback={<PageErrorFallback />}
      onError={handleError}
      resetKeys={resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

export default PageErrorBoundary;
