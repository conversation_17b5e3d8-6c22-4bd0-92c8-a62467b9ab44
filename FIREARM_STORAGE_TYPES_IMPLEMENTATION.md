# Firearm Storage Types Implementation

This document outlines the implementation of the firearm storage types feature in the FLM System.

## Storage Types

The system now supports the following storage types:

1. **Owner** - Firearm is owned and assigned to the owner
2. **Private** - Firearm is privately owned and can be assigned to another client
3. **Dealer** - Firearm is owned by the gunshop and is assigned to a client
4. **Hidden** - Firearm is hidden from normal views

## Payment Rules

- Each client assigned a firearm must pay R225 every 28 days from the date assigned
- Dealer type firearms get 12 months of free storage, after which they must pay R225 per month

## Implementation Details

### Database Changes

Added the following columns to the `firearms` table:
- `storage_type` - VARCHAR NOT NULL DEFAULT 'Owner'
- `free_storage_until` - TIMESTAMP WITH TIME ZONE

### Type Definition Updates

Updated the Firearm type definition to include:
- `storage_type` - 'Owner' | 'Private' | 'Dealer' | 'Hidden'
- `free_storage_until` - string | null
- `payment_status` - 'free' | 'paid' | 'due'
- `next_payment_date` - string

### UI Updates

1. **Firearm Form**
   - Added storage type selection dropdown
   - Added free storage until date field for Dealer type

2. **Firearm Card**
   - Added storage type display with color coding
   - Added free storage until date display for Dealer type
   - Added payment status information

3. **Firearm Storage Page**
   - Added storage type filters
   - Organized filters into sections

4. **Firearm Assignment Form**
   - Added storage type validation (Owner type can only be assigned to owner)
   - Added automatic setting of free_storage_until for Dealer type
   - Added storage type display in the form

### Business Logic Updates

1. **Payment Calculation**
   - Added logic to calculate payment status based on storage type and assignment date
   - For Dealer type, checks if within free storage period
   - For all types, calculates next payment date based on 28-day cycle

2. **Assignment Rules**
   - Owner type firearms can only be assigned to the owner
   - When assigning a Dealer type firearm, free_storage_until is set to 12 months from assignment date

## How to Use

1. When creating a new firearm, select the appropriate storage type
2. For Dealer type firearms, the free storage period is automatically set when assigned
3. Use the storage type filters to view firearms by type
4. The payment status is automatically calculated and displayed on the firearm card

## SQL Migration

A SQL migration script has been created at `database/add_firearm_storage_types.sql` to add the necessary columns to the database.
