import { useEffect, Suspense, lazy } from 'react'
import SearchContainer from '../components/SearchContainer'
import {
  FilterComponent,
  LoanList,
  FocusedLoan,
  EmptyState,
  useLoanService
} from '../components/Loans'
import { Pagination } from '../components/Dashboard'
import { DashboardIcons } from '../components/icons/DashboardIcons'
import { SkeletonLoans } from '../components/SkeletonLoading'
import { Loan } from '../types'
import '../styles/focus-mode.css'

// Lazy load components
const LoanForm = lazy(() => import('../components/Forms/LoanForm'))
const PaymentForm = lazy(() => import('../components/Forms/PaymentForm'))
const LicenseForm = lazy(() => import('../components/Forms/AddLicence'))
const FirearmForm = lazy(() => import('../components/Forms/FirearmForm'))

function Loans(): React.JSX.Element {
  const {
    // State
    loading,
    searchQuery,
    page,
    perPage,
    filter,
    totalRemainingAmount,
    filterCounts,
    formState,
    totalLoans,
    focusedLoanId,
    focusedLoan,
    paginatedLoans,
    loanSearchTips,

    // Actions
    setPage,
    setFilter,
    handleSearch,
    handleSort,
    handleLoanFocusToggle,
    handleCreateLoan,
    handleCreatePayment,
    handleDeleteLoan,
    handleCancelLoan,
    handleAddLicense,
    resetFormState,
    fetchSearchHints,
    fetchFilterCounts,
    debouncedFetch,
    updateLoanWithFirearm,
    updateInvoiceNumber
  } = useLoanService()

  // Add a useEffect to disable scrolling on the main content area and body
  useEffect(() => {
    // Get the main content area
    const mainContent = document.querySelector(
      '#root > div > div:nth-child(2)'
    ) as HTMLElement | null

    // Save original styles
    const originalBodyOverflow = document.body.style.overflow
    const originalHtmlOverflow = document.documentElement.style.overflow

    // Disable scrolling on body and html
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    if (mainContent) {
      // Save the original style
      const originalOverflow = mainContent.style.overflowY

      // Disable scrolling
      mainContent.style.overflowY = 'hidden'

      // Restore original styles when component unmounts
      return () => {
        mainContent.style.overflowY = originalOverflow
        document.body.style.overflow = originalBodyOverflow
        document.documentElement.style.overflow = originalHtmlOverflow
      }
    }

    // Restore original styles when component unmounts (if mainContent not found)
    return () => {
      document.body.style.overflow = originalBodyOverflow
      document.documentElement.style.overflow = originalHtmlOverflow
    }
  }, [])

  // Apply body overflow class when focus mode is active
  useEffect(() => {
    if (focusedLoanId) {
      document.body.classList.add('focus-mode-active')
    } else {
      document.body.classList.remove('focus-mode-active')
    }

    return () => {
      document.body.classList.remove('focus-mode-active')
    }
  }, [focusedLoanId])

  return (
    <div className="min-h-screen bg-gradient-to-b from-stone-900 to-stone-800 p-6 overflow-hidden">
      {/* Page title with pagination */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white flex items-center gap-3">
          Loan Management
          <div className="px-3 py-1 bg-stone-700/50 rounded-full text-sm text-stone-300">
            Total Records: {totalLoans}
          </div>
        </h1>

        {/* Pagination controls next to page title */}
        {paginatedLoans.loans.length > 0 && (
          <Pagination
            page={page}
            setPage={setPage}
            totalItems={paginatedLoans.total}
            itemsPerPage={perPage}
          />
        )}
      </div>

      {/* Two-column layout - always side by side */}
      <div className="flex flex-row h-[calc(100vh-180px)] overflow-hidden">
        {/* Left column: Page tools */}
        <div className="w-80 bg-stone-800/60 rounded-lg shadow-lg p-4 flex-shrink-0 overflow-hidden flex flex-col">
          {/* Action Buttons */}
          <div className="flex items-center justify-between mb-4">
            {/* Refresh Button */}
            <button
              onClick={() => {
                debouncedFetch('')
                fetchFilterCounts()
              }}
              disabled={loading}
              className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-2 rounded-lg
                transition-colors flex items-center gap-1 disabled:opacity-50 h-10"
            >
              <DashboardIcons.Refresh className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
            </button>

            {/* Create New Loan Button */}
            <button
              onClick={handleCreateLoan}
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                text-white px-3 py-2 rounded-lg shadow-md shadow-orange-500/20
                transition-all duration-200 flex items-center gap-2 h-10"
            >
              <DashboardIcons.Add className="w-4 h-4" />
              <span>New Loan</span>
            </button>
          </div>

          {/* Search */}
          <div className="mb-6">
            <SearchContainer
              placeholder="Search loans..."
              onSearch={handleSearch}
              searchTipsContent={
                <>
                  <p>{loanSearchTips.title}</p>
                  <ul className="list-disc pl-4 mt-1 space-y-1">
                    {loanSearchTips.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </>
              }
              searchHintsLoader={fetchSearchHints}
              isLoading={loading}
              showRefreshButton={false}
              showCreateButton={false}
              debounceTime={400}
              className="w-full"
              initialValue={searchQuery}
            />
          </div>

          {/* Filter Component - Now takes remaining height */}
          <div className="flex-1 overflow-hidden">
            <FilterComponent
              filter={filter}
              setFilter={setFilter}
              filterCounts={filterCounts}
              totalRemainingAmount={totalRemainingAmount}
            />
          </div>
        </div>

        {/* Right column: Loan content */}
        <div className="flex-1 ml-6 flex flex-col">
          <div className="bg-stone-800/30 rounded-lg p-4 flex flex-col h-full overflow-hidden">
            {/* Search Results Section */}
            {loading ? (
              <div className="h-full">
                <SkeletonLoans count={5} />
              </div>
            ) : focusedLoanId && focusedLoan ? (
              <div className="focus-mode-container">
                <div className="focus-mode-card">
                  <FocusedLoan
                    loan={focusedLoan}
                    onClose={() => handleLoanFocusToggle(null)}
                    handleCreatePayment={handleCreatePayment}
                    handleDeleteLoan={handleDeleteLoan}
                    handleCancelLoan={handleCancelLoan}
                    handleAddLicense={handleAddLicense}
                    onUpdateInvoiceNumber={updateInvoiceNumber}
                  />
                </div>
              </div>
            ) : paginatedLoans.loans.length > 0 ? (
              <LoanList
                loans={paginatedLoans.loans}
                handleSort={handleSort}
                sortConfig={{ field: 'start_date', direction: 'desc' }}
                handleLoanFocusToggle={handleLoanFocusToggle}
                handleCreatePayment={handleCreatePayment}
                handleDeleteLoan={handleDeleteLoan}
                handleCancelLoan={handleCancelLoan}
                handleAddLicense={handleAddLicense}
                onUpdateInvoiceNumber={updateInvoiceNumber}
              />
            ) : (
              <EmptyState searchQuery={searchQuery} filter={filter} onCreateLoan={handleCreateLoan} />
            )}
          </div>
        </div>
      </div>

      {/* Forms */}
      <Suspense
        fallback={
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
            Loading...
          </div>
        }
      >
        {formState.isOpen && formState.type === 'loan' && (
          <LoanForm
            onClose={resetFormState}
            onSuccess={() => {
              handleSearch(searchQuery)
              resetFormState()
            }}
          />
        )}
        {formState.isOpen && formState.type === 'payment' && formState.selectedLoan && (
          <PaymentForm
            loan={formState.selectedLoan as Loan}
            onClose={resetFormState}
            onSuccess={() => {
              handleSearch(searchQuery)
              resetFormState()
            }}
          />
        )}
        {formState.isOpen &&
          formState.type === 'license' &&
          formState.selectedLoan &&
          formState.selectedClientId && (
            <LicenseForm
              clientId={formState.selectedClientId}
              loanId={formState.selectedLoan.id}
              onClose={resetFormState}
              onSuccess={() => {
                handleSearch(searchQuery)
                resetFormState()
              }}
            />
          )}
        {formState.isOpen &&
          formState.type === 'firearm' &&
          formState.selectedLoan &&
          formState.selectedClientId && (
            <FirearmForm
              clientId={formState.selectedClientId}
              loanId={formState.selectedLoan.id}
              loan={formState.selectedLoan}
              onClose={resetFormState}
              onSuccess={(firearmId, assignmentId) => {
                // If we have both IDs, we can immediately update the UI
                if (firearmId && assignmentId && formState.selectedLoan) {
                  // Update the loan with the firearm information
                  updateLoanWithFirearm(formState.selectedLoan.id, firearmId, assignmentId)
                }
                // Refresh the loans list
                handleSearch(searchQuery)
                resetFormState()
              }}
            />
          )}
      </Suspense>
    </div>
  )
}

export default Loans
