import React, { useState, useRef } from 'react'
import {
  Activity, BarChart, Briefcase, Calendar, ChevronDown, ChevronRight,
  Circle, Copy, Database, Download, Edit, FileText, Filter, GitBranch,
  Layers, ListOrdered, Minus, Plus, RefreshCw, Search, User, Users, X
} from 'lucide-react'
import { useLogsSettings } from './hooks/useLogsSettings'
import { useTableVirtualization } from '../../hooks/useTableVirtualization'
import PipedriveLogVisualization from './PipedriveLogVisualization'

// Add custom scrollbar styles
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(28, 25, 23, 0.5);
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(120, 113, 108, 0.5);
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(168, 162, 158, 0.5);
  }

  /* Table layout styles to prevent horizontal scrolling */
  .logs-table {
    width: 100%;
    table-layout: fixed;
  }

  .logs-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .logs-table td.message-cell,
  .logs-table td.details-cell {
    white-space: normal;
    word-break: break-word;
  }

  /* Ensure the container doesn't overflow */
  .logs-container {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
  }
`

const LogsSettings: React.FC = () => {
  const {
    logs,
    isLoading,
    error,
    filter,
    setFilter,
    logSources,
    logLevels,
    totalLogs,
    page,
    setPage,
    pageSize,
    setPageSize,
    activeLogType,
    changeLogType,
    auditLogTables,
    auditLogActions,
    transactionTypes,
    pipedriveActionTypes,
    pipedriveEntityTypes,
    pipedriveUsers,
    pipedriveClients,
    pipedriveStatuses,
    clearFilters,
    exportLogs,
    refreshLogs
  } = useLogsSettings()

  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedLogs, setExpandedLogs] = useState<Record<string, boolean>>({})
  const [viewMode, setViewMode] = useState<'table' | 'visualization'>('table')

  // Use the table virtualization hook
  const virtualizedLogs = useTableVirtualization(logs, 20, 10)

  // Function to toggle expanded state of a log
  const toggleLogExpanded = (logId: string) => {
    setExpandedLogs(prev => ({
      ...prev,
      [logId]: !prev[logId]
    }))
  }

  // Function to check if a log should be auto-expanded
  const shouldAutoExpand = (log: any) => {
    // Auto-expand if there are only a few changes (3 or fewer fields)
    if (!log.old_data && !log.new_data) return false;

    const oldKeys = Object.keys(log.old_data || {});
    const newKeys = Object.keys(log.new_data || {});
    const uniqueKeys = new Set([...oldKeys, ...newKeys]);

    return uniqueKeys.size <= 3;
  }

  // Function to copy JSON to clipboard
  const copyToClipboard = (data: any) => {
    try {
      // Parse data if it's a string
      let parsedData = data;
      if (typeof data === 'string') {
        try {
          parsedData = JSON.parse(data);
        } catch (parseError) {
          // If parsing fails, use the original string
          console.warn('Failed to parse JSON string:', parseError);
        }
      }
      navigator.clipboard.writeText(JSON.stringify(parsedData, null, 2));
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  }

  // Function to handle search
  const handleSearch = () => {
    setFilter({ ...filter, searchTerm })
    setPage(1)
  }

  // Function to handle key down in search input
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  // Function to format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp)
      return date.toLocaleString()
    } catch (error) {
      return timestamp
    }
  }

  // Function to get color class based on log level
  const getLevelColorClass = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'error':
        return 'text-red-500'
      case 'warn':
        return 'text-amber-500'
      case 'info':
        return 'text-blue-500'
      case 'debug':
        return 'text-green-500'
      default:
        return 'text-stone-300'
    }
  }

  // Function to get color class based on audit action
  const getActionColorClass = (action: string) => {
    switch (action?.toLowerCase()) {
      case 'insert':
        return 'text-green-500'
      case 'update':
        return 'text-blue-500'
      case 'delete':
        return 'text-red-500'
      default:
        return 'text-stone-300'
    }
  }

  // Function to get color class based on transaction type
  const getTransactionColorClass = (_type: string, amount: number) => {
    if (amount < 0) return 'text-red-500'
    return 'text-green-500'
  }

  // Function to format and display differences between old and new JSON data
  const formatJsonDiff = (oldData: any, newData: any) => {
    if (!oldData && !newData) return null;

    try {
      // Parse the data if it's a string
      const oldObj = typeof oldData === 'string' ? JSON.parse(oldData) : oldData || {};
      const newObj = typeof newData === 'string' ? JSON.parse(newData) : newData || {};

      // Get all keys from both objects
      const allKeys = [...new Set([...Object.keys(oldObj), ...Object.keys(newObj)])].sort();

      return (
        <div className="space-y-1 text-xs">
          {allKeys.map(key => {
            const oldValue = oldObj[key];
            const newValue = newObj[key];

            // Skip if both values are undefined or null
            if (oldValue === undefined && newValue === undefined) return null;
            if (oldValue === null && newValue === null) return null;

            // Format date strings for better readability
            const formatValue = (value: any) => {
              if (value === null || value === undefined) return <em className="text-stone-500">null</em>;

              // Check if it's a date string (YYYY-MM-DD format)
              if (typeof value === 'string' &&
                  (value.match(/^\d{4}-\d{2}-\d{2}$/) ||
                   value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/) ||
                   value.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/))) {
                try {
                  const date = new Date(value);
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString();
                  }
                } catch (e) {
                  // If date parsing fails, return the original value
                }
              }

              return typeof value === 'object' ? JSON.stringify(value) : String(value);
            };

            // Determine the type of change
            let changeType = 'unchanged';
            if (oldValue === undefined) changeType = 'added';
            else if (newValue === undefined) changeType = 'removed';
            else if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) changeType = 'modified';

            // Skip unchanged values to reduce clutter
            if (changeType === 'unchanged') return null;

            return (
              <div
                key={key}
                className={`flex items-start gap-1 ${
                  changeType === 'added' ? 'text-green-400' :
                  changeType === 'removed' ? 'text-red-400' :
                  changeType === 'modified' ? 'text-blue-400' :
                  'text-stone-300'
                }`}
              >
                {changeType === 'added' && <Plus size={14} className="mt-0.5 flex-shrink-0" />}
                {changeType === 'removed' && <Minus size={14} className="mt-0.5 flex-shrink-0" />}
                {changeType === 'modified' && <Edit size={14} className="mt-0.5 flex-shrink-0" />}

                <div className="flex-1">
                  <span className="font-semibold">{key}:</span>{' '}

                  {changeType === 'modified' ? (
                    <div className="ml-4">
                      <div className="text-red-400 line-through">{formatValue(oldValue)}</div>
                      <div className="text-green-400">{formatValue(newValue)}</div>
                    </div>
                  ) : (
                    <span>{formatValue(changeType === 'added' ? newValue : oldValue)}</span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      );
    } catch (error) {
      // Fallback to simple display if there's an error parsing the JSON
      console.error('Error parsing JSON diff:', error);
      return (
        <div className="space-y-1">
          {oldData && (
            <div className="text-xs text-red-400">
              <span className="font-semibold">Old:</span> {JSON.stringify(oldData)}
            </div>
          )}
          {newData && (
            <div className="text-xs text-green-400">
              <span className="font-semibold">New:</span> {JSON.stringify(newData)}
            </div>
          )}
        </div>
      );
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalLogs / pageSize)

  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">
      <style>{scrollbarStyles}</style>
      <div className="flex flex-col space-y-2">
        <h2 className="text-lg font-semibold text-white pb-1">
          System Logs
        </h2>

        {/* Log type tabs */}
        <div className="flex flex-wrap border-b border-stone-700/50">
          <button
            onClick={() => changeLogType('application')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeLogType === 'application'
                ? 'border-blue-500 text-blue-500'
                : 'border-transparent text-stone-400 hover:text-white hover:border-stone-500'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <FileText size={16} />
              <span>Application Logs</span>
            </div>
          </button>

          <button
            onClick={() => changeLogType('audit')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeLogType === 'audit'
                ? 'border-blue-500 text-blue-500'
                : 'border-transparent text-stone-400 hover:text-white hover:border-stone-500'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Database size={16} />
              <span>Audit Logs</span>
            </div>
          </button>

          <button
            onClick={() => changeLogType('payment')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeLogType === 'payment'
                ? 'border-blue-500 text-blue-500'
                : 'border-transparent text-stone-400 hover:text-white hover:border-stone-500'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <BarChart size={16} />
              <span>Payment History</span>
            </div>
          </button>

          <button
            onClick={() => changeLogType('credit')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeLogType === 'credit'
                ? 'border-blue-500 text-blue-500'
                : 'border-transparent text-stone-400 hover:text-white hover:border-stone-500'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Activity size={16} />
              <span>Credit Transactions</span>
            </div>
          </button>

          <button
            onClick={() => changeLogType('storage')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeLogType === 'storage'
                ? 'border-blue-500 text-blue-500'
                : 'border-transparent text-stone-400 hover:text-white hover:border-stone-500'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Layers size={16} />
              <span>Storage Transactions</span>
            </div>
          </button>

          <button
            onClick={() => changeLogType('pipedrive')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeLogType === 'pipedrive'
                ? 'border-blue-500 text-blue-500'
                : 'border-transparent text-stone-400 hover:text-white hover:border-stone-500'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Activity size={16} />
              <span>Pipedrive Actions</span>
            </div>
          </button>
        </div>
      </div>

      {/* Controls and filters */}
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={refreshLogs}
              disabled={isLoading}
              className="flex items-center gap-1 px-2 py-1 text-sm bg-stone-700 hover:bg-stone-600 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
              <span>Refresh</span>
            </button>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-1 px-2 py-1 text-sm bg-stone-700 hover:bg-stone-600 text-white rounded-md transition-colors"
            >
              <Filter size={14} />
              <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>
            </button>
            <button
              onClick={exportLogs}
              disabled={logs.length === 0 || isLoading}
              className="flex items-center gap-1 px-2 py-1 text-sm bg-stone-700 hover:bg-stone-600 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Download size={14} />
              <span>Export CSV</span>
            </button>

            {/* View mode toggle buttons (only for Pipedrive logs) */}
            {activeLogType === 'pipedrive' && (
              <div className="flex rounded-md overflow-hidden">
                <button
                  onClick={() => setViewMode('table')}
                  className={`flex items-center gap-1 px-2 py-1 text-sm ${
                    viewMode === 'table'
                      ? 'bg-blue-600 text-white'
                      : 'bg-stone-700 hover:bg-stone-600 text-white'
                  } transition-colors`}
                >
                  <Database size={14} />
                  <span>Table</span>
                </button>
                <button
                  onClick={() => setViewMode('visualization')}
                  className={`flex items-center gap-1 px-2 py-1 text-sm ${
                    viewMode === 'visualization'
                      ? 'bg-blue-600 text-white'
                      : 'bg-stone-700 hover:bg-stone-600 text-white'
                  } transition-colors`}
                >
                  <BarChart size={14} />
                  <span>Charts</span>
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Search logs..."
                className="pl-8 pr-2 py-1 text-sm bg-stone-800 border border-stone-700 rounded-md text-white w-48 focus:outline-none focus:ring-1 focus:ring-stone-500"
              />
              <Search size={14} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-stone-500" />
              {searchTerm && (
                <button
                  onClick={() => {
                    setSearchTerm('')
                    if (filter.searchTerm) {
                      setFilter({ ...filter, searchTerm: undefined })
                    }
                  }}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-stone-500 hover:text-white"
                >
                  <X size={14} />
                </button>
              )}
            </div>
            <button
              onClick={handleSearch}
              className="flex items-center gap-1 px-2 py-1 text-sm bg-stone-700 hover:bg-stone-600 text-white rounded-md transition-colors"
            >
              <Search size={14} />
              <span>Search</span>
            </button>
          </div>
        </div>

        {/* Filters panel */}
        {showFilters && (
          <div className="bg-stone-800/60 p-3 rounded-md border border-stone-700/50 flex flex-wrap gap-4 w-full max-w-full">
            {/* Application log filters */}
            {activeLogType === 'application' && (
              <>
                {/* Log level filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Log Level</label>
                  <select
                    value={filter.level || ''}
                    onChange={(e) => setFilter({ ...filter, level: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-32"
                  >
                    <option value="">All Levels</option>
                    {logLevels.map((level) => (
                      <option key={level} value={level}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Source filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Source</label>
                  <select
                    value={filter.source || ''}
                    onChange={(e) => setFilter({ ...filter, source: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-40"
                  >
                    <option value="">All Sources</option>
                    {logSources.map((source) => (
                      <option key={source} value={source}>
                        {source}
                      </option>
                    ))}
                  </select>
                </div>
              </>
            )}

            {/* Audit log filters */}
            {activeLogType === 'audit' && (
              <>
                {/* Action filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Action</label>
                  <select
                    value={filter.action || ''}
                    onChange={(e) => setFilter({ ...filter, action: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-32"
                  >
                    <option value="">All Actions</option>
                    {auditLogActions.map((action) => (
                      <option key={action} value={action}>
                        {action.charAt(0).toUpperCase() + action.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Table filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Table</label>
                  <select
                    value={filter.table_name || ''}
                    onChange={(e) => setFilter({ ...filter, table_name: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-40"
                  >
                    <option value="">All Tables</option>
                    {auditLogTables.map((table) => (
                      <option key={table} value={table}>
                        {table}
                      </option>
                    ))}
                  </select>
                </div>
              </>
            )}

            {/* Payment log filters */}
            {activeLogType === 'payment' && (
              <>
                {/* Payment type filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Payment Type</label>
                  <select
                    value={filter.payment_type || ''}
                    onChange={(e) => setFilter({ ...filter, payment_type: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-40"
                  >
                    <option value="">All Types</option>
                    {transactionTypes.map((type) => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </>
            )}

            {/* Pipedrive log filters */}
            {activeLogType === 'pipedrive' && (
              <>
                {/* Action type filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Action Type</label>
                  <select
                    value={filter.actionType || ''}
                    onChange={(e) => setFilter({ ...filter, actionType: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-32"
                  >
                    <option value="">All Actions</option>
                    {pipedriveActionTypes.map((type) => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Entity type filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Entity Type</label>
                  <select
                    value={filter.entityType || ''}
                    onChange={(e) => setFilter({ ...filter, entityType: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-40"
                  >
                    <option value="">All Entities</option>
                    {pipedriveEntityTypes.map((type) => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                {/* User filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">User</label>
                  <select
                    value={filter.userId || ''}
                    onChange={(e) => setFilter({ ...filter, userId: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-48"
                  >
                    <option value="">All Users</option>
                    {pipedriveUsers.map((user) => (
                      <option key={user.id} value={user.id}>
                        {user.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Client filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Client</label>
                  <select
                    value={filter.clientId || ''}
                    onChange={(e) => setFilter({ ...filter, clientId: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-48"
                  >
                    <option value="">All Clients</option>
                    {pipedriveClients.map((client) => (
                      <option key={client.id} value={client.id}>
                        {client.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Status filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Status</label>
                  <select
                    value={filter.status || ''}
                    onChange={(e) => setFilter({ ...filter, status: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-32"
                  >
                    <option value="">All Statuses</option>
                    {pipedriveStatuses.map((status) => (
                      <option key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </>
            )}

            {/* Credit transaction filters */}
            {activeLogType === 'credit' && (
              <>
                {/* Transaction type filter */}
                <div className="flex flex-col space-y-1">
                  <label className="text-stone-300 text-xs">Transaction Type</label>
                  <select
                    value={filter.transaction_type || ''}
                    onChange={(e) => setFilter({ ...filter, transaction_type: e.target.value || undefined })}
                    className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-40"
                  >
                    <option value="">All Types</option>
                    {transactionTypes.map((type) => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </>
            )}

            {/* Common date range filters for all log types */}
            <div className="flex flex-col space-y-1">
              <label className="text-stone-300 text-xs">Start Date</label>
              <div className="relative">
                <input
                  type="date"
                  value={filter.startDate || ''}
                  onChange={(e) => setFilter({ ...filter, startDate: e.target.value || undefined })}
                  className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-40"
                />
                <Calendar size={14} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-stone-500" />
              </div>
            </div>

            <div className="flex flex-col space-y-1">
              <label className="text-stone-300 text-xs">End Date</label>
              <div className="relative">
                <input
                  type="date"
                  value={filter.endDate || ''}
                  onChange={(e) => setFilter({ ...filter, endDate: e.target.value || undefined })}
                  className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1 w-40"
                />
                <Calendar size={14} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-stone-500" />
              </div>
            </div>

            {/* Clear filters button */}
            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 px-2 py-1 text-sm bg-stone-700 hover:bg-stone-600 text-white rounded-md transition-colors"
              >
                <X size={14} />
                <span>Clear Filters</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-900/30 border border-red-800 text-red-300 p-3 rounded-md flex items-start gap-2">
          <span className="text-red-500 mt-0.5">
            <X size={16} />
          </span>
          <div>
            <p className="font-medium">Error fetching logs</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Logs table or visualization */}
      {activeLogType === 'pipedrive' && viewMode === 'visualization' ? (
        <PipedriveLogVisualization
          logs={logs}
          onFilterChange={(newFilter) => {
            setFilter({ ...filter, ...newFilter })
            setViewMode('table') // Switch back to table view to see filtered results
          }}
        />
      ) : (
        <div className="bg-stone-800/30 rounded-lg border border-stone-700/50 overflow-hidden logs-container">
          <div
            className="max-h-[calc(100vh-350px)] custom-scrollbar"
            onScroll={(e) => virtualizedLogs.handleScroll(e)}
          >
          <table className="logs-table text-sm text-left">
            <thead className="text-xs uppercase bg-stone-800 text-stone-400 border-b border-stone-700 sticky top-0 z-10">
              {activeLogType === 'application' ? (
                <tr>
                  <th className="px-3 py-2 w-44">Timestamp</th>
                  <th className="px-3 py-2 w-24">Level</th>
                  <th className="px-3 py-2 w-32">Source</th>
                  <th className="px-3 py-2">Message</th>
                </tr>
              ) : activeLogType === 'audit' ? (
                <tr>
                  <th className="px-3 py-2 w-44">Timestamp</th>
                  <th className="px-3 py-2 w-24">Action</th>
                  <th className="px-3 py-2 w-32">Table</th>
                  <th className="px-3 py-2 w-24">Role</th>
                  <th className="px-3 py-2">Changes</th>
                </tr>
              ) : activeLogType === 'payment' ? (
                <tr>
                  <th className="px-3 py-2 w-44">Date</th>
                  <th className="px-3 py-2 w-28">Amount</th>
                  <th className="px-3 py-2 w-28">Type</th>
                  <th className="px-3 py-2 w-28">Status</th>
                  <th className="px-3 py-2 w-40">Transaction ID</th>
                  <th className="px-3 py-2">Client</th>
                </tr>
              ) : activeLogType === 'credit' ? (
                <tr>
                  <th className="px-3 py-2 w-44">Date</th>
                  <th className="px-3 py-2 w-28">Amount</th>
                  <th className="px-3 py-2 w-28">Type</th>
                  <th className="px-3 py-2 w-32">Reference</th>
                  <th className="px-3 py-2">Description</th>
                  <th className="px-3 py-2 w-40">Client</th>
                </tr>
              ) : activeLogType === 'pipedrive' ? (
                <tr>
                  <th className="px-3 py-2 w-44">Timestamp</th>
                  <th className="px-3 py-2 w-28">Action</th>
                  <th className="px-3 py-2 w-28">Entity</th>
                  <th className="px-3 py-2 w-32">User</th>
                  <th className="px-3 py-2 w-32">Client</th>
                  <th className="px-3 py-2 w-24">Status</th>
                  <th className="px-3 py-2">Details</th>
                </tr>
              ) : (
                <tr>
                  <th className="px-3 py-2 w-44">Date</th>
                  <th className="px-3 py-2 w-28">Amount</th>
                  <th className="px-3 py-2 w-20">Days</th>
                  <th className="px-3 py-2 w-20">Rate</th>
                  <th className="px-3 py-2">Firearm</th>
                  <th className="px-3 py-2 w-40">Client</th>
                </tr>
              )}
            </thead>
            <tbody>
              {logs.length === 0 ? (
                <tr className="border-b border-stone-700/30">
                  <td colSpan={
                    activeLogType === 'application' ? 4 :
                    activeLogType === 'audit' ? 5 :
                    activeLogType === 'pipedrive' ? 5 : 6
                  } className="px-3 py-4 text-center text-stone-400">
                    {isLoading ? (
                      <div className="flex items-center justify-center gap-2">
                        <RefreshCw size={16} className="animate-spin" />
                        <span>Loading logs...</span>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center gap-2">
                        <FileText size={24} className="text-stone-500" />
                        <span>No {activeLogType} logs found</span>
                        <span className="text-xs text-stone-500 max-w-md text-center">
                          {activeLogType === 'application' ?
                            'Application logs record events from the desktop application.' :
                           activeLogType === 'audit' ?
                            'Audit logs record database changes and system operations.' :
                           activeLogType === 'payment' ?
                            'Payment logs record loan payment transactions.' :
                           activeLogType === 'credit' ?
                            'Credit logs record wallet credit transactions.' :
                           activeLogType === 'pipedrive' ?
                            'Pipedrive logs record actions performed in the Pipedrive CRM module.' :
                            'Storage logs record firearm storage charge transactions.'
                          }
                        </span>
                      </div>
                    )}
                  </td>
                </tr>
              ) : (
                activeLogType === 'application' ? (
                  virtualizedLogs.items.map((log: any) => (
                    <tr key={log.id} className="border-b border-stone-700/30 hover:bg-stone-700/20">
                      <td className="px-3 py-2 text-stone-300">
                        {formatTimestamp(log.timestamp)}
                      </td>
                      <td className="px-3 py-2">
                        <span className={`font-medium ${getLevelColorClass(log.level)}`}>
                          {log.level?.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-stone-300">{log.source || '-'}</td>
                      <td className="px-3 py-2 text-white message-cell">
                        <div>{log.message}</div>
                        {log.details && (
                          <div className="text-xs text-stone-400 mt-1">{log.details}</div>
                        )}
                      </td>
                    </tr>
                  ))
                ) : activeLogType === 'audit' ? (
                  virtualizedLogs.items.map((log: any) => (
                    <tr key={log.id} className="border-b border-stone-700/30 hover:bg-stone-700/20">
                      <td className="px-3 py-2 text-stone-300">
                        {formatTimestamp(log.timestamp)}
                      </td>
                      <td className="px-3 py-2">
                        <span className={`font-medium ${getActionColorClass(log.action)}`}>
                          {log.action?.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-stone-300">{log.table_name}</td>
                      <td className="px-3 py-2 text-stone-300">{log.role || '-'}</td>
                      <td className="px-3 py-2 text-white details-cell">
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2 mb-1">
                            <button
                              onClick={() => toggleLogExpanded(log.id)}
                              className="text-stone-400 hover:text-white transition-colors"
                            >
                              {expandedLogs[log.id] || (expandedLogs[log.id] === undefined && shouldAutoExpand(log)) ?
                                <ChevronDown size={16} /> :
                                <ChevronRight size={16} />
                              }
                            </button>

                            <span className="text-xs text-stone-400">
                              {log.old_data && log.new_data ?
                                `${Object.keys(log.old_data || {}).length + Object.keys(log.new_data || {}).length} fields` :
                                'No changes'
                              }
                            </span>

                            {(log.old_data || log.new_data) && (
                              <div className="flex gap-1 ml-auto">
                                {log.old_data && (
                                  <button
                                    onClick={() => copyToClipboard(log.old_data)}
                                    className="text-stone-400 hover:text-white transition-colors flex items-center gap-1 text-xs"
                                    title="Copy old data to clipboard"
                                  >
                                    <Copy size={12} />
                                    <span>Old</span>
                                  </button>
                                )}

                                {log.new_data && (
                                  <button
                                    onClick={() => copyToClipboard(log.new_data)}
                                    className="text-stone-400 hover:text-white transition-colors flex items-center gap-1 text-xs"
                                    title="Copy new data to clipboard"
                                  >
                                    <Copy size={12} />
                                    <span>New</span>
                                  </button>
                                )}
                              </div>
                            )}
                          </div>

                          {(expandedLogs[log.id] || (expandedLogs[log.id] === undefined && shouldAutoExpand(log))) && (
                            <div className="max-h-60 overflow-y-auto pr-2 custom-scrollbar">
                              {formatJsonDiff(log.old_data, log.new_data)}
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : activeLogType === 'payment' ? (
                  virtualizedLogs.items.map((log: any) => (
                    <tr key={log.id} className="border-b border-stone-700/30 hover:bg-stone-700/20">
                      <td className="px-3 py-2 text-stone-300">
                        {formatTimestamp(log.payment_date || log.created_at)}
                      </td>
                      <td className="px-3 py-2">
                        <span className={`font-medium ${getTransactionColorClass('', log.payment_amount)}`}>
                          R{Math.abs(log.payment_amount).toFixed(2)}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        {log.payment_type || '-'}
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        {log.payment_status || '-'}
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        {log.transaction_id || '-'}
                      </td>
                      <td className="px-3 py-2 text-white">
                        <div>
                          {log.loans?.clients?.first_name} {log.loans?.clients?.last_name}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : activeLogType === 'credit' ? (
                  virtualizedLogs.items.map((log: any) => (
                    <tr key={log.id} className="border-b border-stone-700/30 hover:bg-stone-700/20">
                      <td className="px-3 py-2 text-stone-300">
                        {formatTimestamp(log.transaction_date || log.created_at)}
                      </td>
                      <td className="px-3 py-2">
                        <span className={`font-medium ${getTransactionColorClass('', log.amount)}`}>
                          R{Math.abs(log.amount).toFixed(2)}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        {log.transaction_type || '-'}
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        {log.reference_type || '-'}
                      </td>
                      <td className="px-3 py-2 text-white message-cell">
                        <div>{log.description || '-'}</div>
                      </td>
                      <td className="px-3 py-2 text-white">
                        <div>
                          {log.client_credit_wallets?.clients?.first_name} {log.client_credit_wallets?.clients?.last_name}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : activeLogType === 'pipedrive' ? (
                  virtualizedLogs.items.map((log: any) => (
                    <tr key={log.id} className="border-b border-stone-700/30 hover:bg-stone-700/20">
                      <td className="px-3 py-2 text-stone-300">
                        {formatTimestamp(log.created_at)}
                      </td>
                      <td className="px-3 py-2">
                        <span className={`font-medium ${
                          log.action_type === 'create' ? 'text-green-500' :
                          log.action_type === 'update' ? 'text-blue-500' :
                          log.action_type === 'delete' ? 'text-red-500' :
                          log.action_type === 'move' ? 'text-amber-500' :
                          log.action_type === 'upload' ? 'text-purple-500' :
                          log.action_type === 'download' ? 'text-cyan-500' :
                          'text-stone-300'
                        }`}>
                          {log.action_type?.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        <div className="flex items-center gap-1">
                          {log.entity_type === 'pipeline' ? <GitBranch size={14} /> :
                           log.entity_type === 'stage' ? <ListOrdered size={14} /> :
                           log.entity_type === 'deal' ? <Briefcase size={14} /> :
                           log.entity_type === 'document' ? <FileText size={14} /> :
                           <Circle size={14} />}
                          <span>{log.entity_type?.charAt(0).toUpperCase() + log.entity_type?.slice(1) || '-'}</span>
                        </div>
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        {log.user_name ? (
                          <div className="flex items-center gap-1">
                            <User size={14} />
                            <span>{log.user_name}</span>
                          </div>
                        ) : log.user_id ? (
                          <span className="text-xs">{log.user_id.substring(0, 8)}</span>
                        ) : '-'}
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        {log.client_name ? (
                          <div className="flex items-center gap-1">
                            <Users size={14} />
                            <span>{log.client_name}</span>
                          </div>
                        ) : log.client_id ? (
                          <span className="text-xs">{log.client_id.substring(0, 8)}</span>
                        ) : '-'}
                      </td>
                      <td className="px-3 py-2">
                        <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                          log.status === 'success' ? 'bg-green-900/30 text-green-400' :
                          log.status === 'error' ? 'bg-red-900/30 text-red-400' :
                          log.status === 'warning' ? 'bg-amber-900/30 text-amber-400' :
                          'bg-stone-700/30 text-stone-400'
                        }`}>
                          {log.status || 'unknown'}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-white details-cell">
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2 mb-1">
                            <button
                              onClick={() => toggleLogExpanded(log.id)}
                              className="text-stone-400 hover:text-white transition-colors"
                            >
                              {expandedLogs[log.id] ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                            </button>

                            <span className="text-xs text-stone-400">
                              {log.details ? 'View details' : 'No details'}
                            </span>

                            {log.details && (
                              <button
                                onClick={() => {
                                  // Handle both string and object formats
                                  copyToClipboard(log.details);
                                }}
                                className="text-stone-400 hover:text-white transition-colors flex items-center gap-1 text-xs ml-auto"
                                title="Copy details to clipboard"
                              >
                                <Copy size={12} />
                                <span>Copy</span>
                              </button>
                            )}
                          </div>

                          {expandedLogs[log.id] && (
                            <div className="max-h-60 overflow-y-auto pr-2 custom-scrollbar">
                              {log.details && (
                                <div className="mb-2">
                                  <h4 className="text-xs font-medium text-stone-300 mb-1">Details:</h4>
                                  <pre className="text-xs whitespace-pre-wrap text-stone-300 bg-stone-800/50 p-2 rounded">
                                    {(() => {
                                      try {
                                        // Parse details if it's a string
                                        let details = log.details;
                                        if (typeof details === 'string') {
                                          details = JSON.parse(details);
                                        }
                                        return JSON.stringify(details, null, 2);
                                      } catch (error) {
                                        console.error('Error parsing details:', error);
                                        return String(log.details);
                                      }
                                    })()}
                                  </pre>
                                </div>
                              )}

                              {log.related_entities && (
                                <div className="mt-2">
                                  <h4 className="text-xs font-medium text-stone-300 mb-1">Related Entities:</h4>
                                  <ul className="text-xs text-stone-300">
                                    {(() => {
                                      // Parse related_entities if it's a string
                                      let entities = log.related_entities;
                                      try {
                                        if (typeof entities === 'string') {
                                          entities = JSON.parse(entities);
                                        }
                                        // Ensure entities is an array
                                        if (!Array.isArray(entities)) {
                                          console.warn('related_entities is not an array:', entities);
                                          return <li>No valid related entities found</li>;
                                        }

                                        return entities.map((entity: any, index: number) => (
                                          <li key={index} className="flex items-center gap-1 mb-1">
                                            {entity.type === 'pipeline' ? <GitBranch size={12} /> :
                                             entity.type === 'stage' ? <ListOrdered size={12} /> :
                                             entity.type === 'deal' ? <Briefcase size={12} /> :
                                             entity.type === 'document' ? <FileText size={12} /> :
                                             <Circle size={12} />}
                                            <span className="font-medium">{entity.type}:</span>
                                            <span>{entity.name || (entity.id && entity.id.substring(0, 8)) || 'Unknown'}</span>
                                          </li>
                                        ));
                                      } catch (error) {
                                        console.error('Error parsing related_entities:', error);
                                        return <li>Error parsing related entities</li>;
                                      }
                                    })()}
                                  </ul>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  virtualizedLogs.items.map((log: any) => (
                    <tr key={log.id} className="border-b border-stone-700/30 hover:bg-stone-700/20">
                      <td className="px-3 py-2 text-stone-300">
                        {formatTimestamp(log.transaction_date || log.created_at)}
                      </td>
                      <td className="px-3 py-2">
                        <span className={`font-medium ${getTransactionColorClass('', log.amount)}`}>
                          R{Math.abs(log.amount).toFixed(2)}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        {log.days_charged || '-'}
                      </td>
                      <td className="px-3 py-2 text-stone-300">
                        R{log.daily_rate?.toFixed(2) || '-'}
                      </td>
                      <td className="px-3 py-2 text-white message-cell">
                        <div>
                          {log.firearms?.make} {log.firearms?.model} ({log.firearms?.serial})
                        </div>
                      </td>
                      <td className="px-3 py-2 text-white">
                        <div>
                          {log.clients?.first_name} {log.clients?.last_name}
                        </div>
                      </td>
                    </tr>
                  ))
                )
              )}
            </tbody>
          </table>

          {/* Loading indicator at the bottom of the table */}
          {virtualizedLogs.isLoading && (
            <div className="py-2 text-center">
              <div className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500"></div>
              <span className="ml-2 text-sm text-stone-400">Loading more logs...</span>
            </div>
          )}

          {/* Load more button */}
          {virtualizedLogs.total > virtualizedLogs.items.length && !virtualizedLogs.isLoading && (
            <div className="text-center py-2">
              <button
                className="text-orange-500 hover:text-orange-400 text-sm"
                onClick={virtualizedLogs.handleLoadMore}
              >
                Load more logs ({virtualizedLogs.total - virtualizedLogs.items.length} remaining)
              </button>
            </div>
          )}
        </div>
      </div>
      )}

      {/* Pagination info */}
      {logs.length > 0 && (
        <div className="flex items-center justify-between text-sm">
          <div className="text-stone-400">
            Showing {virtualizedLogs.items.length} of {totalLogs} logs
          </div>
          <div className="flex items-center gap-2">
            <select
              value={pageSize}
              onChange={(e) => {
                setPageSize(Number(e.target.value))
                setPage(1)
              }}
              className="bg-stone-700 border border-stone-600 rounded-md text-white text-sm px-2 py-1"
            >
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
              <option value={100}>100 per page</option>
            </select>
          </div>
        </div>
      )}
    </div>
  )
}

export default LogsSettings
