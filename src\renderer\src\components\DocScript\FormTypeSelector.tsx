import React from 'react'
import { useWindowSize } from '../../contexts/WindowSizeContext'

interface FormTypeSelectorProps {
  formType: 'select' | 'competency' | 'licence' | 'competency-select' | 'licence-select' | 'miscellaneous-select' | 'miscellaneous' | null
  setFormType: React.Dispatch<
    React.SetStateAction<'select' | 'competency' | 'licence' | 'competency-select' | 'licence-select' | 'miscellaneous-select' | 'miscellaneous' | null>
  >
}

const FormTypeSelector: React.FC<FormTypeSelectorProps> = ({ formType, setFormType }) => {
  const { calculateResponsiveHeight } = useWindowSize()

  if (formType !== 'select') return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div
        className="bg-stone-800/90 backdrop-blur-sm rounded-2xl p-8 border border-stone-700/30 shadow-2xl w-full max-w-2xl relative"
        style={{ maxHeight: `${calculateResponsiveHeight(90)}px` }}
      >
        <button
          onClick={() => setFormType(null)}
          className="absolute top-4 right-4 text-stone-400 hover:text-white transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
        <h2 className="text-2xl font-bold text-white mb-6">Select Document Type</h2>
        <div className="grid grid-cols-1 gap-4">
          <button
            onClick={() => setFormType('competency-select')}
            className="p-6 bg-stone-700/50 rounded-lg text-left hover:bg-stone-600/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Competency</h3>
            <p className="text-sm text-stone-400 mt-1">
              For standard competency certificates
            </p>
          </button>
          <button
            onClick={() => setFormType('licence-select')}
            className="p-6 bg-stone-700/50 rounded-lg text-left hover:bg-stone-600/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Licence</h3>
            <p className="text-sm text-stone-400 mt-1">
              For firearm licences with additional details
            </p>
          </button>
          <button
            onClick={() => setFormType('miscellaneous-select')}
            className="p-6 bg-stone-700/50 rounded-lg text-left hover:bg-stone-600/50 transition-colors"
          >
            <h3 className="text-lg font-medium text-white">Miscellaneous</h3>
            <p className="text-sm text-stone-400 mt-1">
              For other miscellaneous document types
            </p>
          </button>
        </div>
      </div>
    </div>
  )
}

export default FormTypeSelector
