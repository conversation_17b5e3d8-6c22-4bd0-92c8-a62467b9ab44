import React, { useState } from 'react'
import { Loan } from '../../../types'
import { DashboardIcons } from '../../icons/DashboardIcons'
import { Dialog } from '../../../components/Dialog'
// Using native input instead of custom Input component

interface LoanCardActionsProps {
  loan: Loan
  activeSections: {
    client: boolean
    payments: boolean
    details: boolean
  }
  toggleSection: (section: 'client' | 'payments' | 'details') => void
  onAddPayment: (loan: Loan) => void
  onAddLicense: (clientId: string, loanId: string) => void
  handleDelete: () => void
  toggleNotificationsPause: (confirmed: boolean) => void
  isTogglingNotifications: boolean
  onCancelLoan: (loanId: string) => Promise<boolean>
  onUpdateInvoiceNumber: (loanId: string, invoiceNumber: string) => Promise<{ success: boolean; error?: string }>
}

const LoanCardActions: React.FC<LoanCardActionsProps> = ({
  loan,
  activeSections,
  toggleSection,
  onAddPayment,
  onAddLicense,
  handleDelete,
  toggleNotificationsPause,
  isTogglingNotifications,
  onCancelLoan,
  onUpdateInvoiceNumber
}) => {
  const [showCancelDialog, setShowCancelDialog] = useState(false)
  const [showInvoiceDialog, setShowInvoiceDialog] = useState(false)
  const [invoiceNumber, setInvoiceNumber] = useState('')
  const [isUpdatingInvoice, setIsUpdatingInvoice] = useState(false)
  const [invoiceError, setInvoiceError] = useState('')
  const [isCancelling, setIsCancelling] = useState(false)

  const handleCancelLoan = async (): Promise<void> => {
    try {
      setIsCancelling(true)
      const success = await onCancelLoan(loan.id)
      if (success) {
        setShowCancelDialog(false)
        // The parent component will refresh the data when the loan is cancelled
      }
    } catch (error) {
      console.error('Error cancelling loan:', error)
    } finally {
      setIsCancelling(false)
    }
  }

  const handleCancelDialogClose = (): void => {
    setShowCancelDialog(false)
  }

  const handleInvoiceDialogClose = (): void => {
    setShowInvoiceDialog(false)
    setInvoiceNumber('')
    setInvoiceError('')
  }

  const handleAssignInvoice = async (): Promise<void> => {
    if (!invoiceNumber.trim()) {
      setInvoiceError('Invoice number is required')
      return
    }

    try {
      setIsUpdatingInvoice(true)
      setInvoiceError('')
      
      const { success, error } = await onUpdateInvoiceNumber(loan.id, invoiceNumber.trim())
      
      if (success) {
        setShowInvoiceDialog(false)
        setInvoiceNumber('')
      } else {
        setInvoiceError(error || 'Failed to update invoice number')
      }
    } catch (error) {
      console.error('Error assigning invoice:', error)
      setInvoiceError('An unexpected error occurred')
    } finally {
      setIsUpdatingInvoice(false)
    }
  }

  return (
    <>
      {/* Cancel Loan Confirmation Dialog */}
      <Dialog
        isOpen={showCancelDialog}
        title="Cancel Loan"
        message={
          <div className="space-y-2">
            <p>Are you sure you want to cancel this loan?</p>
            <p className="text-sm text-yellow-500">This action cannot be undone.</p>
          </div>
        }
        onConfirm={handleCancelLoan}
        onCancel={handleCancelDialogClose}
        onClose={handleCancelDialogClose}
        confirmText={isCancelling ? 'Cancelling...' : 'Yes, Cancel Loan'}
        cancelText="No, Keep Active"
        isConfirming={isCancelling}
        confirmButtonVariant="danger"
      />

      {/* Assign Invoice Dialog */}
      <Dialog
        isOpen={showInvoiceDialog}
        title="Assign Invoice Number"
        message={
          <div className="space-y-4">
            <p>Enter the invoice number for this paid loan:</p>
            <input
              type="text"
              value={invoiceNumber}
              onChange={(e) => {
                setInvoiceNumber(e.target.value)
                if (invoiceError) setInvoiceError('')
              }}
              placeholder="e.g., INV-2023-001"
              className={`w-full px-3 py-2 bg-stone-800 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 ${invoiceError ? 'border-red-500' : 'border-stone-600'}`}
            />
            {invoiceError && (
              <p className="text-sm text-red-500">{invoiceError}</p>
            )}
          </div>
        }
        onConfirm={handleAssignInvoice}
        onCancel={handleInvoiceDialogClose}
        onClose={handleInvoiceDialogClose}
        confirmText={isUpdatingInvoice ? 'Saving...' : 'Save Invoice'}
        cancelText="Cancel"
        isConfirming={isUpdatingInvoice}
        confirmButtonVariant="primary"
      />

      <div className="flex justify-between items-center gap-2 flex-wrap mt-4">
        {/* Left-side section with Add Payment and Add License buttons */}
        <div className="flex items-center gap-2">
          {loan.status !== 'paid' && (
            <button
              onClick={() => onAddPayment(loan)}
              className="px-3 py-1.5 rounded-md bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                text-white text-sm font-medium flex items-center gap-1.5 shadow-md shadow-orange-900/20 transition-all duration-200"
            >
              <DashboardIcons.Add className="w-3.5 h-3.5" />
              Payment
            </button>
          )}

          {/* Only show Add Firearm button if no firearm is assigned through any method */}
          {!loan.license_id && !loan.gun_licences && !loan.firearm_id && !loan.assignment_id && !loan.firearm && !loan.assignment && (
            <button
              onClick={() => loan.clients && onAddLicense(loan.clients.id, loan.id)}
              className="px-3 py-1.5 rounded-md bg-stone-700 hover:bg-stone-600 text-white text-sm font-medium flex items-center gap-1.5 transition-all duration-200"
            >
              <DashboardIcons.Add className="w-3.5 h-3.5" />
              Add Firearm
            </button>
          )}

          {/* Notification toggle button - Only show for non-paid loans */}
          {loan.status !== 'paid' && (
            <button
              onClick={() => toggleNotificationsPause(false)}
              disabled={isTogglingNotifications}
              className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center gap-1.5 transition-all duration-200 ${
                loan.pause_notifications
                  ? 'bg-red-500/15 hover:bg-red-500/25 text-red-400 border border-red-500/30'
                  : 'bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 border border-blue-500/30'
              }`}
              title={loan.pause_notifications ? 'Enable notifications' : 'Pause notifications'}
            >
              {isTogglingNotifications ? (
                <DashboardIcons.Spinner className="w-3.5 h-3.5 animate-spin" />
              ) : loan.pause_notifications ? (
                <DashboardIcons.BellOff className="w-3.5 h-3.5" />
              ) : (
                <DashboardIcons.Bell className="w-3.5 h-3.5" />
              )}
              Notifications
            </button>
          )}

          {/* Cancel loan button - only show for active loans */}
          {loan.status === 'active' && (
            <button
              onClick={() => setShowCancelDialog(true)}
              className="px-3 py-1.5 rounded-md text-sm font-medium flex items-center gap-1.5 transition-all duration-200
                       bg-red-500/10 hover:bg-red-500/20 text-red-400 border border-red-500/30"
              title="Cancel loan"
            >
              <DashboardIcons.Close className="w-3.5 h-3.5" />
              Cancel
            </button>
          )}

          {/* Assign Invoice button - only show for paid loans without an invoice */}
          {loan.status === 'paid' && !loan.paid_invoice && (
            <button
              onClick={() => setShowInvoiceDialog(true)}
              className="px-3 py-1.5 rounded-md text-sm font-medium flex items-center gap-1.5 transition-all duration-200
                       bg-green-500/10 hover:bg-green-500/20 text-green-400 border border-green-500/30"
              title="Assign invoice number"
            >
              <DashboardIcons.Document className="w-3.5 h-3.5" />
              Assign Invoice
            </button>
          )}

          {/* Show invoice number if assigned */}
          {loan.status === 'paid' && loan.paid_invoice && (
            <div className="px-3 py-1.5 rounded-md text-sm font-medium flex items-center gap-1.5
                        bg-green-500/10 text-green-400 border border-green-500/30">
              <DashboardIcons.Check className="w-3.5 h-3.5" />
              Invoice: {loan.paid_invoice}
            </div>
          )}
        </div>

        {/* Right-side section with Details and Delete buttons */}
        <div className="flex items-center gap-2">
          {/* Toggle section buttons */}
          <div className="flex items-center gap-1 bg-stone-800/50 rounded-md p-1">
            <button
              onClick={() => toggleSection('client')}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
                activeSections.client
                  ? 'bg-orange-500/20 text-white'
                  : 'bg-stone-800/80 text-stone-400 hover:bg-stone-700/80 hover:text-white'
              }`}
            >
              <DashboardIcons.Edit className="w-3.5 h-3.5" />
              <span>Client</span>
            </button>

            <button
              onClick={() => toggleSection('payments')}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
                activeSections.payments
                  ? 'bg-orange-500/20 text-white'
                  : 'bg-stone-800/80 text-stone-400 hover:bg-stone-700/80 hover:text-white'
              }`}
            >
              <DashboardIcons.Payment className="w-3.5 h-3.5" />
              <span>Payments</span>
            </button>

            <button
              onClick={() => toggleSection('details')}
              className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
                activeSections.details
                  ? 'bg-orange-500/20 text-white'
                  : 'bg-stone-800/80 text-stone-400 hover:bg-stone-700/80 hover:text-white'
              }`}
            >
              <DashboardIcons.Add className="w-3.5 h-3.5" />
              <span>Details</span>
            </button>
          </div>

          {loan.status !== 'paid' && (
            <button
              onClick={handleDelete}
              className="p-1.5 rounded bg-red-500/10 hover:bg-red-500/20 text-red-400 hover:text-red-300 transition-colors"
              title="Delete Loan"
            >
              <DashboardIcons.Delete className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </>
  )
}

export default React.memo(LoanCardActions)
