import React from 'react'
import TemplatePlaceholders from './TemplatePlaceholders'
import { useWindowSize } from '../../contexts/WindowSizeContext'

const TemplateSection: React.FC = () => {
  useWindowSize()

  return (
    <div className="w-full h-full">
      <div className="mb-2">
        <p className="text-xs text-stone-400">
          Complete reference of all available template placeholders to use in your document templates.
        </p>
      </div>

      <div className="h-[calc(100%-30px)] overflow-y-auto custom-scrollbar pr-2">
        <TemplatePlaceholders />
      </div>
    </div>
  )
}

export default TemplateSection
