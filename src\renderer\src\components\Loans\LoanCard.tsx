import React from 'react'
import { useState, useMemo, useEffect, memo } from 'react'
import { Loan, LoanPayment } from '../../types'
import { formatCurrency, formatDate } from '../../utils/formatters'
import { getSupabase, getOrInitSupabase } from '../../lib/supabase'
import { Dialog } from '../Dialog'

import {
  LoanCardHeader,
  LoanCardFinancialOverview,
  LoanCardProgressBar,
  LoanCardPaymentSchedule,
  LoanCardActions,
  LoanCardFirearmInfo,
  LoanCardClientInfo,
  LoanCardPaymentsInfo,
  LoanCardDetailsInfo
} from './components'

interface LoanCardProps {
  loan: Loan
  onAddPayment: (loan: Loan) => void
  onCancelLoan: (loanId: string) => Promise<boolean>
  onDeleteLoan: (loanId: string) => Promise<void>
  onUpdateInvoiceNumber: (loanId: string, invoiceNumber: string) => Promise<{ success: boolean; error?: string }>
  onAddLicense: (clientId: string, loanId: string) => void
  highlightTerms?: string[]
  isFocused?: boolean
  isOtherCardFocused?: boolean
  onFocusToggle: (loanId: string | null) => void
}

function LoanCard({
  loan,
  onAddPayment,
  onDeleteLoan,
  onCancelLoan,
  onAddLicense,
  onUpdateInvoiceNumber,
  highlightTerms = [],
  isFocused = false,
  isOtherCardFocused = false,
  onFocusToggle = () => {}
}: LoanCardProps): React.JSX.Element {
  // State management
  const [activeSections, setActiveSections] = useState({
    client: true,
    payments: false,
    details: false
  })
  const [dialogState, setDialogState] = useState({ isOpen: false, loanId: '' })
  const [payments, setPayments] = useState<LoanPayment[]>([])
  const [notificationDialogState, setNotificationDialogState] = useState({
    isOpen: false,
    disable: false
  })
  
  // Local state for loading payments and operations
  const [isTogglingNotifications, setIsTogglingNotifications] = useState(false)
  const [loadingPayments, setLoadingPayments] = useState(false)

  // State for storing firearm and assignment information
  const [firearmInfo, setFirearmInfo] = useState<any>(null)
  const [assignmentInfo, setAssignmentInfo] = useState<any>(null)

  // Fetch payment history for this loan
  useEffect(() => {
    const fetchPayments = async () => {
      if (!loan.id) return

      setLoadingPayments(true)
      try {
        const { data, error } = await getSupabase()
          .from('loan_payments')
          .select('*')
          .eq('loan_id', loan.id)
          .order('payment_date', { ascending: true })

        if (error) throw error
        setPayments(data || [])
      } catch (error) {
        console.error('Error fetching payments:', error)
      } finally {
        setLoadingPayments(false)
      }
    }

    fetchPayments()
  }, [loan.id])

  // Use firearm and assignment information from the loan object directly
  useEffect(() => {
    // Check if we have firearm and assignment information directly in the loan object
    if (loan.firearm) {
      // Using firearm info from loan object
      setFirearmInfo(loan.firearm)
    }

    if (loan.assignment) {
      // Using assignment info from loan object
      setAssignmentInfo(loan.assignment)
    }

    // If we don't have the information directly in the loan object, fetch it
    const fetchFirearmInfo = async () => {
      // Fetching firearm info for loan
      // If we have firearm_id and assignment_id but no firearm or assignment objects
      if (loan.firearm_id && loan.assignment_id && !loan.firearm && !loan.assignment) {
        try {
          // Get the assignment with firearm and client details
          const { data: assignmentData } = await getSupabase()
            .from('firearm_assignments')
            .select(`
              *,
              firearms:firearm_id(*),
              clients:client_id(id, first_name, last_name, email, phone, id_number)
            `)
            .eq('id', loan.assignment_id)
            .maybeSingle()

          if (assignmentData?.firearms) {
            setFirearmInfo(assignmentData.firearms)
            setAssignmentInfo(assignmentData)
            return
          }
        } catch (error) {
          // Error handling for fetching assignment by ID
        }
      }

      // If we have just the firearm_id but no assignment_id and no firearm object
      if (loan.firearm_id && !loan.assignment_id && !loan.firearm) {
        try {
          // First try to get the firearm from the firearm_assignments table with client details
          const { data: assignmentData } = await getSupabase()
            .from('firearm_assignments')
            .select(`
              *,
              firearms:firearm_id(*),
              clients:client_id(id, first_name, last_name, email, phone, id_number)
            `)
            .eq('firearm_id', loan.firearm_id)
            .is('return_date', null)
            .maybeSingle()

          if (assignmentData?.firearms) {
            setFirearmInfo(assignmentData.firearms)
            setAssignmentInfo(assignmentData)
            return
          }

          // If not found in active assignments, check if there's any assignment (including returned)
          const { data: pastAssignmentData } = await getSupabase()
            .from('firearm_assignments')
            .select(`
              *,
              firearms:firearm_id(*),
              clients:client_id(id, first_name, last_name, email, phone, id_number)
            `)
            .eq('firearm_id', loan.firearm_id)
            .order('assigned_date', { ascending: false })
            .limit(1)
            .maybeSingle()

          if (pastAssignmentData?.firearms) {
            setFirearmInfo(pastAssignmentData.firearms)
            setAssignmentInfo(pastAssignmentData)
            return
          }

          // If not found in assignments, try the firearms table directly
          const { data, error } = await getSupabase()
            .from('firearms')
            .select('*')
            .eq('id', loan.firearm_id)
            .maybeSingle()

          if (error) {
            return
          }

          if (data) {
            setFirearmInfo(data)
            // No assignment info in this case
            setAssignmentInfo(null)
          }
        } catch (error) {
          // Error handling for fetching firearm info
        }
      }

      // Legacy support: Check if firearm ID is stored in notes
      if (!loan.firearm_id && !loan.firearm && loan.notes) {
        const match = loan.notes.match(/Firearm ID: ([a-f0-9-]+)/i)
        if (!match || !match[1]) return

        const firearmId = match[1]

        try {
          // First try to get the firearm from the firearm_assignments table with client details
          const { data: assignmentData } = await getSupabase()
            .from('firearm_assignments')
            .select(`
              *,
              firearms:firearm_id(*),
              clients:client_id(id, first_name, last_name, email, phone, id_number)
            `)
            .eq('firearm_id', firearmId)
            .is('return_date', null)
            .maybeSingle()

          if (assignmentData?.firearms) {
            setFirearmInfo(assignmentData.firearms)
            setAssignmentInfo(assignmentData)

            // Update the loan record to use the proper columns
            try {
              await getSupabase()
                .from('loans')
                .update({
                  firearm_id: firearmId,
                  assignment_id: assignmentData.id,
                  notes: 'FIREARM ASSIGNED (Updated from legacy format)'
                })
                .eq('id', loan.id)
            } catch (updateError) {
              // Error handling for updating loan with proper firearm_id and assignment_id
            }

            return
          }

          // If not found in active assignments, check if there's any assignment (including returned)
          const { data: pastAssignmentData } = await getSupabase()
            .from('firearm_assignments')
            .select(`
              *,
              firearms:firearm_id(*),
              clients:client_id(id, first_name, last_name, email, phone, id_number)
            `)
            .eq('firearm_id', firearmId)
            .order('assigned_date', { ascending: false })
            .limit(1)
            .maybeSingle()

          if (pastAssignmentData?.firearms) {
            setFirearmInfo(pastAssignmentData.firearms)
            setAssignmentInfo(pastAssignmentData)

            // Update the loan record to use the proper columns
            try {
              await getSupabase()
                .from('loans')
                .update({
                  firearm_id: firearmId,
                  assignment_id: pastAssignmentData.id,
                  notes: 'FIREARM ASSIGNED (Updated from legacy format)'
                })
                .eq('id', loan.id)
            } catch (updateError) {
              // Error handling for updating loan with proper firearm_id and assignment_id
            }

            return
          }

          // If not found in storage, try the firearms table directly
          const { data, error } = await getSupabase()
            .from('firearms')
            .select('*')
            .eq('id', firearmId)
            .maybeSingle()

          if (error) {
            return
          }

          if (data) {
            setFirearmInfo(data)
            // No assignment info in this case
            setAssignmentInfo(null)

            // Update the loan record to use the proper columns
            try {
              await getSupabase()
                .from('loans')
                .update({
                  firearm_id: firearmId,
                  notes: 'FIREARM ASSIGNED (Updated from legacy format)'
                })
                .eq('id', loan.id)
            } catch (updateError) {
              // Error handling for updating loan with proper firearm_id
            }
          }
        } catch (error) {
          // Error handling for fetching firearm info from notes
        }
      }
    }

    // Only fetch if we don't have the information directly in the loan object
    if (!loan.firearm && !loan.assignment) {
      fetchFirearmInfo()
    }
  }, [loan])

  // Event handlers
  const handleDelete = (): void => {
    setDialogState({ isOpen: true, loanId: loan.id })
  }

  const handleConfirmDelete = (): void => {
    onDeleteLoan(dialogState.loanId)
    setDialogState({ isOpen: false, loanId: '' })
  }

  const toggleSection = (section: 'client' | 'payments' | 'details') => {
    setActiveSections((prev) => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  // Toggle focus mode
  const handleFocusToggle = () => {
    onFocusToggle(isFocused ? null : loan.id)
  }

  // Utility functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400'
      case 'paid':
        return 'bg-blue-500/20 text-blue-400'
      case 'overdue':
        return 'bg-orange-500/20 text-orange-400'
      case 'defaulted':
        return 'bg-red-500/20 text-red-400'
      case 'cancelled':
        return 'bg-red-500/20 text-red-400 line-through'
      default:
        return 'bg-gray-500/20 text-gray-400'
    }
  }

  const calculateProgress = (): number => {
    if (loan.weapon_cost <= 0) return 100
    const paid = loan.weapon_cost - loan.remaining_balance
    return Math.round(Math.min(100, Math.max(0, (paid / loan.weapon_cost) * 100)))
  }

  // Highlight text that matches search terms
  const highlightMatch = (text: string): JSX.Element | string => {
    if (!highlightTerms || highlightTerms.length === 0 || !text) return text || ''

    // Escape special regex characters and filter out empty terms
    const terms = highlightTerms
      .filter((term) => term.trim().length > 0)
      .map((term) => term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))

    if (terms.length === 0) return text

    // Create regex for all terms with case-insensitive matching
    const regex = new RegExp(`(${terms.join('|')})`, 'gi')

    // Split the text by matches
    const parts = text.split(regex)

    if (parts.length <= 1) return text

    return (
      <>
        {parts.map((part, i) => {
          // Check if this part matches any of our search terms (case insensitive)
          const isMatch = terms.some((term) => new RegExp(`^${term}$`, 'i').test(part))

          return isMatch ? (
            <span key={i} className="bg-orange-500/30 text-white font-medium rounded px-0.5">
              {part}
            </span>
          ) : (
            part
          )
        })}
      </>
    )
  }

  // Derived values
  const statusClass = getStatusColor(loan.status)
  const clientName = loan.clients
    ? `${loan.clients.first_name} ${loan.clients.last_name}`.toUpperCase()
    : 'UNKNOWN CLIENT'

  // Check if this loan matches the search term
  const matchesSearch = useMemo(() => {
    if (!highlightTerms || highlightTerms.length === 0) return false

    // Check client info
    const clients = loan.clients
    if (clients) {
      const firstName = clients.first_name || ''
      const lastName = clients.last_name || ''
      const email = clients.email || ''
      const phone = clients.phone || ''
      const idNumber = clients.id_number || ''

      if (
        highlightTerms.some((term) => firstName.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => lastName.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => email.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => phone.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => idNumber.toLowerCase().includes(term.toLowerCase()))
      ) {
        return true
      }
    }

    // Check firearm info
    if (firearmInfo) {
      const make = firearmInfo.make || ''
      const model = firearmInfo.model || ''
      const serial = firearmInfo.serial || ''
      const stockNumber = firearmInfo.stock_number || ''

      if (
        highlightTerms.some((term) => make.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => model.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => serial.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => stockNumber.toLowerCase().includes(term.toLowerCase()))
      ) {
        return true
      }
    }

    // Check assignment info
    if (assignmentInfo && assignmentInfo.clients) {
      const clientFirstName = assignmentInfo.clients.first_name || ''
      const clientLastName = assignmentInfo.clients.last_name || ''
      const clientEmail = assignmentInfo.clients.email || ''
      const clientPhone = assignmentInfo.clients.phone || ''
      const clientIdNumber = assignmentInfo.clients.id_number || ''
      const assignmentNotes = assignmentInfo.notes || ''

      if (
        highlightTerms.some((term) => clientFirstName.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => clientLastName.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => clientEmail.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => clientPhone.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => clientIdNumber.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => assignmentNotes.toLowerCase().includes(term.toLowerCase()))
      ) {
        return true
      }
    }

    // Check gun license info (legacy support)
    const gunLicense = loan.gun_licences
    if (gunLicense) {
      const make = gunLicense.make || ''
      const serialNumber = gunLicense.serial_number || ''
      const caliber = gunLicense.caliber || ''
      const stockCode = gunLicense.stock_code || ''

      if (
        highlightTerms.some((term) => make.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => serialNumber.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => caliber.toLowerCase().includes(term.toLowerCase())) ||
        highlightTerms.some((term) => stockCode.toLowerCase().includes(term.toLowerCase()))
      ) {
        return true
      }
    }

    // Check loan info
    return (
      loan.invoice_number &&
      highlightTerms.some((term) => loan.invoice_number.toLowerCase().includes(term.toLowerCase()))
    )
  }, [loan, highlightTerms, firearmInfo, assignmentInfo])

  // Loan term info calculation
  const getLoanTermInfo = () => {
    const totalMonths = loan.loan_term || 12 // Default to 12 if not specified

    if (!loan.start_date) {
      return {
        totalMonths,
        elapsedMonths: 0,
        remainingMonths: totalMonths,
        formattedTerm: `0/${totalMonths} months`
      }
    }

    const startDate = new Date(loan.start_date)
    const currentDate = new Date()

    // Calculate months elapsed since loan started
    const elapsedMonths =
      (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
      (currentDate.getMonth() - startDate.getMonth())

    // Calculate remaining months (ensure it's not negative)
    const remainingMonths = Math.max(0, totalMonths - elapsedMonths)

    // Format the term string
    const formattedTerm = `${Math.min(elapsedMonths, totalMonths)}/${totalMonths} months`

    return {
      totalMonths,
      elapsedMonths,
      remainingMonths,
      formattedTerm
    }
  }

  // Check if we're still in the month the loan was created (grace period)
  const isCreationMonth = () => {
    if (!loan.start_date) return false

    const startDate = new Date(loan.start_date)
    const now = new Date()

    return startDate.getMonth() === now.getMonth() && startDate.getFullYear() === now.getFullYear()
  }

  // Calculate days active since loan start date
  const getDaysActive = () => {
    if (!loan.start_date) return 0

    const startDate = new Date(loan.start_date)

    // Reset time part to get accurate day calculation
    startDate.setHours(0, 0, 0, 0)
    const nowDateOnly = new Date()
    nowDateOnly.setHours(0, 0, 0, 0)

    // Calculate difference in milliseconds and convert to days
    const differenceMs = nowDateOnly.getTime() - startDate.getTime()
    return Math.floor(differenceMs / (1000 * 60 * 60 * 24))
  }

  // Calculate monthly payment amount
  const getMonthlyPaymentAmount = () => {
    if (loan.status === 'paid' || loan.remaining_balance <= 0) {
      return 0
    }

    const remainingMonths = getLoanTermInfo().remainingMonths

    // Handle edge case: if no remaining months but balance exists
    if (remainingMonths <= 0 && loan.remaining_balance > 0) {
      return loan.remaining_balance
    }

    // Calculate monthly payment by dividing remaining balance by remaining months
    return Math.ceil(loan.remaining_balance / Math.max(1, remainingMonths))
  }

  // Get current month payment status
  const getCurrentMonthPaymentStatus = () => {
    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()

    // Filter payments made in the current month
    const currentMonthPayments = payments.filter((payment) => {
      const paymentDate = new Date(payment.payment_date)
      return paymentDate.getMonth() === currentMonth && paymentDate.getFullYear() === currentYear
    })

    // Sum up all payments made this month
    const paidThisMonth = currentMonthPayments.reduce((total, payment) => total + payment.amount, 0)

    const monthlyRequired = getMonthlyPaymentAmount()
    const remaining = monthlyRequired - paidThisMonth
    const isOverpaid = remaining < 0
    const progress = monthlyRequired > 0 ? Math.min(100, (paidThisMonth / monthlyRequired) * 100) : 100
    const isPaidInFull = monthlyRequired > 0 ? paidThisMonth >= monthlyRequired : true

    return {
      paid: paidThisMonth,
      required: monthlyRequired,
      remaining: Math.abs(remaining),
      isOverpaid,
      progress,
      isPaidInFull
    }
  }

  // Check if this loan has insufficient payments in past months
  const hasInsufficientPastPayments = () => {
    if (!loan.start_date || isCreationMonth()) return false

    // Check if payment is past due (more than 7 days after due date)
    const currentDate = new Date()
    if (loan.payment_due_date) {
      const dueDate = new Date(loan.payment_due_date)
      const sevenDaysAfterDue = new Date(dueDate)
      sevenDaysAfterDue.setDate(dueDate.getDate() + 7)

      if (currentDate > sevenDaysAfterDue) {
        return true
      }
    } else if (loan.next_payment_date) {
      const dueDate = new Date(loan.next_payment_date)
      const sevenDaysAfterDue = new Date(dueDate)
      sevenDaysAfterDue.setDate(dueDate.getDate() + 7)

      if (currentDate > sevenDaysAfterDue) {
        return true
      }
    }

    return false
  }

  // Get payment months
  const getPaymentMonths = () => {
    if (!loan.start_date) return [] as Date[]

    const startDate = new Date(loan.start_date)
    const currentDate = new Date()

    // Create array of months from start date to current date
    const months: Date[] = []
    let currentMonth = new Date(startDate)

    // Set to first day of month for consistent comparison
    currentMonth.setDate(1)

    // Create a date for the end of the current month
    const endOfCurrentMonth = new Date(currentDate)
    endOfCurrentMonth.setMonth(endOfCurrentMonth.getMonth() + 1)
    endOfCurrentMonth.setDate(0)

    while (currentMonth <= endOfCurrentMonth) {
      months.push(new Date(currentMonth))
      currentMonth.setMonth(currentMonth.getMonth() + 1)
    }

    return months
  }

  // Get next payment amount
  const getNextPaymentAmount = () => {
    if (loan.status === 'paid' || loan.remaining_balance <= 0) {
      return { amount: 0, dueDate: null as Date | null }
    }

    // Use payment_due_date if available, fallback to next_payment_date, or calculate it
    let dueDate: Date

    if (loan.payment_due_date) {
      dueDate = new Date(loan.payment_due_date)
    } else if (loan.next_payment_date) {
      dueDate = new Date(loan.next_payment_date)
    } else {
      const currentDate = new Date()
      dueDate = new Date()

      if (isCreationMonth()) {
        dueDate.setMonth(currentDate.getMonth() + 1)
        dueDate.setDate(28)
      } else {
        if (currentDate.getDate() > 28) {
          dueDate.setMonth(currentDate.getMonth() + 1)
        }
        dueDate.setDate(28)
      }
    }

    // Check if we're in the grace period (no payment due yet)
    if (isCreationMonth()) {
      return { amount: 0, dueDate }
    }

    // Get current month payment status
    const currentMonthStatus = getCurrentMonthPaymentStatus()

    // Calculate amount due for current month (if not paid in full)
    const currentMonthDue = currentMonthStatus.isPaidInFull ? 0 : currentMonthStatus.remaining

    // Add penalties if any
    const penalties = loan.penalties || 0

    // Calculate total amount due (monthly payment + penalties)
    let totalAmount = currentMonthDue + penalties

    // If this is the final payment, ensure it covers the remaining balance
    const remainingMonths = getLoanTermInfo().remainingMonths
    if (remainingMonths <= 1 || remainingMonths <= 0) {
      totalAmount = Math.max(totalAmount, loan.remaining_balance)
    }

    return {
      amount: totalAmount,
      dueDate
    }
  }

  // Get missed months details
  const getMissedMonthsDetails = () => {
    if (!loan.start_date || isCreationMonth()) return []

    // Simple implementation for now
    return []
  }

  // Get payment for a specific month
  const getPaymentForMonth = (month: Date): LoanPayment | undefined => {
    return payments.find((p) => {
      const paymentDate = new Date(p.payment_date)
      return (
        paymentDate.getFullYear() === month.getFullYear() &&
        paymentDate.getMonth() === month.getMonth()
      )
    })
  }

  // Check if a month is paid in full
  const getMonthStatus = (month: Date) => {
    // Check if there's a payment for this month
    const payment = getPaymentForMonth(month)
    const monthlyAmount = getMonthlyPaymentAmount()

    // Current month
    const now = new Date()
    const isCurrentMonth =
      month.getFullYear() === now.getFullYear() && month.getMonth() === now.getMonth()

    // Start month
    const startDate = new Date(loan.start_date)
    const isStartMonth =
      month.getFullYear() === startDate.getFullYear() && month.getMonth() === startDate.getMonth()

    // Future month
    const isFutureMonth = month > now

    // Past month with no payment
    const isPastDueMonth = month < now && !payment && !isCurrentMonth

    // Start month is a special case - always consider it 'grace'
    if (isStartMonth) return 'start'

    // Only mark as 'paid' if payment exists AND is equal to or greater than the required amount
    if (payment && payment.amount >= monthlyAmount) return 'paid'

    // Partial payment
    if (payment && payment.amount < monthlyAmount) return 'partial'

    if (isCurrentMonth) return 'current'
    if (isFutureMonth) return 'future'
    if (isPastDueMonth) return 'missed'

    return 'unknown'
  }

  // Function to handle notification toggle with confirmation
  const handleNotificationToggle = async (confirmed = false): Promise<void> => {
    // If not confirmed and trying to disable notifications, show confirmation dialog
    if (!confirmed && !loan.pause_notifications) {
      setNotificationDialogState({ isOpen: true, disable: true })
      return
    }

    if (isTogglingNotifications) return

    setIsTogglingNotifications(true)
    try {
      const newStatus = !(loan.pause_notifications || false)
      const supabase = await getOrInitSupabase()
      
      const { error } = await supabase
        .from('loans')
        .update({ pause_notifications: newStatus })
        .eq('id', loan.id)

      if (error) {
        // Check if error is related to missing column
        if (error.code === 'PGRST204' && error.message?.includes("'pause_notifications' column")) {
          alert(
            'Database setup required:\n\n' +
              "The 'pause_notifications' column needs to be added to the 'loans' table in your Supabase database.\n\n" +
              'Please run the following SQL in your Supabase SQL editor:\n' +
              'ALTER TABLE loans ADD COLUMN IF NOT EXISTS pause_notifications BOOLEAN DEFAULT false;'
          )
          throw error
        }
        throw error
      }

      // Update the loan object locally
      loan.pause_notifications = newStatus
    } catch (error) {
      console.error('Error toggling notifications:', error)
    } finally {
      setIsTogglingNotifications(false)
      setNotificationDialogState({ isOpen: false, disable: false })
    }
  }

  const handleConfirmNotificationToggle = (): void => {
    handleNotificationToggle(true)
  }

  return (
    <>
      <div
        className={`px-2 py-1 transition-all duration-300 ${
          isFocused && !isOtherCardFocused
            ? 'z-10'
            : isOtherCardFocused
              ? 'scale-98 opacity-40'
              : ''
        }`}
      >
        <div
          className={`bg-gradient-to-br from-stone-800 to-stone-900 rounded-2xl p-4 shadow-[0_0_25px_rgba(0,0,0,0.3)] border transition-all duration-300 ease-in-out ${
            isFocused
              ? 'border-orange-500 shadow-lg shadow-orange-500/30 p-5'
              : 'border-orange-500/30'
          } relative overflow-hidden ${matchesSearch ? 'ring-2 ring-orange-500/50' : ''}`}
        >
          {/* Decorative elements with improved transitions */}
          <div
            className={`absolute -top-24 -right-24 w-48 h-48 rounded-full blur-xl transition-all duration-300 ${
              isFocused ? 'bg-orange-500/10 scale-150' : 'bg-orange-500/10'
            }`}
          />
          <div
            className={`absolute -bottom-24 -left-24 w-48 h-48 rounded-full blur-xl transition-all duration-300 ${
              isFocused ? 'bg-orange-500/5 scale-150' : 'bg-orange-500/5'
            }`}
          />

          {/* Status indicator dot - enhanced with transition */}
          <div
            className={`absolute top-3 right-3 w-2.5 h-2.5 rounded-full shadow-md transition-all duration-300 ${
              loan.status === 'active'
                ? 'bg-green-500 animate-pulse'
                : loan.status === 'paid'
                  ? 'bg-blue-500'
                  : loan.status === 'overdue'
                    ? 'bg-red-500 animate-pulse'
                    : 'bg-orange-500'
            } ${isFocused ? 'scale-150 top-4 right-4' : ''}`}
          />

          {/* Status overlays */}
          {loan.status === 'paid' && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-10">
              <div className={`rotate-12 border-8 border-blue-500/30 rounded-lg px-8 py-2 transition-all duration-300 ${
                isFocused ? 'scale-110 opacity-70' : 'opacity-50'
              }`}>
                <span className="text-blue-500 font-bold text-4xl tracking-wider">PAID</span>
              </div>
            </div>
          )}
          
          {/* Cancelled overlay - matches paid style but in red */}
          {loan.status === 'cancelled' && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-10">
              <div className={`rotate-12 border-8 border-red-500/30 rounded-lg px-8 py-2 transition-all duration-300 ${
                isFocused ? 'scale-110 opacity-70' : 'opacity-50'
              }`}>
                <span className="text-red-500 font-bold text-4xl tracking-wider">CANCELLED</span>
              </div>
            </div>
          )}

          <div className={`relative ${loan.status === 'cancelled' ? 'opacity-75' : ''}`}>
            {/* Header Component */}
            <LoanCardHeader
              loan={loan}
              clientName={clientName}
              statusClass={statusClass}
              isFocused={isFocused}
              handleFocusToggle={handleFocusToggle}
              highlightMatch={highlightMatch}
              formatDate={formatDate}
            />

            {/* Firearm Info Component */}
            <LoanCardFirearmInfo
              gunLicence={loan.gun_licences}
              firearmInfo={firearmInfo}
              assignmentInfo={assignmentInfo}
              highlightMatch={highlightMatch}
            />

            {/* Financial Overview Component */}
            <LoanCardFinancialOverview
              loan={loan}
              loanTermInfo={getLoanTermInfo()}
              nextPaymentAmount={getNextPaymentAmount()}
              isCreationMonth={isCreationMonth()}
              hasInsufficientPastPayments={hasInsufficientPastPayments()}
              formatDate={formatDate}
            />

            {/* Progress Bar Component */}
            <LoanCardProgressBar
              progress={calculateProgress()}
              missedMonthsDetails={getMissedMonthsDetails()}
              penalties={loan.penalties || 0}
              isCreationMonth={isCreationMonth()}
              formatCurrency={formatCurrency}
            />

            {/* Payment Schedule Component */}
            <LoanCardPaymentSchedule
              paymentMonths={getPaymentMonths()}
              daysActive={getDaysActive()}
              getMonthStatus={getMonthStatus}
              getPaymentForMonth={getPaymentForMonth}
              getMonthlyPaymentAmount={getMonthlyPaymentAmount}
              getCurrentMonthPaymentStatus={getCurrentMonthPaymentStatus}
              formatDate={formatDate}
            />

            {/* Tabbed Sections */}
            <div className="border border-stone-700 rounded-lg overflow-hidden mt-4">
              {/* Tab Content */}
              <div className="bg-stone-800/30">
                {activeSections.client && (
                  <LoanCardClientInfo
                    loan={loan}
                    highlightMatch={highlightMatch}
                  />
                )}

                {activeSections.payments && (
                  <LoanCardPaymentsInfo
                    payments={payments}
                    loadingPayments={loadingPayments}
                  />
                )}

                {activeSections.details && (
                  <LoanCardDetailsInfo
                    loan={loan}
                    firearmInfo={firearmInfo}
                    assignmentInfo={assignmentInfo}
                    highlightMatch={highlightMatch}
                    onAddLicense={onAddLicense}
                  />
                )}
              </div>
            </div>

            {/* Action Buttons Component - Disabled for cancelled loans */}
            <div className={loan.status === 'cancelled' ? 'pointer-events-none opacity-50' : ''}>
              <LoanCardActions
                loan={loan}
                activeSections={activeSections}
                toggleSection={toggleSection}
                onAddPayment={onAddPayment}
                onAddLicense={onAddLicense}
                handleDelete={handleDelete}
                toggleNotificationsPause={handleNotificationToggle}
                isTogglingNotifications={isTogglingNotifications}
                onCancelLoan={onCancelLoan}
                onUpdateInvoiceNumber={onUpdateInvoiceNumber}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        isOpen={dialogState.isOpen}
        title="Delete Loan"
        message="Are you sure you want to delete this loan? This will permanently remove the loan record and all associated payment history. This action cannot be undone."
        onConfirm={handleConfirmDelete}
        onCancel={() => setDialogState({ isOpen: false, loanId: '' })}
        confirmText="Delete"
        confirmButtonClass="bg-red-500 hover:bg-red-600"
      />

      {/* Notification Disable Confirmation Dialog */}
      <Dialog
        isOpen={notificationDialogState.isOpen}
        title="Disable Notifications"
        message="Are you sure you want to disable notifications for this loan? The client will no longer receive payment reminders."
        onConfirm={handleConfirmNotificationToggle}
        onCancel={() => setNotificationDialogState({ isOpen: false, disable: false })}
        confirmText="Disable"
        confirmButtonClass="bg-stone-700 hover:bg-stone-600"
      />
    </>
  )
}

export default memo(LoanCard)


