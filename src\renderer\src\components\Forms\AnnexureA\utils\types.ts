import { FormData } from '../../../../types/FormData'

/**
 * Common props for all section components
 */
export interface SectionProps {
  formData: FormData
  updateFormData: (updatedData: Partial<FormData>) => void
  className?: string
}

/**
 * Props for sections that use address autocomplete
 */
export interface SectionWithAddressProps extends SectionProps {
  handleAddressChange: (
    address: string,
    postalCode?: string,
    isWorkAddress?: boolean
  ) => void
}

/**
 * Validation status type
 */
export type ValidationStatus = 'idle' | 'processing' | 'success' | 'error'

/**
 * Template status type
 */
export type TemplateStatus = 'loading' | 'ready' | 'error'
