{"name": "firearm-studio", "version": "1.1.3", "description": "Firearm Studio", "main": "./dist/main/index.js", "author": "<PERSON>", "homepage": "https://www.gunlicence.co.za/", "repository": {"type": "git", "url": "https://github.com/SheldonBakker/FLM-Updates"}, "scripts": {"kill-processes": "taskkill /F /IM electron.exe /T 2>nul || exit 0 && taskkill /F /IM Firearm-Studio.exe /T 2>nul || exit 0", "clean": "powershell -ExecutionPolicy Bypass -File scripts/clean.ps1", "clean-install": "rimraf node_modules package-lock.json && npm cache clean --force && npm install", "format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "cross-env NODE_ENV=production electron-vite build", "build:skip-check": "cross-env NODE_ENV=production electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build:skip-check && electron-builder --dir", "encrypt-env": "tsx src/utils/encrypt-env.ts", "copy-keys": "echo Copying encryption key files && copy /Y src\\utils\\env-key.js dist\\main\\ && copy /Y src\\main\\env-key.js dist\\main\\", "generate-icons": "node scripts/generate-icons.js", "build:win": "npm run clean && npm run encrypt-env && npm run generate-icons && npm run build:skip-check && npm run copy-keys && cross-env GH_TOKEN=%GH_TOKEN% electron-builder --win --publish never", "build:win:local": "npm run clean && npm run encrypt-env && npm run generate-icons && npm run build:skip-check && npm run copy-keys && electron-builder --win", "build:win:optimize": "npm run clean && npm run encrypt-env && npm run generate-icons && cross-env NODE_ENV=production ELECTRON_VITE_OPTIMIZE=true npm run build:skip-check && npm run copy-keys && electron-builder --win --publish never", "build:win:analyze": "cross-env ELECTRON_BUILDER_ANALYZE=true npm run build:win:local", "analyze:deps": "npx depcheck", "build:prod": "npm run clean && npm run encrypt-env && npm run build && cross-env GH_TOKEN=%GH_TOKEN% electron-builder --win --publish always", "dev:win": "npm run clean && electron-vite dev", "fix-registry": "reg add \"HKCU\\Software\\Classes\\CLSID\\{86ca1aa0-34aa-4e8b-a509-50c905bae2a2}\\InprocServer32\" /ve /d \"\" /f", "prebuild:win": "npm run fix-registry", "predeploy": "npm run clean && npm run encrypt-env && npm run build", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "build": {"appId": "co.za.gunlicence", "productName": "Firearm Studio", "directories": {"output": "dist", "buildResources": "build"}, "files": ["dist/**/*", "src/utils/env-key.js", "src/main/env-key.js", "!**/*.{js.map,ts}", "!dist/**/*.map", "!dist/**/*.d.ts", "!**/node_modules/**/{test,__tests__,tests,powered-test,example,examples,*.d.ts}", "!**/node_modules/**/{CHANGELOG.md,README.md,README,readme.md,readme,LICENSE,license,License,*.md}", "!**/node_modules/**/.{git,hg,svn,DS_Store}", "!**/node_modules/**/{demo,docs,examples,test,tests,__tests__,powered-test}/**/*"], "extraResources": [{"from": ".env.encrypted", "to": ".env.encrypted", "filter": ["**/*"]}], "asar": true, "asarUnpack": ["resources/**"], "win": {"target": ["nsis"], "icon": "build/icon.ico", "publisherName": "<PERSON>", "verifyUpdateCodeSignature": false, "artifactName": "${productName}-${version}-setup.${ext}", "requestedExecutionLevel": "asInvoker", "legalTrademarks": "© <PERSON>", "compression": "maximum"}, "nsis": {"oneClick": true, "allowToChangeInstallationDirectory": false, "perMachine": false, "license": "build/license.txt", "installerIcon": "build/icon.ico", "uninstallerIcon": "build/icon.ico", "installerHeaderIcon": "build/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Firearm Studio", "deleteAppDataOnUninstall": true, "displayLanguageSelector": false, "installerLanguages": ["en-US"], "language": "1033", "runAfterFinish": true, "include": "build/installer.nsh", "differentialPackage": true}}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@supabase/supabase-js": "^2.49.4", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "docxtemplater": "^3.60.0", "dotenv": "^16.5.0", "electron-log": "^5.2.4", "electron-updater": "^6.3.9", "lodash": "^4.17.21", "lucide-react": "^0.363.0", "node-fetch": "^2.7.0", "node-schedule": "^2.1.1", "pizzip": "^3.1.8", "react-router-dom": "^7.5.3", "recharts": "^2.15.3", "uuid": "^11.1.0", "ws": "^8.18.1"}, "devDependencies": {"@electron-toolkit/tsconfig": "^1.0.1", "@eslint/js": "^9.21.0", "@swc/core": "^1.10.8", "@tailwindcss/postcss": "^4.0.14", "@types/lodash": "^4.14.202", "@types/node": "^20.14.8", "@types/node-schedule": "^2.1.7", "@types/pizzip": "^3.0.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "depcheck": "^1.4.7", "electron": "^28.1.1", "electron-builder": "^24.13.3", "electron-vite": "^1.0.10", "eslint": "^8.57.1", "eslint-plugin-electron": "^7.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-security": "^3.0.1", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.12.0", "tailwind-scrollbar": "^4.0.1", "tailwindcss": "^4.0.12", "terser": "^5.39.0", "tsx": "^4.19.3", "typescript": "^5.5.2", "vite": "^6.3.4"}}