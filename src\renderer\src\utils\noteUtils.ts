/**
 * Parse notes string into an array of individual notes with date stamps
 * @param notes The notes string from a deal
 * @returns Array of parsed notes with date and content
 */
export const parseNotes = (notes: string | null): { date: string; content: string }[] => {
  if (!notes) return [];

  // Split notes by double newlines
  const noteSegments = notes.split('\n\n').filter(Boolean);
  
  const parsedNotes: { date: string; content: string }[] = [];
  
  for (const segment of noteSegments) {
    // Look for date stamp pattern [Date]
    const dateMatch = segment.match(/^\[(.*?)\]/);
    
    if (dateMatch) {
      const date = dateMatch[1];
      const content = segment.substring(dateMatch[0].length).trim();
      parsedNotes.push({ date, content });
    } else {
      // If no date stamp found, treat the whole segment as content with empty date
      parsedNotes.push({ date: '', content: segment });
    }
  }
  
  return parsedNotes;
};
