import React, { Suspense, lazy, useEffect } from 'react'
import { DashboardIcons } from '../components/icons/DashboardIcons'
import SearchContainer from '../components/SearchContainer'
import DebouncedSearchInput from '../components/DebouncedSearchInput'
import Toast from '../components/Toast'
import { SkeletonDashboard } from '../components/SkeletonLoading'
import '../styles/focus-mode.css'
import { useFirearmService } from '../services/useFirearmService'
import {
  FilterComponent,
  EmptyState,
  FirearmCardList,
  FocusedFirearm
} from '../components/FirearmStorage'
import { Pagination } from '../components/Dashboard'

// Lazy load components
const FirearmForm = lazy(() => import('../components/Forms/FirearmForm'))
const FirearmAssignmentForm = lazy(() => import('../components/Forms/FirearmAssignmentForm'))

function FirearmStorage(): React.JSX.Element {
  const {
    // State
    loading,
    searchQuery,
    page,
    perPage,
    activeFilter,
    formState,
    deleteDialog,
    toast,
    focusedFirearmId,
    visibleFirearms,
    totalFirearms,

    // Actions
    setPage,
    setActiveFilter,
    handleSearch,
    fetchFirearms,
    handleDeleteFirearm,
    onEditFirearm,
    resetFormState,
    setDeleteDialog,
    setToast,
    handleFirearmFocusToggle,
    handleAddFirearm,
    handleAssignFirearm,
    handleSignOutFirearm
  } = useFirearmService()

  // Add a useEffect to disable scrolling on the main content area and body
  useEffect(() => {
    // Get the main content area
    const mainContent = document.querySelector(
      '#root > div > div:nth-child(2)'
    ) as HTMLElement | null

    // Save original styles
    const originalBodyOverflow = document.body.style.overflow
    const originalHtmlOverflow = document.documentElement.style.overflow

    // Disable scrolling on body and html
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    if (mainContent) {
      // Save the original style
      const originalOverflow = mainContent.style.overflowY

      // Disable scrolling
      mainContent.style.overflowY = 'hidden'

      // Restore original styles when component unmounts
      return () => {
        mainContent.style.overflowY = originalOverflow
        document.body.style.overflow = originalBodyOverflow
        document.documentElement.style.overflow = originalHtmlOverflow
      }
    }

    // Restore original styles when component unmounts (if mainContent not found)
    return () => {
      document.body.style.overflow = originalBodyOverflow
      document.documentElement.style.overflow = originalHtmlOverflow
    }
  }, [])

  // Apply body overflow class when focus mode is active
  useEffect(() => {
    if (focusedFirearmId) {
      document.body.classList.add('focus-mode-active')
    } else {
      document.body.classList.remove('focus-mode-active')
    }

    return () => {
      document.body.classList.remove('focus-mode-active')
    }
  }, [focusedFirearmId])

  return (
    <div className="min-h-screen bg-gradient-to-b from-stone-900 to-stone-800 p-6">
      {/* Toast notifications */}
      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}

      {/* Page title with pagination */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white flex items-center gap-3">
          Firearm Storage
          <div className="px-3 py-1 bg-stone-700/50 rounded-full text-sm text-stone-300">
            Total Records: {totalFirearms}
          </div>
        </h1>

        {/* Pagination controls next to page title */}
        {visibleFirearms.length > 0 && (
          <Pagination
            page={page}
            setPage={setPage}
            totalItems={totalFirearms}
            itemsPerPage={perPage}
          />
        )}
      </div>

      {/* Two-column layout - always side by side */}
      <div className="flex flex-row h-[calc(100vh-180px)]">
        {/* Left column: Page tools */}
        <div className="w-80 bg-stone-800/60 rounded-lg shadow-lg p-4 flex-shrink-0 flex flex-col">
          {/* Action Buttons */}
          <div className="flex items-center justify-between mb-4">
            {/* Refresh Button */}
            <button
              onClick={() => fetchFirearms()}
              disabled={loading}
              className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-2 rounded-lg
                transition-colors flex items-center gap-1 disabled:opacity-50 h-10"
            >
              <DashboardIcons.Refresh className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
            </button>

            {/* Create New Firearm Button */}
            <button
              onClick={handleAddFirearm}
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                text-white px-3 py-2 rounded-lg shadow-md shadow-orange-500/20
                transition-all duration-200 flex items-center gap-2 h-10"
            >
              <DashboardIcons.Add className="w-4 h-4" />
              <span>Add Firearm</span>
            </button>
          </div>

          {/* Search - Using the new DebouncedSearchInput component */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-stone-400 mb-2">Quick Search</h3>
            <DebouncedSearchInput
              placeholder="Search by stock number, serial, make..."
              onSearch={handleSearch}
              initialValue={searchQuery}
              isLoading={loading}
              debounceTime={400}
              className="w-full"
            />
          </div>

          {/* Advanced Search - Using the original SearchContainer */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-stone-400 mb-2">Advanced Search</h3>
            <SearchContainer
              placeholder="Search by stock number, serial, make, model or owner..."
              onSearch={handleSearch}
              initialValue={searchQuery}
              searchTipsContent={
                <>
                  <p>Search Tips:</p>
                  <ul className="list-disc pl-4 mt-1 space-y-1">
                    <li>Search by stock number: "SN:12345"</li>
                    <li>Search by serial number: "SER:ABC123"</li>
                    <li>Search by make: "MAKE:Glock"</li>
                    <li>Search by model: "MODEL:19"</li>
                    <li>Search by owner: "OWNER:John"</li>
                  </ul>
                </>
              }
              isLoading={loading}
              showRefreshButton={false}
              showCreateButton={false}
              className="w-full"
            />
          </div>

          {/* Filter Components - Now organized in sections */}
          <div className="flex-1 space-y-4">
            <div>
              <h3 className="text-sm font-medium text-stone-400 mb-2">Status Filters</h3>
              <FilterComponent
                activeFilter={activeFilter}
                setActiveFilter={setActiveFilter}
                options={[
                  { value: 'all', label: 'All Firearms' },
                  { value: 'in-storage', label: 'In Storage' },
                  { value: 'assigned', label: 'Assigned' },
                  { value: 'signed-out', label: 'Booked Out' }
                ]}
              />
            </div>

            <div>
              <h3 className="text-sm font-medium text-stone-400 mb-2">Storage Type Filters</h3>
              <FilterComponent
                activeFilter={activeFilter}
                setActiveFilter={setActiveFilter}
                options={[
                  { value: 'owner', label: 'Owner Type' },
                  { value: 'private', label: 'Private Type' },
                  { value: 'dealer', label: 'Dealer Type' }
                ]}
              />
            </div>
          </div>
        </div>

        {/* Right column: Firearm content */}
        <div className="flex-1 ml-6 flex flex-col">
          <div className="bg-stone-800/30 rounded-lg p-4 flex flex-col h-full">
            {/* Search Results Section */}
            {loading ? (
              <div className="h-full">
                <SkeletonDashboard />
              </div>
            ) : focusedFirearmId && visibleFirearms.find(f => f.id === focusedFirearmId) ? (
              <div className="focus-mode-container">
                <div className="focus-mode-card">
                  <FocusedFirearm
                    firearm={visibleFirearms.find(f => f.id === focusedFirearmId) || null}
                    onClose={() => handleFirearmFocusToggle(null)}
                    onEditFirearm={onEditFirearm}
                    onDeleteFirearm={(firearmId) => setDeleteDialog({ isOpen: true, firearmId })}
                    onAssignFirearm={handleAssignFirearm}
                    onSignOutFirearm={handleSignOutFirearm}
                    onAddCredit={() => fetchFirearms()} // Refresh firearms after adding credit
                  />
                </div>
              </div>
            ) : visibleFirearms.length > 0 ? (
              <FirearmCardList
                firearms={visibleFirearms}
                onEditFirearm={onEditFirearm}
                onDeleteFirearm={(firearmId) => setDeleteDialog({ isOpen: true, firearmId })}
                onAssignFirearm={handleAssignFirearm}
                onSignOutFirearm={handleSignOutFirearm}
                onFocusToggle={handleFirearmFocusToggle}
                focusedFirearmId={focusedFirearmId}
                onAddCredit={() => fetchFirearms()} // Refresh firearms after adding credit
              />
            ) : (
              <EmptyState
                searchQuery={searchQuery}
                activeFilter={activeFilter}
                onAddFirearm={handleAddFirearm}
              />
            )}
          </div>
        </div>
      </div>

      {/* Forms */}
      <Suspense fallback={null}>
        {formState.isOpen && formState.type === 'firearm' && (
          <FirearmForm
            firearm={formState.selectedFirearm}
            onClose={resetFormState}
            onSuccess={() => {
              fetchFirearms()
              resetFormState()
            }}
          />
        )}

        {formState.isOpen && formState.type === 'assignment' && (
          <FirearmAssignmentForm
            firearm={formState.selectedFirearm!}
            onClose={resetFormState}
            onSuccess={() => {
              fetchFirearms()
              resetFormState()
            }}
          />
        )}
      </Suspense>

      {/* Delete Confirmation Dialog */}
      {deleteDialog.isOpen && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-stone-800 rounded-xl shadow-2xl w-full max-w-md p-6">
            <h2 className="text-xl font-bold text-white mb-4">Confirm Deletion</h2>
            {deleteDialog.hasAssignment && deleteDialog.assignmentInfo ? (
              <>
                <div className="bg-amber-500/20 border border-amber-500/30 rounded-lg p-3 mb-4">
                  <div className="flex items-center gap-2 text-amber-400 font-medium mb-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                      <line x1="12" y1="9" x2="12" y2="13"></line>
                      <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                    <span>Warning: Firearm is currently assigned</span>
                  </div>
                  <p className="text-stone-300 text-sm">
                    This firearm is currently assigned to <span className="text-white font-medium">
                      {deleteDialog.assignmentInfo.clients?.first_name} {deleteDialog.assignmentInfo.clients?.last_name}
                    </span>.
                  </p>
                </div>
                <p className="text-stone-300 mb-6">
                  Are you sure you want to delete both the assignment and the firearm? This action cannot be undone.
                </p>
              </>
            ) : (
              <p className="text-stone-300 mb-6">
                Are you sure you want to delete this firearm? This action cannot be undone.
              </p>
            )}
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setDeleteDialog({ isOpen: false, firearmId: null, hasAssignment: false, assignmentInfo: null })}
                className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (deleteDialog.firearmId) {
                    if (deleteDialog.hasAssignment && deleteDialog.assignmentInfo) {
                      // Delete both the assignment and the firearm
                      handleDeleteFirearm(deleteDialog.firearmId, true, deleteDialog.assignmentInfo)
                    } else {
                      // Just delete the firearm (no active assignment)
                      handleDeleteFirearm(deleteDialog.firearmId)
                    }
                  }
                  setDeleteDialog({ isOpen: false, firearmId: null, hasAssignment: false, assignmentInfo: null })
                }}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FirearmStorage
