import React from 'react'
import { FormSection, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Application Type section component
 */
const ApplicationType: React.FC<SectionProps> = ({ formData, updateFormData, className = '' }) => {
  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Application Type</h3>
      <FormSection
        title="Application Type"
        subtitle="Select the type of firearm licence application"
      >
        <div className="space-y-3">
          <div className="mb-4">
            <RadioGroup
              name="applicationType"
              value={formData.mainHF ? 'MainHF' : formData.addHF ? 'AddHF' : ''}
              onChange={(value) => {
                updateFormData({
                  mainHF: value === 'MainHF',
                  addHF: value === 'AddHF'
                })
              }}
              options={[
                { value: 'MainHF', label: 'Main firearm licence holder' },
                { value: 'AddHF', label: 'Additional firearm licence holder' }
              ]}
              label="Application Type"
            />
          </div>

          <div className="mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">Licence Type</h3>
            <RadioGroup
              name="licenceType"
              value={formData.s13 ? 'S13' : 
                     formData.s15 ? 'S15' : 
                     formData.s16 ? 'S16' : 
                     formData.s20 ? 'S20' : 
                     formData.s20a ? 'S20A' : 
                     formData.s20b ? 'S20B' : 
                     formData.s20c ? 'S20C' : ''}
              onChange={(value) => {
                // Reset all licence type values
                const resetLicenceTypes = {
                  s13: false,
                  s15: false,
                  s16: false,
                  s20: false,
                  s20a: false,
                  s20b: false,
                  s20c: false
                }
                // Map XML placeholder key to formData key
                const keyMap: Record<string, keyof typeof resetLicenceTypes> = {
                  S13: 's13',
                  S15: 's15',
                  S16: 's16',
                  S20: 's20',
                  S20A: 's20a',
                  S20B: 's20b',
                  S20C: 's20c'
                }
                const selected = keyMap[value] || ''
                updateFormData({
                  ...resetLicenceTypes,
                  ...(selected && { [selected]: true })
                })
              }}
              options={[
                { value: 'S13', label: ' Self-Defence ' },
                { value: 'S15', label: 'Occasional Hunting/Sport-Shooting' },
                { value: 'S16', label: 'Dedicated Hunting/Dedicated Sport-Shooting' },
                { value: 'S20', label: 'Business Purposes: Hunting' },
                { value: 'S20A', label: ' Business Purposes: Other' },
                { value: 'S20B', label: ' Business Purposes: Security' },
                { value: 'S20C', label: ' Business Purposes: Training' },
              ]}
              label="Licence Type"
            />
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default ApplicationType
