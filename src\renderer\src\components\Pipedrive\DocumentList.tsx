import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { DealDocument } from '../../types/pipedrive';
import { DashboardIcons } from '../icons/DashboardIcons';
import { usePipedriveService } from '../../services/usePipedriveService';

// Define the ref type
export interface DocumentListRef {
  refreshDocuments: () => void;
}

interface DocumentListProps {
  dealId: string;
}

const DocumentList = forwardRef<DocumentListRef, DocumentListProps>(({ dealId }, ref) => {
  const [documents, setDocuments] = useState<DealDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0); // Add a refresh key to trigger manual refreshes
  const { fetchDealDocuments, deleteDocument } = usePipedriveService();

  // Function to manually refresh documents
  const refreshDocuments = () => {
    setRefreshKey(prev => prev + 1);
  };

  // Expose the refreshDocuments function to parent components
  useImperativeHandle(ref, () => ({
    refreshDocuments
  }));

  // Handle document deletion
  const handleDeleteDocument = async (documentId: string, filePath: string) => {
    if (window.confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
      try {
        setDeleteLoading(documentId);
        await deleteDocument(documentId, filePath);

        // Update the local state to remove the deleted document
        setDocuments(prevDocuments =>
          prevDocuments.filter(doc => doc.id !== documentId)
        );
      } catch (err) {
        console.error('Error deleting document:', err);
        setError('Failed to delete document');
      } finally {
        setDeleteLoading(null);
      }
    }
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get icon based on file type
  const getFileIcon = (fileType: string) => {
    if (!fileType) {
      return <DashboardIcons.Document className="w-4 h-4 text-stone-400" />;
    }

    const fileTypeLower = fileType.toLowerCase();

    if (fileTypeLower.includes('pdf')) {
      return <DashboardIcons.Document className="w-4 h-4 text-red-400" />;
    } else if (fileTypeLower.includes('image') ||
               fileTypeLower.includes('jpg') ||
               fileTypeLower.includes('jpeg') ||
               fileTypeLower.includes('png') ||
               fileTypeLower.includes('gif')) {
      return <DashboardIcons.Image className="w-4 h-4 text-blue-400" />;
    } else if (fileTypeLower.includes('word') ||
               fileTypeLower.includes('document') ||
               fileTypeLower.includes('docx') ||
               fileTypeLower.includes('doc')) {
      return <DashboardIcons.Document className="w-4 h-4 text-indigo-400" />;
    } else if (fileTypeLower.includes('excel') ||
               fileTypeLower.includes('sheet') ||
               fileTypeLower.includes('xlsx') ||
               fileTypeLower.includes('xls') ||
               fileTypeLower.includes('csv')) {
      return <DashboardIcons.Document className="w-4 h-4 text-green-400" />;
    } else if (fileTypeLower.includes('powerpoint') ||
               fileTypeLower.includes('presentation') ||
               fileTypeLower.includes('pptx') ||
               fileTypeLower.includes('ppt')) {
      return <DashboardIcons.Document className="w-4 h-4 text-orange-400" />;
    } else {
      return <DashboardIcons.Document className="w-4 h-4 text-stone-400" />;
    }
  };

  // Load documents only on initial mount and when refresh is triggered
  useEffect(() => {
    const loadDocuments = async () => {
      if (!dealId) return;

      try {
        setLoading(true);
        setError(null);

        const docs = await fetchDealDocuments(dealId);
        setDocuments(docs);
      } catch (err) {
        console.error('Error loading documents:', err);
        setError('Failed to load documents');
      } finally {
        setLoading(false);
      }
    };

    loadDocuments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dealId, refreshKey]); // Only depend on dealId and refreshKey, not on fetchDealDocuments

  if (loading) {
    return (
      <div className="mt-4 flex items-center justify-center p-4">
        <DashboardIcons.Spinner className="w-4 h-4 animate-spin text-orange-500 mr-2" />
        <span className="text-xs text-stone-400">Loading documents...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-4 p-4 border border-red-500/20 bg-red-500/10 rounded-md">
        <p className="text-xs text-red-400">{error}</p>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="mt-4 p-4 border border-stone-600/20 bg-stone-700/10 rounded-md text-center">
        <p className="text-xs text-stone-400">No documents uploaded yet</p>
        <p className="text-xs text-stone-500 mt-1">Upload documents using the form above</p>
      </div>
    );
  }

  return (
    <div className="mt-4">
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-stone-300">Uploaded Documents</h4>
        <button
          onClick={(e) => {
            e.preventDefault(); // Prevent form submission
            e.stopPropagation(); // Stop event propagation
            refreshDocuments();
          }}
          type="button" // Explicitly set button type to prevent form submission
          className="p-1.5 text-stone-400 hover:text-orange-400 hover:bg-stone-700/50 rounded-md transition-colors"
          title="Refresh documents"
          disabled={loading}
        >
          <DashboardIcons.Refresh className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>
      <div className="space-y-2">
        {documents.map((doc) => (
          <div
            key={doc.id}
            className="p-3 bg-stone-700/30 border border-stone-600/30 rounded-md flex items-center"
          >
            <div className="mr-3">
              {getFileIcon(doc.file_type)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm text-white font-medium truncate">{doc.file_name}</p>
              <div className="flex items-center text-xs text-stone-400">
                <span>{formatFileSize(doc.file_size)}</span>
                <span className="mx-1">•</span>
                <span>{new Date(doc.created_at).toLocaleDateString()}</span>
              </div>
            </div>
            <div className="flex items-center">
              {/* Delete button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  handleDeleteDocument(doc.id, doc.file_path);
                }}
                type="button" // Explicitly set button type to prevent form submission
                className="ml-2 p-1.5 text-stone-400 hover:text-red-400 hover:bg-stone-600/30 rounded-md transition-colors"
                title="Delete document"
                disabled={deleteLoading === doc.id}
              >
                {deleteLoading === doc.id ? (
                  <DashboardIcons.Spinner className="w-4 h-4 animate-spin" />
                ) : (
                  <DashboardIcons.Delete className="w-4 h-4" />
                )}
              </button>

              {/* Open document link */}
              {doc.url ? (
                <a
                  href={doc.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="ml-2 p-1.5 text-orange-400 hover:text-orange-300 hover:bg-stone-600/30 rounded-md transition-colors"
                  title="Open document"
                  onClick={() => console.log('Opening document URL:', doc.url)}
                >
                  <DashboardIcons.ExternalLink className="w-4 h-4" />
                </a>
              ) : (
                <span
                  className="ml-2 p-1.5 text-stone-500 cursor-not-allowed"
                  title={doc.error || "Document URL not available"}
                >
                  <DashboardIcons.ExternalLink className="w-4 h-4" />
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
});

export default DocumentList;
