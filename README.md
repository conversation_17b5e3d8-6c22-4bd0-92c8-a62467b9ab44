# Firearm Licence Management System (FLMS)

A comprehensive desktop application built with Electron, React, and TypeScript for managing firearm licenses, client information, and weapon storage. Designed with security and efficiency in mind, FLMS provides a robust solution for firearms management professionals.

![FLMS Logo](build/icon.ico)

[![GitHub last commit](https://img.shields.io/github/last-commit/Gunlicence/FLM-System-Client)](https://github.com/Gunlicence/FLM-System-Client/commits/main/)
[![GitHub issues](https://img.shields.io/github/issues/Gunlicence/FLM-System-Client)](https://github.com/Gunlicence/FLM-System-Client/issues/)
[![License](https://img.shields.io/badge/license-Custom-blue)](LICENSE)

## Features

### Core Functionality
- **Secure Client Management**: Store and manage client information with strict access control and search capabilities
- **License Tracking**: Track firearm licenses with expiration notifications and renewal workflows
- **Document Generation**: Create standardized documents from templates with comprehensive placeholder support
- **Financial Management**: Track loans and payments for firearms
- **Auto-Updates**: Built-in update system with manual control for security
- **Form System**: Multi-stage form system with progress tracking and validation
- **Template Reference**: Comprehensive template placeholder reference system

### Weapon Storage Management
- **Container Management**: Create and manage storage containers with customizable properties
- **Weapon Assignment**: Assign firearms to specific containers with full history tracking
- **Movement Logging**: Comprehensive audit trail of all weapon movements
- **Reporting & Analytics**: Generate reports on container utilization and weapon locations

## Technology Stack

### Frontend
- **React** with TypeScript for UI components
- **TailwindCSS** for responsive styling
- **React Router** for application navigation
- **React Toastify** for notifications

### Backend & Infrastructure
- **Electron** for cross-platform desktop application
- **TypeScript** for type-safe code
- **Supabase** for database integration

### Document Processing
- **Docxtemplater** for document generation
- **PizZip** for ZIP handling
- **IndexedDB** for template caching
- **Custom error handling** for robust document processing

### Build & Deployment
- **Electron Vite** for bundling
- **Electron Builder** for packaging
- **Electron Updater** for seamless updates

## Installation

### Prerequisites
- Node.js v18+
- npm v9+

### Development Setup
```bash
# Clone the repository
git clone https://github.com/Gunlicence/FLM-System-Client.git
cd FLM-System-Client

# Install dependencies
npm install

# Start the development server
npm run dev

# Run in production mode
npm run start
```

### Building for Distribution
```bash
# Build for Windows
npm run build:win

# Build for Windows (local testing)
npm run build:win:local

# Build for Windows (optimized)
npm run build:win:optimize

# Production build with automated publishing
npm run build:prod
```

## Security Features

- **Content Security Policy (CSP)** enforced
- **Context Isolation** enabled
- **Node Integration** disabled
- **Environment Variable Encryption** for sensitive data
- **Hardened runtime** for Windows
- **Strict TypeScript** type checking
- **Admin-only access** control
- **Encrypted Environment Variables** for secure credential management
- **User-friendly error handling** with detailed error messages
- **Template caching** with secure storage

## Update System

The application includes a controlled update system that:

- Allows manual update checks
- Provides update notifications
- Gives users control over download and installation
- Prevents automatic updates during critical operations

## Recent Improvements

### Document Processing
- **Template Caching System**: Implemented IndexedDB-based caching for document templates to improve performance and reduce network requests
- **Error Handling**: Added robust error handling with user-friendly error messages for document processing
- **Placeholder Reference**: Created comprehensive template reference section with categorized placeholders

### Form System
- **Auto-generated Fields**: Added automatic generation of fields like initials from names
- **Test Data System**: Implemented test data dropdown for efficient form testing (development mode only)
- **Multi-stage Forms**: Enhanced form navigation with progress tracking and validation

### User Experience
- **Improved UI**: Enhanced UI components for better usability and visual consistency
- **Address Handling**: Simple address input with postal code extraction
- **Error Feedback**: Improved error messages and validation feedback

## Database Structure

The system uses a relational database model with the following primary entities:
- Clients
- Licenses
- Loans
- Payments
- Storage Containers
- Weapon Movements

## License

This project is protected under a custom license that restricts modification and distribution rights. See [LICENSE](LICENSE) for details.

## Support & Contact

For support, feature requests, or security-related inquiries:

- **Developer**: Sheldon Bakker
- **Email**: [<EMAIL>](mailto:<EMAIL>)
- **Website**: [www.gunlicence.co.za](https://www.gunlicence.co.za/)

---

© 2023-2025 Sheldon Bakker. All rights reserved.
