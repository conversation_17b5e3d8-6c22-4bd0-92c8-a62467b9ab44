import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Competency Certificate section component for Annexure A form
 */
const CompetencyCertificate: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  const handleCheckboxChange = (name: string) => {
    updateFormData({ [name]: !formData[name as keyof typeof formData] })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Competency Certificate</h3>

      <FormSection title="Competency Type" subtitle="Select the type of competency">
        <div className="flex flex-wrap gap-3 p-3 bg-stone-800 rounded-lg">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="handgunCompetency"
              name="handgunType"
              checked={formData.handgunType || false}
              onChange={() => handleCheckboxChange('handgunType')}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="handgunCompetency" className="ml-2 block text-sm text-stone-300">
              Handgun
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="rifleCompetency"
              name="rifleType"
              checked={formData.rifleType || false}
              onChange={() => handleCheckboxChange('rifleType')}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="rifleCompetency" className="ml-2 block text-sm text-stone-300">
              Rifle
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="shotgunCompetency"
              name="shotgunType"
              checked={formData.shotgunType || false}
              onChange={() => handleCheckboxChange('shotgunType')}
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
            />
            <label htmlFor="shotgunCompetency" className="ml-2 block text-sm text-stone-300">
              Shotgun
            </label>
          </div>
        </div>
      </FormSection>

      <FormSection title="Certificate Details" subtitle="Enter your competency certificate information">
        <div className="space-y-3">
          {/* Certificate Number */}
          <div>
            <label className="block text-sm font-medium text-stone-300 mb-1">
              Competency Certificate Number
            </label>
            <input
              type="text"
              name="certNumber"
              value={formData.certNumber || ''}
              onChange={handleChange}
              placeholder="e.g. CC123456"
              required={true}
              className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
            />
          </div>

          {/* Certificate Dates */}
          <div className="flex flex-row gap-3 overflow-x-auto pb-2">
            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Date of Issue</label>
                <input
                  type="date"
                  name="certIssueDate"
                  value={formData.certIssueDate || ''}
                  onChange={handleChange}
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>

            <div className="flex-1 min-w-[150px]">
              <div className="h-full flex flex-col justify-between">
                <label className="block text-sm font-medium text-stone-300 mb-1">Expiry Date</label>
                <input
                  type="date"
                  name="certExpiryDate"
                  value={formData.certExpiryDate || ''}
                  onChange={handleChange}
                  required={true}
                  className="w-full bg-stone-700 border border-stone-600 rounded-lg px-4 py-2 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500/30 focus:outline-none"
                />
              </div>
            </div>
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default CompetencyCertificate
