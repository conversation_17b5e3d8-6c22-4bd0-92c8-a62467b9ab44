import React, { useState } from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'
import { Firearm } from '../../types/firearm'
import { formatDate, formatCurrency } from '../../utils/formatters'
import FirearmPaymentForm from '../Forms/FirearmPaymentForm'
import FirearmPaymentHistory from './FirearmPaymentHistory'

interface FirearmCardProps {
  firearm: Firearm
  onEdit: (firearm: Firearm) => void
  onDelete: (firearmId: string) => void
  onAssign: (firearm: Firearm) => void
  onSignOut: (firearmId: string) => void
  onFocusToggle: (firearmId: string) => void
  isFocused: boolean
  isOtherCardFocused?: boolean
  onAddCredit?: (firearm: Firearm) => void // Optional callback for when credit is added
}

const FirearmCard: React.FC<FirearmCardProps> = ({
  firearm,
  onEdit,
  onDelete,
  onAssign,
  onSignOut,
  onFocusToggle,
  isFocused,
  isOtherCardFocused = false,
  onAddCredit
}) => {
  // State for payment form, payment history, and return confirmation
  const [showPaymentForm, setShowPaymentForm] = useState(false)
  const [showPaymentHistory, setShowPaymentHistory] = useState(false)
  const [showReturnConfirm, setShowReturnConfirm] = useState(false)
  // Determine card status
  const isSignedOut = !!firearm.date_signed_out
  const isAssigned = !!firearm.is_assigned

  // Format dates
  const signedInDate = formatDate(firearm.date_signed_in)
  const freeStorageUntil = firearm.free_storage_until ? formatDate(firearm.free_storage_until) : null

  // Get storage type display text
  const getStorageTypeDisplay = () => {
    // Default to 'Owner' if storage_type is undefined
    const storageType = firearm.storage_type || 'Owner'

    switch(storageType) {
      case 'Owner': return 'Owner (Self-Owned)'
      case 'Private': return 'Private (Assignable)'
      case 'Dealer': return 'Dealer (Gunshop)'
      default: return storageType
    }
  }

  // Get storage type color
  const getStorageTypeColor = () => {
    // Default to 'Owner' if storage_type is undefined
    const storageType = firearm.storage_type || 'Owner'

    switch(storageType) {
      case 'Owner': return 'bg-green-900/30 text-green-400 border border-green-700/30'
      case 'Private': return 'bg-purple-900/30 text-purple-400 border border-purple-700/30'
      case 'Dealer': return 'bg-amber-900/30 text-amber-400 border border-amber-700/30'
      default: return 'bg-stone-900/30 text-stone-400 border border-stone-700/30'
    }
  }

  // Toggle focus mode
  const handleFocusToggle = () => {
    onFocusToggle(firearm.id)
  }

  return (
    <div
      className={`px-2 py-1 transition-all duration-300 ${
        isFocused && !isOtherCardFocused
          ? 'z-10'
          : isOtherCardFocused
            ? 'scale-98 opacity-40'
            : ''
      }`}
    >
      <div
        className={`bg-gradient-to-br from-stone-800 to-stone-900 rounded-2xl p-4 shadow-[0_0_25px_rgba(0,0,0,0.3)] border transition-all duration-300 ease-in-out ${
          isFocused
            ? 'border-orange-500 shadow-lg shadow-orange-500/30 p-5'
            : 'border-orange-500/30'
        } relative overflow-hidden`}
      >
        {/* Decorative elements with improved transitions */}
        <div
          className={`absolute -top-24 -right-24 w-48 h-48 rounded-full blur-xl transition-all duration-300 ${
            isFocused ? 'bg-orange-500/10 scale-150' : 'bg-orange-500/10'
          }`}
        />
        <div
          className={`absolute -bottom-24 -left-24 w-48 h-48 rounded-full blur-xl transition-all duration-300 ${
            isFocused ? 'bg-orange-500/5 scale-150' : 'bg-orange-500/5'
          }`}
        />

        {/* Status indicator dot - enhanced with transition */}
        <div
          className={`absolute top-3 right-3 w-2.5 h-2.5 rounded-full shadow-md transition-all duration-300 ${
            isSignedOut
              ? 'bg-green-500 animate-pulse'
              : isAssigned
                ? 'bg-blue-500'
                : 'bg-green-500 animate-pulse'
          } ${isFocused ? 'scale-150 top-4 right-4' : ''}`}
        />

        <div className="relative">
          {/* Redesigned header with key information */}
          <div className={`flex flex-col gap-3 mb-4 ${isFocused ? 'gap-4' : ''}`}>
            {/* Firearm info, stock number and status row - simplified layout */}
            <div className="flex justify-between items-start">
              {/* Firearm info */}
              <div className="flex items-start gap-2">
                <div className="mt-1">
                  <span
                    className={`inline-block w-1.5 h-6 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full ${isFocused ? 'w-2 h-8' : ''}`}
                  />
                </div>
                <div>
                  <h2
                    className={`font-semibold text-xl text-white ${isFocused ? 'text-2xl' : ''}`}
                  >
                    {firearm.make} {firearm.model}
                  </h2>
                </div>
              </div>

              {/* Stock number with maximise button - updated positioning */}
              <div className="flex-1 flex justify-center items-center">
                <div className="px-4 py-1.5 bg-gradient-to-r from-stone-800/70 via-stone-700/40 to-stone-800/70 border border-stone-500/20 rounded-md shadow-sm flex items-center">
                  <div className="w-3 h-px bg-orange-400/50 mr-2"></div>
                  <span className="text-lg text-white font-medium tracking-wide">
                    {firearm.stock_number && `STOCK #${firearm.stock_number}`}
                  </span>
                  <div className="w-3 h-px bg-orange-400/50 ml-2"></div>

                  {/* Focus button - only shown when not focused */}
                  {!isFocused && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation(); // Stop event from bubbling up to the card
                        handleFocusToggle();
                      }}
                      className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-stone-600/80 hover:text-white"
                      title="Focus on this firearm"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                      </svg>
                    </button>
                  )}

                  {/* Close button - shown only when focused */}
                  {isFocused && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation(); // Stop event from bubbling up to the card
                        handleFocusToggle();
                      }}
                      className="ml-3 w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out bg-stone-700/80 text-stone-300 hover:bg-red-500/80 hover:text-white"
                      title="Exit focus mode"
                    >
                      <DashboardIcons.Close className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              {/* Status - Redesigned with better organization */}
              <div className="flex flex-col items-end">
                {/* Status badges in a row */}
                <div className="flex items-center gap-1.5 mb-1.5">
                  {/* Assignment Status */}
                  <div className={`px-2 py-0.5 rounded-full ${isFocused ? 'text-sm px-2.5 py-0.75' : 'text-xs'} font-medium ${
                    isSignedOut
                      ? 'bg-green-900/30 text-green-400 border border-green-700/30'
                      : isAssigned
                        ? 'bg-blue-900/30 text-blue-400 border border-blue-700/30'
                        : 'bg-green-900/30 text-green-400 border border-green-700/30'
                  }`}>
                    {isSignedOut ? 'Booked Out' : isAssigned ? 'Assigned' : 'In Storage'}
                  </div>

                  {/* Storage Type */}
                  <div className={`px-2 py-0.5 rounded-full ${isFocused ? 'text-sm px-2.5 py-0.75' : 'text-xs'} font-medium ${getStorageTypeColor()}`}>
                    {getStorageTypeDisplay()}
                  </div>
                </div>

                {/* Structured information box for payment and dates */}
                {isAssigned && (
                  <div className={`bg-stone-800/70 border border-stone-700/50 rounded-md p-2 text-xs w-fit ${isFocused ? 'text-sm' : ''}`}>
                    <div className="grid grid-cols-2 gap-x-3 gap-y-1">
                      <div className="text-stone-400">Signed In:</div>
                      <div className="text-white font-medium">{signedInDate}</div>

                      {/* Free storage until date for Dealer type */}
                      {(firearm.storage_type === 'Dealer') && freeStorageUntil && (
                        <>
                          <div className="text-amber-400">Free Until:</div>
                          <div className="text-amber-300 font-medium">{freeStorageUntil}</div>
                        </>
                      )}

                      {/* Payment status */}
                      {firearm.payment_status && (
                        <>
                          <div className="text-stone-400">Payment:</div>
                          <div className={`font-medium ${firearm.payment_status === 'free' ? 'text-amber-300' :
                                                  firearm.payment_status === 'paid' ? 'text-green-400' :
                                                  'text-red-400'}`}>
                            {firearm.payment_status === 'free' ? 'Free Storage' :
                                   firearm.payment_status === 'paid' ? 'Paid' :
                                   'Payment Due'}
                          </div>
                        </>
                      )}

                      {/* We've moved the Credit Balance to the main card */}
                    </div>
                  </div>
                )}

                {/* For non-assigned firearms, show a simpler info box */}
                {!isAssigned && (
                  <div className={`bg-stone-800/70 border border-stone-700/50 rounded-md p-2 text-xs w-fit ${isFocused ? 'text-sm' : ''}`}>
                    <div className="grid grid-cols-2 gap-x-3 gap-y-1">
                      <div className="text-stone-400">Signed In:</div>
                      <div className="text-white font-medium">{signedInDate}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Firearm details */}
            <div className="bg-stone-800/50 rounded-lg p-3 border border-stone-700/50">
              <div className="grid grid-cols-3 gap-3 text-sm">
                <div className="text-center rounded-md p-1.5">
                  <div className="text-stone-300 text-xs font-medium mb-1">Serial Number</div>
                  <div className="text-white font-medium">{firearm.serial}</div>
                </div>
                <div className="text-center rounded-md p-1.5">
                  <div className="text-stone-300 text-xs font-medium mb-1">Owner</div>
                  <div className="text-white font-medium">{firearm.full_name || 'N/A'}</div>
                </div>
                <div className="text-center rounded-md p-1.5">
                  <div className="text-stone-300 text-xs font-medium mb-1">Wallet Balance</div>
                  <div className={`font-medium ${(firearm.wallet_balance || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {formatCurrency(firearm.wallet_balance || 0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Owner Details and Firearm Notes side by side */}
            <div className="grid grid-cols-2 gap-4">
              {/* Owner Details */}
              <div className="bg-stone-800/50 rounded-lg p-3 border border-stone-700/50">
                <div className="flex items-center gap-2 mb-2">
                  <DashboardIcons.Assign className="w-4 h-4 text-orange-400" />
                  <span className="text-orange-400 text-sm font-medium">Owner Details</span>
                </div>
                <div className="pl-6 space-y-2 text-sm">
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <div className="text-stone-400 text-xs">Name:</div>
                      <div className="text-white truncate">{firearm.full_name}</div>
                    </div>
                    <div className="flex-1">
                      <div className="text-stone-400 text-xs">ID/Dealer #:</div>
                      <div className="text-white truncate">{firearm.dealer_id_number}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Firearm Notes */}
              <div className="bg-stone-800/50 rounded-lg p-3 border border-stone-700/50">
                <div className="flex items-center gap-2 mb-2">
                  <DashboardIcons.Edit className="w-4 h-4 text-orange-400" />
                  <span className="text-orange-400 text-sm font-medium">Firearm Notes</span>
                </div>
                <div className="text-stone-300 text-sm pl-6 max-h-[120px] overflow-y-auto">
                  {firearm.notes || 'No firearm notes available'}
                </div>
              </div>
            </div>

            {/* Assignment information and notes side by side (if assigned) */}
            {isAssigned && firearm.assigned_to && (
              <div className="grid grid-cols-2 gap-4 mt-4">
                {/* Assigned Client Details */}
                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-2">
                    <DashboardIcons.Assign className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 text-sm font-medium">Assigned Client Details</span>
                  </div>
                  {firearm.assigned_client_details ? (
                    <div className="pl-6 space-y-2">
                      <div className="flex gap-4">
                        <div className="flex-1">
                          <div className="text-stone-400 text-xs">Name:</div>
                          <div className="text-white truncate">{firearm.assigned_client_details.first_name} {firearm.assigned_client_details.last_name}</div>
                        </div>
                        <div className="flex-1">
                          <div className="text-stone-400 text-xs">ID Number:</div>
                          <div className="text-white truncate">{firearm.assigned_client_details.id_number}</div>
                        </div>
                      </div>
                      <div className="flex gap-4">
                        <div className="flex-1">
                          <div className="text-stone-400 text-xs">Email:</div>
                          <div className="text-white truncate">{firearm.assigned_client_details.email}</div>
                        </div>
                        <div className="flex-1">
                          <div className="text-stone-400 text-xs">Phone:</div>
                          <div className="text-white truncate">{firearm.assigned_client_details.phone}</div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-white text-sm pl-6">{firearm.assigned_to}</div>
                  )}
                </div>

                {/* Assignment Notes */}
                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-2">
                    <DashboardIcons.Edit className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-400 text-sm font-medium">Assignment Notes</span>
                  </div>
                  <div className="text-stone-300 text-sm pl-6 max-h-[120px] overflow-y-auto">
                    {firearm.assignment_notes || 'No assignment notes available'}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center gap-2 flex-wrap mt-4">
            {/* Left-side section with Edit and Assign buttons */}
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation(); // Stop event from bubbling up to the card
                  onEdit(firearm);
                }}
                className="px-3 py-1.5 rounded-md bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                  text-white text-sm font-medium flex items-center gap-1.5 shadow-md shadow-orange-900/20 transition-all duration-200"
              >
                <DashboardIcons.Edit className="w-3.5 h-3.5" />
                Edit
              </button>

              {/* Assign and Book Out buttons - only shown when not signed out and not Owner type */}
              {!isSignedOut && (
                <>
                  {/* Only show Return/Assign buttons if storage type is not Owner */}
                  {(firearm.storage_type !== 'Owner') && (
                    <>
                      {isAssigned ? (
                        <button
                          onClick={(e) => {
                            e.stopPropagation(); // Stop event from bubbling up to the card
                            setShowReturnConfirm(true);
                          }}
                          className="px-3 py-1.5 rounded-md bg-amber-500/20 hover:bg-amber-500/30
                            text-amber-400 hover:text-amber-300 text-sm font-medium flex items-center gap-1.5 border border-amber-500/30 transition-all duration-200"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="w-3.5 h-3.5"
                          >
                            <path d="M9 10L4 15L9 20" />
                            <path d="M20 4v7a4 4 0 0 1-4 4H4" />
                          </svg>
                          Return
                        </button>
                      ) : (
                        <button
                          onClick={(e) => {
                            e.stopPropagation(); // Stop event from bubbling up to the card
                            onAssign(firearm);
                          }}
                          className="px-3 py-1.5 rounded-md bg-blue-500/20 hover:bg-blue-500/30
                            text-blue-400 hover:text-blue-300 text-sm font-medium flex items-center gap-1.5 border border-blue-500/30 transition-all duration-200"
                        >
                          <DashboardIcons.Assign className="w-3.5 h-3.5" />
                          Assign
                        </button>
                      )}
                    </>
                  )}

                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // Stop event from bubbling up to the card
                      onSignOut(firearm.id);
                    }}
                    className="px-3 py-1.5 rounded-md bg-red-500/20 hover:bg-red-500/30
                      text-red-400 hover:text-red-300 text-sm font-medium flex items-center gap-1.5 border border-red-500/30 transition-all duration-200"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="w-3.5 h-3.5"
                    >
                      <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                      <polyline points="16 17 21 12 16 7" />
                      <line x1="21" y1="12" x2="9" y2="12" />
                    </svg>
                    Book Out
                  </button>
                </>
              )}

              {/* Payment History button - shown for assigned or Owner type firearms */}
              {(isAssigned || firearm.storage_type === 'Owner') && (
                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Stop event from bubbling up to the card
                    setShowPaymentHistory(true);
                  }}
                  className="px-3 py-1.5 rounded-md bg-green-500/20 hover:bg-green-500/30
                    text-green-400 hover:text-green-300 text-sm font-medium flex items-center gap-1.5 border border-green-500/30 transition-all duration-200"
                >
                  <DashboardIcons.Payment className="w-3.5 h-3.5" />
                  Payment History
                </button>
              )}
            </div>

            {/* Right-side section with Delete button */}
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation(); // Stop event from bubbling up to the card
                  onDelete(firearm.id);
                }}
                className="p-1.5 rounded bg-red-500/10 hover:bg-red-500/20 text-red-400 hover:text-red-300 transition-colors"
                title="Delete Firearm"
              >
                <DashboardIcons.Delete className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Form */}
      {showPaymentForm && (
        <FirearmPaymentForm
          firearmId={firearm.id}
          clientId={
            firearm.assigned_client_details
              ? firearm.assigned_client_details.id
              : firearm.storage_type === 'Owner'
                ? firearm.client_id || '' // For Owner type, use client_id if available
                : ''
          }
          clientName={
            firearm.assigned_client_details
              ? `${firearm.assigned_client_details.first_name} ${firearm.assigned_client_details.last_name}`
              : firearm.storage_type === 'Owner'
                ? firearm.full_name // For Owner type, use full_name
                : 'Client'
          }
          assignmentId={firearm.assignment_id || ''}
          currentBalance={firearm.wallet_balance || 0}
          storageType={firearm.storage_type}
          onClose={() => setShowPaymentForm(false)}
          onSuccess={() => {
            setShowPaymentForm(false)
            if (onAddCredit) onAddCredit(firearm)
          }}
        />
      )}

      {/* Payment History */}
      {showPaymentHistory && (
        <FirearmPaymentHistory
          firearmId={firearm.id}
          clientName={
            firearm.assigned_client_details
              ? `${firearm.assigned_client_details.first_name} ${firearm.assigned_client_details.last_name}`
              : firearm.storage_type === 'Owner'
                ? firearm.full_name // For Owner type, use full_name
                : 'Client'
          }
          onClose={() => setShowPaymentHistory(false)}
        />
      )}

      {/* Return Confirmation Dialog - only shown for non-Owner type firearms */}
      {showReturnConfirm && firearm.storage_type !== 'Owner' && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-stone-800 rounded-xl shadow-xl w-full max-w-md overflow-hidden border border-orange-500/30">
            <div className="p-6">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-amber-400"
                >
                  <path d="M9 10L4 15L9 20" />
                  <path d="M20 4v7a4 4 0 0 1-4 4H4" />
                </svg>
                Return Firearm
              </h2>

              <div className="mb-6 p-4 bg-stone-700/50 rounded-lg">
                <p className="text-white mb-2">Are you sure you want to return this firearm?</p>
                <div className="text-stone-300 text-sm">
                  <p><span className="text-stone-400">Make:</span> {firearm.make}</p>
                  <p><span className="text-stone-400">Model:</span> {firearm.model}</p>
                  <p><span className="text-stone-400">Serial:</span> {firearm.serial}</p>
                  {firearm.assigned_client_details && (
                    <p><span className="text-stone-400">Assigned to:</span> {firearm.assigned_client_details.first_name} {firearm.assigned_client_details.last_name}</p>
                  )}
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => setShowReturnConfirm(false)}
                  className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-md"
                >
                  Cancel
                </button>

                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowReturnConfirm(false);
                    // Call the onAssign function which will handle the return in the parent component
                    onAssign(firearm);
                  }}
                  className="px-4 py-2 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700
                    text-white rounded-md flex items-center gap-1.5"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="w-4 h-4"
                  >
                    <path d="M9 10L4 15L9 20" />
                    <path d="M20 4v7a4 4 0 0 1-4 4H4" />
                  </svg>
                  Confirm Return
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FirearmCard
