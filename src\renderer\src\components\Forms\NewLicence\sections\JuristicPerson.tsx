import React from 'react'
import { FormSection, FormField, RadioGroup } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionWithAddressProps } from '../utils/types'

/**
 * Juristic Person section component
 *
 * This component handles the Juristic Person's Details section of the New License form.
 * It collects information about the company that the firearm is registered to.
 *
 * Placeholders used in this section:
 * - {JPNAME} - Registered company name (Fill in)
 * - {JPTN} - Trading as name (Fill in)
 * - {JPFARN} - FAR number (Fill in)
 * - {JPADD} - Postal address & Business address (Fill in)
 * - {JPPOSTAL} - Postal Code (Fill in)
 * - {JPBTN} - Business telephone number (Fill in)
 * - {JPEM} - E-mail address (Fill in)
 * - {JPFRB} - Firearm registered TO business (Fill in)
 * - {JPFRBNO} - Number of persons employed by the business to handle firearms (Fill in)
 * - {JPRPNS} - Responsible person (Name and surname) (Fill in)
 * - {JPRPIDSA} - Type of identification Responsible person (Mark with an X) for South African
 * - {JPRPIDNO} - Type of identification Responsible person (Mark with an X) for NONE South African
 * - {JPRPID} - Identity number of responsible person (Fill in)
 * - {JPRPCALL} - Cellphone number of responsible person (Fill in)
 * - {JPRPADD} - Physical address of responsible person (Fill in)
 * - {JPRPOSTAL} - Postal Code of responsible person (Fill in)
 */
const JuristicPerson: React.FC<SectionWithAddressProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Juristic Person's Details</h3>
      <FormSection
        title="Juristic Person's Details - Other Bodies"
        subtitle="Please provide details if the firearm is registered to a juristic person"
      >
        <div className="space-y-3">
          <FormField
            label="Registered Company Name"
            name="jpname"
            value={formData.jpname || ''}
            onChange={handleChange}
            placeholder="Enter registered company name"
          />

          <FormField
            label="Trading As"
            name="jptn"
            value={formData.jptn || ''}
            onChange={handleChange}
            placeholder="Enter trading as name"
          />

          <FormField
            label="FAR Number"
            name="jpfarn"
            value={formData.jpfarn || ''}
            onChange={handleChange}
            placeholder="Enter FAR number"
          />

          <AddressInput
            label="Business Address"
            value={formData.jpadd || ''}
            postalCode={formData.jppostal || ''}
            onChange={(address, postalCode) => {
              updateFormData({
                jpadd: address,
                jppostal: postalCode || ''
              })
            }}
            placeholder="Enter business address"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              label="Business Telephone Number"
              name="jpbtn"
              value={formData.jpbtn || ''}
              onChange={handleChange}
              placeholder="Enter business telephone number"
            />

            <FormField
              label="Email Address"
              name="jpem"
              value={formData.jpem || ''}
              onChange={handleChange}
              placeholder="Enter email address"
              type="email"
            />
          </div>

          <div className="border-b border-gray-700 pb-4 mb-4">
            <h4 className="text-sm font-semibold text-white mb-2">Business Information</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <FormField
                label="Firearm registered TO business"
                name="jpfrb"
                value={formData.jpfrb || ''}
                onChange={handleChange}
                placeholder="Enter details about firearm registration"
              />

              <FormField
                label="Number of persons employed to handle firearms"
                name="jpfrbno"
                value={formData.jpfrbno || ''}
                onChange={handleChange}
                placeholder="Enter number of persons"
                type="number"
              />
            </div>
          </div>

          <div className="border-b border-gray-700 pb-4 mb-4">
            <h4 className="text-sm font-semibold text-white mb-2">Responsible Person Information</h4>

            <FormField
              label="Responsible Person (Name and Surname)"
              name="jprpns"
              value={formData.jprpns || ''}
              onChange={handleChange}
              placeholder="Enter responsible person's name"
            />

            <div className="mt-2">
              <RadioGroup
                name="responsiblePersonIdType"
                value={formData.jprpidsa ? 'sa' : formData.jprpidno ? 'non' : ''}
                onChange={(value) => {
                  updateFormData({
                    jprpidsa: value === 'sa',
                    jprpidno: value === 'non'
                  })
                }}
                options={[
                  { value: 'sa', label: 'South African Citizen' },
                  { value: 'non', label: 'Non-South African Citizen' }
                ]}
                label="Type of Identification"
              />
            </div>

            <FormField
              label="Identity Number of Responsible Person"
              name="jprpid"
              value={formData.jprpid || ''}
              onChange={handleChange}
              placeholder="Enter ID number"
            />

            <FormField
              label="Cellphone Number of Responsible Person"
              name="jprpcall"
              value={formData.jprpcall || ''}
              onChange={handleChange}
              placeholder="Enter cellphone number"
            />

            <AddressInput
              label="Physical Address of Responsible Person"
              value={formData.jprpadd || ''}
              postalCode={formData.jprpostal || ''}
              onChange={(address, postalCode) => {
                updateFormData({
                  jprpadd: address,
                  jprpostal: postalCode || ''
                })
              }}
              placeholder="Enter physical address of responsible person"
            />
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default JuristicPerson
