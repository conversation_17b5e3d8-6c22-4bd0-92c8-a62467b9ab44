import React from 'react';
import { Pipeline } from '../../types/pipedrive';
import { DashboardIcons } from '../icons/DashboardIcons';

interface PipelineListProps {
  pipelines: Pipeline[];
  activePipeline: string | null;
  onSelectPipeline: (id: string) => void;
  onEditPipeline?: (pipeline: Pipeline) => void;
  onDeletePipeline?: (id: string, name: string) => void;
}

const PipelineList: React.FC<PipelineListProps> = ({
  pipelines,
  activePipeline,
  onSelectPipeline,
  onEditPipeline,
  onDeletePipeline
}) => {
  if (pipelines.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-stone-400 text-sm">No pipelines found</p>
        <p className="text-stone-500 text-xs mt-1">Create a new pipeline to get started</p>
      </div>
    );
  }

  return (
    <div className="space-y-1">
      {pipelines.map((pipeline) => (
        <div key={pipeline.id} className="flex items-center group">
          <button
            onClick={() => onSelectPipeline(pipeline.id)}
            className={`flex-1 text-left px-3 py-2 rounded-md text-sm transition-all duration-200 ${
              activePipeline === pipeline.id
                ? 'bg-gradient-to-r from-orange-500/20 to-orange-600/20 text-orange-400 font-medium shadow-sm'
                : 'text-stone-300 hover:bg-stone-700/50 hover:text-white'
            }`}
          >
            {pipeline.name}
          </button>

          <div className="flex space-x-1">
            {onEditPipeline && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEditPipeline(pipeline);
                }}
                className="p-1.5 rounded opacity-0 group-hover:opacity-100 text-stone-400 hover:text-orange-400 hover:bg-orange-500/10 transition-all duration-200"
                title="Edit Pipeline"
              >
                <DashboardIcons.Edit className="w-3.5 h-3.5" />
              </button>
            )}

            {onDeletePipeline && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeletePipeline(pipeline.id, pipeline.name);
                }}
                className="p-1.5 rounded opacity-0 group-hover:opacity-100 text-stone-400 hover:text-red-400 hover:bg-red-500/10 transition-all duration-200"
                title="Delete Pipeline"
              >
                <DashboardIcons.Delete className="w-3.5 h-3.5" />
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default PipelineList;
