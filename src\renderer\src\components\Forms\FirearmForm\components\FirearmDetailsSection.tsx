import React from 'react';
import { FormField } from '../../../FormComponents';
import { DashboardIcons } from '../../../icons/DashboardIcons';
import { Firearm } from '../../../../types/firearm';

interface FirearmDetailsSectionProps {
  formData: Partial<Firearm>;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

export const FirearmDetailsSection: React.FC<FirearmDetailsSectionProps> = ({
  formData,
  onChange
}) => {
  return (
    <div>
      <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
        <DashboardIcons.Firearm className="w-3.5 h-3.5 text-orange-400" />
        Firearm Details
      </h2>
      <div className="grid grid-cols-3 gap-2">
        <FormField
          label="Make"
          name="make"
          value={formData.make || ''}
          onChange={onChange}
          required
          inputClassName="premium-field"
        />
        <FormField
          label="Model"
          name="model"
          value={formData.model || ''}
          onChange={onChange}
          required
          inputClassName="premium-field"
        />
        <FormField
          label="Serial #"
          name="serial"
          value={formData.serial || ''}
          onChange={onChange}
          required
          inputClassName="premium-field"
        />
      </div>
    </div>
  );
};
