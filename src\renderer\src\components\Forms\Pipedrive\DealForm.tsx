import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Deal } from '../../../types/pipedrive';
import { getOrInitSupabase } from '../../../lib/supabase';
import { debounce } from 'lodash';
import { DashboardIcons } from '../../icons/DashboardIcons';
import DocumentUpload from '../../Pipedrive/DocumentUpload';
import DocumentList, { DocumentListRef } from '../../Pipedrive/DocumentList';
import NotesDisplay from '../../Pipedrive/NotesDisplay';

// Simple client type for search results
interface SimpleClient {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  id_number: string;
}

interface UnifiedDealFormProps {
  mode: 'create' | 'edit';
  deal?: Deal; // Required for edit mode
  onSubmit: (formData: any) => Promise<{ id: string } | undefined> | void;
  onCancel: () => void;
  onUploadDocument?: (dealId: string, file: File) => Promise<void>;
}

const UnifiedDealForm: React.FC<UnifiedDealFormProps> = ({
  mode,
  deal,
  onSubmit,
  onCancel,
  onUploadDocument
}) => {
  // Form state
  const [formData, setFormData] = useState({
    clientId: '',
    title: '',
    notes: ''
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [createdDealId, setCreatedDealId] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [showDocuments, setShowDocuments] = useState(false);
  const [showNotesEditor, setShowNotesEditor] = useState(false);
  const [newNote, setNewNote] = useState('');

  // Client search state (only for create mode)
  const [clients, setClients] = useState<SimpleClient[]>([]);
  const [clientSearchTerm, setClientSearchTerm] = useState('');
  const [selectedClient, setSelectedClient] = useState<SimpleClient | null>(null);
  const [isLoadingClients, setIsLoadingClients] = useState(false);

  // Refs
  const filesToUploadRef = useRef<File[]>([]);
  const documentListRef = useRef<DocumentListRef>(null);

  // Initialize form with deal data in edit mode
  useEffect(() => {
    if (mode === 'edit' && deal) {
      setFormData({
        clientId: deal.client_id || '',
        title: deal.title,
        notes: deal.notes || ''
      });
    }
  }, [mode, deal]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Client search functionality (only for create mode)
  const searchClients = useCallback(
    debounce(async (searchTerm: string) => {
      if (!searchTerm.trim() || searchTerm.length < 3) {
        setClients([]);
        return;
      }

      try {
        setIsLoadingClients(true);
        const supabase = await getOrInitSupabase();

        // Search by name or ID number with improved search logic
        let query = supabase
          .from('clients')
          .select('id, first_name, last_name, email, phone, id_number');

        const trimmedTerm = searchTerm.trim();
        const terms = trimmedTerm.split(' ').filter(t => t.length > 0);

        // Check if it's an ID number (only digits)
        if (/^\d+$/.test(trimmedTerm)) {
          query = query.ilike('id_number', `%${trimmedTerm}%`);
        }
        // If multiple terms (likely a full name)
        else if (terms.length > 1) {
          // Try different combinations for first name and last name
          const possibleFirstName = terms[0];
          const possibleLastName = terms[terms.length - 1];

          // First name + last name pattern (most common case)
          query = query.or(
            `and(first_name.ilike.%${possibleFirstName}%,last_name.ilike.%${possibleLastName}%),` +
            // Also try the reverse in case names are entered in reverse order
            `and(first_name.ilike.%${possibleLastName}%,last_name.ilike.%${possibleFirstName}%),` +
            // Also check if the full search term is in either field (for compound names)
            `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%`
          );
        }
        // Single term (could be first name, last name, or partial ID)
        else {
          query = query.or(
            `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%,id_number.ilike.%${trimmedTerm}%`
          );
        }

        const { data, error } = await query.limit(5);

        if (error) throw error;
        setClients(data || []);
      } catch (err) {
        console.error('Error searching clients:', err);
      } finally {
        setIsLoadingClients(false);
      }
    }, 300),
    []
  );

  // Handle client search input change
  const handleClientSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setClientSearchTerm(value);
    searchClients(value);
  };

  // Handle client selection
  const handleSelectClient = (client: SimpleClient) => {
    setSelectedClient(client);
    setFormData(prev => ({ ...prev, clientId: client.id }));
    setClientSearchTerm(`${client.first_name} ${client.last_name} (${client.id_number})`);
    setClients([]);
  };

  // Handle adding a new note (edit mode)
  const handleAddNote = () => {
    if (!newNote.trim()) return;

    const dateStamp = formatDateStamp();
    const stampedNote = `[${dateStamp}] ${newNote.trim()}`;

    // Append the new note to existing notes
    const updatedNotes = formData.notes
      ? `${formData.notes}\n\n${stampedNote}`
      : stampedNote;

    setFormData(prev => ({ ...prev, notes: updatedNotes }));
    setNewNote(''); // Clear the new note input
  };

  // Handle document upload
  const handleUploadDocument = async (dealId: string, file: File) => {
    if (!onUploadDocument) return;

    try {
      await onUploadDocument(dealId, file);
      setUploadSuccess(true);

      // Refresh the document list after successful upload
      if (documentListRef.current) {
        documentListRef.current.refreshDocuments();
      }

      return true;
    } catch (error) {
      console.error('Error uploading document:', error);
      setError(error instanceof Error ? error.message : 'Failed to upload document');
      return false;
    }
  };

  // Upload all queued files
  const uploadQueuedFiles = async (dealId: string) => {
    if (!onUploadDocument || filesToUploadRef.current.length === 0) return;

    for (const file of filesToUploadRef.current) {
      await handleUploadDocument(dealId, file);
    }

    // Clear the queue after uploading
    filesToUploadRef.current = [];
  };

  // Format the current date for the date stamp
  const formatDateStamp = () => {
    const now = new Date();
    return now.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Add date stamp to notes
  const addDateStampToNotes = (notes: string, previousNotes?: string | null) => {
    if (!notes.trim()) return null;

    // For edit mode, only add date stamp if notes have changed
    if (mode === 'edit' && previousNotes === notes) {
      return notes;
    }

    // For create mode or if notes have changed in edit mode
    const dateStamp = formatDateStamp();

    // If in edit mode and we're adding to existing notes
    if (mode === 'edit' && previousNotes && notes !== previousNotes) {
      // Check if the notes are completely different or just an addition
      if (!notes.includes(previousNotes)) {
        // Notes were completely changed, add date stamp
        return `[${dateStamp}] ${notes}`;
      } else {
        // Notes were appended to, don't add another date stamp
        return notes;
      }
    }

    // Check if the notes already have a date stamp
    const dateStampRegex = /^\[.*?\]/;
    if (dateStampRegex.test(notes.trim())) {
      // Notes already have a date stamp, don't add another one
      return notes;
    }

    // For create mode or completely new notes in edit mode
    return `[${dateStamp}] ${notes}`;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      setError('Please enter a deal title');
      return;
    }

    if (mode === 'create' && !formData.clientId) {
      setError('Please select a client');
      return;
    }

    try {
      setLoading(true);

      if (mode === 'create') {
        // Create mode - add date stamp to notes
        const notesWithDateStamp = addDateStampToNotes(formData.notes);

        // Create the deal
        const result = await onSubmit({
          clientId: formData.clientId,
          title: formData.title,
          notes: notesWithDateStamp
        }) as { id: string } | undefined;

        // If we have a deal ID returned and files to upload
        if (result?.id && filesToUploadRef.current.length > 0 && onUploadDocument) {
          setCreatedDealId(result.id);
          await uploadQueuedFiles(result.id);
        } else if (result?.id) {
          setCreatedDealId(result.id);
        } else {
          // If no deal ID returned, close the form
          onCancel();
        }
      } else if (mode === 'edit' && deal) {
        // Edit mode - add date stamp to notes if they've changed
        const notesWithDateStamp = addDateStampToNotes(formData.notes, deal.notes);

        await onSubmit({
          dealId: deal.id,
          title: formData.title,
          notes: notesWithDateStamp
        });

        // Show document upload section after successful update
        setShowDocuments(true);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Render the form
  return (
    <>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
          <h2 className="text-lg font-semibold text-white mb-4">
            {mode === 'create' ? 'Create New Deal' : 'Edit Deal'}
          </h2>

          {error && (
            <div className="mb-4 p-3 bg-red-900/30 border border-red-800 rounded-md">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={(e) => {
            // Only submit the form if we're not in document management mode
            if ((mode === 'create' && createdDealId) || (mode === 'edit' && showDocuments)) {
              e.preventDefault();
            } else {
              handleSubmit(e);
            }
          }}>
            {/* Client Section - Different for create and edit modes */}
            {mode === 'create' ? (
              <div className="mb-4">
                <label className="block text-sm font-medium text-stone-300 mb-1">
                  Client
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={clientSearchTerm}
                    onChange={handleClientSearchChange}
                    placeholder="Search by name or ID number"
                    className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                  />
                  {isLoadingClients && (
                    <div className="absolute right-3 top-2">
                      <DashboardIcons.Spinner className="w-5 h-5 text-stone-400 animate-spin" />
                    </div>
                  )}

                  {/* Client search results dropdown */}
                  {clients.length > 0 && (
                    <div className="absolute z-10 mt-1 w-full bg-stone-700 border border-stone-600 rounded-md shadow-lg max-h-60 overflow-auto">
                      {clients.map(client => (
                        <div
                          key={client.id}
                          onClick={() => handleSelectClient(client)}
                          className="px-3 py-2 hover:bg-stone-600 cursor-pointer text-white text-sm"
                        >
                          <div className="font-medium">
                            {client.first_name} {client.last_name}
                          </div>
                          <div className="text-xs text-stone-400">
                            ID: {client.id_number} • {client.email || 'No email'} • {client.phone || 'No phone'}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                {selectedClient && (
                  <div className="mt-2 p-2 bg-stone-700/50 rounded-md">
                    <div className="text-sm text-white">
                      Selected: {selectedClient.first_name} {selectedClient.last_name}
                    </div>
                    <div className="text-xs text-stone-400">
                      ID: {selectedClient.id_number}
                    </div>
                  </div>
                )}
              </div>
            ) : deal?.client ? (
              <div className="mb-4">
                <label className="block text-sm font-medium text-stone-300 mb-1">
                  Client
                </label>
                <div className="p-3 bg-stone-700/50 rounded-md">
                  <div className="text-sm text-white font-medium mb-1">
                    {deal.client.first_name} {deal.client.last_name}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-xs text-stone-400">
                      <span className="text-stone-500">ID:</span> {deal.client.id_number}
                    </div>
                    <div className="text-xs text-stone-400">
                      <span className="text-stone-500">Phone:</span> {deal.client.phone}
                    </div>
                    <div className="text-xs text-stone-400 col-span-2 truncate">
                      <span className="text-stone-500">Email:</span> {deal.client.email}
                    </div>
                  </div>
                </div>
              </div>
            ) : null}

            {/* Deal Title - Common to both modes */}
            <div className="mb-4">
              <label htmlFor="title" className="block text-sm font-medium text-stone-300 mb-1">
                Deal Title
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                required
              />
            </div>

            {/* Notes Section - Different for create and edit modes */}
            {mode === 'create' ? (
              <div className="mb-4">
                <label htmlFor="notes" className="block text-sm font-medium text-stone-300 mb-1">
                  Notes (Optional)
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Add notes here (a date stamp will be added automatically)"
                  className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                />
              </div>
            ) : (
              <div className="mb-4">
                <div className="flex justify-between items-center mb-1">
                  <label className="block text-sm font-medium text-stone-300">
                    Notes History
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowNotesEditor(true)}
                    className="text-xs text-orange-400 hover:text-orange-300"
                  >
                    Edit All Notes
                  </button>
                </div>
                <div className="bg-stone-700/50 rounded-md p-3 mb-3 max-h-32 overflow-y-auto custom-scrollbar">
                  <NotesDisplay notes={formData.notes} />
                </div>
                <div className="flex">
                  <input
                    type="text"
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newNote.trim()) {
                        e.preventDefault();
                        handleAddNote();
                      }
                    }}
                    placeholder="Add a new note..."
                    className="flex-1 bg-stone-700 border border-stone-600 rounded-l-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
                  />
                  <button
                    type="button"
                    onClick={handleAddNote}
                    disabled={!newNote.trim()}
                    className="bg-orange-500 hover:bg-orange-600 disabled:bg-stone-600 disabled:text-stone-400 text-white px-3 py-2 rounded-r-md transition-colors"
                  >
                    Add
                  </button>
                </div>
                <p className="text-xs text-stone-500 mt-1">
                  Press Enter to add a note. A date stamp will be added automatically.
                </p>

                {/* Hidden input for notes value */}
                <input
                  type="hidden"
                  id="notes"
                  name="notes"
                  value={formData.notes}
                />
              </div>
            )}

            {/* Document Upload Section - Different based on state */}
            {mode === 'create' && createdDealId ? (
              <div className="mb-6">
                <DocumentUpload
                  dealId={createdDealId}
                  onUpload={handleUploadDocument}
                />
                {uploadSuccess && (
                  <p className="mt-2 text-xs text-green-400 text-center">
                    Document uploaded successfully!
                  </p>
                )}

                {/* Document List */}
                <DocumentList ref={documentListRef} dealId={createdDealId} />

                <div className="mt-4 flex justify-end">
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
                  >
                    Done
                  </button>
                </div>
              </div>
            ) : mode === 'edit' && showDocuments && deal ? (
              <div className="mb-6">
                <DocumentUpload
                  dealId={deal.id}
                  onUpload={handleUploadDocument}
                />
                {uploadSuccess && (
                  <p className="mt-2 text-xs text-green-400 text-center">
                    Document uploaded successfully!
                  </p>
                )}

                {/* Document List with delete functionality */}
                <DocumentList ref={documentListRef} dealId={deal.id} />

                <div className="mt-4 flex justify-end">
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
                  >
                    Done
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="mb-6">
                  {mode === 'create' ? (
                    <>
                      <label className="block text-sm font-medium text-stone-300 mb-1">
                        Documents (Optional)
                      </label>
                      <div className="border border-dashed rounded-md p-3 text-center transition-colors border-stone-600/50 hover:bg-stone-700/10">
                        <p className="text-xs text-stone-300 mb-1">
                          You'll be able to upload documents after creating the deal
                        </p>
                        <p className="text-xs text-stone-500">
                          PDF, Word, Excel, or image files
                        </p>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex justify-between items-center mb-1">
                        <label className="block text-sm font-medium text-stone-300">
                          Documents
                        </label>
                        <button
                          type="button"
                          onClick={() => setShowDocuments(true)}
                          className="text-xs text-orange-400 hover:text-orange-300"
                        >
                          Manage Documents
                        </button>
                      </div>
                    </>
                  )}
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onCancel}
                    className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
                    disabled={loading}
                  >
                    {loading ? (
                      <span className="flex items-center">
                        <DashboardIcons.Spinner className="w-4 h-4 animate-spin mr-2" />
                        {mode === 'create' ? 'Creating...' : 'Saving...'}
                      </span>
                    ) : (
                      mode === 'create' ? 'Create Deal' : 'Save Changes'
                    )}
                  </button>
                </div>
              </>
            )}
          </form>
        </div>
      </div>

      {/* Notes Editor Modal - Only for edit mode */}
      {mode === 'edit' && showNotesEditor && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Edit Notes</h2>

            <div className="mb-4">
              <p className="text-xs text-stone-400 mb-2">
                Edit the notes below. Date stamps will be preserved.
              </p>
              <textarea
                value={formData.notes}
                onChange={handleChange}
                name="notes"
                rows={10}
                className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowNotesEditor(false)}
                className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => setShowNotesEditor(false)}
                className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
              >
                Save Notes
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default UnifiedDealForm;
