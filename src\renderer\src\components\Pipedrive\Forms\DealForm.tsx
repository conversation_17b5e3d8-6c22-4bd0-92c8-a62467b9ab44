import React, { useState, useCallback, useRef } from 'react';
import { getOrInitSupabase } from '../../../lib/supabase';
import { debounce } from 'lodash';
import { DashboardIcons } from '../../icons/DashboardIcons';
import FormField from '../../FormComponents/FormField';
import DocumentUpload from '../../Pipedrive/DocumentUpload';
import DocumentList, { DocumentListRef } from '../DocumentList';

// Simple client type for search results
interface SimpleClient {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  id_number: string;
}

interface DealFormProps {
  stageId: string;
  onSubmit: (formData: { clientId: string; title: string; notes: string | null }) => Promise<{ id: string } | undefined>;
  onCancel: () => void;
  onUploadDocument?: (dealId: string, file: File) => Promise<void>;
}

const DealForm: React.FC<DealFormProps> = ({ onSubmit, onCancel, onUploadDocument }) => {
  const [formData, setFormData] = useState({
    clientId: '',
    title: '',
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [createdDealId, setCreatedDealId] = useState<string | null>(null);
  const [, setUploadEnabled] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  // Ref to store files to upload after deal creation
  const filesToUploadRef = useRef<File[]>([]);

  // Ref for the document list component
  const documentListRef = useRef<DocumentListRef>(null);

  // Client search state
  const [clients, setClients] = useState<SimpleClient[]>([]);
  const [clientSearchTerm, setClientSearchTerm] = useState('');
  const [selectedClient, setSelectedClient] = useState<SimpleClient | null>(null);
  const [isLoadingClients, setIsLoadingClients] = useState(false);

  // Fetch clients with debounce for search
  const fetchClients = useCallback(
    debounce(async (searchTerm: string) => {
      try {
        setIsLoadingClients(true);
        const supabase = await getOrInitSupabase();
        let query = supabase.from('clients').select('id, first_name, last_name, email, phone, id_number');

        if (searchTerm) {
          query = query.or(
            `first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,id_number.ilike.%${searchTerm}%`
          );
        }

        const { data, error } = await query.limit(10);

        if (error) throw error;
        setClients(data || []);
      } catch (error) {
        console.error('Error fetching clients:', error);
        setError('Failed to load clients. Please try again.');
      } finally {
        setIsLoadingClients(false);
      }
    }, 300),
    []
  );

  // Handle client selection
  const handleClientSelect = (client: SimpleClient) => {
    setSelectedClient(client);
    setFormData(prev => ({
      ...prev,
      clientId: client.id
    }));
    setClientSearchTerm('');
  };

  // Handle client search change
  const handleClientSearchChange = (searchTerm: string) => {
    setClientSearchTerm(searchTerm);
    fetchClients(searchTerm);
  };

  // Handle client removal
  const handleClientRemove = () => {
    setSelectedClient(null);
    setFormData(prev => ({ ...prev, clientId: '' }));
  };

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle file selection for upload

  // Handle document upload
  const handleUploadDocument = async (dealId: string, file: File) => {
    if (!onUploadDocument) return;

    try {
      await onUploadDocument(dealId, file);
      setUploadSuccess(true);

      // Refresh the document list after successful upload
      if (documentListRef.current) {
        documentListRef.current.refreshDocuments();
      }

      return true;
    } catch (error) {
      console.error('Error uploading document:', error);
      setError(error instanceof Error ? error.message : 'Failed to upload document');
      return false;
    }
  };

  // Upload all queued files
  const uploadQueuedFiles = async (dealId: string) => {
    if (!onUploadDocument || filesToUploadRef.current.length === 0) return;

    for (const file of filesToUploadRef.current) {
      await handleUploadDocument(dealId, file);
    }

    // Clear the queue after uploading
    filesToUploadRef.current = [];
  };

  // Format the current date for the date stamp
  const formatDateStamp = () => {
    const now = new Date();
    return now.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Add date stamp to notes
  const addDateStampToNotes = (notes: string) => {
    if (!notes.trim()) return null;

    // Check if the notes already have a date stamp
    const dateStampRegex = /^\[.*?\]/;
    if (dateStampRegex.test(notes.trim())) {
      // Notes already have a date stamp, don't add another one
      return notes;
    }
    const dateStamp = formatDateStamp();
    return `[${dateStamp}] ${notes}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.clientId) {
      setError('Please select a client');
      return;
    }

    try {
      setLoading(true);

      // Add date stamp to notes
      const notesWithDateStamp = addDateStampToNotes(formData.notes);

      // Create the deal
      const result = await onSubmit({
        clientId: formData.clientId,
        title: formData.title,
        notes: notesWithDateStamp
      });

      // If we have a deal ID returned and files to upload
      if (result?.id && filesToUploadRef.current.length > 0 && onUploadDocument) {
        setCreatedDealId(result.id);
        setUploadEnabled(true);
        await uploadQueuedFiles(result.id);
      } else {
        // If no files to upload or no upload handler, close the form
        onCancel();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-stone-800 rounded-lg shadow-xl w-full max-w-md p-6">
        <h2 className="text-lg font-semibold text-white mb-4">Create New Deal</h2>

        {error && (
          <div className="mb-4 p-3 bg-red-900/30 border border-red-800 rounded-md">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={(e) => {
            // Only submit the form if we're not in document management mode
            if (createdDealId) {
              e.preventDefault();
            } else {
              handleSubmit(e);
            }
          }}>
          {/* Client Selection Section */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-stone-300 mb-1">
              Client
            </label>

            {selectedClient ? (
              <div className="mb-2 p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                <div className="flex justify-between items-start">
                  <div className="space-y-2 w-full">
                    {/* Client Full Name */}
                    <div>
                      <h3 className="font-semibold text-white text-sm">
                        {selectedClient.first_name.toUpperCase()} {selectedClient.last_name.toUpperCase()}
                      </h3>
                    </div>

                    {/* Contact Number */}
                    <div className="flex">
                      <span className="text-stone-400 text-xs w-20">Contact:</span>
                      <a
                        href={`tel:${selectedClient.phone}`}
                        className="text-orange-400 hover:text-orange-300 text-xs"
                      >
                        {selectedClient.phone}
                      </a>
                    </div>

                    {/* Email Address */}
                    <div className="flex">
                      <span className="text-stone-400 text-xs w-20">Email:</span>
                      <a
                        href={`mailto:${selectedClient.email}`}
                        className="text-orange-400 hover:text-orange-300 text-xs truncate"
                      >
                        {selectedClient.email}
                      </a>
                    </div>

                    {/* ID Number (for reference) */}
                    <div className="flex">
                      <span className="text-stone-400 text-xs w-20">ID Number:</span>
                      <span className="text-stone-300 text-xs">{selectedClient.id_number}</span>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={handleClientRemove}
                    className="text-orange-400 hover:text-orange-300 ml-2"
                  >
                    <DashboardIcons.Close className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ) : (
              <div>
                <FormField
                  label=""
                  name="client-search"
                  value={clientSearchTerm}
                  onChange={(e) => handleClientSearchChange(e.target.value)}
                  placeholder="Search client by name or ID number"
                />

                {isLoadingClients && (
                  <div className="flex items-center justify-center p-1 mt-1">
                    <DashboardIcons.Spinner className="w-4 h-4 animate-spin text-orange-500" />
                    <span className="ml-1 text-stone-400 text-xs">Searching clients...</span>
                  </div>
                )}

                {clients.length > 0 && clientSearchTerm && (
                  <div className="mt-1 bg-stone-700/50 rounded-lg border border-stone-600/50 max-h-48 overflow-y-auto">
                    {clients.map(client => (
                      <div
                        key={client.id}
                        onClick={() => handleClientSelect(client)}
                        className="p-2 hover:bg-orange-500/20 cursor-pointer border-b border-stone-600/30 last:border-0 transition-colors"
                      >
                        <p className="text-white font-medium text-xs">
                          {client.first_name.toUpperCase()} {client.last_name.toUpperCase()}
                        </p>
                        <div className="flex">
                          <span className="text-stone-400 text-xs w-16">Contact:</span>
                          <span className="text-stone-300 text-xs">{client.phone}</span>
                        </div>
                        <div className="flex">
                          <span className="text-stone-400 text-xs w-16">Email:</span>
                          <span className="text-stone-300 text-xs truncate">{client.email}</span>
                        </div>
                        <div className="flex">
                          <span className="text-stone-400 text-xs w-16">ID:</span>
                          <span className="text-stone-300 text-xs">{client.id_number}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {clientSearchTerm && clients.length === 0 && !isLoadingClients && (
                  <p className="mt-1 text-stone-400 text-xs">No clients found. Try a different search term.</p>
                )}
              </div>
            )}
          </div>

          <div className="mb-4">
            <label htmlFor="title" className="block text-sm font-medium text-stone-300 mb-1">
              Deal Title
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
              required
            />
          </div>

          <div className="mb-4">
            <label htmlFor="notes" className="block text-sm font-medium text-stone-300 mb-1">
              Notes (Optional)
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              placeholder="Add notes here (a date stamp will be added automatically)"
              className="w-full bg-stone-700 border border-stone-600 rounded-md px-3 py-2 text-white text-sm focus:outline-none focus:ring-1 focus:ring-orange-500"
            />
          </div>

          {/* Document Upload Section */}
          {createdDealId ? (
            <div className="mb-6">
              <DocumentUpload
                dealId={createdDealId}
                onUpload={handleUploadDocument}
              />
              {uploadSuccess && (
                <p className="mt-2 text-xs text-green-400 text-center">
                  Document uploaded successfully!
                </p>
              )}

              {/* Document List */}
              <DocumentList ref={documentListRef} dealId={createdDealId} />

              <div className="mt-4 flex justify-end">
                <button
                  type="button"
                  onClick={onCancel}
                  className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
                >
                  Done
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="mb-6">
                <label className="block text-sm font-medium text-stone-300 mb-1">
                  Documents (Optional)
                </label>
                <div className="border border-dashed rounded-md p-3 text-center transition-colors border-stone-600/50 hover:bg-stone-700/10">
                  <p className="text-xs text-stone-300 mb-1">
                    You'll be able to upload documents after creating the deal
                  </p>
                  <p className="text-xs text-stone-500">
                    PDF, Word, Excel, or image files
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={onCancel}
                  className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-stone-300 rounded-md text-sm transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm transition-colors"
                  disabled={loading}
                >
                  {loading ? (
                    <span className="flex items-center">
                      <DashboardIcons.Spinner className="w-4 h-4 animate-spin mr-2" />
                      Creating...
                    </span>
                  ) : (
                    'Create Deal'
                  )}
                </button>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default DealForm;
