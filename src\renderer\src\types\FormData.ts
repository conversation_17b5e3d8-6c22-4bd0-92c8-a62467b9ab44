// FormData interface for document templates

export interface FormData {
  mainHF: boolean
  addHF: boolean
  s13: boolean
  s15: boolean
  s16: boolean
  s20: boolean
  s20a: boolean
  s20b: boolean
  s20c: boolean
  // Personal Info
  fullName: string
  firstName: string
  lastName: string
  phoneNumber: string
  email: string
  physicalAddress: string
  idNumber: string
  postalCode?: string
  houseUnitNumber?: string

  // Professional Info
  companyName: string
  tradeProfession: string
  workAddress: string
  workPostalCode: string
  workNumber: string
  workHouseUnitNumber?: string

  // Current Owner (Private Owner) Fields
  pcoa?: boolean  // Private owner
  pcob?: boolean  // Firearm dealer
  pcoc?: boolean  // Company
  pcoe?: boolean  // Estate
  cell?: string   // Cell phone for current owner
  workC?: string  // Work number for current owner
  address?: string // Address for current owner
  passport?: string // Passport number
  afhy?: boolean  // Additional firearm licence holders - Yes
  afhn?: boolean  // Additional firearm licence holders - No

  // Firearm Dealer Fields
  fdrcn?: string  // Registered company name
  fdtas?: string  // Trading as name
  fdfarn?: string // FAR number
  fdbadre?: string // Business address
  fdpostal?: string // Postal code
  fdbcall?: string // Business telephone
  fdbmail?: string // Business email
  fdrpns?: string  // Responsible person name/surname
  fdrpidsa?: boolean // Responsible person SA ID
  fdrpidno?: boolean // Responsible person non-SA ID
  fdrpid?: string   // Responsible person ID number
  fdrpcall?: string // Responsible person cell
  fdrpadd?: string  // Responsible person address
  fdrpostal?: string // Responsible person postal code

  // Firearm Details
  section?: string
  make?: string
  model?: string
  serialNumber?: string
  caliber?: string

  // Executor Info
  executorFullName?: string
  executorNumber?: string
  executorPhysicalAddress?: string

  // Firearm Components
  barrelSerialNumber?: string
  barrelMake?: string
  frameSerialNumber?: string
  frameMake?: string
  receiverSerialNumber?: string
  receiverMake?: string
  engravedDetails?: string

  // E350 Information
  periodDateFrom?: string
  receiverName?: string
  receiverId?: string
  dateReceivedFrom?: string

  // SAPS Inspection Report
  representativeName?: string
  representativeId?: string
  clientName?: string
  clientId?: string
  otherFirearmType?: boolean
  otherFirearmTypeDetails?: string
  bolt?: boolean
  breakneck?: boolean
  pump?: boolean
  lever?: boolean
  cylinder?: boolean
  fallingBlock?: boolean
  cappingBreechLoader?: boolean
  otherActionTypeChecked?: boolean
  otherActionTypeDetails?: string
  engravedMake?: string
  comments?: string
  countryOfOrigin?: string
  reportDate?: string

  // Competency Form
  pistol?: boolean
  rifle?: boolean
  shotgun?: boolean
  revolver?: boolean
  selfLoading?: boolean
  combination?: boolean
  carbine?: boolean

  // Applicant Particulars
  saId: boolean
  fId: boolean
  permRes: boolean
  initials?: string

  // Action Type
  actionType?: string
  semiAutomatic?: boolean
  automatic?: boolean
  manual?: boolean
  otherActionType?: string
  age?: string
  sexM: boolean
  sexF: boolean
  birthDate?: string
  houseType?: string

  // Marital Status
  singles: boolean
  married: boolean
  divorced: boolean
  widower: boolean
  widow: boolean
  maritalStatus: 'single' | 'married' | 'divorced' | 'widower' | 'widow' | '';

  // Spouse Particulars
  spouseId?: boolean
  spousePort?: boolean
  spouseIdNo?: string
  spousePassN?: string
  spouseIdType?: 'spouseId' | 'spousePort' | 'none' | '';

  // Training Institution
  nameInst?: string
  serialCert?: string
  certIssueDate?: string

  // Offense information
  offenseYes?: boolean
  offenseNo?: boolean
  policeStation?: string
  caseNumber?: string
  charge?: string
  outcomeVerdict?: string
  offenseDetails?: string

  // Pending cases
  pendingCaseYes?: boolean
  pendingCaseNo?: boolean
  pendingCasePoliceStation?: string
  pendingCasePoliceStationV2?: string
  pendingCaseCaseNumber?: string
  pendingCaseCaseNumberV2?: string
  pendingCaseOffence?: string
  pendingCaseOffenceV2?: string
  pendingCaseCharge?: string
  pendingCaseOutcome?: string

  // Lost/Stolen firearms
  lostStolenYes?: boolean
  lostStolenNo?: boolean
  lostStolenPoliceStation?: string
  lostStolenPoliceStationV2?: string
  lostStolenCaseNumber?: string
  lostStolenCaseNumberV2?: string
  lostStolenCircumstances?: string
  lostStolenCircumstancesV2?: string
  lostStolenFirearmDetails?: string
  lostStolenFirearmDetailsV2?: string

  // Investigation
  investigationYes?: boolean
  investigationNo?: boolean
  investigationPoliceStation?: string
  investigationCaseNumber?: string
  investigationCharge?: string
  investigationOutcome?: string

  // Declared unfit
  declaredUnfitYes?: boolean
  declaredUnfitNo?: boolean
  declaredUnfitPoliceStation?: string
  declaredUnfitCaseNumber?: string
  declaredUnfitCharge?: string
  declaredUnfitDate?: string
  declaredUnfitPeriod?: string

  // Confiscated
  confiscatedYes?: boolean
  confiscatedNo?: boolean
  confiscatedPoliceStation?: string
  confiscatedCaseNumber?: string
  confiscatedCircumstances?: string
  confiscatedOutcome?: string

  // Protection allegations
  protectionAllegationsYes?: boolean
  protectionAllegationsNo?: boolean
  protectionAllegationsDetails?: string

  // Denied Firearm License/Permit
  deniedFirearmYes?: boolean
  deniedFirearmNo?: boolean
  deniedFirearmReason?: string
  deniedFirearmPoliceStation?: string
  deniedFirearmCaseNumber?: string

  // Under Age of 21
  conductBusiness?: boolean
  gainfullyEmployed?: boolean
  dedicatedHunter?: boolean
  dedicatedSportPersonal?: boolean
  privateCollector?: boolean
  publicCollector?: boolean
  otherUnderAge21?: boolean
  otherDetails?: string
  specifyDetails?: string

  // Competency Certificate Details
  certNumber?: string
  certIssueDate1?: string
  certExpiryDate?: string

  // Additional certificate types
  handgunRifleCertNumber?: string
  handgunRifleCertIssueDate?: string
  handgunRifleCertExpiryDate?: string

  rifleCertNumber?: string
  rifleCertIssueDate?: string
  rifleCertExpiryDate?: string

  shotgunCertNumber?: string
  shotgunCertIssueDate?: string
  shotgunCertExpiryDate?: string

  // Competency Certificate Types (detailed)
  handgunType?: boolean
  handgunRifleType?: boolean
  rifleType?: boolean
  shotgunType?: boolean
  shotgunHandgunType?: boolean
  rifleShotgunType?: boolean
  handgunRifleShotgunType?: boolean
  handMachineCarbineType?: boolean
  handgunHandMachineType?: boolean
  handgunRifleHandMachineType?: boolean
  handgunRifleShotgunHandMachineType?: boolean
  rifleHandMachineType?: boolean
  rifleShotgunHandMachineType?: boolean
  shotgunHandMachineType?: boolean

  // Certificate numbers by type
  handgunCertNumber?: string
  handgunCertIssueDate?: string
  handgunCertExpiryDate?: string

  shotgunHandgunCertNumber?: string
  shotgunHandgunCertIssueDate?: string
  shotgunHandgunCertExpiryDate?: string

  rifleShotgunCertNumber?: string
  rifleShotgunCertIssueDate?: string
  rifleShotgunCertExpiryDate?: string

  handgunRifleShotgunCertNumber?: string
  handgunRifleShotgunCertIssueDate?: string
  handgunRifleShotgunCertExpiryDate?: string

  handMachineCarbineCertNumber?: string
  handMachineCarbineCertIssueDate?: string
  handMachineCarbineCertExpiryDate?: string

  handgunHandMachineCertNumber?: string
  handgunHandMachineCertIssueDate?: string
  handgunHandMachineCertExpiryDate?: string

  handgunRifleHandMachineCertNumber?: string
  handgunRifleHandMachineCertIssueDate?: string
  handgunRifleHandMachineCertExpiryDate?: string

  handgunRifleShotgunHandMachineCertNumber?: string
  handgunRifleShotgunHandMachineCertIssueDate?: string
  handgunRifleShotgunHandMachineCertExpiryDate?: string

  rifleHandMachineCertNumber?: string
  rifleHandMachineCertIssueDate?: string
  rifleHandMachineCertExpiryDate?: string

  rifleShotgunHandMachineCertNumber?: string
  rifleShotgunHandMachineCertIssueDate?: string
  rifleShotgunHandMachineCertExpiryDate?: string

  shotgunHandMachineCertNumber?: string
  shotgunHandMachineCertIssueDate?: string
  shotgunHandMachineCertExpiryDate?: string

  // Competency Expiry Status
  competencyExpiry90DaysBeforeYes?: boolean
  competencyExpiry90DaysBeforeNo?: boolean
  competencyExpiry90DaysBeforeReason?: string

  afterExpiryYes?: boolean
  afterExpiryNo?: boolean
  afterExpiryReason?: string

  // Competency type
  competencyType?: 'trade' | 'possess' | '';

  // Citizen type
  citizenType: 'saId' | 'fId' | '';

  // Template information
  templateUrl?: string;
  templateName?: string;
  documentProcessed?: boolean;
}

// New interface for Further Competency form
export interface FurtherCompetencyData extends FormData {
  // Previous/New Competency Applicant Details
  competencyCertificateType?: string;
  competencyCertificateNumber?: string;
  competencyIssueDate?: string;
  competencyExpiryDate?: string;

  // Member of Association
  isMemberOfAssociation?: boolean;
  associationName?: string;
  associationMembershipNumber?: string;
  associationDateJoined?: string;

  // Competency Form specific fields
  tradeFirearm?: boolean;
  possessFirearm?: boolean;

  // Type of Firearms specific fields
  pistol?: boolean;
  rifle?: boolean;
  shotgun?: boolean;
  revolver?: boolean;
  selfLoading?: boolean;
  selfLoadingT?: string;

  // Additional fields for criminal history
  h5a?: boolean; // Offense Yes
  h5b?: boolean; // Offense No
  h51?: string;  // Police Station
  h52?: string;  // Case Number
  h53?: string;  // Charge
  h54?: string;  // Outcome/Verdict

  h6a?: boolean; // Pending Case Yes
  h6b?: boolean; // Pending Case No
  h61?: string;  // Pending Case Police Station
  h62?: string;  // Pending Case Case Number
  h6a3?: string; // Pending Case Offence

  h7a?: boolean; // Lost/Stolen Yes
  h7b?: boolean; // Lost/Stolen No
  h71?: string;  // Lost/Stolen Police Station
  h72?: string;  // Lost/Stolen Case Number
  h73?: string;  // Lost/Stolen Circumstances
  h74?: string;  // Lost/Stolen Firearm Details

  h8a?: boolean; // Investigation Yes
  h8b?: boolean; // Investigation No
  h81?: string;  // Investigation Police Station
  h82?: string;  // Investigation Case Number
  h83?: string;  // Investigation Charge
  h84?: string;  // Investigation Outcome/Verdict

  h9a?: boolean; // Declared Unfit Yes
  h9b?: boolean; // Declared Unfit No
  h91?: string;  // Declared Unfit Police Station
  h92?: string;  // Declared Unfit Case Number
  h93?: string;  // Declared Unfit Charge
  h94?: string;  // Declared Unfit Date
  h95?: string;  // Declared Unfit Period

  h10a?: boolean; // Confiscated Yes
  h10b?: boolean; // Confiscated No
  h101?: string;  // Confiscated Police Station
  h102?: string;  // Confiscated Case Number
  h103?: string;  // Confiscated Circumstances
  h104?: string;  // Confiscated Outcome/Verdict

  // Association membership fields
  f5a?: boolean; // Member of Association Yes
  f5b?: boolean; // Member of Association No
  f6?: string;   // Association Name
  f7?: string;   // Membership Number
  f8?: string;   // Date Joined Association
}

// Default initial form data
export const initialFormData: FormData = {
  // Required properties for Annexure A and License forms
  mainHF: false,
  addHF: false,
  s13: false,
  s15: false,
  s16: false,
  s20: false,
  s20a: false,
  s20b: false,
  s20c: false,

  // Personal Info
  fullName: '',
  firstName: '',
  lastName: '',
  phoneNumber: '',
  email: '',
  physicalAddress: '',
  idNumber: '',
  companyName: '',
  tradeProfession: '',
  workAddress: '',
  workPostalCode: '',
  workNumber: '',
  workHouseUnitNumber: '',
  saId: false,
  fId: false,
  permRes: false,
  sexM: false,
  sexF: false,
  age: '',
  birthDate: '',
  initials: '',
  singles: false,
  married: false,
  divorced: false,
  widower: false,
  widow: false,
  citizenType: '',
  maritalStatus: "",
  spouseIdType: 'none',

  // Add default values for criminal record fields
  offenseYes: false,
  offenseNo: false,

  // Add default values for pending cases
  pendingCaseYes: false,
  pendingCaseNo: false,
  pendingCasePoliceStation: '',
  pendingCaseCaseNumber: '',
  pendingCaseOffence: '',

  // Add default values for lost/stolen firearms
  lostStolenYes: false,
  lostStolenNo: false,
  lostStolenPoliceStation: '',
  lostStolenCaseNumber: '',
  lostStolenCircumstances: '',
  lostStolenFirearmDetails: '',

  // Add default values for investigation
  investigationYes: false,
  investigationNo: false,
  investigationPoliceStation: '',
  investigationCaseNumber: '',
  investigationCharge: '',
  investigationOutcome: '',

  // Add default values for declared unfit
  declaredUnfitYes: false,
  declaredUnfitNo: false,
  declaredUnfitPoliceStation: '',
  declaredUnfitCaseNumber: '',
  declaredUnfitCharge: '',
  declaredUnfitDate: '',
  declaredUnfitPeriod: '',

  // Add default values for confiscated
  confiscatedYes: false,
  confiscatedNo: false,
  confiscatedPoliceStation: '',
  confiscatedCaseNumber: '',
  confiscatedCircumstances: '',
  confiscatedOutcome: '',

  // Add default values for protection allegations
  protectionAllegationsYes: false,
  protectionAllegationsNo: false,
  protectionAllegationsDetails: '',

  // Add default values for denied firearm
  deniedFirearmYes: false,
  deniedFirearmNo: false,
  deniedFirearmReason: '',
  deniedFirearmPoliceStation: '',
  deniedFirearmCaseNumber: '',

  // Add default values for under age of 21
  conductBusiness: false,
  gainfullyEmployed: false,
  dedicatedHunter: false,
  dedicatedSportPersonal: false,
  privateCollector: false,
  publicCollector: false,
  otherUnderAge21: false,
  otherDetails: '',
  specifyDetails: '',

  templateUrl: '',
  templateName: '',
  documentProcessed: false,
  houseUnitNumber: '',
}

// Initial data for Further Competency form
export const initialFurtherCompetencyData: FurtherCompetencyData = {
  ...initialFormData,
  competencyCertificateType: '',
  competencyCertificateNumber: '',
  competencyIssueDate: '',
  competencyExpiryDate: '',
  isMemberOfAssociation: false,
  associationName: '',
  associationMembershipNumber: '',
  associationDateJoined: '',

  // Competency Form specific fields
  tradeFirearm: false,
  possessFirearm: false,

  // Type of Firearms specific fields
  pistol: false,
  rifle: false,
  shotgun: false,
  revolver: false,
  selfLoading: false,
  selfLoadingT: '',

  // Additional fields for criminal history
  h5a: false, // Offense Yes
  h5b: false, // Offense No
  h51: '',    // Police Station
  h52: '',    // Case Number
  h53: '',    // Charge
  h54: '',    // Outcome/Verdict

  h6a: false, // Pending Case Yes
  h6b: false, // Pending Case No
  h61: '',    // Pending Case Police Station
  h62: '',    // Pending Case Case Number
  h6a3: '',   // Pending Case Offence

  h7a: false, // Lost/Stolen Yes
  h7b: false, // Lost/Stolen No
  h71: '',    // Lost/Stolen Police Station
  h72: '',    // Lost/Stolen Case Number
  h73: '',    // Lost/Stolen Circumstances
  h74: '',    // Lost/Stolen Firearm Details

  h8a: false, // Investigation Yes
  h8b: false, // Investigation No
  h81: '',    // Investigation Police Station
  h82: '',    // Investigation Case Number
  h83: '',    // Investigation Charge
  h84: '',    // Investigation Outcome/Verdict

  h9a: false, // Declared Unfit Yes
  h9b: false, // Declared Unfit No
  h91: '',    // Declared Unfit Police Station
  h92: '',    // Declared Unfit Case Number
  h93: '',    // Declared Unfit Charge
  h94: '',    // Declared Unfit Date
  h95: '',    // Declared Unfit Period

  h10a: false, // Confiscated Yes
  h10b: false, // Confiscated No
  h101: '',    // Confiscated Police Station
  h102: '',    // Confiscated Case Number
  h103: '',    // Confiscated Circumstances
  h104: '',    // Confiscated Outcome/Verdict

  // Association membership fields
  f5a: false,  // Member of Association Yes
  f5b: false,  // Member of Association No
  f6: '',      // Association Name
  f7: '',      // Membership Number
  f8: ''       // Date Joined Association
}