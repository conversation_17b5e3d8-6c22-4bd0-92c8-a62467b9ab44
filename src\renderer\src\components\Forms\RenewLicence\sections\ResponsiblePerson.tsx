import React from 'react'
import { RenewLicenceData } from '../../../../types/RenewLicenceData'
import { FormField, RadioGroup, FormSection } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'

interface ResponsiblePersonProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  handleAddressChange: (
    address: string,
    postalCode?: string,
    houseNumber?: string,
    isWorkAddress?: boolean,
    isResponsiblePerson?: boolean
  ) => void
  handleHouseNumberChange: (houseNumber: string, isWorkAddress?: boolean, isResponsiblePerson?: boolean) => void
  className?: string
}

const ResponsiblePerson: React.FC<ResponsiblePersonProps> = ({
  formData,
  updateFormData,
  handleAddressChange,
  handleHouseNumberChange,
  className = ''
}) => {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Responsible Person's Details</h3>
      <FormSection
        title="Responsible Person's Details"
        subtitle="Please provide details about the responsible person"
      >
        <div className="space-y-4">
          <FormField
            label="Responsible Person (Full Names and Surname)"
            name="responsiblePersonName"
            value={formData.responsiblePersonName || ''}
            onChange={handleChange}
            placeholder="Enter full names and surname"
            required
          />

          <div className="mb-4">
            <RadioGroup
              label="Identification Type"
              name="responsiblePersonIdType"
              value={
                formData.responsiblePersonSaId ? 'saId' :
                formData.responsiblePersonPassport ? 'passport' : ''
              }
              onChange={(value) => {
                updateFormData({
                  responsiblePersonSaId: value === 'saId',
                  responsiblePersonPassport: value === 'passport'
                })
              }}
              options={[
                { value: 'saId', label: 'SA ID' },
                { value: 'passport', label: 'Passport' }
              ]}
              required
            />
          </div>

          {formData.responsiblePersonSaId && (
            <FormField
              label="Identity Number of Responsible Person"
              name="responsiblePersonIdNumber"
              value={formData.responsiblePersonIdNumber || ''}
              onChange={handleChange}
              placeholder="Enter ID number"
              required
            />
          )}

          {formData.responsiblePersonPassport && (
            <FormField
              label="Passport Number of Responsible Person"
              name="responsiblePersonPassportNumber"
              value={formData.responsiblePersonPassportNumber || ''}
              onChange={handleChange}
              placeholder="Enter passport number"
              required
            />
          )}

          <FormField
            label="Cell Phone Number"
            name="responsiblePersonCellNumber"
            value={formData.responsiblePersonCellNumber || ''}
            onChange={handleChange}
            placeholder="Enter cell phone number"
            required
          />

          <AddressInput
            label="Physical Address"
            value={formData.responsiblePersonAddress || ''}
            postalCode={formData.responsiblePersonPostalCode || ''}
            onChange={(address, postalCode) =>
              handleAddressChange(address, postalCode, undefined, false, true)
            }
            required
            postalCodeRequired={true}
            placeholder="Enter physical address"
          />
        </div>
      </FormSection>
    </div>
  )
}

export default ResponsiblePerson
