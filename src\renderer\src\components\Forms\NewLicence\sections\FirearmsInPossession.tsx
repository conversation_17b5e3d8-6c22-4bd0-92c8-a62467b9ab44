import React from 'react'
import { FormSection } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Firearms in Possession section component
 *
 * This component displays a table with 10 rows and 6 columns for entering
 * details about firearms in possession.
 *
 * Each cell in the table has a unique placeholder for the document generator:
 *
 * Row 1:
 * - Type: {FIP_TYPE_1}
 * - Calibre: {FIP_CALIBRE_1}
 * - Make: {FIP_MAKE_1}
 * - Barrel Serial No: {FIP_BARREL_1}
 * - Frame/receiver Serial No: {FIP_FRAME_1}
 * - Licence authorization No: {FIP_LICENCE_1}
 *
 * Row 2:
 * - Type: {FIP_TYPE_2}
 * - Calibre: {FIP_CALIBRE_2}
 * - Make: {FIP_MAKE_2}
 * - Barrel Serial No: {FIP_BARREL_2}
 * - Frame/receiver Serial No: {FIP_FRAME_2}
 * - Licence authorization No: {FIP_LICENCE_2}
 *
 * And so on for rows 3-10, following the same pattern with numbers 3-10.
 */
const FirearmsInPossession: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  // Create an array of 10 rows for the table
  const rows = Array.from({ length: 10 }, (_, i) => i + 1)

  const handleChange = (rowIndex: number, field: string, value: string) => {
    // Create a copy of the current firearms array or initialize if it doesn't exist
    const updatedFirearms = [...(formData.firearmsInPossession || [])]

    // Ensure the row exists in the array
    if (!updatedFirearms[rowIndex - 1]) {
      updatedFirearms[rowIndex - 1] = {
        type: '',
        calibre: '',
        make: '',
        barrelSerialNo: '',
        frameSerialNo: '',
        licenceNo: ''
      }
    }

    // Update the specific field
    updatedFirearms[rowIndex - 1] = {
      ...updatedFirearms[rowIndex - 1],
      [field]: value
    }

    // Update the form data
    updateFormData({
      firearmsInPossession: updatedFirearms
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Firearms in Possession</h3>
      <FormSection
        title="Firearms in Possession"
        subtitle="Please provide details of firearms currently in your possession"
      >
        <div className="overflow-x-auto">
          <table className="min-w-full bg-stone-800/50 rounded-lg">
            <thead>
              <tr className="border-b border-stone-700">
                <th className="px-4 py-2 text-left text-sm font-medium text-stone-300">#</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-stone-300">Type</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-stone-300">Calibre</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-stone-300">Make</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-stone-300">Barrel Serial No</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-stone-300">Frame/receiver Serial No</th>
                <th className="px-4 py-2 text-left text-sm font-medium text-stone-300">Licence authorization No</th>
              </tr>
            </thead>
            <tbody>
              {rows.map((rowIndex) => {
                const firearm = formData.firearmsInPossession?.[rowIndex - 1] || {
                  type: '',
                  calibre: '',
                  make: '',
                  barrelSerialNo: '',
                  frameSerialNo: '',
                  licenceNo: ''
                }

                return (
                  <tr key={rowIndex} className="border-b border-stone-700 last:border-b-0">
                    <td className="px-4 py-2 text-sm text-stone-400">{rowIndex}</td>
                    <td className="px-2 py-2">
                      <input
                        type="text"
                        value={firearm.type}
                        onChange={(e) => handleChange(rowIndex, 'type', e.target.value)}
                        className="w-full bg-stone-700 border border-stone-600 rounded px-2 py-1 text-sm text-white focus:ring-1 focus:ring-orange-500/30 focus:border-orange-500"
                        placeholder="Type"
                        title={`Placeholder: {FIP_TYPE_${rowIndex}}`}
                      />
                    </td>
                    <td className="px-2 py-2">
                      <input
                        type="text"
                        value={firearm.calibre}
                        onChange={(e) => handleChange(rowIndex, 'calibre', e.target.value)}
                        className="w-full bg-stone-700 border border-stone-600 rounded px-2 py-1 text-sm text-white focus:ring-1 focus:ring-orange-500/30 focus:border-orange-500"
                        placeholder="Calibre"
                        title={`Placeholder: {FIP_CALIBRE_${rowIndex}}`}
                      />
                    </td>
                    <td className="px-2 py-2">
                      <input
                        type="text"
                        value={firearm.make}
                        onChange={(e) => handleChange(rowIndex, 'make', e.target.value)}
                        className="w-full bg-stone-700 border border-stone-600 rounded px-2 py-1 text-sm text-white focus:ring-1 focus:ring-orange-500/30 focus:border-orange-500"
                        placeholder="Make"
                        title={`Placeholder: {FIP_MAKE_${rowIndex}}`}
                      />
                    </td>
                    <td className="px-2 py-2">
                      <input
                        type="text"
                        value={firearm.barrelSerialNo}
                        onChange={(e) => handleChange(rowIndex, 'barrelSerialNo', e.target.value)}
                        className="w-full bg-stone-700 border border-stone-600 rounded px-2 py-1 text-sm text-white focus:ring-1 focus:ring-orange-500/30 focus:border-orange-500"
                        placeholder="Barrel Serial No"
                        title={`Placeholder: {FIP_BARREL_${rowIndex}}`}
                      />
                    </td>
                    <td className="px-2 py-2">
                      <input
                        type="text"
                        value={firearm.frameSerialNo}
                        onChange={(e) => handleChange(rowIndex, 'frameSerialNo', e.target.value)}
                        className="w-full bg-stone-700 border border-stone-600 rounded px-2 py-1 text-sm text-white focus:ring-1 focus:ring-orange-500/30 focus:border-orange-500"
                        placeholder="Frame/receiver Serial No"
                        title={`Placeholder: {FIP_FRAME_${rowIndex}}`}
                      />
                    </td>
                    <td className="px-2 py-2">
                      <input
                        type="text"
                        value={firearm.licenceNo}
                        onChange={(e) => handleChange(rowIndex, 'licenceNo', e.target.value)}
                        className="w-full bg-stone-700 border border-stone-600 rounded px-2 py-1 text-sm text-white focus:ring-1 focus:ring-orange-500/30 focus:border-orange-500"
                        placeholder="Licence authorization No"
                        title={`Placeholder: {FIP_LICENCE_${rowIndex}}`}
                      />
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </FormSection>
    </div>
  )
}

export default FirearmsInPossession
