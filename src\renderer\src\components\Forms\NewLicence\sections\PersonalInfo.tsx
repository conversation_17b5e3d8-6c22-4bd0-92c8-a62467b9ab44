import React from 'react'
import { FormSection, FormField, RadioGroup, SelectField } from '../../../FormComponents'
import AddressInput from '../../../AddressInput'
import { SectionWithAddressProps } from '../utils/types'
import { extractFromIdNumber, generateInitials } from '../utils/helpers'

/**
 * Personal Information section component
 *
 * This component handles the Natural Person's Details section of the New License form.
 * It collects all the personal information needed for the license application and
 * maps the form fields to the XML placeholders for document generation.
 *
 * XML Placeholders for Natural Person's Details:
 *
 * // Personal Info
 * - {FirstName} - First Names (Fill in)
 * - {LastName} - Last Name (Fill in)
 * - {Cell} - Phone Number (Fill in)
 * - {Email} - Email Address (Fill in)
 * - {Address} - Physical Address (Fill in)
 * - {POSTALCODE} - Postal Code (Fill in)
 * - {ID} - ID Number (Fill in)
 * - {PASSPORT} - Passport Number (Fill in)
 *
 * // Professional Info
 * - {Company} - Company Name (Fill in)
 * - {Trade} - Trade/Profession (Fill in)
 * - {WorkA} - Work Address (Fill in)
 * - {WorkP} - Work Postal Code (Fill in)
 * - {WorkC} - Work Contact (Fill in)
 *
 * // Marital Status
 * - {SINGLES} - Singles (Mark with X)
 * - {MARRIED} - Married (Mark with X)
 * - {DIVORCED} - Divorced (Mark with X)
 * - {WIDOWER} - Widower (Mark with X)
 * - {WIDOW} - Widow (Mark with X)
 *
 * // Spouse Particulars
 * - {SPOUSEID} - Spouse ID (Mark with X)
 * - {SPOUSEPORT} - Spouse Passport (Mark with X)
 * - {SPOUSEIDNO} - Spouse ID Number (Fill in)
 * - {SPOUSEPASSN} - Spouse Passport Number (Fill in)
 * - {SPOUSEFULLNAME} - Spouse Name and Surname (Fill in)
 *
 * // Personal Information Applicant Particulars
 * - {SAID} - SA Citizen (Mark with X)
 * - {FID} - Foreign Citizen (Mark with X)
 * - {PermRes} - Permanent Resident (Mark with X)
 * - {Initials} - Initials (Fill in)
 * - {AGE} - Age (Fill in)
 * - {SEXM} - Sex Male (Mark with X)
 * - {SEXF} - Sex Female (Mark with X)
 * - {BirthDate} - Birth Date (Fill in)
 * - {HOUSETYPE} - House Type (Fill in)
 */
const PersonalInfo: React.FC<SectionWithAddressProps> = ({
  formData,
  updateFormData,
  handleAddressChange,
  handleHouseNumberChange,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target

    const updatedData: any = {
      [name]: value
    }

    // For personal info fields
    if (name === 'npname') {
      updatedData.npinitials = generateInitials(value)
      updatedData.fullName = value + ' ' + (formData.nplname || '')
    }
    // For last name field
    else if (name === 'nplname') {
      updatedData.fullName = (formData.npname || '') + ' ' + value
    }
    // For private owner fields
    else if (name === 'firstName') {
      updatedData.initials = generateInitials(value)
      updatedData.fullName = value + ' ' + (formData.lastName || '')
    }
    // For estate section fields
    else if (name === 'deFullName') {
      updatedData.deInitials = generateInitials(value)
    }
    // For phone number fields
    else if (name === 'npcell') {
      updatedData.phoneNumber = value
    }
    // For work number fields
    else if (name === 'npworkc') {
      updatedData.workNumber = value
    }
    // For email fields
    else if (name === 'npemail') {
      // Only update the npemail field, not the email field used in Current Owner
    }
    // For trade profession fields
    else if (name === 'tradeProfession') {
      // Also update the npprof field for consistency if it's not already set
      if (!formData.npprof) {
        updatedData.npprof = value
      }
    }
    // For address fields
    else if (name === 'npaddress') {
      updatedData.physicalAddress = value
    }
    // For ID number fields
    else if (name === 'npid') {
      // Only update the npid field, not the idNumber field used in Current Owner

      // If ID number has 13 digits, extract all possible information
      if (value.length === 13) {
        const extractedData = extractFromIdNumber(value)
        if (extractedData) {
          updatedData.npdateb = extractedData.birthDate
          updatedData.npage = extractedData.age
          updatedData.npsexmale = extractedData.npsexmale
          updatedData.npsexfemale = extractedData.npsexfemale

          // Also update the birthDate and age fields for other sections
          updatedData.birthDate = extractedData.birthDate
          updatedData.age = extractedData.age
          updatedData.sexM = extractedData.npsexmale
          updatedData.sexF = extractedData.npsexfemale
        }
      }
    }
    // For passport number fields
    else if (name === 'passport') {
      // This is for the Current Owner section, don't cross-update
    }
    // For spouse details
    else if (name === 'spouseFullName') {
      // Update the SPOUSEFULLNAME field for document generation
      // This field is already mapped correctly in the form
    }
    // For spouse ID number
    else if (name === 'spouseIdNo') {
      // Update the SPOUSEIDNO field for document generation
      // This field is already mapped correctly in the form

      // If ID number has 13 digits, we could potentially extract spouse information
      // but we'll leave that for future enhancement
    }
    // For spouse passport number
    else if (name === 'spousePassN') {
      // Update the SPOUSEPASSN field for document generation
      // This field is already mapped correctly in the form
    }

    updateFormData(updatedData)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-1">Natural Person's Details</h3>
      <FormSection title="Personal Information" subtitle="Basic identity details">
        <div className="space-y-2">
          {/* Basic Identity Information */}
          <div className="flex flex-row space-x-3 mb-1">
            <FormField
              label="First Names"
              name="npname"
              value={formData.npname || ''}
              onChange={handleChange}
              placeholder="First Names"
              // All fields are optional
              className="flex-1"
            />

            <FormField
              label="Last Name"
              name="nplname"
              value={formData.nplname || ''}
              onChange={handleChange}
              placeholder="Last name"
              // All fields are optional
              className="flex-1"
            />

            <FormField
              label="Initials"
              name="npinitials"
              value={formData.npinitials || ''}
              onChange={handleChange}
              placeholder="Initials"
              className="w-24"
            />
          </div>
          <p className="text-xs text-stone-400 -mt-1 mb-1">
            Enter your full first name(s) - initials will be auto-generated
          </p>

          {/* Citizenship and ID Information */}
          <div className="flex flex-row">
            <div className="w-full">
              <RadioGroup
                name="idType"
                value={formData.npsaid ? 'sa' : formData.npidno ? 'non' : ''}
                onChange={(value) => {
                  updateFormData({
                    npsaid: value === 'sa',
                    npidno: value === 'non',
                    // Update the citizenType field for other sections, but don't update saId and fId
                    // which are used in the Current Owner section
                    citizenType: value === 'sa' ? 'saId' : value === 'non' ? 'fId' : ''
                  })
                }}
                options={[
                  { value: 'sa', label: 'South African Citizen' },
                  { value: 'non', label: 'Non-South African Citizen' }
                ]}
                label="Citizenship Type"
                // All fields are optional
              />
            </div>
          </div>

          <div className="flex flex-row space-x-3 mt-1">
            {formData.npsaid ? (
              <FormField
                label="ID Number"
                name="npid"
                value={formData.npid || ''}
                onChange={handleChange}
                placeholder="Enter your 13-digit ID number"
                // All fields are optional
                className="flex-1"
                pattern="[0-9]{13}"
                maxLength={13}
                additionalContent={
                  <p className="text-xs text-stone-400 mt-1">
                    Enter your 13-digit South African ID number
                  </p>
                }
              />
            ) : (
              <FormField
                label="Passport Number"
                name="passport"
                value={formData.passport || ''}
                onChange={handleChange}
                placeholder="Enter your passport number"
                // All fields are optional
                className="flex-1"
                additionalContent={
                  <p className="text-xs text-stone-400 mt-1">
                    Enter your passport number as a non-South African citizen
                  </p>
                }
              />
            )}

            <FormField
              label="Date of Birth"
              name="npdateb"
              value={formData.npdateb || ''}
              onChange={handleChange}
              placeholder="YYYY-MM-DD"
              type="date"
              // All fields are optional
              className="w-1/4"
            />

            <div className="w-1/5">
              <RadioGroup
                name="gender"
                value={formData.npsexmale ? 'male' : formData.npsexfemale ? 'female' : ''}
                onChange={(value) => {
                  updateFormData({
                    npsexmale: value === 'male',
                    npsexfemale: value === 'female',
                    // Update the standard gender fields for other sections
                    sexM: value === 'male',
                    sexF: value === 'female'
                  })
                }}
                options={[
                  { value: 'male', label: 'Male' },
                  { value: 'female', label: 'Female' }
                ]}
                label="Gender"
                // All fields are optional
              />
            </div>

            <FormField
              label="Age"
              name="npage"
              value={formData.npage || ''}
              onChange={handleChange}
              placeholder="Age"
              type="number"
              // All fields are optional
              min={18}
              max={120}
              className="w-20"
            />
          </div>
          <p className="text-xs text-stone-400 -mt-1 mb-1">
            {formData.npsaid
              ? "Enter your 13-digit ID number - age, birth date, and gender will be auto-calculated"
              : "Enter your passport number and personal details"}
          </p>
        </div>
      </FormSection>

      {/* Contact Information */}
      <FormSection
        title="Contact Information"
        subtitle="Contact details and residence"
        className="mt-2"
      >
        <div className="space-y-2">
          <div className="flex flex-row space-x-3">
            <FormField
              label="Cell Phone Number"
              name="npcell"
              value={formData.npcell || ''}
              onChange={handleChange}
              placeholder="Enter your cell phone number"
              // All fields are optional
              type="tel"
              pattern="[0-9]{10}"
              maxLength={10}
              className="flex-1"
              additionalContent={
                <p className="text-xs text-stone-400 mt-1">
                  Enter your 10-digit cell phone number without spaces or dashes
                </p>
              }
            />

            <FormField
              label="Email Address"
              name="npemail"
              value={formData.npemail || ''}
              onChange={handleChange}
              type="email"
              placeholder="Enter your email address"
              className="flex-1"
              additionalContent={
                <p className="text-xs text-stone-400 mt-1">
                  Optional - enter a valid email address for correspondence
                </p>
              }
            />

            <FormField
              label="Work Number"
              name="npworkc"
              value={formData.npworkc || ''}
              onChange={handleChange}
              placeholder="Enter your work number"
              type="tel"
              className="flex-1"
              additionalContent={
                <p className="text-xs text-stone-400 mt-1">
                  Optional - enter your work phone number if applicable
                </p>
              }
            />
          </div>

          <div className="flex flex-row space-x-3">
            <SelectField
              label="Type of Residence"
              name="nptypeofres"
              value={formData.nptypeofres || ''}
              onChange={handleChange}
              placeholder="Select your type of residence"
              className="flex-1"
              options={[
                { value: 'House', label: 'House' },
                { value: 'Apartment', label: 'Apartment' },
                { value: 'Townhouse', label: 'Townhouse' },
                { value: 'Farm', label: 'Farm' },
                { value: 'Estate', label: 'Estate' },
                { value: 'Informal Settlement', label: 'Informal Settlement' },
                { value: 'Other', label: 'Other' }
              ]}
              // All fields are optional
            />

            <FormField
              label="Profession"
              name="npprof"
              value={formData.npprof || ''}
              onChange={handleChange}
              placeholder="Enter or select your profession"
              className="flex-1"
              // All fields are optional
              list="profession-options"
              additionalContent={
                <>
                  <p className="text-xs text-stone-400 mt-1">
                    Enter your profession or select from the suggestions
                  </p>
                  <datalist id="profession-options">
                    <option value="Accountant" />
                    <option value="Attorney/Lawyer" />
                    <option value="Business Owner" />
                    <option value="Doctor/Medical Professional" />
                    <option value="Engineer" />
                    <option value="Farmer" />
                    <option value="Government Employee" />
                    <option value="IT Professional" />
                    <option value="Law Enforcement" />
                    <option value="Military" />
                    <option value="Retired" />
                    <option value="Security Professional" />
                    <option value="Student" />
                    <option value="Teacher/Educator" />
                    <option value="Unemployed" />
                  </datalist>
                </>
              }
            />
          </div>
          <p className="text-xs text-stone-400 -mt-1 mb-1">
            Select your residence type and enter your profession - these are required for the license application
          </p>

          <AddressInput
            label="Physical Address"
            value={formData.npaddress || ''}
            postalCode={formData.nppostal || ''}
            onChange={(address, postalCode) => {
              // Update both the natural person's address and the standard address fields
              handleAddressChange(address, postalCode);

              // Don't update the address field for the current owner section
            }}
            // All fields are optional
            placeholder="Enter your physical address"
          />
          <p className="text-xs text-stone-400 -mt-1 mb-1">
            Enter your full physical address and postal code
          </p>
        </div>
      </FormSection>

      {/* Professional Information */}
      <FormSection
        title="Company Information"
        subtitle="Employment details (if applicable)"
        className="mt-2"
      >
        <div className="space-y-2">
          <div className="flex flex-row space-x-3">
            <FormField
              label="Company Name"
              name="npecname"
              value={formData.npecname || ''}
              onChange={handleChange}
              placeholder="Enter your company name (if applicable)"
              className="flex-1"
              additionalContent={
                <p className="text-xs text-stone-400 mt-1">
                  Optional - enter your employer's name if you are employed
                </p>
              }
            />

            <FormField
              label="Position/Job Title"
              name="tradeProfession"
              value={formData.tradeProfession || ''}
              onChange={handleChange}
              placeholder="Enter your position or job title"
              className="flex-1"
              additionalContent={
                <p className="text-xs text-stone-400 mt-1">
                  Optional - enter your position or job title at the company
                </p>
              }
            />
          </div>

          {formData.npecname && (
            <>
              <AddressInput
                label="Company Address"
                value={formData.npecadd || ''}
                postalCode={formData.npecpostal || ''}
                onChange={(address, postalCode) => {
                  // Update both the company address fields
                  handleAddressChange(address, postalCode, undefined, true);

                  // Update the workAddress field for other sections
                  updateFormData({
                    workAddress: address,
                    companyName: formData.npecname
                  });
                }}
                placeholder="Enter company address"
              />
              <p className="text-xs text-stone-400 -mt-1 mb-1">
                Enter your company's physical address and postal code
              </p>
            </>
          )}
        </div>
      </FormSection>

      {/* Marital Status */}
      <FormSection
        title="Marital Status"
        subtitle="Current marital status details"
        className="mt-2"
      >
        <div className="space-y-2">
          <RadioGroup
            name="maritalStatus"
            value={
              formData.singles
                ? 'single'
                : formData.married
                  ? 'married'
                  : formData.divorced
                    ? 'divorced'
                    : formData.widower
                      ? 'widower'
                      : formData.widow
                        ? 'widow'
                        : ''
            }
            onChange={(value) => {
              updateFormData({
                singles: value === 'single',
                married: value === 'married',
                divorced: value === 'divorced',
                widower: value === 'widower',
                widow: value === 'widow',
                maritalStatus: value as 'single' | 'married' | 'divorced' | 'widower' | 'widow' | ''
              })
            }}
            options={[
              { value: 'single', label: 'Single' },
              { value: 'married', label: 'Married' },
              { value: 'divorced', label: 'Divorced' },
              { value: 'widower', label: 'Widower' },
              { value: 'widow', label: 'Widow' }
            ]}
            label="Marital Status"
          />
          <p className="text-xs text-stone-400 -mt-1 mb-1">
            Select your current marital status - additional fields will appear if married
          </p>

          {formData.married && (
            <div className="ml-4 border-l-2 border-orange-500 pl-3 space-y-2 mt-2">
              <h3 className="text-sm font-semibold text-white mb-1">Spouse Details</h3>
              <div className="flex flex-row space-x-3">
                <div className="w-1/2">
                  <RadioGroup
                    name="spouseIdType"
                    value={formData.spouseIdType || 'none'}
                    onChange={(value) => {
                      updateFormData({
                        spouseIdType: value as 'spouseId' | 'spousePort' | 'none' | ''
                      })
                    }}
                    options={[
                      { value: 'spouseId', label: 'ID Number' },
                      { value: 'spousePort', label: 'Passport' },
                      { value: 'none', label: 'None' }
                    ]}
                    label="Spouse ID Type"
                    // All fields are optional
                  />
                </div>

                {formData.spouseIdType === 'spouseId' && (
                  <FormField
                    label="Spouse ID Number"
                    name="spouseIdNo"
                    value={formData.spouseIdNo || ''}
                    onChange={handleChange}
                    placeholder="Enter spouse ID number"
                    pattern="[0-9]{13}"
                    maxLength={13}
                    // All fields are optional
                    className="flex-1"
                    additionalContent={
                      <p className="text-xs text-stone-400 mt-1">
                        Enter your spouse's 13-digit South African ID number
                      </p>
                    }
                  />
                )}

                {formData.spouseIdType === 'spousePort' && (
                  <FormField
                    label="Spouse Passport Number"
                    name="spousePassN"
                    value={formData.spousePassN || ''}
                    onChange={handleChange}
                    placeholder="Enter spouse passport number"
                    // All fields are optional
                    className="flex-1"
                    additionalContent={
                      <p className="text-xs text-stone-400 mt-1">
                        Enter your spouse's passport number
                      </p>
                    }
                  />
                )}
              </div>

              <div className="flex flex-row space-x-3">
                <FormField
                  label="Spouse Full Name"
                  name="spouseFullName"
                  value={formData.spouseFullName || ''}
                  onChange={handleChange}
                  placeholder="Enter spouse's full name"
                  // All fields are optional
                  className="flex-1"
                  additionalContent={
                    <p className="text-xs text-stone-400 mt-1">
                      Enter your spouse's full name (first and last name)
                    </p>
                  }
                />
              </div>
              <p className="text-xs text-stone-400 mb-1">
                All spouse details are required if you are married
              </p>
            </div>
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default PersonalInfo
