import React from 'react'
import { Client } from '../../../types'
import { FormSection, FormField } from '../../FormComponents'
import { DashboardIcons } from '../../icons/DashboardIcons'

interface ClientStepProps {
  selectedClient: Client | null
  clientSearchTerm: string
  clients: Client[]
  onClientSearchChange: (searchTerm: string) => void
  onClientSelect: (client: Client) => void
  onClientRemove: () => void
  error?: string
}

const ClientStep: React.FC<ClientStepProps> = ({
  selectedClient,
  clientSearchTerm,
  clients,
  onClientSearchChange,
  onClientSelect,
  onClientRemove,
  error
}) => {
  return (
    <FormSection title="Select Client">
      {selectedClient ? (
        <div className="mb-6 p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-semibold text-white">
                {selectedClient.first_name.toUpperCase()} {selectedClient.last_name.toUpperCase()}
              </h3>
              <p className="text-stone-400 text-sm">ID: {selectedClient.id_number.toUpperCase()}</p>
              <p className="text-stone-400 text-sm">
                {selectedClient.address}, {selectedClient.city}
              </p>
            </div>
            <button
              type="button"
              onClick={onClientRemove}
              className="text-orange-400 hover:text-orange-300"
            >
              <DashboardIcons.Close className="w-5 h-5" />
            </button>
          </div>
        </div>
      ) : (
        <div className="mb-6">
          <FormField
            label="Search Clients"
            name="client-search"
            value={clientSearchTerm}
            onChange={(e) => onClientSearchChange(e.target.value)}
            placeholder="Enter name or ID number"
            error={error}
          />

          {clients.length > 0 && clientSearchTerm && (
            <div className="mt-2 bg-stone-700/50 rounded-lg border border-stone-600/50 max-h-60 overflow-y-auto">
              {clients.map(client => (
                <div
                  key={client.id}
                  onClick={() => onClientSelect(client)}
                  className="p-3 hover:bg-orange-500/20 cursor-pointer border-b border-stone-600/30 last:border-0 transition-colors"
                >
                  <p className="text-white font-medium">
                    {client.first_name.toUpperCase()} {client.last_name.toUpperCase()}
                  </p>
                  <p className="text-stone-400 text-sm">ID: {client.id_number.toUpperCase()}</p>
                </div>
              ))}
            </div>
          )}

          {clientSearchTerm && clients.length === 0 && (
            <p className="mt-2 text-stone-400 text-sm">No clients found. Try a different search term.</p>
          )}
        </div>
      )}
    </FormSection>
  )
}

export default ClientStep
