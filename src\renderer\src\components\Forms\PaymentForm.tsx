import { useState, useEffect, useMemo } from 'react'
import { Loan, LoanPayment } from '../../types'
import { getSupabase } from '../../lib/supabase'
import { DashboardIcons } from '../icons/DashboardIcons'
import { formatCurrency } from '../../utils/formatters'
import { v4 as uuidv4 } from 'uuid'
import {
  getMonthlyPaymentStatuses,
  allocatePaymentToMonths,
  getPaymentAllocationNotes,
  MonthlyPaymentStatus} from '../../utils/paymentAllocation'
import { sendLoanPaymentNotification } from '../../utils/whatsAppNotificationService'

interface PaymentFormProps {
  loan: Loan
  onClose: () => void
  onSuccess: () => void
}

function PaymentForm({ loan, onClose, onSuccess }: PaymentFormProps): React.JSX.Element {
  const [formData, setFormData] = useState<Partial<LoanPayment>>({
    loan_id: loan.id,
    payment_date: new Date().toISOString().split('T')[0],
    amount: 0,
    penalties_paid: 0,
    payment_number: 1,
    status: 'paid',
    method: '',
    receipt_number: '',
    notes: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [previousPayments, setPreviousPayments] = useState<LoanPayment[]>([])
  const [maxPayment, setMaxPayment] = useState(loan.remaining_balance)
  const [penaltyCount, setPenaltyCount] = useState(0)
  const [monthlyStatuses, setMonthlyStatuses] = useState<MonthlyPaymentStatus[]>([])
  const [, setWillUpdateDueDate] = useState(false)

  // Calculate monthly payment amount
  const getMonthlyPaymentAmount = (): number => {
    const totalPayments = loan.loan_term || 12;
    return Math.ceil(loan.loan_amount / totalPayments);
  };

  // Get current month's payments total
  const getCurrentMonthPayments = (): number => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Filter payments made in the current month
    const currentMonthPayments = previousPayments.filter(payment => {
      const paymentDate = new Date(payment.payment_date);
      return paymentDate.getMonth() === currentMonth &&
             paymentDate.getFullYear() === currentYear;
    });

    // Sum up all payments made this month
    return currentMonthPayments.reduce((total, payment) => total + payment.amount, 0);
  };

  // Calculate current month payment status including the new payment
  const getUpdatedMonthPaymentStatus = (): {
    currentPaid: number;
    withNewPayment: number;
    required: number;
    progress: number;
    newProgress: number;
    willBeOverpaid: boolean;
    isPaidInFull: boolean;
  } => {
    const monthlyRequired = getMonthlyPaymentAmount();
    const currentPaid = getCurrentMonthPayments();
    const withNewPayment = currentPaid + (formData.amount || 0);
    const progress = monthlyRequired > 0 ? Math.min(100, (currentPaid / monthlyRequired) * 100) : 100;
    const newProgress = monthlyRequired > 0 ? Math.min(100, (withNewPayment / monthlyRequired) * 100) : 100;
    const willBeOverpaid = withNewPayment > monthlyRequired;
    const isPaidInFull = withNewPayment >= monthlyRequired;

    return {
      currentPaid,
      withNewPayment,
      required: monthlyRequired,
      progress,
      newProgress,
      willBeOverpaid,
      isPaidInFull
    };
  };

  // Add function to calculate loan term info from start date
  const getLoanTermInfo = (): {
    totalMonths: number;
    elapsedMonths: number;
    remainingMonths: number;
    formattedTerm: string;
  } => {
    const totalMonths = loan.loan_term || 12; // Default to 12 if not specified

    if (!loan.start_date) {
      return {
        totalMonths,
        elapsedMonths: 0,
        remainingMonths: totalMonths,
        formattedTerm: `0/${totalMonths} months`
      };
    }

    const startDate = new Date(loan.start_date);
    const currentDate = new Date();

    // Calculate months elapsed since loan started
    const elapsedMonths =
      (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
      (currentDate.getMonth() - startDate.getMonth());

    // Calculate remaining months (ensure it's not negative)
    const remainingMonths = Math.max(0, totalMonths - elapsedMonths);

    // Format the term string
    const formattedTerm = `${Math.min(elapsedMonths, totalMonths)}/${totalMonths} months`;

    return {
      totalMonths,
      elapsedMonths,
      remainingMonths,
      formattedTerm
    };
  };

  // Update maxPayment when loan changes
  useEffect(() => {
    setMaxPayment(loan.remaining_balance)
  }, [loan.remaining_balance])

  // Use the existing penalties from the loan if available
  useEffect(() => {
    // If loan has penalties, use them as the default
    if (loan.penalties && loan.penalties > 0) {
      setFormData(prev => ({
        ...prev,
        penalties_paid: loan.penalties
      }));
      // Set penaltyCount based on the loan penalties (1 count per 10% of weapon cost)
      const penaltyCountEstimate = Math.ceil(loan.penalties / (loan.weapon_cost * 0.1));
      setPenaltyCount(penaltyCountEstimate);
    } else {
      // If manually adjusting penalties
      const penaltyAmount = Math.round((loan.weapon_cost * 0.1 * penaltyCount) * 100) / 100;
      setFormData(prev => ({
        ...prev,
        penalties_paid: penaltyAmount
      }));
    }
  }, [penaltyCount, loan.weapon_cost, loan.penalties]);

  // Fetch previous payments
  useEffect(() => {
    const fetchPayments = async () => {
      try {
        const { data, error } = await getSupabase()
          .from('loan_payments')
          .select('*')
          .eq('loan_id', loan.id)
          .order('payment_date', { ascending: false })

        if (error) throw error
        setPreviousPayments(data || [])

        // Set next payment number
        if (data && data.length > 0) {
          setFormData(prev => ({
            ...prev,
            payment_number: data.length + 1
          }))
        }
      } catch (error) {
        console.error('Error fetching previous payments:', error)
      }
    }

    fetchPayments()
  }, [loan.id])

  // Calculate monthly statuses when payments are loaded
  useEffect(() => {
    if (loan && previousPayments) {
      setMonthlyStatuses(getMonthlyPaymentStatuses(loan, previousPayments));
    }
  }, [loan, previousPayments]);

  // Calculate payment allocation when amount changes
  const paymentAllocation = useMemo(() => {
    if (!formData.amount || formData.amount <= 0 || !monthlyStatuses.length) {
      setWillUpdateDueDate(false);
      return { monthAllocations: [] };
    }

    const allocation = allocatePaymentToMonths(formData.amount, monthlyStatuses);

    // Calculate if this payment will update the due date
    const currentMonthStatus = getUpdatedMonthPaymentStatus();
    const currentMonthPaid = currentMonthStatus.isPaidInFull;

    // Check if previous month is paid
    const isPreviousMonthPaid = (): boolean => {
      const currentDate = new Date();
      const previousMonth = new Date(currentDate);
      previousMonth.setMonth(previousMonth.getMonth() - 1);

      const previousMonthStatus = monthlyStatuses.find(status =>
        status.month.getMonth() === previousMonth.getMonth() &&
        status.month.getFullYear() === previousMonth.getFullYear()
      );

      if (!previousMonthStatus || previousMonthStatus.isGracePeriod) return true;

      const allocatedToPreviousMonth = allocation.monthAllocations.find(month =>
        month.month.getMonth() === previousMonth.getMonth() &&
        month.month.getFullYear() === previousMonth.getFullYear()
      );

      const previousMonthAllocatedAmount = allocatedToPreviousMonth ? allocatedToPreviousMonth.amount : 0;

      return previousMonthStatus.paid + previousMonthAllocatedAmount >= previousMonthStatus.required;
    };

    // Check if all past due payments are covered
    const pastDueMonths = allocation.monthAllocations.filter(a => a.isPastDue);
    const allPastDueCovered = pastDueMonths.length > 0 &&
      pastDueMonths.every(month => {
        const status = monthlyStatuses.find(s =>
          s.month.getMonth() === month.month.getMonth() &&
          s.month.getFullYear() === month.month.getFullYear()
        );
        return status && (status.paid + month.amount >= status.required);
      });

    setWillUpdateDueDate(currentMonthPaid && (isPreviousMonthPaid() || allPastDueCovered));

    return allocation;
  }, [formData.amount, monthlyStatuses]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target

    // Convert numeric values
    if (type === 'number') {
      // Convert empty string to 0
      const numValue = value === '' ? 0 : parseFloat(value) || 0

      // Only allow manual penalty changes when penalty count is 0
      if (name === 'penalties_paid' && penaltyCount > 0) {
        return; // Don't allow changes when penalties are auto-calculated
      }

      // Ensure payment amount doesn't exceed remaining balance
      if (name === 'amount' && numValue > maxPayment) {
        setFormData(prev => ({
          ...prev,
          [name]: maxPayment
        }))
      } else {
        setFormData(prev => ({
          ...prev,
          [name]: numValue
        }))
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }

    // Clear error when field is updated
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.payment_date) newErrors.payment_date = 'Payment date is required'
    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Valid payment amount is required'
    }
    if (formData.amount && formData.amount > loan.remaining_balance) {
      newErrors.amount = `Payment cannot exceed remaining balance (${formatCurrency(loan.remaining_balance)})`
    }
    if (formData.penalties_paid && formData.penalties_paid < 0) {
      newErrors.penalties_paid = 'Penalties paid cannot be negative'
    }

    // If penalty count is set but penalty amount is 0, show a warning
    if (penaltyCount > 0 && (!formData.penalties_paid || formData.penalties_paid <= 0)) {
      newErrors.penalties_paid = 'Penalty count is set but no penalty amount calculated'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Send WhatsApp notification for payment confirmation
  const sendPaymentWhatsAppNotification = async () => {
    try {
      // Skip notification if notifications are paused or client data is missing
      if (loan.pause_notifications || !loan.clients || !loan.clients.phone) return;

      const client = loan.clients;
      const paymentAmount = formData.amount || 0;
      const newRemainingBalance = loan.remaining_balance - paymentAmount;
      const paymentDate = new Date(formData.payment_date || new Date()).toLocaleDateString();
      const clientName = `${client.first_name} ${client.last_name}`;

      console.log('Preparing to send WhatsApp payment notification for loan:', loan.invoice_number);

      // Use the shared WhatsApp notification service
      const result = await sendLoanPaymentNotification(
        clientName,
        client.phone,
        loan.invoice_number,
        paymentAmount,
        paymentDate,
        newRemainingBalance
      );

      if (result.success) {
        console.log('Successfully sent WhatsApp payment notification');
      } else {
        console.error('Failed to send WhatsApp payment notification:', result.error);
      }
    } catch (error) {
      console.error('Error sending WhatsApp payment notification:', error);
      // Don't throw error - we don't want to fail payment recording if WhatsApp fails
    }
  };

  // Create a separate function to record payment directly without triggering problematic behavior
  const recordPaymentWithoutTriggers = async () => {
    try {
      setLoading(true);

      // Step 1: Create the payment record first
      const paymentData = {
        ...formData,
        id: uuidv4(),
        notes: formData.notes ?
          `${formData.notes}\n\n${getPaymentAllocationNotes(paymentAllocation.monthAllocations)}` :
          getPaymentAllocationNotes(paymentAllocation.monthAllocations)
      };

      console.log('Creating payment record:', paymentData);
      const { error: paymentError } = await getSupabase()
        .from('loan_payments')
        .insert(paymentData);

      if (paymentError) {
        console.error('Error creating payment record:', paymentError);
        alert('Failed to create payment record');
        setLoading(false);
        return;
      }

      // Send WhatsApp notification after successful payment recording
      await sendPaymentWhatsAppNotification();

      // Silently close the form without alert
      onSuccess();
    } catch (error) {
      console.error('Error in recordPaymentWithoutTriggers:', error);
      alert('Failed to record payment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Use the safer direct method instead of the problematic one
    recordPaymentWithoutTriggers();
  };

  // Function to clear loan penalties
  const clearLoanPenalties = async () => {
    try {
      setLoading(true)

      // Calculate the total amount paid by all payments (excluding penalty payments)
      const totalPaid = previousPayments.reduce((sum, payment) => sum + payment.amount, 0)

      // Calculate the original remaining balance (without penalties)
      // Loan amount - total payments
      const originalRemainingBalance = loan.loan_amount - totalPaid

      // Update loan to set penalties to 0 and correct the remaining balance
      const { error } = await getSupabase()
        .from('loans')
        .update({
          penalties: 0,
          remaining_balance: originalRemainingBalance
        })
        .eq('id', loan.id)

      if (error) {
        console.error('Error clearing loan penalties:', error)
        alert('Failed to clear loan penalties')
      } else {
        alert('Loan penalties cleared successfully')
        // Reset UI penalty count
        setPenaltyCount(0)
        setFormData(prev => ({
          ...prev,
          penalties_paid: 0
        }))
        onSuccess() // Refresh the loan data
      }
    } catch (error) {
      console.error('Error clearing loan penalties:', error)
      alert('Failed to clear loan penalties')
    } finally {
      setLoading(false)
    }
  }

  // Get status color for the payment status badge
  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30'
      case 'overdue':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'paid':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      default:
        return 'bg-stone-500/20 text-stone-400 border-stone-500/30'
    }
  }


  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-2 backdrop-blur-sm transition-all duration-300 animate-fadeIn">
      <div className="bg-gradient-to-b from-stone-800 to-stone-900 rounded-xl shadow-2xl w-full max-w-[1200px] flex flex-col border border-stone-700/50 h-[90vh] max-h-[720px] animate-slideUp">
        {/* Header with fixed position - Condensed */}
        <div className="bg-stone-900 py-2 px-4 border-b border-stone-700/50 flex flex-col justify-between">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="bg-gradient-to-r from-orange-500 to-amber-500 p-1.5 rounded-lg shadow-lg transform transition-transform hover:scale-105">
                <DashboardIcons.Payment className="w-4 h-4 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-white">Record Payment</h2>
                <p className="text-stone-400 text-xs">
                  Quote <span className="text-orange-400 font-medium">{loan.invoice_number}</span> |
                  Client: <span className="text-white font-medium">{loan.clients ? `${loan.clients.first_name} ${loan.clients.last_name}` : 'Unknown Client'}</span>
                </p>
              </div>
            </div>

            <button
              onClick={onClose}
              className="text-stone-400 hover:text-white transition-colors duration-200 p-1.5 rounded-full hover:bg-stone-700/50"
            >
              <DashboardIcons.Close className="w-4 h-4" />
            </button>
          </div>

          {/* Add payment priority notice - inline with header when needed */}
          {monthlyStatuses.some(status => !status.paid && status.isPast && !status.isGracePeriod) && (
            <div className="mt-1.5 text-xs font-medium text-orange-400 bg-orange-500/10 border border-orange-500/20 px-2.5 py-1 rounded-lg flex items-center gap-1 shadow-inner">
              <DashboardIcons.Refresh className="w-2.5 h-2.5 text-orange-500 animate-pulse" />
              Priority: This payment will first cover missed month(s) before current month
            </div>
          )}
        </div>

        {/* Form content - Main area with grid layout */}
        <div className="flex-1 overflow-hidden flex flex-col">
          <form id="payment-form" onSubmit={handleSubmit} className="flex flex-col h-full">
            <div className="flex-1 flex flex-col gap-3 p-3 overflow-y-auto custom-scrollbar">

              {/* Top row with payment amount and details in a grid */}
              <div className="grid grid-cols-12 gap-3">
                {/* LEFT: Payment Amount Section */}
                <div className="col-span-12 lg:col-span-5 bg-gradient-to-r from-stone-800/90 to-stone-900/80 p-4 rounded-xl border border-orange-500/30 shadow-lg transform transition-all hover:shadow-orange-500/5">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="bg-gradient-to-r from-orange-500 to-amber-500 p-1.5 rounded-lg shadow-md transform transition-transform hover:scale-105">
                      <DashboardIcons.Payment className="w-4 h-4 text-white" />
                    </div>
                    <h3 className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-r from-white to-stone-300">Payment Amount</h3>
                  </div>

                  <div className="relative transform transition-all duration-200 hover:translate-y-[-2px]">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <span className="text-orange-400 text-lg font-semibold">R</span>
                    </div>
                    <input
                      type="number"
                      name="amount"
                      value={formData.amount === 0 ? '' : formData.amount}
                      onChange={handleChange}
                      min="0"
                      max={loan.remaining_balance}
                      step="0.01"
                      placeholder="0.00"
                      autoFocus
                      className="w-full bg-stone-700/80 text-white p-3 pl-8 rounded-lg border border-stone-600/50 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none text-2xl text-center hover:bg-stone-700 transition-all shadow-inner placeholder:text-stone-500"
                    />
                    <div className="absolute inset-0 rounded-lg pointer-events-none shadow-[0_0_15px_rgba(234,88,12,0.15)] opacity-0 transition-opacity duration-300 peer-focus:opacity-100"></div>
                  </div>

                  {errors.amount && (
                    <p className="text-red-400 text-xs mt-1.5 flex items-center justify-center gap-1 animate-fadeIn">
                      <DashboardIcons.Close className="w-3 h-3" />
                      {errors.amount}
                    </p>
                  )}

                  {/* Quick payment options in a row */}
                  <div className="flex flex-wrap gap-1.5 mt-3 justify-center">
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, amount: getMonthlyPaymentAmount()})}
                      className="px-2 py-1.5 bg-stone-700/80 text-white rounded-md text-xs hover:bg-stone-600 transition-all duration-200 border border-stone-600/50 flex items-center gap-1 flex-1 hover:shadow-md hover:translate-y-[-1px]"
                    >
                      <DashboardIcons.Payment className="w-3 h-3 text-orange-400" />
                      Monthly
                    </button>
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, amount: Math.ceil(getMonthlyPaymentAmount() / 2)})}
                      className="px-2 py-1.5 bg-stone-700/80 text-white rounded-md text-xs hover:bg-stone-600 transition-all duration-200 border border-stone-600/50 flex items-center gap-1 flex-1 hover:shadow-md hover:translate-y-[-1px]"
                    >
                      <DashboardIcons.Payment className="w-3 h-3 text-stone-400" />
                      Half
                    </button>
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, amount: loan.remaining_balance})}
                      className="px-2 py-1.5 bg-stone-700/80 text-white rounded-md text-xs hover:bg-stone-600 transition-all duration-200 border border-stone-600/50 flex items-center gap-1 flex-1 hover:shadow-md hover:translate-y-[-1px]"
                    >
                      <DashboardIcons.Check className="w-3 h-3 text-emerald-400" />
                      Full
                    </button>
                  </div>

                  {/* Missed payment suggestion - made more compact */}
                  {paymentAllocation.monthAllocations.some(a => a.isPastDue) && (
                    <div className="mt-3">
                      <button
                        type="button"
                        onClick={() => {
                          const pastDueTotal = monthlyStatuses
                            .filter(status => status.isPast && !status.isCurrentMonth && !status.isGracePeriod && status.remaining > 0)
                            .reduce((total, status) => total + status.remaining, 0);
                          setFormData({...formData, amount: pastDueTotal});
                        }}
                        className="w-full px-2 py-1.5 bg-red-500/20 text-red-300 rounded-md text-xs hover:bg-red-500/30 transition-all duration-200 border border-red-500/30 inline-flex items-center gap-1 justify-center hover:shadow-md hover:shadow-red-500/10 hover:translate-y-[-1px]"
                      >
                        <DashboardIcons.Refresh className="w-2.5 h-2.5 animate-pulse" />
                        Cover Missed ({formatCurrency(monthlyStatuses
                          .filter(status => status.isPast && !status.isCurrentMonth && !status.isGracePeriod && status.remaining > 0)
                          .reduce((total, status) => total + status.remaining, 0))})
                      </button>
                    </div>
                  )}
                </div>

                {/* RIGHT: Payment Details & Loan Summary */}
                <div className="col-span-12 lg:col-span-7">
                  <div className="grid grid-cols-12 gap-3 h-full">
                    {/* Payment Details - Compact Form */}
                    <div className="col-span-12 md:col-span-6 bg-stone-800/50 p-3 rounded-lg border border-stone-700/40 shadow-md transition-all hover:shadow-lg hover:border-stone-700/60">
                      <h3 className="text-xs font-semibold text-white mb-2 uppercase tracking-wider flex items-center gap-1.5">
                        <DashboardIcons.Payment className="w-3 h-3 text-orange-500" />
                        Payment Details
                      </h3>

                      <div className="grid grid-cols-2 gap-x-3 gap-y-2.5">
                        <div>
                          <label className="block text-stone-300 text-xs mb-1 font-medium">Payment Date</label>
                          <input
                            type="date"
                            name="payment_date"
                            value={formData.payment_date}
                            onChange={handleChange}
                            className="w-full bg-stone-700/70 text-white p-1.5 rounded-md border border-stone-600/50 focus:ring-1 focus:ring-orange-500/70 focus:border-orange-500 outline-none text-xs hover:bg-stone-700/90 transition-colors"
                          />
                          {errors.payment_date && (
                            <p className="text-red-400 text-xs mt-0.5 flex items-center gap-1 animate-fadeIn">
                              <DashboardIcons.Close className="w-2.5 h-2.5" />
                              {errors.payment_date}
                            </p>
                          )}
                        </div>

                        <div>
                          <label className="block text-stone-300 text-xs mb-1 font-medium">Payment Method</label>
                          <select
                            name="method"
                            value={formData.method || ''}
                            onChange={handleChange}
                            className="w-full bg-stone-700/70 text-white p-1.5 rounded-md border border-stone-600/50 focus:ring-1 focus:ring-orange-500/70 focus:border-orange-500 outline-none text-xs hover:bg-stone-700/90 transition-colors"
                          >
                            <option value="">Select method</option>
                            <option value="cash">Cash</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="credit_card">Credit Card</option>
                            <option value="debit_card">Debit Card</option>
                            <option value="check">Check</option>
                            <option value="other">Other</option>
                          </select>
                        </div>

                        <div className="col-span-2">
                          <label className="block text-stone-300 text-xs mb-1 font-medium">Receipt Number</label>
                          <input
                            type="text"
                            name="receipt_number"
                            value={formData.receipt_number || ''}
                            onChange={handleChange}
                            placeholder="Receipt/Reference #"
                            className="w-full bg-stone-700/70 text-white p-1.5 rounded-md border border-stone-600/50 focus:ring-1 focus:ring-orange-500/70 focus:border-orange-500 outline-none text-xs hover:bg-stone-700/90 transition-colors"
                          />
                        </div>
                      </div>

                      {/* Loan Summary - Integrated into payment details */}
                      <div className="mt-2.5 bg-stone-700/30 rounded-lg p-2 border border-stone-600/30 hover:bg-stone-700/40 transition-colors">
                        <div className="grid grid-cols-2 gap-y-1.5 text-2xs">
                          <div className="text-stone-400 font-medium">Original Amount:</div>
                          <div className="text-white text-right font-medium">{formatCurrency(loan.loan_amount)}</div>

                          <div className="text-stone-400 font-medium">Term Progress:</div>
                          <div className="text-white text-right font-medium">
                            {getLoanTermInfo().formattedTerm}
                          </div>

                          <div className="text-stone-400 font-medium">Loan Status:</div>
                          <div className="text-right">
                            <span className={`inline-block px-1.5 py-0.5 rounded-full text-2xs font-medium capitalize border transition-colors ${getStatusColor(loan.status)}`}>
                              {loan.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Penalties & Notes Combined */}
                    <div className="col-span-12 md:col-span-6 flex flex-col gap-3">
                      {/* Penalties section - more compact */}
                      <div className="bg-stone-700/30 rounded-lg p-2.5 flex-none transition-all hover:bg-stone-700/40 border border-stone-600/30">
                        <label className="text-stone-300 text-xs mb-1.5 font-medium flex items-center gap-1.5">
                          <DashboardIcons.Refresh className="w-3 h-3 text-amber-500" />
                          Penalties
                        </label>

                        <div className="flex items-center gap-2">
                          <select
                            value={penaltyCount}
                            onChange={(e) => setPenaltyCount(parseInt(e.target.value))}
                            className="w-24 bg-stone-700/70 text-white p-1.5 rounded-md border border-stone-600/50 focus:ring-1 focus:ring-orange-500/70 focus:border-orange-500 outline-none text-xs transition-colors"
                          >
                            {[0, 1, 2, 3, 4, 5].map(count => (
                              <option key={count} value={count}>{count} {count === 1 ? 'penalty' : 'penalties'}</option>
                            ))}
                          </select>

                          <div className="flex-1">
                            {penaltyCount > 0 ? (
                              <div className="bg-amber-500/20 text-amber-400 border border-amber-500/30 rounded-md px-2 py-1 text-xs text-center font-medium shadow-inner">
                                {formatCurrency(formData.penalties_paid || 0)}
                                <span className="text-2xs text-stone-400 ml-1">(10% each)</span>
                              </div>
                            ) : (
                              <div className="text-stone-400 text-xs py-1 text-center">No penalties</div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Notes section - resized to balance the layout */}
                      <div className="bg-stone-700/30 rounded-lg p-2.5 flex-1 border border-stone-600/30">
                        <label className="block text-stone-300 text-xs mb-1 font-medium">Notes</label>
                        <textarea
                          name="notes"
                          value={formData.notes || ''}
                          onChange={handleChange}
                          placeholder="Optional payment notes"
                          rows={2}
                          className="w-full bg-stone-700/70 text-white p-1.5 rounded-md border border-stone-600/50 focus:ring-1 focus:ring-orange-500/70 focus:border-orange-500 outline-none text-xs hover:bg-stone-700/90 transition-colors resize-none"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Allocation & Progress - Full width on the second row */}
              <div className="bg-stone-800/50 p-3 rounded-lg border border-stone-700/40 shadow-md transition-all hover:shadow-lg hover:border-stone-700/60">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-xs font-semibold text-white uppercase tracking-wider flex items-center gap-1.5">
                    <DashboardIcons.Payment className="w-3.5 h-3.5 text-orange-500" />
                    Payment Allocation
                  </h3>
                  {getUpdatedMonthPaymentStatus().isPaidInFull && (
                    <span className="bg-emerald-500/20 text-emerald-400 border border-emerald-500/30 rounded-full px-2 py-0.5 text-2xs flex items-center gap-1 animate-pulse">
                      <DashboardIcons.Check className="w-2.5 h-2.5" />
                      Month Paid in Full
                    </span>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
                  {/* LEFT: Payment Allocation table */}
                  <div className="md:col-span-4">
                    {paymentAllocation.monthAllocations.length > 0 ? (
                      <div className="bg-stone-700/30 rounded-lg border border-stone-600/30 p-2.5 h-40 overflow-y-auto custom-scrollbar">
                        {/* Legend for payment allocation */}
                        <div className="flex gap-3 text-2xs px-1 pb-1.5 mb-1.5 border-b border-stone-600/30">
                          <div className="flex items-center gap-1.5">
                            <span className="w-1.5 h-1.5 rounded-full bg-emerald-500"></span>
                            <span className="text-stone-300 font-medium">Current</span>
                          </div>
                          <div className="flex items-center gap-1.5">
                            <span className="w-1.5 h-1.5 rounded-full bg-red-500"></span>
                            <span className="text-stone-300 font-medium">Missed</span>
                          </div>
                        </div>

                        <div className="space-y-1.5">
                          {paymentAllocation.monthAllocations.map((allocation, index) => (
                            <div key={index} className={`rounded-md p-1.5 flex justify-between items-center transition-all hover:translate-x-1 ${
                              allocation.isPastDue
                                ? 'bg-red-500/10 border border-red-500/20 hover:bg-red-500/15'
                                : 'bg-stone-700/50 border border-stone-600/30 hover:bg-stone-700/70'
                            }`}>
                              <div className="flex items-center gap-1.5">
                                <span className={`w-2 h-2 rounded-full ${
                                  allocation.isPastDue ? 'bg-red-500' : 'bg-emerald-500'
                                } ${allocation.isPastDue ? 'animate-pulse' : ''}`}></span>
                                <span className="text-white text-xs">
                                  {allocation.description}
                                  {allocation.isPastDue &&
                                    <span className="ml-1 text-2xs px-1 py-0 bg-red-500/20 text-red-400 border border-red-500/30 rounded-full">
                                      missed
                                    </span>
                                  }
                                </span>
                              </div>
                              <div className="text-right text-emerald-400 font-medium text-xs">
                                {formatCurrency(allocation.amount)}
                              </div>
                            </div>
                          ))}

                          <div className="flex justify-between items-center pt-1.5 mt-1.5 border-t border-stone-600/50">
                            <div className="text-white font-medium text-xs">Total:</div>
                            <div className="text-right text-white font-bold text-xs">
                              {formatCurrency(formData.amount || 0)}
                            </div>
                          </div>
                        </div>

                        {/* Payment allocation explanation - more compact */}
                        {paymentAllocation.monthAllocations.some(a => a.isPastDue) && (
                          <div className="mt-1.5 p-1.5 bg-stone-700/50 rounded-md border border-orange-500/20">
                            <p className="text-2xs text-orange-400 flex items-center gap-1">
                              <DashboardIcons.Refresh className="w-2.5 h-2.5 animate-spin-slow" />
                              <span>Payments applied to missed months first, then current month</span>
                            </p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="bg-stone-700/30 rounded-lg border border-stone-600/30 p-3 flex flex-col items-center justify-center h-40">
                        <DashboardIcons.Payment className="w-6 h-6 text-stone-500 mb-1.5" />
                        <p className="text-stone-400 text-xs text-center">
                          Enter a payment amount to see allocation details
                        </p>
                      </div>
                    )}
                  </div>

                  {/* RIGHT: Monthly Payment Progress */}
                  <div className="md:col-span-2">
                    <div className="bg-stone-700/30 rounded-lg border border-stone-600/30 p-2.5 h-40 flex flex-col">
                      <div className="flex justify-between items-center mb-1.5 text-xs">
                        <div className="text-white font-medium">Monthly Progress:</div>
                        <div>
                          <span className="text-stone-400 text-2xs">
                            Required: {formatCurrency(getUpdatedMonthPaymentStatus().required)}
                          </span>
                        </div>
                      </div>

                      <div className="relative h-3 bg-stone-700 rounded-full overflow-hidden mb-1.5 border border-stone-600/30">
                        {/* Current payment progress */}
                        <div
                          className="absolute h-full bg-orange-500/50 rounded-full transition-all duration-500 ease-out"
                          style={{ width: `${getUpdatedMonthPaymentStatus().progress}%` }}
                        ></div>
                        {/* New payment progress */}
                        <div
                          className={`absolute h-full rounded-full transition-all duration-500 ease-out ${
                            getUpdatedMonthPaymentStatus().isPaidInFull
                              ? 'bg-gradient-to-r from-emerald-500 to-emerald-400'
                              : 'bg-gradient-to-r from-orange-500 to-orange-400'
                          }`}
                          style={{ width: `${getUpdatedMonthPaymentStatus().newProgress}%` }}
                        ></div>
                      </div>

                      <div className="grid grid-cols-2 gap-1 text-2xs mb-auto">
                        <div>
                          <span className="text-stone-400 font-medium block">Current Payment:</span>
                          <span className="text-white">{formatCurrency(getUpdatedMonthPaymentStatus().currentPaid)}</span>
                        </div>
                        <div>
                          <span className="text-stone-400 font-medium block">After Payment:</span>
                          <span className="text-white">{formatCurrency(getUpdatedMonthPaymentStatus().withNewPayment)}</span>
                        </div>
                      </div>

                      {/* Progress indicators */}
                      <div className="mt-auto flex flex-col gap-1">
                        <div className="flex items-center justify-between text-2xs">
                          <div className="flex items-center gap-1">
                            <span className="w-1.5 h-1.5 rounded-full bg-emerald-500"></span>
                            <span className="text-emerald-400 font-medium">
                              {Math.round(getUpdatedMonthPaymentStatus().newProgress)}% Paid
                            </span>
                          </div>

                          {getUpdatedMonthPaymentStatus().willBeOverpaid && (
                            <span className="text-2xs bg-green-900/40 px-1.5 py-0.5 rounded-md text-green-400 border border-green-800/30">
                              CREDIT
                            </span>
                          )}
                        </div>

                        {getUpdatedMonthPaymentStatus().isPaidInFull ? (
                          <div className="text-2xs bg-emerald-500/10 text-emerald-400 p-1 rounded border border-emerald-500/20 text-center">
                            This month's payment will be complete
                          </div>
                        ) : (
                          <div className="text-2xs bg-amber-500/10 text-amber-400 p-1 rounded border border-amber-500/20 text-center">
                            {formatCurrency(getUpdatedMonthPaymentStatus().required - getUpdatedMonthPaymentStatus().withNewPayment)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer with buttons */}
            <div className="p-3 flex justify-end items-center border-t border-stone-700/50 bg-stone-900/30">
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-3 py-2 bg-stone-700/70 hover:bg-stone-600 text-white rounded-md text-xs border border-stone-600/50 flex items-center gap-1.5 shadow-md transition-all duration-200 hover:shadow-lg"
                >
                  <DashboardIcons.Close className="w-3.5 h-3.5" />
                  Cancel
                </button>

                {/* Add button to clear loan penalties */}
                <button
                  type="button"
                  onClick={clearLoanPenalties}
                  disabled={!loan.penalties || loan.penalties <= 0}
                  className="px-3 py-2 bg-blue-700/70 hover:bg-blue-600 text-white rounded-md text-xs border border-blue-600/50 flex items-center gap-1.5 shadow-md transition-all duration-200 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <DashboardIcons.Close className="w-3.5 h-3.5" />
                  Remove Penalties
                </button>

                <button
                  type="submit"
                  disabled={loading || (formData.amount || 0) <= 0}
                  className="px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-600 hover:from-orange-600 hover:to-amber-700
                    text-white rounded-md flex items-center gap-1.5 disabled:opacity-70 text-xs shadow-md font-medium transition-all duration-200
                    hover:shadow-lg hover:shadow-orange-500/20 hover:translate-y-[-1px] disabled:hover:translate-y-0 disabled:hover:shadow-none"
                >
                  {loading ? (
                    <>
                      <DashboardIcons.Spinner className="w-3.5 h-3.5 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <DashboardIcons.Payment className="w-3.5 h-3.5" />
                      Record Payment
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default PaymentForm
