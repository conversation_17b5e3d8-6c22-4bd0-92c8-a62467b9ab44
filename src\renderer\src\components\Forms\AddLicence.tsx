import { useState, useMemo, useCallback } from 'react'
import { getSupabase } from '../../lib/supabase'
import { SelectSection } from '../icons/SelectSection'
import { formatDateForDatabase } from '../../utils/dateUtils'
import { FormField } from '../../components/FormComponents'
import { DashboardIcons } from '../../components/icons/DashboardIcons'
import { AlertTriangle, FileText, Calendar, Layers } from 'lucide-react'
import { FormErrorBoundary } from '../../components/ErrorBoundary'

interface License {
  id?: string
  client_id?: string
  make: string
  issue_date?: string
  type?: string
  caliber: string
  serial_number: string
  section?: string
  expiry_date?: string
  stock_code?: string
  barrel_serial?: string
  barrel_make?: string
  receiver_serial?: string
  receiver_make?: string
  frame_serial?: string
  frame_make?: string
  lic_number?: string
}

interface LicenseFormProps {
  license?: License | null
  clientId: string | null
  onClose: () => void
  onSuccess: () => void
  title?: string
  loanId?: string
}

export default function LicenseForm({
  license,
  clientId,
  onClose,
  onSuccess,
  title,
  loanId
}: LicenseFormProps): JSX.Element {
  const [showSectionModal, setShowSectionModal] = useState(false)
  // Memoize initial form state to prevent unnecessary recalculations
  const initialFormState = useMemo(
    () => ({
      make: license?.make || '',
      issue_date: license?.issue_date || '',
      type: license?.type || '',
      caliber: license?.caliber || '',
      serial_number: license?.serial_number || '',
      section: license?.section || '',
      expiry_date: license?.expiry_date || '',
      stock_code: license?.stock_code || '',
      barrel_serial: license?.barrel_serial || '',
      barrel_make: license?.barrel_make || '',
      receiver_serial: license?.receiver_serial || '',
      receiver_make: license?.receiver_make || '',
      frame_serial: license?.frame_serial || '',
      frame_make: license?.frame_make || '',
      lic_number: license?.lic_number || ''
    }),
    [license]
  )

  const [formData, setFormData] = useState(initialFormState)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Memoize handleDateChange to prevent unnecessary re-renders
  const handleDateChange = useCallback(
    (dateType: 'issue' | 'expiry', value: string, isCalculation?: boolean): void => {
      const formattedValue = value.replace(/\D/g, '')
      let formattedDate = ''

      if (formattedValue.length <= 4) {
        formattedDate = formattedValue.slice(0, 4)
      } else if (formattedValue.length <= 6) {
        formattedDate = `${formattedValue.slice(0, 4)}-${formattedValue.slice(4, 6)}`
      } else {
        formattedDate = `${formattedValue.slice(0, 4)}-${formattedValue.slice(4, 6)}-${formattedValue.slice(6, 8)}`
      }

      setFormData((prev) => {
        const newData = {
          ...prev,
          [dateType === 'issue' ? 'issue_date' : 'expiry_date']: formattedDate
        }

        // Only calculate expiry date when explicitly triggered by the button
        if (isCalculation && dateType === 'issue' && formattedDate.length === 10) {
          // Only calculate expiry if section is provided
          if (!prev.section) {
            setError('Please select a section first')
            return newData
          }

          try {
            // Parse the issue date properly
            const [year, month, day] = formattedDate.split('-').map(num => parseInt(num, 10))

            // Validate date components
            if (isNaN(year) || isNaN(month) || isNaN(day) ||
                month < 1 || month > 12 || day < 1 || day > 31) {
              setError('Invalid issue date format')
              return prev
            }

            // Create a proper date object (month is 0-indexed in JavaScript Date)
            const issueDate = new Date(year, month - 1, day)

            // Validate the date is valid
            if (isNaN(issueDate.getTime())) {
              setError('Invalid issue date')
              return prev
            }

            // Create a new date object for expiry
            const expiryDate = new Date(issueDate)

            // Calculate expiry based on section
            if (prev.section === 'Section 13') {
              expiryDate.setFullYear(issueDate.getFullYear() + 5)
            } else if (prev.section === 'Section 15' || prev.section === 'Section 16') {
              expiryDate.setFullYear(issueDate.getFullYear() + 10)
            }

            // Set expiry date to one day before the anniversary
            expiryDate.setDate(expiryDate.getDate() - 1)

            // Format the expiry date as YYYY-MM-DD
            const expiryYear = expiryDate.getFullYear()
            const expiryMonth = String(expiryDate.getMonth() + 1).padStart(2, '0')
            const expiryDay = String(expiryDate.getDate()).padStart(2, '0')

            newData.expiry_date = `${expiryYear}-${expiryMonth}-${expiryDay}`
          } catch (err) {
            console.error('Error calculating expiry date:', err)
            setError('Error calculating expiry date')
            return prev
          }
        }

        return newData
      })
    },
    [formData.section]
  )

  // Memoize handleSubmit to prevent unnecessary re-renders
  const handleSubmit = useCallback(
    async (e: React.FormEvent): Promise<void> => {
      e.preventDefault()
      setLoading(true)
      setError(null)

      try {
        // Validate required fields
        if (!formData.make || !formData.serial_number || !formData.caliber) {
          throw new Error('Make & Model, Serial Number, and Caliber are required')
        }

        // Set default values if empty
        let updatedFormData = { ...formData }

        // Default Section to "Section 13" if empty
        if (!updatedFormData.section || updatedFormData.section.trim() === '') {
          updatedFormData.section = 'Section 13'
        }

        // Default Issue Date to current date if empty
        if (!updatedFormData.issue_date || updatedFormData.issue_date.trim() === '') {
          updatedFormData.issue_date = formatDateForDatabase(new Date()) // YYYY-MM-DD format
        }

        // Calculate Expiry Date if empty based on Section and Issue Date
        if (!updatedFormData.expiry_date || updatedFormData.expiry_date.trim() === '') {
          const issueDate = new Date(updatedFormData.issue_date)

          // Validate issue date
          if (!isNaN(issueDate.getTime())) {
            const expiryDate = new Date(issueDate)

            // Calculate based on section
            if (updatedFormData.section === 'Section 13') {
              expiryDate.setFullYear(issueDate.getFullYear() + 5)
            } else if (
              updatedFormData.section === 'Section 15' ||
              updatedFormData.section === 'Section 16'
            ) {
              expiryDate.setFullYear(issueDate.getFullYear() + 10)
            }

            // Set expiry date to one day before the anniversary
            expiryDate.setDate(issueDate.getDate() - 1)

            updatedFormData.expiry_date = formatDateForDatabase(expiryDate)
          }
        }

        // Convert all string values to uppercase
        Object.keys(updatedFormData).forEach((key) => {
          if (typeof updatedFormData[key] === 'string' && updatedFormData[key] !== '') {
            updatedFormData[key] = updatedFormData[key].toUpperCase()
          }
        })

        const supabase = getSupabase()
        const {
          data: { user },
          error: authError
        } = await supabase.auth.getUser()

        if (authError || !user) {
          throw new Error('User authentication required')
        }

        // Check for duplicate serial number
        const { data: existingSerial } = await supabase
          .from('gun_licences')
          .select('id')
          .eq('serial_number', updatedFormData.serial_number)
          .neq('id', license?.id || '')
          .single()

        if (existingSerial) {
          throw new Error('Serial number already exists')
        }

        // Only check for duplicate stock code if it has a value
        if (updatedFormData.stock_code && updatedFormData.stock_code.trim() !== '') {
          const { data: existingStock } = await supabase
            .from('gun_licences')
            .select('id')
            .eq('stock_code', updatedFormData.stock_code)
            .neq('id', license?.id || '')
            .single()

          if (existingStock) {
            throw new Error('Stock code already exists')
          }
        }

        // Prepare submission data, excluding empty optional fields
        const submissionData: Partial<License> = {
          make: updatedFormData.make,
          caliber: updatedFormData.caliber,
          serial_number: updatedFormData.serial_number,
          section: updatedFormData.section,
          issue_date: formatDateForDatabase(updatedFormData.issue_date),
          expiry_date: formatDateForDatabase(updatedFormData.expiry_date)
        }

        // Add optional fields only if they have values
        if (updatedFormData.type && updatedFormData.type.trim() !== '')
          submissionData.type = updatedFormData.type
        if (updatedFormData.stock_code && updatedFormData.stock_code.trim() !== '')
          submissionData.stock_code = updatedFormData.stock_code
        if (updatedFormData.barrel_serial && updatedFormData.barrel_serial.trim() !== '')
          submissionData.barrel_serial = updatedFormData.barrel_serial
        if (updatedFormData.barrel_make && updatedFormData.barrel_make.trim() !== '')
          submissionData.barrel_make = updatedFormData.barrel_make
        if (updatedFormData.receiver_serial && updatedFormData.receiver_serial.trim() !== '')
          submissionData.receiver_serial = updatedFormData.receiver_serial
        if (updatedFormData.receiver_make && updatedFormData.receiver_make.trim() !== '')
          submissionData.receiver_make = updatedFormData.receiver_make
        if (updatedFormData.frame_serial && updatedFormData.frame_serial.trim() !== '')
          submissionData.frame_serial = updatedFormData.frame_serial
        if (updatedFormData.frame_make && updatedFormData.frame_make.trim() !== '')
          submissionData.frame_make = updatedFormData.frame_make
        if (updatedFormData.lic_number && updatedFormData.lic_number.trim() !== '')
          submissionData.lic_number = updatedFormData.lic_number

        let licenseId: string | undefined

        if (license?.id) {
          const { error } = await supabase
            .from('gun_licences')
            .update({
              ...submissionData,
              auth_user_id: user.id
            })
            .eq('id', license.id)

          if (error) {
            if (error.code === '23505') {
              throw new Error('Duplicate entry detected. Please check your input values.')
            }
            throw error
          }

          licenseId = license.id
        } else {
          if (!clientId) throw new Error('Client ID required')

          const { data: newLicense, error } = await supabase
            .from('gun_licences')
            .insert({
              ...submissionData,
              client_id: clientId,
              auth_user_id: user.id
            })
            .select('id')
            .single()

          if (error) {
            if (error.code === '23505') {
              throw new Error('Duplicate entry detected. Please check your input values.')
            }
            throw error
          }

          licenseId = newLicense?.id
        }

        // If loanId is provided, update the loan with the new license
        // Note: We're skipping this for now as the license_id column doesn't exist in the loans table
        // This will need to be revisited when the database schema is updated
        if (loanId && licenseId) {
          console.log('Would update loan with license, but skipping due to schema issues:', {
            loanId,
            licenseId
          })
          // Commented out due to schema issues:
          // const { error: loanUpdateError } = await supabase
          //   .from('loans')
          //   .update({ license_id: licenseId })
          //   .eq('id', loanId)
          //
          // if (loanUpdateError) {
          //   console.error('Error updating loan with license:', loanUpdateError)
          //   // Don't fail the whole operation if this update fails
          // }
        }

        onSuccess()
        clearAndClose()
      } catch (err) {
        console.error('License submission error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    },
    [formData, license, clientId, loanId, onSuccess]
  )

  // Memoize clearAndClose to prevent unnecessary re-renders
  const clearAndClose = useCallback((): void => {
    setFormData(initialFormState)
    setError(null)
    onClose()
  }, [initialFormState, onClose])

  // We don't need memoized form inputs as we're using the handleChange function

  // Update the calculate expiry date button handler
  const handleCalculateExpiry = useCallback((): void => {
    if (!formData.issue_date) {
      setError('Please enter an issue date first')
      return
    }

    if (!formData.section) {
      setShowSectionModal(true)
      return
    }

    // Clear any previous errors
    setError(null)

    // Check if the issue date is in the correct format
    if (formData.issue_date.length !== 10 || !formData.issue_date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      setError('Issue date must be in YYYY-MM-DD format')
      return
    }

    if (['Section 13', 'Section 15', 'Section 16'].includes(formData.section)) {
      handleDateChange('issue', formData.issue_date, true)
    }
  }, [formData.section, formData.issue_date, handleDateChange])

  // We don't need responsive dimensions as we'll use fixed width like in ClientForm

  // Define isEditing variable
  const isEditing = !!license;

  // Create a generic handleChange function for form fields
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const element = e.target;

    // Only convert text inputs to uppercase, not select elements
    let processedValue = value;
    if (typeof value === 'string' && element.tagName !== 'SELECT') {
      processedValue = value.toUpperCase();
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));
  };

  // Handle form errors
  const handleFormError = (error: Error) => {
    console.error('Error in license form:', error);
  };

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-black/80 via-stone-900/90 to-stone-800/90 flex items-center justify-center z-50 p-3 overflow-y-auto">
      {/* Add SelectSection modal */}
      <SelectSection isOpen={showSectionModal} onClose={() => setShowSectionModal(false)} />

      <div className="bg-stone-800/95 rounded-2xl shadow-2xl border border-orange-400/10 w-full max-w-xl relative max-h-[90vh] backdrop-blur-xl">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-stone-400 hover:text-white focus:outline-none focus:text-white transition-colors z-10"
          aria-label="Close form"
        >
          <DashboardIcons.Close className="w-4 h-4" />
        </button>

        <div className="p-4 overflow-y-auto max-h-[90vh] flex flex-col gap-3">
          <h1 className="text-xl font-extrabold text-white mb-2 flex items-center gap-2 tracking-tight drop-shadow-lg">
            <span className="inline-flex items-center justify-center bg-gradient-to-tr from-orange-500/80 to-orange-400/70 rounded-full p-1.5 shadow-md">
              <FileText className="w-5 h-5 text-white" />
            </span>
            {title || (isEditing ? 'Edit License' : 'Add New License')}
          </h1>

          <FormErrorBoundary
            formName={isEditing ? 'Edit License' : 'Add New License'}
            onError={handleFormError}
            resetKeys={[license?.id]}
          >
            <form onSubmit={handleSubmit} className="overflow-y-auto">
              {error && (
                <div className="flex items-center gap-1.5 bg-red-600/20 text-red-200 border border-red-500/40 p-1.5 rounded-lg mb-1.5 text-xs animate-fade-in shadow">
                  <AlertTriangle className="w-3.5 h-3.5 text-red-300" />
                  <span>{error}</span>
                </div>
              )}

              <div className="grid grid-cols-1 gap-3">
                {/* Firearm Information */}
                <div>
                  <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                    <Layers className="w-3.5 h-3.5 text-orange-400" />
                    Firearm Information
                  </h2>
                  <div className="grid grid-cols-2 gap-2">
                    <FormField
                      label="Make & Model"
                      name="make"
                      value={formData.make}
                      onChange={handleChange}
                      required
                      inputClassName="premium-field"
                    />
                    <div className="space-y-1">
                      <label htmlFor="type" className="block text-xs font-medium text-stone-300 mb-0.5">
                        Type
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={formData.type || ''}
                        onChange={handleChange}
                        className="w-full premium-field"
                      >
                        <option value="">Select Type</option>
                        <option value="Handgun">Handgun</option>
                        <option value="Shotgun">Shotgun</option>
                        <option value="Self Loading">Self Loading</option>
                        <option value="Manual Operated">Manual Operated</option>
                        <option value="Combination">Combination</option>
                      </select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <FormField
                      label="Caliber"
                      name="caliber"
                      value={formData.caliber}
                      onChange={handleChange}
                      required
                      inputClassName="premium-field"
                    />
                    <FormField
                      label="Serial Number"
                      name="serial_number"
                      value={formData.serial_number}
                      onChange={handleChange}
                      required
                      inputClassName="premium-field"
                    />
                  </div>
                </div>

                {/* License Information */}
                <div>
                  <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                    <FileText className="w-3.5 h-3.5 text-orange-400" />
                    License Information
                  </h2>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-stone-300 mb-0.5">Section</label>
                      <select
                        name="section"
                        value={formData.section || ''}
                        onChange={handleChange}
                        className="w-full premium-field"
                      >
                        <option value="">Select Section</option>
                        <option value="Section 13">Section 13</option>
                        <option value="Section 15">Section 15</option>
                        <option value="Section 16">Section 16</option>
                      </select>
                    </div>
                    <FormField
                      label="License Number"
                      name="lic_number"
                      value={formData.lic_number || ''}
                      onChange={handleChange}
                      inputClassName="premium-field"
                    />
                  </div>
                </div>

                {/* Date Information */}
                <div>
                  <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                    <Calendar className="w-3.5 h-3.5 text-orange-400" />
                    Date Information
                  </h2>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-stone-300 mb-0.5">Issue Date</label>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          name="issue_date"
                          value={formData.issue_date || ''}
                          onChange={(e) => handleDateChange('issue', e.target.value)}
                          placeholder="YYYY-MM-DD"
                          maxLength={10}
                          className="w-full premium-field"
                        />
                        <button
                          type="button"
                          onClick={handleCalculateExpiry}
                          className="px-3 py-1 bg-orange-500/10 border border-orange-500/20 text-orange-400 rounded-lg hover:bg-orange-500/20 transition-colors"
                          title="Calculate Expiry Date"
                        >
                          ↻
                        </button>
                      </div>
                      <p className="text-xs text-stone-400 mt-1">Format: YYYY-MM-DD</p>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-stone-300 mb-0.5">Expiry Date</label>
                      <input
                        type="text"
                        name="expiry_date"
                        value={formData.expiry_date || ''}
                        onChange={(e) => handleDateChange('expiry', e.target.value)}
                        placeholder="YYYY-MM-DD"
                        maxLength={10}
                        className="w-full premium-field"
                      />
                      <p className="text-xs text-stone-400 mt-1">Format: YYYY-MM-DD</p>
                    </div>
                  </div>
                </div>

                {/* Additional Information */}
                <div>
                  <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                    <Layers className="w-3.5 h-3.5 text-orange-400" />
                    Additional Information
                  </h2>
                  <div className="grid grid-cols-1 gap-2">
                    <FormField
                      label="Stock Code"
                      name="stock_code"
                      value={formData.stock_code || ''}
                      onChange={handleChange}
                      inputClassName="premium-field"
                    />
                  </div>
                </div>

                {/* Component Parts */}
                <div>
                  <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
                    <Layers className="w-3.5 h-3.5 text-orange-400" />
                    Component Parts
                  </h2>
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-stone-300 mb-0.5">Barrel</label>
                      <input
                        type="text"
                        placeholder="Make"
                        name="barrel_make"
                        value={formData.barrel_make || ''}
                        onChange={handleChange}
                        className="w-full premium-field mb-2"
                      />
                      <input
                        type="text"
                        placeholder="Serial"
                        name="barrel_serial"
                        value={formData.barrel_serial || ''}
                        onChange={handleChange}
                        className="w-full premium-field"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-stone-300 mb-0.5">Receiver</label>
                      <input
                        type="text"
                        placeholder="Make"
                        name="receiver_make"
                        value={formData.receiver_make || ''}
                        onChange={handleChange}
                        className="w-full premium-field mb-2"
                      />
                      <input
                        type="text"
                        placeholder="Serial"
                        name="receiver_serial"
                        value={formData.receiver_serial || ''}
                        onChange={handleChange}
                        className="w-full premium-field"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-stone-300 mb-0.5">Frame</label>
                      <input
                        type="text"
                        placeholder="Make"
                        name="frame_make"
                        value={formData.frame_make || ''}
                        onChange={handleChange}
                        className="w-full premium-field mb-2"
                      />
                      <input
                        type="text"
                        placeholder="Serial"
                        name="frame_serial"
                        value={formData.frame_serial || ''}
                        onChange={handleChange}
                        className="w-full premium-field"
                      />
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end gap-2 mt-1">
                  <button
                    type="button"
                    onClick={clearAndClose}
                    className="px-3 py-1.5 bg-stone-700 hover:bg-stone-600 text-white rounded-lg text-xs font-semibold shadow transition-all duration-150 active:scale-95 focus:ring-2 focus:ring-orange-400/50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-3 py-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-lg flex items-center gap-2 text-xs font-semibold shadow-lg transition-all duration-150 active:scale-95 focus:ring-2 focus:ring-orange-400/50 disabled:opacity-60 disabled:cursor-not-allowed"
                  >
                    {loading && <DashboardIcons.Spinner className="w-3.5 h-3.5 animate-spin" />}
                    {isEditing ? 'Update License' : 'Add License'}
                  </button>
                </div>
              </div>
            </form>
          </FormErrorBoundary>

          {/* Premium input field style override */}
          <style>{`
            .premium-field {
              background-color: rgba(68, 64, 60, 0.8);
              border: 1px solid rgba(251, 146, 60, 0.2);
              border-radius: 0.5rem;
              padding: 0.5rem 0.75rem;
              color: white;
              font-size: 0.875rem;
              box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
              transition-property: all;
              transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
              transition-duration: 150ms;
            }
            .premium-field:focus {
              outline: none;
              --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
              --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) rgba(251, 146, 60, 0.6);
              box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
              border-color: rgb(251, 146, 60);
            }
          `}</style>
        </div>
      </div>
    </div>
  )
}
