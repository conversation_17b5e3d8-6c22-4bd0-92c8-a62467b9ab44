import React from 'react'
import { FormSection, FormField, CheckboxGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Competency Certificate section component
 */
const CompetencyCertificate: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Competency Certificate</h3>
      <FormSection
        title="Particulars of Existing Competency Certificate"
        subtitle="Please provide competency certificate details"
      >
        <div className="space-y-3">
          <div className="mb-4">
            {/* These checkboxes use the {TradeFirearm} and {PossessFirearm} placeholders */}
            <CheckboxGroup
              options={[
                {
                  name: 'tradeFirearm',
                  label: 'Trade in Firearm',
                  checked: formData.tradeFirearm || false
                },
                {
                  name: 'possessFirearm',
                  label: 'To Possess Firearm',
                  checked: formData.possessFirearm || false
                }
              ]}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const { name, checked } = e.target
                updateFormData({
                  [name]: checked
                })
              }}
              columns={4}
            />
          </div>

          <div className="mb-4">
            <h4 className="text-sm font-medium text-stone-300 mb-2">Competency Type</h4>
            {/* These checkboxes use the {CompHandgun}, {CompRifle}, {CompShotgun}, and {CompSelfLoading} placeholders */}
            <CheckboxGroup
              options={[
                {
                  name: 'compHandgun',
                  label: 'Handgun',
                  checked: formData.compHandgun || false
                },
                {
                  name: 'compRifle',
                  label: 'Rifle',
                  checked: formData.compRifle || false
                },
                {
                  name: 'compShotgun',
                  label: 'Shotgun',
                  checked: formData.compShotgun || false
                },
                {
                  name: 'compSelfLoading',
                  label: 'Self-Loading',
                  checked: formData.compSelfLoading || false
                }
              ]}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const { name, checked } = e.target
                updateFormData({
                  [name]: checked
                })
              }}
              columns={4}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {/* This field uses the {F2AB} placeholder */}
            <FormField
              label="Competency Certificate Number"
              name="f2ab"
              value={formData.f2ab || ''}
              onChange={handleChange}
              placeholder="Enter certificate number"
            />

            {/* This field uses the {F3} placeholder */}
            <FormField
              label="Issue Date"
              name="f3"
              value={formData.f3 || ''}
              onChange={handleChange}
              placeholder="YYYY-MM-DD"
              type="date"
            />

            {/* This field uses the {F4} placeholder */}
            <FormField
              label="Expiry Date"
              name="f4"
              value={formData.f4 || ''}
              onChange={handleChange}
              placeholder="YYYY-MM-DD"
              type="date"
            />
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default CompetencyCertificate
