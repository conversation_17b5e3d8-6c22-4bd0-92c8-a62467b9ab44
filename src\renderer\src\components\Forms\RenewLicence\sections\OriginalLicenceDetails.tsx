import React from 'react'
import { RenewLicenceData, OriginalLicenceDetails as OriginalLicenceDetailsType } from '../../../../types/RenewLicenceData'
import { FormField, FormSection } from '../../../FormComponents'

interface OriginalLicenceDetailsProps {
  formData: RenewLicenceData
  updateFormData: (data: Partial<RenewLicenceData>) => void
  className?: string
}

const OriginalLicenceDetails: React.FC<OriginalLicenceDetailsProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  // Handle adding a new original licence
  const handleAddLicence = () => {
    const updatedLicences = [...(formData.additionalOriginalLicences || [])]
    updatedLicences.push({ licenceNumber: '', issueDate: '', expiryDate: '' })
    updateFormData({
      showAdditionalLicences: true,
      additionalOriginalLicences: updatedLicences
    })
  }

  // Handle removing an original licence
  const handleRemoveLicence = (index: number) => {
    const updatedLicences = [...(formData.additionalOriginalLicences || [])]
    updatedLicences.splice(index, 1)
    updateFormData({
      additionalOriginalLicences: updatedLicences
    })
  }

  // Handle changes to additional original licences
  const handleAdditionalLicenceChange = (index: number, field: keyof OriginalLicenceDetailsType, value: string) => {
    const updatedLicences = [...(formData.additionalOriginalLicences || [])]
    if (!updatedLicences[index]) {
      updatedLicences[index] = { licenceNumber: '', issueDate: '', expiryDate: '' }
    }
    updatedLicences[index] = { ...updatedLicences[index], [field]: value }
    updateFormData({
      additionalOriginalLicences: updatedLicences
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Details of Original Licence</h3>
      <FormSection
        title="Details of Original Licence"
        subtitle="Please provide details about your original licence"
      >
        <div className="space-y-4">
          {/* Primary original licence */}
          <div className="p-4 bg-stone-800/50 rounded-lg">
            <h4 className="text-md font-semibold text-white mb-3">Original Licence 1</h4>
            <div className="space-y-4">
              <FormField
                label="Licence Number"
                name="originalLicenceNumber"
                value={formData.originalLicenceNumber || ''}
                onChange={handleChange}
                placeholder="Enter your original licence number"
                required
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  label="Date Issued"
                  name="originalLicenceIssueDate"
                  value={formData.originalLicenceIssueDate || ''}
                  onChange={handleChange}
                  type="date"
                  required
                />

                <FormField
                  label="Expiry Date"
                  name="originalLicenceExpiryDate"
                  value={formData.originalLicenceExpiryDate || ''}
                  onChange={handleChange}
                  type="date"
                  required
                />
              </div>
            </div>
          </div>

          {/* Additional original licences */}
          {formData.additionalOriginalLicences?.map((licence, index) => (
            <div key={index} className="p-4 bg-stone-800/50 rounded-lg relative">
              <h4 className="text-md font-semibold text-white mb-3">Original Licence {index + 2}</h4>
              <button
                type="button"
                onClick={() => handleRemoveLicence(index)}
                className="absolute top-2 right-2 text-stone-400 hover:text-red-500 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <div className="space-y-4">
                <FormField
                  label="Licence Number"
                  value={licence.licenceNumber || ''}
                  onChange={(e) => handleAdditionalLicenceChange(index, 'licenceNumber', e.target.value)}
                  placeholder="Enter original licence number"
                  required name={''}                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    label="Date Issued"
                    value={licence.issueDate || ''}
                    onChange={(e) => handleAdditionalLicenceChange(index, 'issueDate', e.target.value)}
                    type="date"
                    required name={''}                  />

                  <FormField
                    label="Expiry Date"
                    value={licence.expiryDate || ''}
                    onChange={(e) => handleAdditionalLicenceChange(index, 'expiryDate', e.target.value)}
                    type="date"
                    required name={''}                  />
                </div>
              </div>
            </div>
          ))}

          {/* Add button - only show if we have fewer than 3 additional licences */}
          {(formData.additionalOriginalLicences?.length || 0) < 3 && (
            <div className="flex justify-center mt-4">
              <button
                type="button"
                onClick={handleAddLicence}
                className="flex items-center px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Another Original Licence
              </button>
            </div>
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default OriginalLicenceDetails
