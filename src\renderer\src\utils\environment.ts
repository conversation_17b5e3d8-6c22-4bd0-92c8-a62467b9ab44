/**
 * Utility functions for environment detection
 */

/**
 * Check if the application is running in development mode
 * @returns {boolean} True if in development mode, false otherwise
 */
export const isDevelopment = (): boolean => {
  // Check if we're in Electron and use its environment variable
  if (window.electron && window.electron.process && window.electron.process.env && window.electron.process.env.NODE_ENV) {
    // Ensure NODE_ENV is a string before comparing
    return typeof window.electron.process.env.NODE_ENV === 'string' && window.electron.process.env.NODE_ENV === 'development';
  }

  // For web environments, check if we're on localhost or a development domain
  const hostname = window.location.hostname;
  return (
    hostname === 'localhost' ||
    hostname === '127.0.0.1' ||
    hostname.includes('dev.') ||
    hostname.includes('.local') ||
    hostname.includes('staging.') ||
    // Check for common development ports
    window.location.port === '3000' ||
    window.location.port === '8080' ||
    window.location.port === '5173' ||
    // Check for Vite's development mode
    import.meta.env?.MODE === 'development'
  );
};

/**
 * Check if the application is running in production mode
 * @returns {boolean} True if in production mode, false otherwise
 */
export const isProduction = (): boolean => {
  return !isDevelopment();
};


