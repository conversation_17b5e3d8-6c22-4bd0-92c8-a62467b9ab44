import React from 'react'
import { FormSection, FormField } from '../../../FormComponents'
import { SectionWithAddressProps } from '../utils/types'
import AddressInput from '../../../AddressInput'

/**
 * SAP 350 (A) DETAILS section component
 *
 * This component displays fields for "Firearm received from" details:
 * - Name: {SAP350A_NAME}
 * - Identification number or FAR number: {SAP350A_ID_FAR}
 * - Address: {SAP350A_ADDRESS}
 * - Postal code: {SAP350A_POSTAL}
 * - Date received: {SAP350A_DATE}
 */
const SAP350ADetails: React.FC<SectionWithAddressProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">SAP 350 (A) DETAILS</h3>
      <FormSection
        title="Firearm received from details"
        subtitle="Please provide details about where the firearm was received from"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <FormField
              label="Name"
              name="sap350aName"
              value={formData.sap350aName || ''}
              onChange={handleChange}
              placeholder="Enter name"
              title="Placeholder: {SAP350A_NAME}"
            />
            <FormField
              label="Identification number or FAR number"
              name="sap350aIdFar"
              value={formData.sap350aIdFar || ''}
              onChange={handleChange}
              placeholder="Enter ID or FAR number"
              title="Placeholder: {SAP350A_ID_FAR}"
            />
          </div>

          <AddressInput
            label="Address"
            value={formData.sap350aAddress || ''}
            postalCode={formData.sap350aPostal || ''}
            onChange={(address, postalCode) => {
              updateFormData({
                sap350aAddress: address,
                sap350aPostal: postalCode || ''
              })
            }}
            placeholder="Enter address"
            isTextarea={false}
            required={false}
            postalCodeRequired={false}
          />
          <p className="text-xs text-stone-400 mt-1 mb-3">
            Enter the address and postal code.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-1 gap-3">
            <FormField
              label="Date received"
              name="sap350aDate"
              value={formData.sap350aDate || ''}
              onChange={handleChange}
              placeholder="YYYY-MM-DD"
              type="date"
              title="Placeholder: {SAP350A_DATE}"
            />
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default SAP350ADetails
