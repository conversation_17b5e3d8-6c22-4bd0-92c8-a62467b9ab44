import React from 'react'
import { LoanPayment } from '../../../types'
import { DashboardIcons } from '../../icons/DashboardIcons'
import { formatCurrency, formatDate } from '../../../utils/formatters'

interface LoanCardPaymentsInfoProps {
  payments: LoanPayment[]
  loadingPayments: boolean
}

const LoanCardPaymentsInfo: React.FC<LoanCardPaymentsInfoProps> = ({ payments, loadingPayments }) => {
  return (
    <div className="overflow-hidden">
      {loadingPayments ? (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-5 w-5 border-2 border-orange-500 border-t-transparent" />
          <span className="ml-3 text-sm text-stone-400">Loading...</span>
        </div>
      ) : (
        <div className="p-3 space-y-3">
          {/* Payment Summary */}
          <div className="px-2 py-2 bg-stone-800/50 rounded-md mb-2">
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs text-orange-400 font-medium">Total Paid</span>
              <span className="text-sm text-white font-medium">
                {formatCurrency(
                  payments.reduce(
                    (sum, payment) =>
                      sum + payment.amount + (payment.penalties_paid || 0),
                    0
                  )
                )}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs text-orange-400 font-medium">
                Last Payment
              </span>
              <span className="text-sm text-white">
                {payments.length > 0
                  ? formatDate(payments[payments.length - 1].payment_date)
                  : 'No payments yet'}
              </span>
            </div>
          </div>

          {/* Recent Payments */}
          {payments.length > 0 ? (
            <div className="mb-3">
              <div className="overflow-hidden rounded-md border border-stone-700/50">
                <table className="w-full text-xs">
                  <thead className="bg-stone-800">
                    <tr>
                      <th className="text-left px-2 py-1 text-stone-400 font-medium">
                        Date
                      </th>
                      <th className="text-right px-2 py-1 text-stone-400 font-medium">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {[...payments]
                      .sort(
                        (a, b) =>
                          new Date(b.payment_date).getTime() -
                          new Date(a.payment_date).getTime()
                      )
                      .slice(0, 3)
                      .map((payment, index) => (
                        <tr
                          key={index}
                          className={
                            index % 2 === 0 ? 'bg-stone-800/30' : 'bg-stone-800/10'
                          }
                        >
                          <td className="px-2 py-1 text-white">
                            {formatDate(payment.payment_date)}
                            {payment.method && (
                              <span className="block text-xs text-stone-400 capitalize">
                                {payment.method.replace('_', ' ')}
                              </span>
                            )}
                          </td>
                          <td className="px-2 py-1 text-white text-right">
                            {formatCurrency(payment.amount)}
                            {payment.penalties_paid > 0 && (
                              <span className="block text-xs text-orange-400">
                                +{formatCurrency(payment.penalties_paid)} pen.
                              </span>
                            )}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="py-4 text-center text-stone-400 text-sm">
              <DashboardIcons.Payment className="w-5 h-5 mx-auto mb-2 opacity-50" />
              <p>No payment history yet</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default React.memo(LoanCardPaymentsInfo)
