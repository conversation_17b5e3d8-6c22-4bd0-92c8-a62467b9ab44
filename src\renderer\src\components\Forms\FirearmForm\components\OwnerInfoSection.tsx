import React from 'react';
import { FormField } from '../../../FormComponents';
import { DashboardIcons } from '../../../icons/DashboardIcons';
import { Firearm } from '../../../../types/firearm';
import { ClientSearch } from './ClientSearch';
import { SimpleClient } from '../hooks';

interface OwnerInfoSectionProps {
  formData: Partial<Firearm>;
  clientSearchTerm: string;
  isLoadingClients: boolean;
  clients: SimpleClient[];
  selectedClient: SimpleClient | null;
  onClientSearchChange: (searchTerm: string) => void;
  onClientSelect: (client: SimpleClient) => void;
  onClientRemove: () => void;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

export const OwnerInfoSection: React.FC<OwnerInfoSectionProps> = ({
  formData,
  clientSearchTerm,
  isLoadingClients,
  clients,
  selectedClient,
  onClientSearchChange,
  onClientSelect,
  onClientRemove,
  onChange
}) => {
  return (
    <div>
      <h2 className="text-sm font-semibold text-orange-300 mb-1 flex items-center gap-1.5">
        <DashboardIcons.Assign className="w-3.5 h-3.5 text-orange-400" />
        Owner Info
      </h2>

      {formData.storage_type === 'Owner' ? (
        <ClientSearch
          clientSearchTerm={clientSearchTerm}
          isLoadingClients={isLoadingClients}
          clients={clients}
          selectedClient={selectedClient}
          onClientSearchChange={onClientSearchChange}
          onClientSelect={onClientSelect}
          onClientRemove={onClientRemove}
        />
      ) : (
        <FormField
          label="Owner Name"
          name="full_name"
          value={formData.full_name || ''}
          onChange={onChange}
          required
          inputClassName="premium-field"
        />
      )}
    </div>
  );
};
