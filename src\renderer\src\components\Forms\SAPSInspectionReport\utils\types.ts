import { FormData } from '../../../../types/FormData'

/**
 * Common props for all section components
 */
export interface SectionProps {
  formData: FormData
  updateFormData: (updatedData: Partial<FormData>) => void
  className?: string
}

/**
 * Validation status type
 */
export type ValidationStatus = 'idle' | 'processing' | 'success' | 'error'

/**
 * Template status type
 */
export type TemplateStatus = 'loading' | 'ready' | 'error'
