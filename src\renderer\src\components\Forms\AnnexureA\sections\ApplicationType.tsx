import React from 'react'
import { FormSection, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Application Type section component for Annexure A form
 */
const ApplicationType: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleCheckboxChange = (sectionType: string) => {
    updateFormData({ section: sectionType })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Application Type</h3>

      <FormSection title="Firearm Licence Holder Type" subtitle="Select your licence holder status">
        <RadioGroup
          name="licenceHolderType"
          value={formData.afhy ? 'additional' : 'main'}
          onChange={(value) =>
            updateFormData({
              afhy: value === 'additional',
              afhn: value === 'main'
            })
          }
          options={[
            { value: 'main', label: 'Main firearm licence holder' },
            { value: 'additional', label: 'Additional firearm licence holder' }
          ]}
        />
      </FormSection>

      <FormSection title="Type of Licence" subtitle="Select the applicable section">
        <div className="space-y-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="section13"
                name="section13"
                checked={formData.section === 'section13'}
                onChange={() => handleCheckboxChange('section13')}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="section13" className="ml-2 block text-sm text-stone-300">
                Section 13: Licence to possess a firearm for self-defence
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="section15"
                name="section15"
                checked={formData.section === 'section15'}
                onChange={() => handleCheckboxChange('section15')}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="section15" className="ml-2 block text-sm text-stone-300">
                Section 15: Licence for occasional hunting/sport-shooting
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="section16"
                name="section16"
                checked={formData.section === 'section16'}
                onChange={() => handleCheckboxChange('section16')}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="section16" className="ml-2 block text-sm text-stone-300">
                Section 16: Licence for dedicated hunting/sport-shooting
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="section20"
                name="section20"
                checked={formData.section === 'section20'}
                onChange={() => handleCheckboxChange('section20')}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="section20" className="ml-2 block text-sm text-stone-300">
                Section 20: Business purposes - Business in hunting
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="section20A"
                name="section20A"
                checked={formData.section === 'section20A'}
                onChange={() => handleCheckboxChange('section20A')}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="section20A" className="ml-2 block text-sm text-stone-300">
                Section 20A: Business purposes - Other business purposes
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="section20B"
                name="section20B"
                checked={formData.section === 'section20B'}
                onChange={() => handleCheckboxChange('section20B')}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="section20B" className="ml-2 block text-sm text-stone-300">
                Section 20B: Business purposes - Security business
              </label>
            </div>

            <div className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700/70 transition-colors">
              <input
                type="checkbox"
                id="section20C"
                name="section20C"
                checked={formData.section === 'section20C'}
                onChange={() => handleCheckboxChange('section20C')}
                className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-stone-600 rounded"
              />
              <label htmlFor="section20C" className="ml-2 block text-sm text-stone-300">
                Section 20C: Business purposes - Training purposes
              </label>
            </div>
          </div>
        </div>
      </FormSection>
    </div>
  )
}

export default ApplicationType
