import React, { useState, useEffect, useCallback } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { getSupabase } from '../../../lib/supabase'
import { Firearm, FirearmAssignment } from '../../../types/firearm'
import { FormField } from '../../FormComponents'
import { DashboardIcons } from '../../icons/DashboardIcons'
import { debounce } from 'lodash'

// Define a simplified client type for the form
interface SimpleClient {
  id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  id_number: string
}

interface FirearmAssignmentFormProps {
  firearm: Firearm
  onClose: () => void
  onSuccess: () => void
}

const FirearmAssignmentForm: React.FC<FirearmAssignmentFormProps> = ({
  firearm,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState({
    client_id: '',
    assigned_date: new Date().toISOString().split('T')[0],
    notes: ''
  })
  const [clients, setClients] = useState<SimpleClient[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoadingClients, setIsLoadingClients] = useState(false)
  const [currentAssignment, setCurrentAssignment] = useState<FirearmAssignment | null>(null)
  const [isReturning, setIsReturning] = useState(false)
  const [clientSearchTerm, setClientSearchTerm] = useState('')
  const [selectedClient, setSelectedClient] = useState<SimpleClient | null>(null)

  // Check if firearm is already assigned on component mount
  useEffect(() => {
    const checkAssignment = async () => {
      try {
        const supabase = getSupabase()

        // Check if firearm is already assigned
        const { data: assignmentData, error: assignmentError } = await supabase
          .from('firearm_assignments')
          .select('*, client:client_id(id, first_name, last_name)')
          .eq('firearm_id', firearm.id)
          .is('return_date', null)
          .single()

        if (assignmentError && assignmentError.code !== 'PGRST116') { // PGRST116 is the code for no rows returned
          console.error('Error checking assignment:', assignmentError)
        } else if (assignmentData) {
          setCurrentAssignment(assignmentData)
        }
      } catch (err: any) {
        console.error('Error checking assignment:', err)
        setError('Failed to check current assignment. Please try again.')
      }
    }

    checkAssignment()
  }, [])

  // Fetch clients with debounce for search
  const fetchClients = useCallback(
    debounce(async (searchTerm: string) => {
      try {
        setIsLoadingClients(true)
        const supabase = getSupabase()
        let query = supabase.from('clients').select('id, first_name, last_name, email, phone, id_number')

        if (searchTerm) {
          const trimmedTerm = searchTerm.trim();
          const terms = trimmedTerm.split(' ').filter(t => t.length > 0);

          // Check if it's an ID number (only digits)
          if (/^\d+$/.test(trimmedTerm)) {
            query = query.ilike('id_number', `%${trimmedTerm}%`);
          }
          // If multiple terms (likely a full name)
          else if (terms.length > 1) {
            // Try different combinations for first name and last name
            const possibleFirstName = terms[0];
            const possibleLastName = terms[terms.length - 1];

            // First name + last name pattern (most common case)
            query = query.or(
              `and(first_name.ilike.%${possibleFirstName}%,last_name.ilike.%${possibleLastName}%),` +
              // Also try the reverse in case names are entered in reverse order
              `and(first_name.ilike.%${possibleLastName}%,last_name.ilike.%${possibleFirstName}%),` +
              // Also check if the full search term is in either field (for compound names)
              `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%`
            );
          }
          // Single term (could be first name, last name, or partial ID)
          else {
            query = query.or(
              `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%,id_number.ilike.%${trimmedTerm}%`
            );
          }
        }

        const { data, error } = await query.limit(10)

        if (error) throw error
        setClients(data || [])
      } catch (error) {
        console.error('Error fetching clients:', error)
      } finally {
        setIsLoadingClients(false)
      }
    }, 300),
    []
  )

  // Handle client selection
  const handleClientSelect = (client: SimpleClient) => {
    setSelectedClient(client)
    setFormData(prev => ({
      ...prev,
      client_id: client.id,
    }))
    setClientSearchTerm('')
  }

  // Handle client search change
  const handleClientSearchChange = (searchTerm: string) => {
    setClientSearchTerm(searchTerm)
    fetchClients(searchTerm)
  }

  // Handle client removal
  const handleClientRemove = () => {
    setSelectedClient(null)
    setFormData(prev => ({ ...prev, client_id: '' }))
  }

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target

    // Convert text inputs to uppercase
    const uppercaseValue = typeof value === 'string' ? value.toUpperCase() : value

    setFormData(prev => ({
      ...prev,
      [name]: uppercaseValue
    }))
  }

  // Handle form submission for new assignment
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const supabase = getSupabase()

      // Validate required fields
      if (!formData.client_id) {
        setError('Please select a client')
        setLoading(false)
        return
      }

      if (!formData.assigned_date) {
        setError('Please enter an assignment date')
        setLoading(false)
        return
      }

      // Validate storage type - Owner type can only be assigned to the owner
      if (firearm.storage_type === 'Owner') {
        setError('Owner type firearms can only be assigned to the owner. Please change the storage type to Private or Dealer to assign to another client.')
        setLoading(false)
        return
      }

      // Check if firearm is already assigned
      const { data: existingAssignments, error: checkError } = await supabase
        .from('firearm_assignments')
        .select('*')
        .eq('firearm_id', firearm.id)
        .is('return_date', null)

      if (checkError) {
        throw checkError
      }

      if (existingAssignments && existingAssignments.length > 0) {
        setError('This firearm is already assigned to a client. Please return it first.')
        setLoading(false)
        return
      }

      // Prepare assignment data
      const assignedDate = new Date(formData.assigned_date)
      let freeStorageUntil: string | null = null

      // For Dealer type, calculate free_storage_until (12 months from assignment date)
      if (firearm.storage_type === 'Dealer') {
        const freeUntilDate = new Date(assignedDate)
        freeUntilDate.setFullYear(assignedDate.getFullYear() + 1) // Add 12 months
        freeStorageUntil = freeUntilDate.toISOString()
      }

      // Create new assignment with free_storage_until if applicable
      const { error: insertError } = await supabase
        .from('firearm_assignments')
        .insert({
          id: uuidv4(), // Generate a UUID for the assignment
          firearm_id: firearm.id,
          client_id: formData.client_id,
          assigned_date: formData.assigned_date,
          notes: formData.notes,
          free_storage_until: freeStorageUntil,
          last_charge_date: new Date(formData.assigned_date).toISOString(), // Initialize last_charge_date to assigned_date
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (insertError) {
        throw insertError
      }

      // WhatsApp notification functionality has been removed

      // Success
      onSuccess()
    } catch (err: any) {
      console.error('Error assigning firearm:', err)
      setError(err.message || 'An error occurred while assigning the firearm')
    } finally {
      setLoading(false)
    }
  }

  // Handle returning a firearm
  const handleReturn = async () => {
    if (!currentAssignment) return

    setIsReturning(true)
    setError(null)

    try {
      const supabase = getSupabase()

      // Update the assignment with a return date
      const { error: updateError } = await supabase
        .from('firearm_assignments')
        .update({
          return_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', currentAssignment.id)

      if (updateError) {
        throw updateError
      }

      // Success
      onSuccess()
    } catch (err: any) {
      console.error('Error returning firearm:', err)
      setError(err.message || 'An error occurred while returning the firearm')
    } finally {
      setIsReturning(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-stone-800 rounded-xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-white">
              {currentAssignment ? 'Firearm Assignment' : 'Assign Firearm to Client'}
            </h2>
            <button
              type="button"
              onClick={onClose}
              className="text-stone-400 hover:text-white transition-colors"
            >
              <DashboardIcons.Close className="w-6 h-6" />
            </button>
          </div>

          {/* Firearm details */}
          <div className="mb-6 p-4 bg-stone-700/50 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">
              {firearm.make} {firearm.model}
            </h3>
            <div className="text-sm text-stone-300">
              <div>Stock #: {firearm.stock_number}</div>
              <div>Serial: {firearm.serial}</div>
              <div>Owner: {firearm.full_name}</div>
              <div className="mt-2 pt-2 border-t border-stone-600">
                <span className="font-medium">Storage Type: </span>
                <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                  firearm.storage_type === 'Owner' ? 'bg-green-900/30 text-green-400' :
                  firearm.storage_type === 'Private' ? 'bg-purple-900/30 text-purple-400' :
                  firearm.storage_type === 'Dealer' ? 'bg-amber-900/30 text-amber-400' :
                  'bg-stone-900/30 text-stone-400'
                }`}>
                  {firearm.storage_type || 'Owner'}
                </span>

                {firearm.storage_type === 'Dealer' && firearm.free_storage_until && (
                  <div className="mt-1 text-amber-400">
                    Free storage until: {new Date(firearm.free_storage_until).toLocaleDateString()}
                  </div>
                )}
              </div>
            </div>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-900/30 border border-red-700 rounded-lg text-red-400">
              {error}
            </div>
          )}

          {currentAssignment ? (
            <div className="space-y-4">
              <div className="p-4 bg-blue-900/20 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-2">Current Assignment</h3>
                {currentAssignment.client && (
                  <div className="mb-3">
                    <div className="text-sm text-stone-400">Assigned To:</div>
                    <div className="text-md font-medium text-white">
                      {currentAssignment.client.first_name} {currentAssignment.client.last_name}
                    </div>
                  </div>
                )}
                <div className="mb-3">
                  <div className="text-sm text-stone-400">Assigned Date:</div>
                  <div className="text-md text-white">
                    {new Date(currentAssignment.assigned_date).toLocaleDateString()}
                  </div>
                </div>
                {currentAssignment.notes && (
                  <div className="mb-3">
                    <div className="text-sm text-stone-400">Notes:</div>
                    <div className="text-md text-white">{currentAssignment.notes}</div>
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleReturn}
                  disabled={isReturning}
                  className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
                >
                  {isReturning && <DashboardIcons.Spinner className="w-4 h-4 animate-spin" />}
                  Return Firearm
                </button>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                {/* Client Selection */}
                <div>
                  <label className="block text-sm font-medium text-stone-300 mb-1">
                    Select Client <span className="text-orange-500">*</span>
                  </label>

                  {selectedClient ? (
                    <div className="mb-4 p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-semibold text-white">
                            {selectedClient.first_name.toUpperCase()} {selectedClient.last_name.toUpperCase()}
                          </h3>
                          <p className="text-stone-400 text-sm">ID: {selectedClient.id_number.toUpperCase()}</p>
                          <p className="text-stone-400 text-sm">{selectedClient.email.toUpperCase()}</p>
                        </div>
                        <button
                          type="button"
                          onClick={handleClientRemove}
                          className="text-orange-400 hover:text-orange-300"
                        >
                          <DashboardIcons.Close className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <FormField
                        label=""
                        name="client-search"
                        value={clientSearchTerm}
                        onChange={(e) => handleClientSearchChange(e.target.value)}
                        placeholder="Search by name or ID number"
                      />

                      {isLoadingClients && (
                        <div className="flex items-center justify-center p-2 mt-2">
                          <DashboardIcons.Spinner className="w-5 h-5 animate-spin text-orange-500" />
                          <span className="ml-2 text-stone-400">Searching clients...</span>
                        </div>
                      )}

                      {clients.length > 0 && clientSearchTerm && (
                        <div className="mt-2 bg-stone-700/50 rounded-lg border border-stone-600/50 max-h-60 overflow-y-auto">
                          {clients.map(client => (
                            <div
                              key={client.id}
                              onClick={() => handleClientSelect(client)}
                              className="p-3 hover:bg-orange-500/20 cursor-pointer border-b border-stone-600/30 last:border-0 transition-colors"
                            >
                              <p className="text-white font-medium">
                                {client.first_name.toUpperCase()} {client.last_name.toUpperCase()}
                              </p>
                              <p className="text-stone-400 text-sm">ID: {client.id_number.toUpperCase()}</p>
                            </div>
                          ))}
                        </div>
                      )}

                      {clientSearchTerm && clients.length === 0 && !isLoadingClients && (
                        <p className="mt-2 text-stone-400 text-sm">No clients found. Try a different search term.</p>
                      )}
                    </div>
                  )}
                </div>

                {/* Assignment Date */}
                <FormField
                  label="Assignment Date"
                  name="assigned_date"
                  value={formData.assigned_date}
                  onChange={handleChange}
                  type="date"
                  required
                  helpText={firearm.storage_type === 'Dealer' ? "For Dealer type, free storage period of 12 months will start from this date" : undefined}
                />

                {/* Notes */}
                <FormField
                  label="Notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  type="textarea"
                  rows={3}
                  placeholder="Add any notes about this assignment..."
                />

                {/* Form Actions */}
                <div className="flex justify-end gap-3 mt-6">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading || isLoadingClients}
                    className="px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-lg transition-colors flex items-center gap-2"
                  >
                    {loading && <DashboardIcons.Spinner className="w-4 h-4 animate-spin" />}
                    Assign Firearm
                  </button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  )
}

export default FirearmAssignmentForm
