import React from 'react'
import { NotificationStatus } from './types'

interface NotificationToastProps {
  notificationStatus: NotificationStatus
}

export const NotificationToast: React.FC<NotificationToastProps> = ({ notificationStatus }) => {
  if (!notificationStatus.error && !notificationStatus.success) {
    return null
  }

  return (
    <div
      className={`fixed bottom-4 left-1/2 transform -translate-x-1/2 px-5 py-3 rounded-xl shadow-2xl backdrop-blur-sm flex items-center gap-3 z-50 ${
        notificationStatus.error ? 'bg-red-500/90 text-white' : 'bg-green-500/90 text-white'
      }`}
    >
      {notificationStatus.error ? (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clipRule="evenodd"
          />
        </svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
            clipRule="evenodd"
          />
        </svg>
      )}
      <span className="font-medium">{notificationStatus.error || notificationStatus.success}</span>
    </div>
  )
}
