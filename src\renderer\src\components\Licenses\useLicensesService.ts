import { useState, useEffect, useCallback, useMemo } from 'react'
import { getOrInitSupabase } from '../../lib/supabase'
import { License, Client } from '../../types'
import { getAppropriateClient } from '../../utils/authUtils'
import {
  FormState,
  DeleteDialogState,
  ToastState,
  FilterState,
  PaginationState,
  VirtualizationResult,
  LicenseSearchTips
} from './types'

export const useLicensesService = () => {
  // State declarations
  const [licenses, setLicenses] = useState<License[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [page, setPage] = useState<PaginationState['page']>(1)
  const [perPage] = useState<PaginationState['perPage']>(5)
  const [activeFilter, setActiveFilter] = useState<FilterState['activeFilter']>('all')
  const [formState, setFormState] = useState<FormState>({
    type: null,
    isOpen: false,
    selectedLicense: null,
    selectedClientId: null
  })
  const [deleteDialog, setDeleteDialog] = useState<DeleteDialogState>({
    isOpen: false,
    licenseId: null
  })
  const [toast, setToast] = useState<ToastState | null>(null)
  const [focusedLicenseId, setFocusedLicenseId] = useState<string | null>(null)

  // Fetch licenses
  const fetchLicenses = useCallback(async (): Promise<void> => {
    try {
      setLoading(true)
      // Use service role client to bypass RLS if needed
      const supabase = await getAppropriateClient(true)
      const { data, error } = await supabase
        .from('gun_licences')
        .select(
          `
          id,
          make,
          type,
          caliber,
          serial_number,
          section,
          expiry_date,
          stock_code,
          lic_number,
          issue_date,
          clients:client_id (
            id,
            first_name,
            last_name,
            email,
            phone,
            id_number,
            address,
            city,
            state,
            postal_code
          )
        `
        )
        .order('expiry_date', { ascending: true })

      if (error) throw error
      const transformedData =
        data?.map((license) => ({
          ...license,
          client_id: (license.clients as unknown as Client)?.id,
          client: license.clients as unknown as Client,
          license_number: license.lic_number,
          issue_date: license.issue_date,
          expiry_date: license.expiry_date,
          firearm_type: license.type,
          caliber: license.caliber || '',
          section: license.section || ''
        })) || []
      setLicenses(transformedData)
    } catch (error) {
      console.error('Error fetching licenses:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // Custom virtualization hook implementation
  function useVirtualization<T>(
    items: T[],
    page: number,
    perPage: number
  ): VirtualizationResult<T> {
    return useMemo(() => {
      const lastIndex = page * perPage
      const firstIndex = lastIndex - perPage
      return {
        items: items.slice(firstIndex, lastIndex),
        total: items.length
      }
    }, [items, page, perPage])
  }

  // Filter licenses based on active filter and search query
  const filteredLicenses = useMemo(() => {
    // First, apply the active filter
    let filteredByStatus = [...licenses]
    const today = new Date()

    if (activeFilter === 'expired') {
      filteredByStatus = filteredByStatus.filter((license) => {
        if (!license.expiry_date) return false
        const expiry = new Date(license.expiry_date)
        return expiry < today
      })
    } else if (activeFilter === 'expiring') {
      filteredByStatus = filteredByStatus.filter((license) => {
        if (!license.expiry_date) return false
        const expiry = new Date(license.expiry_date)
        const daysUntilExpiry = Math.ceil(
          (expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
        )
        return daysUntilExpiry >= 0 && daysUntilExpiry <= 30
      })
    }

    // Then, apply search query if present
    if (!searchQuery) return filteredByStatus

    const query = searchQuery.toLowerCase().trim()
    return filteredByStatus.filter((license) => {
      const client = license.client as Client
      return (
        // License properties
        license.make?.toLowerCase().includes(query) ||
        license.firearm_type?.toLowerCase().includes(query) ||
        license.caliber?.toLowerCase().includes(query) ||
        license.serial_number?.toLowerCase().includes(query) ||
        license.license_number?.toLowerCase().includes(query) ||
        license.section?.toLowerCase().includes(query) ||
        // Client properties
        client?.first_name?.toLowerCase().includes(query) ||
        client?.last_name?.toLowerCase().includes(query) ||
        client?.id_number?.toLowerCase().includes(query) ||
        client?.phone?.toLowerCase().includes(query) ||
        client?.email?.toLowerCase().includes(query) ||
        // Combined properties
        `${client?.first_name} ${client?.last_name}`.toLowerCase().includes(query)
      )
    })
  }, [licenses, activeFilter, searchQuery])

  // Apply virtualization to filtered licenses
  const { items: visibleLicenses, total } = useVirtualization(filteredLicenses, page, perPage)

  // Get the focused license
  const focusedLicense = useMemo(() => {
    if (!focusedLicenseId) return null
    return licenses.find((license) => license.id === focusedLicenseId) || null
  }, [licenses, focusedLicenseId])

  // Handle license focus toggle
  const handleLicenseFocusToggle = (licenseId: string | null) => {
    setFocusedLicenseId(licenseId)
  }

  // Fetch licenses on component mount
  useEffect(() => {
    fetchLicenses()
  }, [fetchLicenses])

  // Reset page when filter or search changes
  useEffect(() => {
    setPage(1)
  }, [activeFilter, searchQuery])

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  // Handle delete license
  const handleDeleteLicense = async (licenseId: string) => {
    try {
      // Use service role client for admin operations
      const supabase = await getAppropriateClient(true)
      const { error } = await supabase.from('gun_licences').delete().eq('id', licenseId)

      if (error) throw error

      // Close the dialog
      setDeleteDialog({ isOpen: false, licenseId: null })

      // Show success toast
      setToast({
        message: 'License deleted successfully',
        type: 'success'
      })

      // Refresh licenses
      fetchLicenses()

      // Clear focus if the deleted license was focused
      if (focusedLicenseId === licenseId) {
        setFocusedLicenseId(null)
      }
    } catch (error) {
      console.error('Error deleting license:', error)
      setToast({
        message: 'Failed to delete license',
        type: 'error'
      })
    }
  }

  // Handle edit license
  const onEditLicense = (license: License, clientId: string) => {
    setFormState({
      type: 'license',
      isOpen: true,
      selectedLicense: license,
      selectedClientId: clientId
    })
  }

  // Reset form state
  const resetFormState = () => {
    setFormState({
      type: null,
      isOpen: false,
      selectedLicense: null,
      selectedClientId: null
    })
  }

  // Handle add license button click
  const handleAddLicense = () => {
    // Show a message that they need to add a license from the client page
    setToast({
      message: 'Add a license from the client page',
      type: 'info'
    })
  }

  // Custom search tips for licenses
  const licenseSearchTips: LicenseSearchTips = {
    title: 'Search tips:',
    items: [
      'Search by make (e.g., "Glock")',
      'Search by type (e.g., "Pistol")',
      'Search by caliber (e.g., "9mm")',
      'Search by serial number',
      'Search by license number',
      'Search by client name'
    ]
  }

  return {
    // State
    licenses,
    loading,
    searchQuery,
    page,
    perPage,
    activeFilter,
    formState,
    deleteDialog,
    toast,
    focusedLicenseId,
    focusedLicense,
    visibleLicenses,
    total,
    licenseSearchTips,

    // Actions
    setPage,
    setActiveFilter,
    handleSearch,
    fetchLicenses,
    handleDeleteLicense,
    onEditLicense,
    resetFormState,
    setDeleteDialog,
    setToast,
    handleLicenseFocusToggle,
    handleAddLicense
  }
}
