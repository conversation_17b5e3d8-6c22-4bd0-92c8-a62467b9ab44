import React from 'react'
import { FormSection, FormField, RadioGroup } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Spouse Information section component
 */
const SpouseInfo: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    updateFormData({
      [name]: value
    })
  }

  const handleSpouseIdTypeChange = (value: string) => {
    updateFormData({
      spouseIdType: value as 'spouseId' | 'spousePort' | 'none' | ''
    })
  }

  if (formData.maritalStatus !== 'married') {
    return null
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <FormSection title="Particulars of Spouse/Partner" subtitle="Details of your spouse or partner">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <RadioGroup
              label="Spouse ID Type"
              name="spouseIdType"
              value={formData.spouseIdType || 'none'}
              onChange={handleSpouseIdTypeChange}
              options={[
                { value: 'spouseId', label: 'South African ID' },
                { value: 'spousePort', label: 'Passport' }
              ]}
            />
          </div>

          {formData.spouseIdType === 'spouseId' && (
            <FormField
              label="Spouse ID Number"
              name="spouseIdNo"
              value={formData.spouseIdNo || ''}
              onChange={handleChange}
              type="text"
              placeholder="Enter spouse's ID number"
              pattern="[0-9]{13}"
              maxLength={13}
            />
          )}

          {formData.spouseIdType === 'spousePort' && (
            <FormField
              label="Spouse Passport Number"
              name="spousePassN"
              value={formData.spousePassN || ''}
              onChange={handleChange}
              type="text"
              placeholder="Enter spouse's passport number"
            />
          )}
        </div>
      </FormSection>
    </div>
  )
}

export default SpouseInfo
