import React, { useState, useEffect } from 'react'
import { useWindowSize } from '../../contexts/WindowSizeContext'

// Define the template placeholders data structure for the download functionality
const placeholderData = [
  {
    title: 'Application Type',
    items: [
      { key: '{MainHF}', value: 'Main Heading Form (Mark with X)' },
      { key: '{AddHF}', value: 'Additional Heading Form (Mark with X)' }
    ]
  },
  {
    title: 'License Types',
    items: [
      { key: '{S13}', value: 'Section 13 - Self-defence (Mark with X)' },
      { key: '{S15}', value: 'Section 15 - Occasional hunting/sport (Mark with X)' },
      { key: '{S16}', value: 'Section 16 - Dedicated hunting/sport (Mark with X)' },
      { key: '{S20}', value: 'Section 20 - Business - hunting (Mark with X)' },
      { key: '{S20A}', value: 'Section 20A - Business - other (Mark with X)' },
      { key: '{S20B}', value: 'Section 20B - Business - security (Mark with X)' },
      { key: '{S20C}', value: 'Section 20C - Business - training (Mark with X)' }
    ]
  },
  {
    title: 'Personal Info',
    items: [
      { key: '{FirstName}', value: 'First Name' },
      { key: '{LastName}', value: 'Last Name' },
      { key: '{Name}', value: 'Full Name' },
      { key: '{Cell}', value: 'Phone Number' },
      { key: '{Email}', value: 'Email Address' },
      { key: '{Address}', value: 'Physical Address' },
      { key: '{POSTALCODE}', value: 'Postal Code' },
      { key: '{ID}', value: 'ID Number' },
      { key: '{PASSPORT}', value: 'Passport Number' },
      { key: '{Initials}', value: 'Initials' },
      { key: '{AGE}', value: 'Age' }
    ]
  },
  {
    title: 'Professional Info',
    items: [
      { key: '{Company}', value: 'Company Name' },
      { key: '{Trade}', value: 'Trade/Profession' },
      { key: '{WorkA}', value: 'Work Address' },
      { key: '{WorkP}', value: 'Work Postal Code' },
      { key: '{WorkC}', value: 'Work Contact' }
    ]
  },
  {
    title: 'Firearm Types',
    items: [
      { key: '{Rifle}', value: 'Rifle (Mark with X)' },
      { key: '{Shotgun}', value: 'Shotgun (Mark with X)' },
      { key: '{Pistol}', value: 'Pistol (Mark with X)' },
      { key: '{Comb}', value: 'Combination Firearm (Mark with X)' },
      { key: '{OtherDesign}', value: 'Other Design (Mark with X)' },
      { key: '{OtherDesignE}', value: 'Other Design Description' }
    ]
  },
  {
    title: 'SAP 350 (A) DETAILS',
    items: [
      { key: '{SAP350A_NAME}', value: 'Name' },
      { key: '{SAP350A_ID_FAR}', value: 'Identification number or FAR number' },
      { key: '{SAP350A_ADDRESS}', value: 'Address' },
      { key: '{SAP350A_POSTAL}', value: 'Postal code' },
      { key: '{SAP350A_DATE}', value: 'Date received' }
    ]
  },
  {
    title: 'Firearm Action Types',
    items: [
      { key: '{Semi}', value: 'Semi-automatic (Mark with X)' },
      { key: '{Auto}', value: 'Automatic (Mark with X)' },
      { key: '{Man}', value: 'Manual (Mark with X)' },
      { key: '{OtherF}', value: 'Other Action Description' }
    ]
  },
  {
    title: 'Firearm Details',
    items: [
      { key: '{Section}', value: 'Firearm Section' },
      { key: '{Make}', value: 'Firearm Make' },
      { key: '{Model}', value: 'Firearm Model' },
      { key: '{SerialNumber}', value: 'Serial Number' },
      { key: '{Caliber}', value: 'Caliber' },
      { key: '{ENGG}', value: 'Names and Addresses engraved in metal' },
      { key: '{PAFK}', value: 'Physical address where firearm is kept' },
      { key: '{PAFKPOSTAL}', value: 'Postal Code for firearm address' },
      { key: '{FLAP}', value: 'Name of current owner/authorized person' },
      { key: '{FLID}', value: 'ID number of current owner/authorized person' },
      { key: '{DESIGNATION}', value: 'Designation' },
      { key: '{APDATE}', value: 'Date' },
      { key: '{APPLACE}', value: 'Place' }
    ]
  },
  {
    title: 'Firearms in Possession',
    items: [
      // Row 1
      { key: '{FIP_TYPE_1}', value: 'Type - Row 1' },
      { key: '{FIP_CALIBRE_1}', value: 'Calibre - Row 1' },
      { key: '{FIP_MAKE_1}', value: 'Make - Row 1' },
      { key: '{FIP_BARREL_1}', value: 'Barrel Serial No - Row 1' },
      { key: '{FIP_FRAME_1}', value: 'Frame/receiver Serial No - Row 1' },
      { key: '{FIP_LICENCE_1}', value: 'Licence authorization No - Row 1' },
      // Row 2
      { key: '{FIP_TYPE_2}', value: 'Type - Row 2' },
      { key: '{FIP_CALIBRE_2}', value: 'Calibre - Row 2' },
      { key: '{FIP_MAKE_2}', value: 'Make - Row 2' },
      { key: '{FIP_BARREL_2}', value: 'Barrel Serial No - Row 2' },
      { key: '{FIP_FRAME_2}', value: 'Frame/receiver Serial No - Row 2' },
      { key: '{FIP_LICENCE_2}', value: 'Licence authorization No - Row 2' },
      // Row 3
      { key: '{FIP_TYPE_3}', value: 'Type - Row 3' },
      { key: '{FIP_CALIBRE_3}', value: 'Calibre - Row 3' },
      { key: '{FIP_MAKE_3}', value: 'Make - Row 3' },
      { key: '{FIP_BARREL_3}', value: 'Barrel Serial No - Row 3' },
      { key: '{FIP_FRAME_3}', value: 'Frame/receiver Serial No - Row 3' },
      { key: '{FIP_LICENCE_3}', value: 'Licence authorization No - Row 3' },
      // Rows 4-10 follow the same pattern
      { key: '{FIP_TYPE_4}', value: 'Type - Row 4' },
      { key: '{FIP_TYPE_5}', value: 'Type - Row 5' },
      { key: '{FIP_TYPE_6}', value: 'Type - Row 6' },
      { key: '{FIP_TYPE_7}', value: 'Type - Row 7' },
      { key: '{FIP_TYPE_8}', value: 'Type - Row 8' },
      { key: '{FIP_TYPE_9}', value: 'Type - Row 9' },
      { key: '{FIP_TYPE_10}', value: 'Type - Row 10' }
    ]
  },
  {
    title: 'Current Owner Types',
    items: [
      { key: '{PCOA}', value: 'Private Owner (Mark with X)' },
      { key: '{PCOB}', value: 'Firearm Dealer (Mark with X)' },
      { key: '{PCOC}', value: 'Company (Mark with X)' },
      { key: '{PCOE}', value: 'Estate (Mark with X)' },
      { key: '{POAFHY}', value: 'Additional Firearm Licence Holders - Yes (Mark with X)' },
      { key: '{POAFHN}', value: 'Additional Firearm Licence Holders - No (Mark with X)' }
    ]
  },
  {
    title: 'Firearm Component Details',
    items: [
      { key: '{BSN}', value: 'Barrel Serial Number' },
      { key: '{FSN}', value: 'Frame Serial Number' },
      { key: '{RSN}', value: 'Receiver Serial Number' },
      { key: '{BSNM}', value: 'Barrel Make' },
      { key: '{FSNM}', value: 'Frame Make' },
      { key: '{RSNM}', value: 'Receiver Make' }
    ]
  },
  {
    title: 'Executor Info',
    items: [
      { key: '{EXECUTORNAME}', value: 'Executor Name' },
      { key: '{EXECUTORNUMBER}', value: 'Executor Phone' },
      { key: '{ExecutorAddress}', value: 'Executor Address' }
    ]
  },
  {
    title: 'Competency Form',
    items: [
      { key: '{TradeFirearm}', value: 'Trade in Firearm' },
      { key: '{PossessFirearm}', value: 'To Possess Firearm' },
      { key: '{Pistol}', value: 'Pistol' },
      { key: '{Rifle}', value: 'Rifle' },
      { key: '{Shotgun}', value: 'Shotgun' },
      { key: '{Revolver}', value: 'Revolver' },
      { key: '{SelfLoading}', value: 'X' },
      { key: '{SelfLoadingT}', value: 'Self-Loading' }
    ]
  },
  {
    title: 'Applicant Particulars',
    items: [
      { key: '{SAID}', value: 'SA Citizen' },
      { key: '{FID}', value: 'Foreign Citizen' },
      { key: '{PermRes}', value: 'Permanent Resident' },
      { key: '{SEXM}', value: 'Sex Male' },
      { key: '{SEXF}', value: 'Sex Female' },
      { key: '{BirthDate}', value: 'Birth Date' },
      { key: '{HOUSETYPE}', value: 'House Type' }
    ]
  },
  {
    title: 'Marital Status',
    items: [
      { key: '{SINGLES}', value: 'Single' },
      { key: '{MARRIED}', value: 'Married' },
      { key: '{DIVORCED}', value: 'Divorced' },
      { key: '{WIDOWER}', value: 'Widower' },
      { key: '{WIDOW}', value: 'Widow' }
    ]
  },
  {
    title: 'Spouse Particulars',
    items: [
      { key: '{SPOUSEID}', value: 'Spouse ID' },
      { key: '{SPOUSEPORT}', value: 'Spouse Passport' },
      { key: '{SPOUSEIDNO}', value: 'Spouse ID Number' },
      { key: '{SPOUSEPASSN}', value: 'Spouse Passport Number' },
      { key: '{SPOUSEFULLNAME}', value: 'Spouse Full Name' }
    ]
  },
  {
    title: 'Association Membership',
    items: [
      { key: '{F5A}', value: 'Member of Association - Yes (Mark with X)' },
      { key: '{F5B}', value: 'Member of Association - No (Mark with X)' },
      { key: '{F6}', value: 'Association Name' },
      { key: '{ASSFARN}', value: 'Association FAR number' },
      { key: '{F7}', value: 'Membership Number' },
      { key: '{F8}', value: 'Date Joined Association' },
      { key: '{ASSEXPIRE}', value: 'Date of Expiry of Membership' }
    ]
  },
  {
    title: 'Training Institution',
    items: [
      { key: '{NameInst}', value: 'Training Institution Name' },
      { key: '{SerialCert}', value: 'Serial Certificate' },
      { key: '{CertIssueDate}', value: 'Date Certificate Issued' }
    ]
  },
  {
    title: 'Competency Certificate Types',
    items: [
      { key: '{D1.6A1}', value: 'Handgun' },
      { key: '{D1.6B1}', value: 'Certificate Number' },
      { key: '{D1.6C1}', value: 'Date of Issue' },
      { key: '{D1.6D1}', value: 'Expiry Date' },

      { key: '{D1.6A2}', value: 'Handgun & Rifle' },
      { key: '{D1.6B2}', value: 'Certificate Number' },
      { key: '{D1.6C2}', value: 'Date of Issue' },
      { key: '{D1.6D2}', value: 'Expiry Date' },

      { key: '{D1.6A3}', value: 'Rifle' },
      { key: '{D1.6B3}', value: 'Certificate Number' },
      { key: '{D1.6C3}', value: 'Date of Issue' },
      { key: '{D1.6D3}', value: 'Expiry Date' },

      { key: '{D1.6A4}', value: 'Shotgun' },
      { key: '{D1.6B4}', value: 'Certificate Number' },
      { key: '{D1.6C4}', value: 'Date of Issue' },
      { key: '{D1.6D4}', value: 'Expiry Date' },

      { key: '{D1.6A5}', value: 'Shotgun & Handgun' },
      { key: '{D1.6B5}', value: 'Certificate Number' },
      { key: '{D1.6C5}', value: 'Date of Issue' },
      { key: '{D1.6D5}', value: 'Expiry Date' },

      { key: '{D1.6A6}', value: 'Rifle & Shotgun' },
      { key: '{D1.6B6}', value: 'Certificate Number' },
      { key: '{D1.6C6}', value: 'Date of Issue' },
      { key: '{D1.6D6}', value: 'Expiry Date' },

      { key: '{D1.6A7}', value: 'Handgun & Rifle & Shotgun' },
      { key: '{D1.6B7}', value: 'Certificate Number' },
      { key: '{D1.6C7}', value: 'Date of Issue' },
      { key: '{D1.6D7}', value: 'Expiry Date' },

      { key: '{D1.6A8}', value: 'Hand Machine Carbine' },
      { key: '{D1.6B8}', value: 'Certificate Number' },
      { key: '{D1.6C8}', value: 'Date of Issue' },
      { key: '{D1.6D8}', value: 'Expiry Date' },

      { key: '{D1.6A9}', value: 'Handgun & Hand Machine Carbine' },
      { key: '{D1.6B9}', value: 'Certificate Number' },
      { key: '{D1.6C9}', value: 'Date of Issue' },
      { key: '{D1.6D9}', value: 'Expiry Date' },

      { key: '{D1.6A10}', value: 'Handgun & Rifle & Hand Machine Carbine' },
      { key: '{D1.6B10}', value: 'Certificate Number' },
      { key: '{D1.6C10}', value: 'Date of Issue' },
      { key: '{D1.6D10}', value: 'Expiry Date' },

      { key: '{D1.6A11}', value: 'Handgun & Rifle & Shotgun & Hand Machine Carbine' },
      { key: '{D1.6B11}', value: 'Certificate Number' },
      { key: '{D1.6C11}', value: 'Date of Issue' },
      { key: '{D1.6D11}', value: 'Expiry Date' },

      { key: '{D1.6A12}', value: 'Rifle & Hand Machine Carbine' },
      { key: '{D1.6B12}', value: 'Certificate Number' },
      { key: '{D1.6C12}', value: 'Date of Issue' },
      { key: '{D1.6D12}', value: 'Expiry Date' },

      { key: '{D1.6A13}', value: 'Rifle & Shotgun & Hand Machine Carbine' },
      { key: '{D1.6B13}', value: 'Certificate Number' },
      { key: '{D1.6C13}', value: 'Date of Issue' },
      { key: '{D1.6D13}', value: 'Expiry Date' },

      { key: '{D1.6A14}', value: 'Shotgun & Hand Machine Carbine' },
      { key: '{D1.6B14}', value: 'Certificate Number' },
      { key: '{D1.6C14}', value: 'Date of Issue' },
      { key: '{D1.6D14}', value: 'Expiry Date' }
    ]
  },
  {
    title: 'Competency Expiry Status',
    items: [
      { key: '{15A}', value: 'Competency Expiry Yes' },
      { key: '{15B}', value: 'Competency Expiry No' },
      { key: '{15C}', value: 'Competency Expiry Reason' },
      { key: '{16A}', value: 'After Expiry Yes' },
      { key: '{16B}', value: 'After Expiry No' },
      { key: '{16C}', value: 'After Expiry Reason' }
    ]
  },
  {
    title: 'Criminal Record',
    items: [
      { key: '{H5A}', value: 'Offense Yes' },
      { key: '{H5B}', value: 'Offense No' },
      { key: '{H5.1}', value: 'Police Station' },
      { key: '{H5.2}', value: 'Case Number' },
      { key: '{H5.3}', value: 'Charge' },
      { key: '{H5.4}', value: 'Outcome/Verdict' }
    ]
  },
  {
    title: 'Pending Cases',
    items: [
      { key: '{H6A}', value: 'Pending Case Yes' },
      { key: '{H6B}', value: 'Pending Case No' },
      { key: '{H6.1}', value: 'Police Station' },
      { key: '{H6.2}', value: 'Case Number' },
      { key: '{H6.A3}', value: 'Offence' }
    ]
  },
  {
    title: 'Lost/Stolen Firearms',
    items: [
      { key: '{H7A}', value: 'Lost/Stolen Yes' },
      { key: '{H7B}', value: 'Lost/Stolen No' },
      { key: '{H7.1}', value: 'Police Station' },
      { key: '{H7.2}', value: 'Case Number' },
      { key: '{H7.3}', value: 'Circumstances' },
      { key: '{H7.4}', value: 'Details of Firearm' }
    ]
  },
  {
    title: 'Negligence Investigation',
    items: [
      { key: '{H8A}', value: 'Investigation Yes' },
      { key: '{H8B}', value: 'Investigation No' },
      { key: '{H8.1}', value: 'Investigation Police Station' },
      { key: '{H8.2}', value: 'Investigation Case Number' },
      { key: '{H8.3}', value: 'Investigation Charge' },
      { key: '{H8.4}', value: 'Investigation Outcome/Verdict' }
    ]
  },
  {
    title: 'Declared Unfit',
    items: [
      { key: '{H9A}', value: 'Declared Unfit Yes' },
      { key: '{H9B}', value: 'Declared Unfit No' },
      { key: '{H9.1}', value: 'Police Station' },
      { key: '{H9.2}', value: 'Case Number' },
      { key: '{H9.3}', value: 'Charge' },
      { key: '{H9.4}', value: 'Date from which unfit' },
      { key: '{H9.5}', value: 'Period of Unfitness' }
    ]
  },
  {
    title: 'Confiscated',
    items: [
      { key: '{H10A}', value: 'Confiscated Yes' },
      { key: '{H10B}', value: 'Confiscated No' },
      { key: '{H10.1}', value: 'Police Station' },
      { key: '{H10.2}', value: 'Case Number' },
      { key: '{H10.3}', value: 'Circumstances' },
      { key: '{H10.4}', value: 'Outcome/Verdict' }
    ]
  },
  {
    title: 'Protection Allegations',
    items: [
      { key: '{H11A}', value: 'Protection Allegations Yes' },
      { key: '{H11B}', value: 'Protection Allegations No' },
      { key: '{H11.1}', value: 'Details' }
    ]
  },
  {
    title: 'Denied Firearm License/Permit',
    items: [
      { key: '{H12A}', value: 'Denied Firearm (Mark Yes X)' },
      { key: '{H12B}', value: 'Denied Firearm (Mark No X)' },
      { key: '{H12A1}', value: 'Denied Firearm give reason' },
      { key: '{H12.1}', value: 'Police Station' },
      { key: '{H12.2}', value: 'Case Number' }
    ]
  },
  {
    title: 'Under Age of 21',
    items: [
      { key: '{H17.1A}', value: 'Conduct Business' },
      { key: '{H17.1B}', value: 'Gainfully Employed' },
      { key: '{H17.1C}', value: 'Dedicated Hunter' },
      { key: '{H17.1D}', value: 'Dedicated Sport-Personal' },
      { key: '{H17.1E}', value: 'Private Collector' },
      { key: '{H17.1F}', value: 'Public Collector' },
      { key: '{H17.1G}', value: 'Other' },
      { key: '{H17.1H}', value: 'Submit Full Details' }
    ]
  },
  {
    title: 'Safe Storage',
    items: [
      { key: '{SAFEYES}', value: 'Safe - Yes (Mark with X)' },
      { key: '{SAFENO}', value: 'Safe - No (Mark with X)' },
      { key: '{SAFEH}', value: 'Safe - Home (Mark with X)' },
      { key: '{SAFER}', value: 'Safe - Retail (Mark with X)' },
      { key: '{SAFES}', value: 'Safe - Strongroom (Mark with X)' },
      { key: '{SAFED}', value: 'Safe - Display (Mark with X)' },
      { key: '{SAFESE}', value: 'Safe - Security Equipment' },
      { key: '{SAFEDINFO}', value: 'Safe - Display Info' },
      { key: '{SAFEMOUNTYES}', value: 'Safe Mount - Yes (Mark with X)' },
      { key: '{SAFEMOUNTNO}', value: 'Safe Mount - No (Mark with X)' },
      { key: '{SAFEWALL}', value: 'Safe Wall (Mark with X)' },
      { key: '{SAFEFLOOR}', value: 'Safe Floor (Mark with X)' }
    ]
  }
]

// Group placeholders into logical sections
const placeholderSections = [
  {
    id: 'application',
    title: 'Application & License',
    categories: ['Application Type', 'License Types']
  },
  {
    id: 'personal',
    title: 'Personal & Professional',
    categories: ['Personal Info', 'Professional Info']
  },
  {
    id: 'firearm',
    title: 'Firearm & Competency',
    categories: [
      'Firearm Types',
      'SAP 350 (A) DETAILS',
      'Firearm Action Types',
      'Firearm Details',
      'Firearm Component Details',
      'Firearms in Possession',
      'Current Owner Types',
      'Competency Form',
      'Training Institution',
      'Competency Certificate Types',
      'Competency Expiry Status'
    ]
  },
  {
    id: 'legal',
    title: 'Legal & Records',
    categories: [
      'Criminal Record',
      'Pending Cases',
      'Lost/Stolen Firearms',
      'Negligence Investigation',
      'Declared Unfit',
      'Confiscated'
    ]
  },
  {
    id: 'other',
    title: 'Other Information',
    categories: [
      'Executor Info',
      'Applicant Particulars',
      'Marital Status',
      'Spouse Particulars',
      'Association Membership',
      'Protection Allegations',
      'Denied Firearm License/Permit',
      'Under Age of 21',
      'Safe Storage'
    ]
  }
]

// New filter categories
const fieldTypes = [
  {
    id: 'text',
    title: 'Text Fields',
    description: 'Fields that require text input',
    keys: [
      '{FirstName}',
      '{LastName}',
      '{Name}',
      '{Cell}',
      '{Email}',
      '{Address}',
      '{ID}',
      '{PASSPORT}',
      '{Initials}',
      '{Company}',
      '{Trade}',
      '{WorkA}',
      '{WorkP}',
      '{WorkC}',
      '{Make}',
      '{Model}',
      '{SerialNumber}',
      '{Caliber}',
      '{ENGG}',
      '{PAFK}',
      '{FLAP}',
      '{OtherDesignE}',
      '{OtherF}',
      '{BSN}',
      '{FSN}',
      '{RSN}',
      '{BSNM}',
      '{FSNM}',
      '{RSNM}',
      '{EXECUTORNAME}',
      '{EXECUTORNUMBER}',
      '{ExecutorAddress}',
      '{NameInst}',
      '{SerialCert}',
      '{SPOUSEFULLNAME}',
      '{F6}',
      '{ASSFARN}',
      '{F7}',
      '{F8}',
      '{ASSEXPIRE}',
      '{SAFESE}',
      '{SAFEDINFO}',
      '{15C}',
      '{16C}'
    ]
  },
  {
    id: 'checkbox',
    title: 'Checkboxes',
    description: 'Fields that are marked with X',
    keys: [
      '{MainHF}',
      '{AddHF}',
      '{S13}',
      '{S15}',
      '{S16}',
      '{S20}',
      '{S20A}',
      '{S20B}',
      '{S20C}',
      '{Rifle}',
      '{Shotgun}',
      '{Pistol}',
      '{Comb}',
      '{OtherDesign}',
      '{Semi}',
      '{Auto}',
      '{Man}',
      '{PCOA}',
      '{PCOB}',
      '{PCOC}',
      '{PCOE}',
      '{POAFHY}',
      '{POAFHN}',
      '{SAID}',
      '{FID}',
      '{PermRes}',
      '{SEXM}',
      '{SEXF}',
      '{TradeFirearm}',
      '{PossessFirearm}',
      '{Revolver}',
      '{SelfLoading}',
      '{SINGLES}',
      '{MARRIED}',
      '{DIVORCED}',
      '{WIDOWER}',
      '{WIDOW}',
      '{SPOUSEID}',
      '{SPOUSEPORT}',
      '{F5A}',
      '{F5B}',
      '{H5A}',
      '{H5B}',
      '{H6A}',
      '{H6B}',
      '{H7A}',
      '{H7B}',
      '{H8A}',
      '{H8B}',
      '{H9A}',
      '{H9B}',
      '{H10A}',
      '{H10B}',
      '{H11A}',
      '{H11B}',
      '{H12A}',
      '{H12B}',
      '{H17.1A}',
      '{H17.1B}',
      '{H17.1C}',
      '{H17.1D}',
      '{H17.1E}',
      '{H17.1F}',
      '{H17.1G}',
      '{SAFEYES}',
      '{SAFENO}',
      '{SAFEH}',
      '{SAFER}',
      '{SAFES}',
      '{SAFED}',
      '{SAFEMOUNTYES}',
      '{SAFEMOUNTNO}',
      '{SAFEWALL}',
      '{SAFEFLOOR}',
      '{15A}',
      '{15B}',
      '{16A}',
      '{16B}',
      '{D1.6A1}',
      '{D1.6A2}',
      '{D1.6A3}',
      '{D1.6A4}',
      '{D1.6A5}',
      '{D1.6A6}',
      '{D1.6A7}',
      '{D1.6A8}',
      '{D1.6A9}',
      '{D1.6A10}',
      '{D1.6A11}',
      '{D1.6A12}',
      '{D1.6A13}',
      '{D1.6A14}'
    ]
  },
  {
    id: 'date',
    title: 'Date Fields',
    description: 'Fields for dates',
    keys: [
      '{BirthDate}',
      '{APDATE}',
      '{CertIssueDate}',
      '{CertExpiryDate}',
      '{F8}',
      '{ASSEXPIRE}',
      '{D1.6C1}',
      '{D1.6D1}',
      '{H9.4}',
      '{D1.6C2}',
      '{D1.6D2}',
      '{D1.6C3}',
      '{D1.6D3}',
      '{D1.6C4}',
      '{D1.6D4}',
      '{D1.6C5}',
      '{D1.6D5}',
      '{D1.6C6}',
      '{D1.6D6}',
      '{D1.6C7}',
      '{D1.6D7}',
      '{D1.6C8}',
      '{D1.6D8}',
      '{D1.6C9}',
      '{D1.6D9}',
      '{D1.6C10}',
      '{D1.6D10}',
      '{D1.6C11}',
      '{D1.6D11}',
      '{D1.6C12}',
      '{D1.6D12}',
      '{D1.6C13}',
      '{D1.6D13}',
      '{D1.6C14}',
      '{D1.6D14}'
    ]
  },
  {
    id: 'number',
    title: 'Number Fields',
    description: 'Fields for numeric values',
    keys: [
      '{AGE}',
      '{POSTALCODE}',
      '{PAFKPOSTAL}',
      '{FLID}',
      '{NP_POSTALCODE}',
      '{NPECPOSTAL}',
      '{FDPOSTAL}',
      '{FDRPOSTAL}',
      '{CPOSTAL}',
      '{CRPOSTAL}',
      '{DEEIDNO}',
      '{DEECELL}',
      '{DEEPOSTAL}',
      '{JPPOSTAL}',
      '{JPRPOSTAL}',
      '{H5.2}',
      '{H6.2}',
      '{H7.2}',
      '{H8.2}',
      '{H9.2}',
      '{H9.5}',
      '{H10.2}',
      '{H12.2}',
      '{D1.6B1}',
      '{D1.6B2}',
      '{D1.6B3}',
      '{D1.6B4}',
      '{D1.6B5}',
      '{D1.6B6}',
      '{D1.6B7}',
      '{D1.6B8}',
      '{D1.6B9}',
      '{D1.6B10}',
      '{D1.6B11}',
      '{D1.6B12}',
      '{D1.6B13}',
      '{D1.6B14}'
    ]
  }
]

const formTypes = [
  {
    id: 'all-forms',
    title: 'All Forms',
    description: 'Used in multiple form types',
    keys: [
      '{FirstName}',
      '{LastName}',
      '{Name}',
      '{Cell}',
      '{Email}',
      '{Address}',
      '{ID}',
      '{PASSPORT}',
      '{Initials}',
      '{POSTALCODE}',
      '{AGE}',
      '{SAID}',
      '{FID}',
      '{SEXM}',
      '{SEXF}',
      '{BirthDate}'
    ]
  },
  {
    id: 'competency',
    title: 'Competency',
    description: 'Specific to competency forms',
    keys: [
      '{TradeFirearm}',
      '{PossessFirearm}',
      '{Pistol}',
      '{Rifle}',
      '{Shotgun}',
      '{Revolver}',
      '{SelfLoading}',
      '{SelfLoadingT}',
      '{NameInst}',
      '{SerialCert}',
      '{CertIssueDate}',
      '{D1.6B1}',
      '{D1.6C1}',
      '{D1.6D1}',
      '{D1.6A1}',
      '{D1.6A2}',
      '{D1.6A3}',
      '{D1.6A4}',
      '{D1.6A5}',
      '{D1.6A6}',
      '{D1.6A7}',
      '{D1.6A8}',
      '{D1.6A9}',
      '{D1.6A10}',
      '{D1.6A11}',
      '{D1.6A12}',
      '{D1.6A13}',
      '{D1.6A14}',
      '{D1.6B2}',
      '{D1.6B3}',
      '{D1.6B4}',
      '{D1.6B5}',
      '{D1.6B6}',
      '{D1.6B7}',
      '{D1.6B8}',
      '{D1.6B9}',
      '{D1.6B10}',
      '{D1.6B11}',
      '{D1.6B12}',
      '{D1.6B13}',
      '{D1.6B14}',
      '{D1.6C2}',
      '{D1.6C3}',
      '{D1.6C4}',
      '{D1.6C5}',
      '{D1.6C6}',
      '{D1.6C7}',
      '{D1.6C8}',
      '{D1.6C9}',
      '{D1.6C10}',
      '{D1.6C11}',
      '{D1.6C12}',
      '{D1.6C13}',
      '{D1.6C14}',
      '{D1.6D2}',
      '{D1.6D3}',
      '{D1.6D4}',
      '{D1.6D5}',
      '{D1.6D6}',
      '{D1.6D7}',
      '{D1.6D8}',
      '{D1.6D9}',
      '{D1.6D10}',
      '{D1.6D11}',
      '{D1.6D12}',
      '{D1.6D13}',
      '{D1.6D14}',
      '{15A}',
      '{15B}',
      '{15C}',
      '{16A}',
      '{16B}',
      '{16C}'
    ]
  },
  {
    id: 'license',
    title: 'License',
    description: 'Specific to license forms',
    keys: ['{Section}', '{Make}', '{Model}', '{SerialNumber}', '{Caliber}']
  },
  {
    id: 'deceased',
    title: 'Deceased Estate',
    description: 'For deceased estate processing',
    keys: ['{ExecutorName}', '{ExecutorNumber}', '{ExecutorAddress}']
  }
]

const usageFrequency = [
  {
    id: 'common',
    title: 'Common',
    description: 'Frequently used placeholders',
    keys: [
      '{FirstName}',
      '{LastName}',
      '{Name}',
      '{Cell}',
      '{Email}',
      '{Address}',
      '{ID}',
      '{PASSPORT}',
      '{POSTALCODE}',
      '{Make}',
      '{Model}',
      '{SerialNumber}',
      '{Caliber}'
    ]
  },
  {
    id: 'declarations',
    title: 'Declarations',
    description: 'For declarations and statements',
    keys: [
      '{H5A}',
      '{H5B}',
      '{H5.1}',
      '{H5.2}',
      '{H5.3}',
      '{H5.4}',
      '{H6A}',
      '{H6B}',
      '{H6.1}',
      '{H6.2}',
      '{H6.A3}',
      '{H7A}',
      '{H7B}',
      '{H7.1}',
      '{H7.2}',
      '{H7.3}',
      '{H7.4}',
      '{H8A}',
      '{H8B}',
      '{H8.1}',
      '{H8.2}',
      '{H8.3}',
      '{H8.4}',
      '{H9A}',
      '{H9B}',
      '{H9.1}',
      '{H9.2}',
      '{H9.3}',
      '{H9.4}',
      '{H9.5}',
      '{H10A}',
      '{H10B}',
      '{H10.1}',
      '{H10.2}',
      '{H10.3}',
      '{H10.4}',
      '{H11A}',
      '{H11B}',
      '{H11.1}',
      '{H12A}',
      '{H12B}',
      '{H12A1}',
      '{H12.1}',
      '{H12.2}',
      '{15A}',
      '{15B}',
      '{15C}',
      '{16A}',
      '{16B}',
      '{16C}'
    ]
  },
  {
    id: 'specialized',
    title: 'Specialized',
    description: 'For specific circumstances',
    keys: [
      '{H17.1A}',
      '{H17.1B}',
      '{H17.1C}',
      '{H17.1D}',
      '{H17.1E}',
      '{H17.1F}',
      '{H17.1G}',
      '{H17.1H}',
      '{HOUSETYPE}',
      '{TradeFirearm}',
      '{PossessFirearm}',
      '{Pistol}',
      '{Rifle}',
      '{Shotgun}',
      '{Revolver}',
      '{SelfLoading}',
      '{SelfLoadingT}',
      '{D1.6A1}',
      '{D1.6A2}',
      '{D1.6A3}',
      '{D1.6A4}',
      '{D1.6A5}',
      '{D1.6A6}',
      '{D1.6A7}',
      '{D1.6A8}',
      '{D1.6A9}',
      '{D1.6A10}',
      '{D1.6A11}',
      '{D1.6A12}',
      '{D1.6A13}',
      '{D1.6A14}'
    ]
  },
  {
    id: 'identification',
    title: 'Identification',
    description: 'For identifying personal details',
    keys: [
      '{SINGLES}',
      '{MARRIED}',
      '{DIVORCED}',
      '{WIDOWER}',
      '{WIDOW}',
      '{SPOUSEID}',
      '{SPOUSEPORT}',
      '{SPOUSEIDNO}',
      '{SPOUSEPASSN}',
      '{SAID}',
      '{FID}',
      '{PermRes}',
      '{SEXM}',
      '{SEXF}'
    ]
  },
  {
    id: 'competency',
    title: 'Competency',
    description: 'For competency certificates',
    keys: [
      '{D1.6B1}',
      '{D1.6C1}',
      '{D1.6D1}',
      '{D1.6B2}',
      '{D1.6C2}',
      '{D1.6D2}',
      '{D1.6B3}',
      '{D1.6C3}',
      '{D1.6D3}',
      '{D1.6B4}',
      '{D1.6C4}',
      '{D1.6D4}',
      '{D1.6B5}',
      '{D1.6C5}',
      '{D1.6D5}',
      '{D1.6B6}',
      '{D1.6C6}',
      '{D1.6D6}',
      '{D1.6B7}',
      '{D1.6C7}',
      '{D1.6D7}',
      '{D1.6B8}',
      '{D1.6C8}',
      '{D1.6D8}',
      '{D1.6B9}',
      '{D1.6C9}',
      '{D1.6D9}',
      '{D1.6B10}',
      '{D1.6C10}',
      '{D1.6D10}',
      '{D1.6B11}',
      '{D1.6C11}',
      '{D1.6D11}',
      '{D1.6B12}',
      '{D1.6C12}',
      '{D1.6D12}',
      '{D1.6B13}',
      '{D1.6C13}',
      '{D1.6D13}',
      '{D1.6B14}',
      '{D1.6C14}',
      '{D1.6D14}'
    ]
  }
]

// Create alphabetical filters
const alphabeticalFilters = Array.from({ length: 26 }, (_, i) => {
  const letter = String.fromCharCode(65 + i)
  return {
    id: letter.toLowerCase(),
    title: letter,
    description: `Starts with ${letter}`,
    keys: placeholderData.flatMap((category) =>
      category.items
        .filter((item) => item.key.replace(/[{}]/g, '').startsWith(letter))
        .map((item) => item.key)
    )
  }
}).filter((filter) => filter.keys.length > 0)

// Add a "#" filter for items starting with numbers or symbols
const numberSymbolFilter = {
  id: 'symbol',
  title: '#',
  description: 'Starts with number or symbol',
  keys: placeholderData.flatMap((category) =>
    category.items
      .filter((item) => {
        const firstChar = item.key.replace(/[{}]/g, '').charAt(0)
        return !/[A-Za-z]/.test(firstChar)
      })
      .map((item) => item.key)
  )
}

if (numberSymbolFilter.keys.length > 0) {
  alphabeticalFilters.unshift(numberSymbolFilter)
}

const TemplatePlaceholders: React.FC = () => {
  const [downloadStatus, setDownloadStatus] = useState<string | null>(null)
  const [activeSection, setActiveSection] = useState<string>('personal')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [filteredPlaceholders, setFilteredPlaceholders] = useState<
    Array<{ category: string; items: Array<{ key: string; value: string }> }>
  >([])
  const [copiedKey, setCopiedKey] = useState<string | null>(null)
  const [filterMode, setFilterMode] = useState<'category' | 'field' | 'form' | 'usage' | 'alpha'>(
    'category'
  )
  const [activeFilter, setActiveFilter] = useState<string>('personal')

  // Use the window size context
  const { calculateResponsiveHeight } = useWindowSize()

  // Function to download the placeholders as a text file
  const handleDownload = () => {
    try {
      // Create a formatted text content from the placeholders data
      let textContent = 'TEMPLATE PLACEHOLDERS REFERENCE\n'
      textContent += '================================\n\n'

      placeholderData.forEach((section) => {
        textContent += `## ${section.title} ##\n`
        section.items.forEach((item) => {
          textContent += `${item.key} - ${item.value}\n`
        })
        textContent += '\n'
      })

      // Create a blob with the text content
      const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' })

      // Create a URL for the blob
      const url = URL.createObjectURL(blob)

      // Create a link element to trigger the download
      const link = document.createElement('a')
      link.href = url
      link.download = 'template_placeholders.txt'

      // Append the link to the document, click it, and remove it
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Revoke the URL to free up memory
      URL.revokeObjectURL(url)

      // Set download status
      setDownloadStatus('Downloaded successfully!')

      // Clear status after 3 seconds
      setTimeout(() => {
        setDownloadStatus(null)
      }, 3000)
    } catch (error) {
      console.error('Error downloading placeholders:', error)
      setDownloadStatus('Error downloading. Please try again.')

      // Clear error status after 3 seconds
      setTimeout(() => {
        setDownloadStatus(null)
      }, 3000)
    }
  }

  // Handle search functionality
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredPlaceholders([])
      return
    }

    const query = searchQuery.toLowerCase()
    const results: Array<{ category: string; items: Array<{ key: string; value: string }> }> = []

    placeholderData.forEach((category) => {
      const matchedItems = category.items.filter(
        (item) => item.key.toLowerCase().includes(query) || item.value.toLowerCase().includes(query)
      )

      if (matchedItems.length > 0) {
        results.push({
          category: category.title,
          items: matchedItems
        })
      }
    })

    setFilteredPlaceholders(results)
  }, [searchQuery])

  // Copy to clipboard function
  const handleCopy = (key: string) => {
    navigator.clipboard.writeText(key)
    setCopiedKey(key)
    setTimeout(() => setCopiedKey(null), 1500)
  }

  // Get categories for the active section
  const getActiveSectionCategories = (): CategoryData[] => {
    const section = placeholderSections.find((s) => s.id === activeSection)
    if (!section) return []
    return placeholderData
      .filter((cat) => section.categories.includes(cat.title))
      .map(cat => ({
        category: cat.title,
        items: cat.items
      }))
  }

  // Calculate placeholder items count for a section
  const getItemsCount = (sectionId: string): number => {
    const section = placeholderSections.find((s) => s.id === sectionId)
    if (!section) return 0

    return placeholderData
      .filter((cat) => section.categories.includes(cat.title))
      .reduce((sum, cat) => sum + cat.items.length, 0)
  }

  // Get filtered placeholders based on current filter mode and active filter
  const getFilteredPlaceholders = (): CategoryData[] => {
    let filteredItems: CategoryData[] = []

    if (filterMode === 'category') {
      // Use the existing category filtering logic
      return getActiveSectionCategories()
    }

    // For other filter modes, we need to filter across all categories
    let filterObj: FilterItem | undefined

    switch (filterMode) {
      case 'field':
        filterObj = fieldTypes.find((f) => f.id === activeFilter)
        break
      case 'form':
        filterObj = formTypes.find((f) => f.id === activeFilter)
        break
      case 'usage':
        filterObj = usageFrequency.find((f) => f.id === activeFilter)
        break
      case 'alpha':
        filterObj = alphabeticalFilters.find((f) => f.id === activeFilter)
        break
    }

    if (!filterObj || !filterObj.keys) return []

    // Get all matching items across all categories
    placeholderData.forEach((category) => {
      const matchedItems = category.items.filter((item) => filterObj?.keys?.includes(item.key))

      if (matchedItems.length > 0) {
        filteredItems.push({
          category: category.title,
          items: matchedItems
        })
      }
    })

    return filteredItems
  }

  // Set active filter and mode
  const handleFilterChange = (
    mode: 'category' | 'field' | 'form' | 'usage' | 'alpha',
    filterId: string
  ) => {
    setFilterMode(mode)
    setActiveFilter(filterId)

    if (mode === 'category') {
      setActiveSection(filterId)
    }
  }

  // Define interfaces for filter objects and data structures
  interface FilterItem {
    id: string;
    title: string;
    description?: string;
    keys?: string[];
    categories?: string[];
  }

  interface PlaceholderItem {
    key: string;
    value: string;
  }

  interface CategoryData {
    category: string;
    items: PlaceholderItem[];
  }

  // Get the list of filters for the current filter mode
  const getCurrentFilterList = (): FilterItem[] => {
    switch (filterMode) {
      case 'category':
        return placeholderSections
      case 'field':
        return fieldTypes
      case 'form':
        return formTypes
      case 'usage':
        return usageFrequency
      case 'alpha':
        return alphabeticalFilters
      default:
        return placeholderSections
    }
  }

  // Filter mode tabs
  const filterModes = [
    { id: 'category', title: 'Categories', icon: 'M4 6h16M4 10h16M4 14h16M4 18h16' },
    {
      id: 'field',
      title: 'Field Types',
      icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2'
    },
    {
      id: 'form',
      title: 'Form Types',
      icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
    },
    { id: 'usage', title: 'Usage', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' },
    { id: 'alpha', title: 'A-Z', icon: 'M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12' }
  ]

  return (
    <div className="bg-gradient-to-br from-stone-800/90 to-stone-900/90 backdrop-blur-md rounded-md shadow-md border border-orange-500/30 relative overflow-hidden">
      {/* Smaller decorative elements */}
      <div className="absolute -top-16 -right-16 w-32 h-32 bg-orange-500/10 rounded-full blur-xl pointer-events-none" />
      <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-orange-500/5 rounded-full blur-xl pointer-events-none" />

      <div className="relative">
        {/* Compact filter mode tabs */}
        <div className="flex border-b border-stone-700/50 bg-stone-800/50 overflow-x-auto scrollbar-thin scrollbar-thumb-stone-600 scrollbar-track-transparent">
          {filterModes.map((mode) => (
            <button
              key={mode.id}
              onClick={() => {
                const newMode = mode.id as 'category' | 'field' | 'form' | 'usage' | 'alpha'
                const firstFilter =
                  newMode === 'category'
                    ? placeholderSections[0]?.id
                    : newMode === 'field'
                      ? fieldTypes[0]?.id
                      : newMode === 'form'
                        ? formTypes[0]?.id
                        : newMode === 'usage'
                          ? usageFrequency[0]?.id
                          : alphabeticalFilters[0]?.id

                handleFilterChange(newMode, firstFilter || '')
              }}
              className={`px-2 py-1 text-xs flex items-center gap-1 border-b-2 ${
                filterMode === mode.id
                  ? 'text-orange-300 border-orange-500 bg-stone-800/40'
                  : 'text-stone-400 border-transparent hover:text-stone-300 hover:bg-stone-800/30'
              }`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={mode.icon} />
              </svg>
              {mode.title}
            </button>
          ))}
        </div>

        {/* Compact filter selection and search */}
        <div className="border-b border-stone-700/50 bg-stone-800/30">
          {/* Filter selection - horizontal scrollable */}
          <div className="flex overflow-x-auto py-1 px-1 scrollbar-thin scrollbar-thumb-stone-600 scrollbar-track-transparent">
            {getCurrentFilterList().map((filter) => (
              <button
                key={filter.id}
                onClick={() => handleFilterChange(filterMode, filter.id)}
                className={`px-2 py-1 text-xs whitespace-nowrap mr-1 rounded transition-all flex items-center ${
                  (filterMode === 'category' && activeSection === filter.id) ||
                  (filterMode !== 'category' && activeFilter === filter.id)
                    ? 'bg-orange-500/20 text-orange-300 border border-orange-500/40'
                    : 'bg-stone-700/50 text-stone-400 hover:bg-stone-700 hover:text-stone-300 border border-transparent'
                }`}
              >
                {filter.title}
                {(filterMode === 'category' || (filterMode !== 'category' && filter.keys)) && (
                  <span className="ml-1 bg-stone-700/80 text-stone-400 text-[9px] px-1 py-0.5 rounded-full">
                    {filterMode === 'category' ? getItemsCount(filter.id) : filter.keys?.length}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Search and download - more compact */}
          <div className="flex items-center px-1 pb-1 gap-1">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 flex items-center pl-1.5 pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3 text-stone-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search placeholders..."
                className="w-full bg-stone-800/60 border border-stone-700/70 rounded pl-6 pr-6 py-1 text-xs text-stone-300 placeholder-stone-500 focus:outline-none focus:ring-1 focus:ring-orange-500/50 focus:border-orange-500/50"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute inset-y-0 right-0 flex items-center pr-1.5 text-stone-500 hover:text-stone-300"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              )}
            </div>

            <button
              onClick={handleDownload}
              className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs rounded shadow-sm border border-orange-500/30 whitespace-nowrap"
              title="Download complete reference"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                />
              </svg>
              Download
            </button>
          </div>
        </div>

        {/* Compact download status message */}
        {downloadStatus && (
          <div
            className={`mx-1 my-1 p-1 rounded text-xs flex items-center gap-1 ${
              downloadStatus.includes('Error')
                ? 'bg-red-500/10 text-red-400 border border-red-500/20'
                : 'bg-green-500/10 text-green-400 border border-green-500/20'
            }`}
          >
            {downloadStatus.includes('Error') ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            )}
            {downloadStatus}
          </div>
        )}

        {/* Main content area with reduced height */}
        <div
          className="overflow-y-auto"
          style={{ maxHeight: `${calculateResponsiveHeight(40)}px` }}
        >
          {searchQuery.trim() !== '' ? (
            /* Compact search results */
            <div className="p-1">
              {filteredPlaceholders.length > 0 ? (
                <>
                  <div className="text-xs text-stone-400 mb-1 px-1">
                    Found {filteredPlaceholders.reduce((total, cat) => total + cat.items.length, 0)}{' '}
                    placeholders
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-1">
                    {filteredPlaceholders.flatMap((category) =>
                      category.items.map((item, idx) => (
                        <div
                          key={`${category.category}-${idx}`}
                          className="p-1 rounded bg-stone-800/80 border border-stone-700/70 hover:border-orange-500/30 transition-all group relative hover:bg-stone-800/90 cursor-pointer"
                          onClick={() => handleCopy(item.key)}
                        >
                          <div className="flex flex-col h-full">
                            <div className="flex justify-between items-start">
                              <code className="text-orange-300 font-mono text-[10px] truncate block max-w-[90%]">
                                {item.key}
                              </code>
                              {copiedKey === item.key ? (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-3 w-3 text-green-400 flex-shrink-0"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                              ) : (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-3 w-3 text-stone-500 flex-shrink-0 opacity-50 group-hover:opacity-100"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2"
                                  />
                                </svg>
                              )}
                            </div>
                            <p className="text-stone-400 text-[9px] mt-0.5 line-clamp-1">
                              {item.value}
                            </p>
                            <div className="text-[8px] text-stone-500 mt-0.5 uppercase opacity-75">
                              {category.category}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center h-20 text-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-stone-500 mb-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <p className="text-stone-500 text-xs">
                    No placeholders found matching "{searchQuery}"
                  </p>
                </div>
              )}
            </div>
          ) : (
            /* Compact content based on current filter mode */
            <div className="p-1">
              {filterMode === 'category'
                ? // Category view (compact)
                  getActiveSectionCategories().map((category, catIdx) => (
                    <div key={catIdx} className="mb-2">
                      <h3 className="text-xs font-medium text-orange-300 mb-1 flex items-center px-1">
                        <span className="inline-block mr-1 w-0.5 h-3 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                        {category.category}
                        <span className="ml-1 text-[9px] opacity-70">
                          ({category.items.length})
                        </span>
                      </h3>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-1">
                        {category.items.map((item, idx) => (
                          <div
                            key={idx}
                            className="p-1 rounded bg-stone-800/80 border border-stone-700/70 hover:border-orange-500/30 transition-all group relative hover:bg-stone-800/90 cursor-pointer"
                            onClick={() => handleCopy(item.key)}
                          >
                            <div className="flex justify-between items-start">
                              <code className="text-orange-300 font-mono text-[10px] truncate block max-w-[90%]">
                                {item.key}
                              </code>
                              {copiedKey === item.key ? (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-3 w-3 text-green-400 flex-shrink-0"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                              ) : (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-3 w-3 text-stone-500 flex-shrink-0 opacity-50 group-hover:opacity-100"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2"
                                  />
                                </svg>
                              )}
                            </div>
                            <p className="text-stone-400 text-[9px] mt-0.5 line-clamp-1">
                              {item.value}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))
                : // Other filter modes
                  getFilteredPlaceholders().map((category, catIdx) => (
                    <div key={catIdx} className="mb-4">
                      <h3 className="text-xs font-medium text-orange-300 mb-2 flex items-center px-1">
                        <span className="inline-block mr-1.5 w-1 h-3.5 bg-gradient-to-b from-orange-400 to-orange-600 rounded-full" />
                        {category.category}
                        <span className="ml-1.5 text-[10px] opacity-70">
                          ({category.items.length})
                        </span>
                      </h3>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-2">
                        {category.items.map((item, idx) => (
                          <div
                            key={idx}
                            className="p-2 rounded-md bg-stone-800/80 border border-stone-700/70 hover:border-orange-500/30 transition-all group relative hover:bg-stone-800/90 cursor-pointer"
                            onClick={() => handleCopy(item.key)}
                          >
                            <div className="flex justify-between items-start">
                              <code className="text-orange-300 font-mono text-xs truncate block max-w-[90%]">
                                {item.key}
                              </code>
                              {copiedKey === item.key ? (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-3.5 w-3.5 text-green-400 shrink-0"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                              ) : (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-3.5 w-3.5 text-stone-500 shrink-0 opacity-50 group-hover:opacity-100"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2"
                                  />
                                </svg>
                              )}
                            </div>
                            <p className="text-stone-400 text-[11px] mt-1 line-clamp-2">
                              {item.value}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
            </div>
          )}
        </div>

        {/* Help text footer */}
        <div className="text-[10px] text-stone-500 py-1 px-2 border-t border-stone-700/50 text-center">
          Click any placeholder to copy it to your clipboard. Use the filters above to organize
          placeholders.
        </div>
      </div>
    </div>
  )
}

export default TemplatePlaceholders
