/**
 * Custom module for <PERSON>x<PERSON>plate<PERSON> to fix the unclosed "Initi" tag in templates
 * This module specifically targets the "Initi" tag that's causing issues
 */

class FixInitiTagModule {
  name = 'FixInitiTagModule';
  xmlDocuments: any;

  constructor() {
    // No options needed for this simple module
  }

  optionsTransformer(options: any) {
    // Modify options if needed
    return options;
  }

  set(options: any) {
    // Store any needed references from options
    this.xmlDocuments = options.xmlDocuments;
  }

  preparse(parsed: any) {
    // This method is called before the template is parsed
    // We can use it to fix the XML content before parsing

    if (!this.xmlDocuments) return parsed;

    // Process each XML document in the template
    Object.keys(this.xmlDocuments).forEach(fileName => {
      let content = this.xmlDocuments[fileName];
      let modified = false;

      // Check if the content contains the problematic tag
      if (content.includes('{Initi')) {
        console.log(`Found unclosed {Initi tag in ${fileName}`);

        // Replace {Initi with {Initials} to fix the unclosed tag
        content = content.replace(/{Initi([^}]*)}/g, '{Initials$1}');

        // If there are still unclosed tags, add closing braces
        content = content.replace(/{Initi([^}]*)(?!})/g, '{Initials$1}');

        // Handle the specific case where {Initi is at the end of a line or text
        content = content.replace(/{Initi(?![^{]*})/g, '{Initials}');

        modified = true;
        console.log(`Fixed unclosed {Initi tag in ${fileName}`);
      }

      // Check for other common unclosed tags
      const commonTags = [
        // Personal Information
        'Initial', 'Initials', 'INITIAL', 'INITIALS', 'Initi', 'INITI',
        'LastName', 'LASTNAME', 'FirstNames', 'FIRSTNAMES', 'FirstName', 'FIRSTNAME',
        'Name', 'NAME', 'Address', 'ADDRESS', 'PostalCode', 'POSTALCODE',
        'WorkNumber', 'WORKNUMBER', 'PhoneNumber', 'PHONENUMBER', 'Cell', 'CELL',
        'Email', 'EMAIL', 'FULLNAME', 'Fullname', 'fullName', 'fullname',
        'PHYSICALADDRESS', 'PhysicalAddress', 'physicalAddress', 'physicaladdress',

        // ID Types
        'ID', 'Id', 'id', 'IDNUMBER', 'IdNumber', 'idNumber', 'idnumber', 'SAID', 'SAId', 'SAID_NUM',
        'Passport', 'PASSPORT', 'FId', 'FID', 'PassportNumber', 'PASSPORTNUMBER',
        'PassportNum', 'PASSPORTNUM', 'PermRes', 'PERMRES', 'PermResNumber', 'PERMRESNUMBER',
        'PermResNum', 'PERMRESNUM',

        // Licence Types
        'S13', 's13', 'S15', 's15', 'S16', 's16', 'S20', 's20', 'S20A', 's20a', 'S20B', 's20b', 'S20C', 's20c',

        // Original Licence Details
        'OrigLicNum', 'ORIGLICNUM', 'OriginalLicenceNumber', 'ORIGINALLICENCENUMBER',
        'OrigDateIssued', 'ORIGDATEISSUED', 'OriginalLicenceIssueDate', 'ORIGINALLICENCEISSUEDATE',
        'OrigExpiryDate', 'ORIGEXPIRYDATE', 'OriginalLicenceExpiryDate', 'ORIGINALLICENCEEXPIRYDATE',
        'OrigLicNum2', 'ORIGLICNUM2', 'OrigDateIssued2', 'ORIGDATEISSUED2', 'OrigExpiryDate2', 'ORIGEXPIRYDATE2',
        'OrigLicNum3', 'ORIGLICNUM3', 'OrigDateIssued3', 'ORIGDATEISSUED3', 'OrigExpiryDate3', 'ORIGEXPIRYDATE3',
        'OrigLicNum4', 'ORIGLICNUM4', 'OrigDateIssued4', 'ORIGDATEISSUED4', 'OrigExpiryDate4', 'ORIGEXPIRYDATE4',

        // Juristic Person Details
        'CompanyName', 'COMPANYNAME', 'TradingAs', 'TRADINGAS', 'FARNumber', 'FARNUMBER',
        'PostalAddress', 'POSTALADDRESS', 'JuristicPostalCode', 'JURISTICPOSTALCODE',
        'BusinessTelNumber', 'BUSINESSTELNUMBER', 'JuristicWorkNumber', 'JURISTICWORKNUMBER',
        'JuristicEmail', 'JURISTICEMAIL',

        // Responsible Person Details
        'ResponsiblePersonName', 'RESPONSIBLEPERSONNAME', 'ResponsiblePersonSAID', 'RESPONSIBLEPERSONSAID',
        'ResponsiblePersonPassport', 'RESPONSIBLEPERSONPASSPORT', 'ResponsiblePersonIDNumber', 'RESPONSIBLEPERSONIDNUMBER',
        'ResponsiblePersonPassportNumber', 'RESPONSIBLEPERSONPASSPORTNUMBER', 'ResponsiblePersonCellNumber', 'RESPONSIBLEPERSONCELLNUMBER',
        'ResponsiblePersonAddress', 'RESPONSIBLEPERSONADDRESS', 'ResponsiblePersonPostalCode', 'RESPONSIBLEPERSONPOSTALCODE',

        // Other Information
        '15A', '15a', '15B', '15b', '15C', '15c', 'Before90DaysReason', 'BEFORE90DAYSREASON',
        '16A', '16a', '16B', '16b', '16C', '16c', 'AfterExpiryReason', 'AFTEREXPIRYREASON',
        '17A', '17a', '17B', '17b', '17C', '17c', 'AfterDueBeforeExpiryReason', 'AFTERDUEBEFOREEXPIRYREASON',

        // Application Type (New Licence)
        'MainHF', 'MAINHF', 'mainHF', 'mainhf', 'AddHF', 'ADDHF', 'addHF', 'addhf',

        // Firearm Types (New Licence)
        'Rifle', 'RIFLE', 'rifle', 'Shotgun', 'SHOTGUN', 'shotgun',
        'Pistol', 'PISTOL', 'pistol', 'Comb', 'COMB', 'comb',
        'OtherDesign', 'OTHERDESIGN', 'otherDesign', 'otherdesign',
        'OtherDesignE', 'OTHERDESIGNE', 'otherDesignE', 'otherdesigne',

        // Firearm Action Types (New Licence)
        'Semi', 'SEMI', 'semi', 'Auto', 'AUTO', 'auto', 'Man', 'MAN', 'man',
        'OtherF', 'OTHERF', 'otherF', 'otherf',

        // Professional Information (New Licence)
        'COMPANYNAME', 'CompanyName', 'companyName', 'companyname',
        'TRADEPROFESSION', 'TradeProfession', 'tradeProfession', 'tradeprofession',
        'WORKADDRESS', 'WorkAddress', 'workAddress', 'workaddress',
        'WORKPOSTALCODE', 'WorkPostalCode', 'workPostalCode', 'workpostalcode',

        // Firearm Details (New Licence)
        'Make', 'MAKE', 'make', 'Model', 'MODEL', 'model',
        'Caliber', 'CALIBER', 'caliber', 'ENGG', 'Engg', 'engg',

        // Firearm component type (New Licence)
        'BSN', 'Bsn', 'bsn', 'FSN', 'Fsn', 'fsn', 'RSN', 'Rsn', 'rsn',
        'BSNM', 'Bsnm', 'bsnm', 'FSNM', 'Fsnm', 'fsnm', 'RSNM', 'Rsnm', 'rsnm',

        // SAP 350 (A) DETAILS (New Licence)
        'SAP350A_NAME', 'Sap350a_Name', 'sap350aName', 'sap350a_name',
        'SAP350A_ID_FAR', 'Sap350a_Id_Far', 'sap350aIdFar', 'sap350a_id_far',
        'SAP350A_ADDRESS', 'Sap350a_Address', 'sap350aAddress', 'sap350a_address',
        'SAP350A_POSTAL', 'Sap350a_Postal', 'sap350aPostal', 'sap350a_postal',
        'SAP350A_DATE', 'Sap350a_Date', 'sap350aDate', 'sap350a_date',

        // Safe Storage (New Licence)
        'SAFEYES', 'SafeYes', 'safeYes', 'safeyes', 'SAFENO', 'SafeNo', 'safeNo', 'safeno',
        'SAFEH', 'SafeH', 'safeH', 'safeh', 'SAFER', 'SafeR', 'safeR', 'safer',
        'SAFES', 'SafeS', 'safeS', 'safes', 'SAFED', 'SafeD', 'safeD', 'safed',
        'SAFESE', 'SafeSe', 'safeSe', 'safese', 'SAFEDINFO', 'SafeDInfo', 'safeDInfo', 'safedinfo',
        'SAFEMOUNTYES', 'SafeMountYes', 'safeMountYes', 'safemountyes',
        'SAFEMOUNTNO', 'SafeMountNo', 'safeMountNo', 'safemountno',
        'SAFEWALL', 'SafeWall', 'safeWall', 'safewall',
        'SAFEFLOOR', 'SafeFloor', 'safeFloor', 'safefloor',

        // Competency Certificate (New Licence and Renew Competency)
        'TradeFirearm', 'TRADEFIREARM', 'tradeFirearm', 'tradefirearm',
        'PossessFirearm', 'POSSESSFIREARM', 'possessFirearm', 'possessfirearm',
        'F2AB', 'f2ab', 'F3', 'f3', 'F4', 'f4',
        'WorkC', 'WORKC', 'workC', 'workc',

        // Competency Certificate Types (Renew Competency)
        'D1.6A1', 'd1.6a1', 'D1.6B1', 'd1.6b1', 'D1.6C1', 'd1.6c1', 'D1.6D1', 'd1.6d1',
        'D1.6A2', 'd1.6a2', 'D1.6B2', 'd1.6b2', 'D1.6C2', 'd1.6c2', 'D1.6D2', 'd1.6d2',
        'D1.6A3', 'd1.6a3', 'D1.6B3', 'd1.6b3', 'D1.6C3', 'd1.6c3', 'D1.6D3', 'd1.6d3',
        'D1.6A4', 'd1.6a4', 'D1.6B4', 'd1.6b4', 'D1.6C4', 'd1.6c4', 'D1.6D4', 'd1.6d4',
        'D1.6A5', 'd1.6a5', 'D1.6B5', 'd1.6b5', 'D1.6C5', 'd1.6c5', 'D1.6D5', 'd1.6d5',
        'D1.6A6', 'd1.6a6', 'D1.6B6', 'd1.6b6', 'D1.6C6', 'd1.6c6', 'D1.6D6', 'd1.6d6',
        'D1.6A7', 'd1.6a7', 'D1.6B7', 'd1.6b7', 'D1.6C7', 'd1.6c7', 'D1.6D7', 'd1.6d7',
        'D1.6A8', 'd1.6a8', 'D1.6B8', 'd1.6b8', 'D1.6C8', 'd1.6c8', 'D1.6D8', 'd1.6d8',
        'D1.6A9', 'd1.6a9', 'D1.6B9', 'd1.6b9', 'D1.6C9', 'd1.6c9', 'D1.6D9', 'd1.6d9',
        'D1.6A10', 'd1.6a10', 'D1.6B10', 'd1.6b10', 'D1.6C10', 'd1.6c10', 'D1.6D10', 'd1.6d10',
        'D1.6A11', 'd1.6a11', 'D1.6B11', 'd1.6b11', 'D1.6C11', 'd1.6c11', 'D1.6D11', 'd1.6d11',
        'D1.6A12', 'd1.6a12', 'D1.6B12', 'd1.6b12', 'D1.6C12', 'd1.6c12', 'D1.6D12', 'd1.6d12',
        'D1.6A13', 'd1.6a13', 'D1.6B13', 'd1.6b13', 'D1.6C13', 'd1.6c13', 'D1.6D13', 'd1.6d13',
        'D1.6A14', 'd1.6a14', 'D1.6B14', 'd1.6b14', 'D1.6C14', 'd1.6c14', 'D1.6D14', 'd1.6d14',

        // Criminal History (New Licence)
        'H5A', 'h5a', 'H5B', 'h5b', 'H5.1', 'h5.1', 'H51', 'h51',
        'H5.2', 'h5.2', 'H52', 'h52', 'H5.3', 'h5.3', 'H53', 'h53',
        'H5.4', 'h5.4', 'H54', 'h54', 'H6A', 'h6a', 'H6B', 'h6b',
        'H6.1', 'h6.1', 'H61', 'h61', 'H6.2', 'h6.2', 'H62', 'h62',
        'H6.A3', 'h6.a3', 'H6A3', 'h6a3', 'H7A', 'h7a', 'H7B', 'h7b',
        'H7.1', 'h7.1', 'H71', 'h71', 'H7.2', 'h7.2', 'H72', 'h72',
        'H7.3', 'h7.3', 'H73', 'h73', 'H7.4', 'h7.4', 'H74', 'h74',
        'H8A', 'h8a', 'H8B', 'h8b', 'H8.1', 'h8.1', 'H81', 'h81',
        'H8.2', 'h8.2', 'H82', 'h82', 'H8.3', 'h8.3', 'H83', 'h83',
        'H8.4', 'h8.4', 'H84', 'h84', 'H9A', 'h9a', 'H9B', 'h9b',
        'H9.1', 'h9.1', 'H91', 'h91', 'H9.2', 'h9.2', 'H92', 'h92',
        'H9.3', 'h9.3', 'H93', 'h93', 'H9.4', 'h9.4', 'H94', 'h94',
        'H9.5', 'h9.5', 'H95', 'h95', 'H10A', 'h10a', 'H10B', 'h10b',
        'H10.1', 'h10.1', 'H101', 'h101', 'H10.2', 'h10.2', 'H102', 'h102',
        'H10.3', 'h10.3', 'H103', 'h103', 'H10.4', 'h10.4', 'H104', 'h104',
        'H11A', 'h11a', 'H11B', 'h11b', 'H11.1', 'h11.1', 'H111', 'h111',
        'H12A', 'h12a', 'H12B', 'h12b', 'H12A1', 'h12a1', 'H12.1', 'h12.1', 'H121', 'h121',
        'H12.2', 'h12.2', 'H122', 'h122'
      ];

      commonTags.forEach(tag => {
        const openTag = `{${tag}`;
        if (content.includes(openTag) && !content.includes(`${openTag}}`)) {
          console.log(`Found potentially unclosed {${tag} tag in ${fileName}`);

          // Replace unclosed tags with properly closed ones
          content = content.replace(new RegExp(`{${tag}(?![^{]*})`, 'g'), `{${tag}}`);

          modified = true;
          console.log(`Fixed unclosed {${tag} tag in ${fileName}`);
        }
      });

      // Update the XML document with the fixed content if modified
      if (modified) {
        this.xmlDocuments[fileName] = content;
      }
    });

    return parsed;
  }
}

export default FixInitiTagModule;
