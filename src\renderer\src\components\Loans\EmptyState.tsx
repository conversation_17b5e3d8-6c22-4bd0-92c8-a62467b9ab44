import React from 'react'
import { DashboardIcons } from '../icons/DashboardIcons'
import { LoanFilterType } from './types'

interface EmptyStateProps {
  searchQuery: string
  filter: LoanFilterType
  onCreateLoan: () => void
}

export const EmptyState: React.FC<EmptyStateProps> = ({ searchQuery, filter, onCreateLoan }) => {
  return (
    <div className="flex flex-col items-center justify-center h-64 text-stone-400">
      <DashboardIcons.EmptyState className="w-16 h-16 text-stone-600 mb-4" />
      {searchQuery ? (
        <>
          <p className="text-lg">No results found for "{searchQuery}"</p>
          <p className="text-sm mt-2">Try a different search term or clear the search</p>
        </>
      ) : filter !== 'all' ? (
        <>
          <p className="text-lg">No loans found with the selected filter</p>
          <p className="text-sm mt-2">Try a different filter or create a new loan</p>
        </>
      ) : (
        <>
          <p className="text-lg">No loans found</p>
          <p className="text-sm mt-2">Create your first loan to get started</p>
        </>
      )}
      <button
        onClick={onCreateLoan}
        className="mt-4 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
          text-white px-4 py-2 rounded-lg shadow-md shadow-orange-500/20
          inline-flex items-center gap-2 transition-all duration-200"
      >
        <span className="w-4 h-4 flex items-center justify-center text-lg font-bold">+</span>
        New Loan
      </button>
    </div>
  )
}

export default EmptyState
