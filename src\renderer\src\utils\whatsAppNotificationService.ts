import { formatPhoneForWhatsApp, callWhatsAppApi } from '../components/Settings/utils'
import { formatCurrency } from './formatters'

/**
 * WhatsApp Notification Service
 *
 * This service provides a unified way to send WhatsApp notifications across the application.
 * It ensures all notifications use the WhatsApp Business API credentials configured in settings.
 * This service exclusively uses the WhatsApp Business API configured in the settings page.
 */

// Default session ID to use for WhatsApp messages
// This should be configured in settings and stored in local storage
const DEFAULT_SESSION_ID = 'Gunnery'

/**
 * Get the configured WhatsApp session ID from settings or use default
 * @returns The WhatsApp session ID to use
 */
export const getWhatsAppSessionId = (): string => {
  // Try to get from local storage first
  const storedSessionId = localStorage.getItem('whatsapp_session_id')
  if (storedSessionId) {
    return storedSessionId
  }

  // Fall back to default
  return DEFAULT_SESSION_ID
}

/**
 * Set the WhatsApp session ID to use for notifications
 * @param sessionId The session ID to use
 */
export const setWhatsAppSessionId = (sessionId: string): void => {
  localStorage.setItem('whatsapp_session_id', sessionId)
}

/**
 * Send a WhatsApp notification
 * @param phoneNumber The recipient's phone number
 * @param message The message to send
 * @returns Promise resolving to the result of the API call
 */
export const sendWhatsAppNotification = async (
  phoneNumber: string,
  message: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Format the phone number for WhatsApp
    const whatsappNumber = formatPhoneForWhatsApp(phoneNumber)

    // Get the configured session ID
    const sessionId = getWhatsAppSessionId()

    console.log(`Sending WhatsApp message using session ${sessionId} to ${whatsappNumber}`)

    // Use the helper function to call the WhatsApp API from settings
    // This ensures we're using the WhatsApp Business API configured in settings
    const result = await callWhatsAppApi('sendMessage', sessionId, whatsappNumber, message)

    if (result.success) {
      console.log('Successfully sent WhatsApp message using WhatsApp Business API')
      return { success: true }
    } else {
      console.error('Failed to send WhatsApp message:', result)
      return { success: false, error: result.error || 'Failed to send WhatsApp notification' }
    }
  } catch (error) {
    console.error('Error sending WhatsApp notification:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send WhatsApp notification'
    }
  }
}

/**
 * Send a wallet transaction notification
 * @param clientName The client's name
 * @param phoneNumber The client's phone number
 * @param transactionAmount The transaction amount
 * @param updatedBalance The updated wallet balance
 * @param transactionType The type of transaction ('add' or 'deduct')
 * @returns Promise resolving to the result of the API call
 */
export const sendWalletTransactionNotification = async (
  clientName: string,
  phoneNumber: string,
  transactionAmount: number,
  updatedBalance: number,
  transactionType: 'add' | 'deduct'
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Skip if phone number is missing
    if (!phoneNumber) {
      console.log('Cannot send WhatsApp notification: Missing phone number')
      return { success: false, error: 'Missing phone number' }
    }

    console.log('Preparing to send WhatsApp payment notification for wallet transaction')

    // Format the date
    const paymentDate = new Date().toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })

    // Create the message
    const action = transactionType === 'add' ? 'added to' : 'deducted from'
    const message = `*GUNNERY ARMS & AMMO*
*WALLET TRANSACTION NOTIFICATION*

Dear ${clientName},

This is to confirm that ${formatCurrency(transactionAmount)} has been ${action} your wallet on ${paymentDate}.

Your current wallet balance is: ${formatCurrency(updatedBalance)}

Thank you for your continued support.

*GUNNERY ARMS & AMMO*
*TERMS & CONDITIONS*

There is a 50% cancellation fee on all proficiency courses, and all proficiency courses will expire after 12 months from date of purchase. No-shows and cancellations within 72 hours of the class will lead to a penalty fee.

Goods remain property of Gunnery Arms & Ammo until paid in full. Thank you for the support!`

    // Send the notification
    return await sendWhatsAppNotification(phoneNumber, message)
  } catch (error) {
    console.error('Error sending wallet transaction WhatsApp notification:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send WhatsApp notification'
    }
  }
}

/**
 * Send a loan payment notification
 * @param clientName The client's name
 * @param phoneNumber The client's phone number
 * @param invoiceNumber The loan invoice number
 * @param paymentAmount The payment amount
 * @param paymentDate The payment date
 * @param remainingBalance The remaining loan balance
 * @returns Promise resolving to the result of the API call
 */
export const sendLoanPaymentNotification = async (
  clientName: string,
  phoneNumber: string,
  invoiceNumber: string,
  paymentAmount: number,
  paymentDate: string,
  remainingBalance: number
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Skip if phone number is missing
    if (!phoneNumber) {
      console.log('Cannot send WhatsApp notification: Missing phone number')
      return { success: false, error: 'Missing phone number' }
    }

    console.log('Preparing to send WhatsApp payment notification for loan:', invoiceNumber)

    // Format the payment date if it's a string
    const formattedPaymentDate = typeof paymentDate === 'string'
      ? new Date(paymentDate).toLocaleDateString('en-ZA', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      : paymentDate

    // Create the message
    const message = `*GUNNERY ARMS & AMMO*
*PAYMENT CONFIRMATION*

Dear ${clientName},

This is to confirm that we have received your payment of ${formatCurrency(paymentAmount)} for invoice ${invoiceNumber} on ${formattedPaymentDate}.

Your remaining balance is: ${formatCurrency(remainingBalance)}

Thank you for your payment.

*GUNNERY ARMS & AMMO*
*TERMS & CONDITIONS*

There is a 50% cancellation fee on all proficiency courses, and all proficiency courses will expire after 12 months from date of purchase. No-shows and cancellations within 72 hours of the class will lead to a penalty fee.

Goods remain property of Gunnery Arms & Ammo until paid in full. Thank you for the support!`

    // Send the notification
    return await sendWhatsAppNotification(phoneNumber, message)
  } catch (error) {
    console.error('Error sending loan payment WhatsApp notification:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send WhatsApp notification'
    }
  }
}

/**
 * Send a firearm storage payment notification
 * @param clientName The client's name
 * @param phoneNumber The client's phone number
 * @param stockNumber The firearm stock number
 * @param firearmDetails The firearm details
 * @param paymentAmount The payment amount
 * @param startDate The storage start date
 * @param remainingBalance The remaining balance
 * @param paymentRates The payment rates information
 * @returns Promise resolving to the result of the API call
 */
export const sendFirearmStoragePaymentNotification = async (
  clientName: string,
  phoneNumber: string,
  stockNumber: string,
  firearmDetails: string,
  paymentAmount: number,
  startDate: string,
  remainingBalance: number,
  paymentRates: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Skip if phone number is missing
    if (!phoneNumber) {
      console.log('Cannot send WhatsApp notification: Missing phone number')
      return { success: false, error: 'Missing phone number' }
    }

    console.log('Preparing to send WhatsApp payment notification for firearm storage')

    // Format the date
    const formattedStartDate = typeof startDate === 'string'
      ? new Date(startDate).toLocaleDateString('en-ZA', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      : startDate

    // Create the message
    const message = `*GUNNERY ARMS & AMMO*
*FIREARM STORAGE PAYMENT CONFIRMATION*

Dear ${clientName},

This is to confirm that we have received your payment of ${formatCurrency(paymentAmount)} for the storage of:

Stock Number: ${stockNumber}
Firearm: ${firearmDetails}
Storage Start Date: ${formattedStartDate}

Your remaining balance is: ${formatCurrency(remainingBalance)}

Payment Rates:
${paymentRates}

*GUNNERY ARMS & AMMO*
*BANKING DETAILS*

Bank: FNB
Account Name: Gunnery Arms & Ammo
Account Number: ***********
Branch Code: 250655
Reference: Your Name & Invoice Number

*TERMS & CONDITIONS*

There is a 50% cancellation fee on all proficiency courses, and all proficiency courses will expire after 12 months from date of purchase. No-shows and cancellations within 72 hours of the class will lead to a penalty fee.

Goods remain property of Gunnery Arms & Ammo until paid in full. Thank you for the support!`

    // Send the notification
    return await sendWhatsAppNotification(phoneNumber, message)
  } catch (error) {
    console.error('Error sending firearm storage payment WhatsApp notification:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send WhatsApp notification'
    }
  }
}
