
/**
 * Windows-specific performance optimizations for the Electron app
 */
import { app, BrowserWindow } from 'electron'
import os from 'os'

/**
 * Applies Windows-specific optimizations to improve app performance
 * @param mainWindow The main BrowserWindow instance
 */
export function applyWindowsOptimizations(mainWindow: BrowserWindow): void {
  if (process.platform !== 'win32') return

  // Add performance metrics logging
  const startTime = performance.now()

  try {
    const { spawn } = require('child_process')
    const proc = spawn('powershell', [
      '-Command',
      `$process = Get-Process -Id ${process.pid}; $process.PriorityClass = 'AboveNormal'`
    ], {
      detached: true,
      stdio: 'pipe' // Change from 'ignore' to capture potential errors
    })

    proc.on('error', (error) => {
      console.error('Process priority setting failed:', error)
    })

    proc.unref()
  } catch (error) {
    console.error('Failed to set process priority:', error)
  }

  // Add memory usage monitoring
  setInterval(() => {
    const memoryUsage = process.memoryUsage()
    if (memoryUsage.heapUsed > 1024 * 1024 * 512) { // 512MB
      console.warn('High memory usage detected:', memoryUsage)
    }
  }, 300000) // Check every 5 minutes

  // Disable hardware acceleration for certain GPUs with known issues
  const gpuVendors = ['Intel(R) UHD Graphics', 'Intel(R) HD Graphics']
  const gpuInfo = app.getGPUInfo('basic') as any

  if (
    gpuInfo &&
    gpuInfo.gpus &&
    gpuInfo.gpus.some((gpu: any) => gpuVendors.some(vendor => gpu.vendor?.includes(vendor)))
  ) {
    app.disableHardwareAcceleration()
  }

  // Set desktop compositor optimizations via Windows API
  if (mainWindow) {
    mainWindow.once('ready-to-show', () => {
      try {
        // Hint to Windows to prioritize this process for better performance
        const { execSync } = require('child_process')
        execSync(`powershell -Command "$Process = Get-Process -Id ${process.pid}; $Process.PriorityClass = 'AboveNormal'"`)

        // Optimize memory usage
        if (os.totalmem() >= 8 * 1024 * 1024 * 1024) { // 8GB or more RAM
          mainWindow.webContents.setVisualZoomLevelLimits(1, 3)
        } else {
          // For lower memory systems, reduce memory usage
          app.commandLine.appendSwitch('js-flags', '--max-old-space-size=2048')
        }
      } catch (error) {
        console.error('Failed to apply window optimizations:', error)
      }
    })
  }

  // Reduce resource usage when window is not focused
  mainWindow.on('blur', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.setFrameRate(30) // Lower framerate when not focused
    }
  })

  mainWindow.on('focus', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.setFrameRate(60) // Restore framerate when focused
    }
  })
}

/**
 * Applies storage optimization for Windows
 */
export function optimizeStorage(): void {
  if (process.platform !== 'win32') return

  const userDataPath = app.getPath('userData')

  // Clean up old logs periodically
  try {
    const fs = require('fs')
    const path = require('path')
    const logsDir = path.join(userDataPath, 'logs')

    if (fs.existsSync(logsDir)) {
      const files = fs.readdirSync(logsDir)
      const now = new Date().getTime()

      files.forEach((file: string) => {
        const filePath = path.join(logsDir, file)
        const stats = fs.statSync(filePath)
        const fileAge = now - stats.mtime.getTime()

        // Delete logs older than 7 days
        if (fileAge > 7 * 24 * 60 * 60 * 1000) {
          fs.unlinkSync(filePath)
        }
      })
    }
  } catch (error) {
    console.error('Failed to clean up logs:', error)
  }
} 
