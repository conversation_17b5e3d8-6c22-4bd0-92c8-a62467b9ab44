import React, { memo } from 'react'
import { Loan } from '../../types'
import { SortConfig } from './types'
import { SkeletonLoans } from '../SkeletonLoading'
import { LoanCard } from '.'
import { useVirtualization } from '../../hooks/useVirtualization'

interface LoanListProps {
  loans: Loan[]
  handleSort: (field: string) => void
  sortConfig: SortConfig
  handleLoanFocusToggle: (loanId: string | null) => void
  handleCreatePayment: (loan: Loan) => void
  handleDeleteLoan: (loanId: string) => void
  handleCancelLoan: (loanId: string) => Promise<boolean>
  handleAddLicense: (clientId: string, loanId: string) => void
  onUpdateInvoiceNumber?: (loanId: string, invoiceNumber: string) => Promise<{ success: boolean; error?: string }>
}

// Number of items to load initially and in each batch
const INITIAL_BATCH_SIZE = 5
const BATCH_INCREMENT = 5

const LoanList: React.FC<LoanListProps> = memo(({
  loans,
  handleLoanFocusToggle,
  handleCreatePayment,
  handleDeleteLoan,
  handleCancelLoan,
  handleAddLicense,
  onUpdateInvoiceNumber
}) => {
  // Use the virtualization hook
  const {
    items: visibleLoans,
    total,
    isLoading: isLoadingMore,
    handleScroll,
    handleLoadMore
  } = useVirtualization<Loan>(loans, INITIAL_BATCH_SIZE, BATCH_INCREMENT)

  return (
    <div className="space-y-3 overflow-y-auto h-full custom-scrollbar" onScroll={handleScroll}>
      {visibleLoans.map((loan) => (
        <div key={loan.id} className="animate-fadeIn">
          <LoanCard
            loan={loan}
            onAddPayment={() => handleCreatePayment(loan)}
            onDeleteLoan={async (loanId) => { await handleDeleteLoan(loanId) }}
            onCancelLoan={handleCancelLoan}
            onAddLicense={(clientId, loanId) => handleAddLicense(clientId, loanId)}
            onUpdateInvoiceNumber={onUpdateInvoiceNumber || (async () => ({ success: false, error: 'Not implemented' }))}
            onFocusToggle={() => handleLoanFocusToggle(loan.id)}
          />
        </div>
      ))}
      {isLoadingMore && (
        <div className="py-2">
          <SkeletonLoans count={1} />
        </div>
      )}
      {total > visibleLoans.length && !isLoadingMore && (
        <div className="text-center py-3">
          <button
            className="px-4 py-2 bg-stone-700/70 hover:bg-stone-600/70 text-white rounded-lg
              transition-colors flex items-center gap-2 mx-auto"
            onClick={handleLoadMore}
          >
            <span>Load more loans</span>
            <span className="px-2 py-0.5 bg-stone-800/70 rounded-md text-xs">
              {total - visibleLoans.length} remaining
            </span>
          </button>
        </div>
      )}
    </div>
  )
})

export default LoanList
