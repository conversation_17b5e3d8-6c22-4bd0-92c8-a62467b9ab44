import React from 'react'
import { DashboardIcons } from '../../icons/DashboardIcons'

interface FormStepperProps { 
  steps: { id: string; label: string; optional?: boolean }[];
  currentStep: string;
  onStepClick: (stepId: string) => void;
}

const FormStepper: React.FC<FormStepperProps> = ({ 
  steps, 
  currentStep, 
  onStepClick 
}) => (
  <div className="mb-8">
    <div className="flex items-center justify-between">
      {steps.map((step, index) => {
        const isActive = step.id === currentStep;
        const isPast = steps.findIndex(s => s.id === currentStep) > index;
        
        return (
          <div key={step.id} className="flex flex-col items-center relative flex-1">
            {/* Connector line */}
            {index > 0 && (
              <div 
                className={`absolute left-0 right-0 top-4 h-1 -translate-y-1/2 transform ${
                  isPast ? 'bg-orange-500' : 'bg-stone-600'
                }`} 
                style={{ width: 'calc(100% - 2rem)', left: '-50%', zIndex: 0 }}
              ></div>
            )}
            
            {/* Step circle */}
            <button
              onClick={() => isPast && onStepClick(step.id)}
              disabled={!isPast && !isActive}
              className={`relative z-10 flex h-8 w-8 items-center justify-center rounded-full ${
                isActive
                  ? 'bg-orange-500 text-white ring-4 ring-orange-500/30'
                  : isPast
                  ? 'bg-orange-500 text-white cursor-pointer'
                  : 'bg-stone-600 text-stone-300'
              } transition-all duration-300`}
            >
              {isPast ? (
                <DashboardIcons.Check className="h-4 w-4" />
              ) : (
                <span className="text-xs font-semibold">{index + 1}</span>
              )}
            </button>
            
            {/* Step label */}
            <span className={`mt-2 text-xs font-medium ${
              isActive ? 'text-orange-400' : isPast ? 'text-orange-400' : 'text-stone-400'
            }`}>
              {step.label}
              {step.optional && <span className="ml-1 text-stone-500">(Optional)</span>}
            </span>
          </div>
        );
      })}
    </div>
  </div>
);

export default FormStepper;
