import React, { useState, memo } from 'react';
import { PipelineStage, Deal } from '../../types/pipedrive';
import StageColumn from './StageColumn';

interface PipelineViewProps {
  stages: PipelineStage[];
  deals: Deal[];
  onAddDeal: (stageId: string) => void;
  onEditStage?: (stage: PipelineStage) => void;
  onDeleteStage?: (stage: PipelineStage) => void;
  onEditDeal?: (deal: Deal) => void;
  onDeleteDeal?: (deal: Deal) => void;
  onMoveDeal: (dealId: string, newStageId: string) => void;
}

const PipelineView: React.FC<PipelineViewProps> = memo(({
  stages,
  deals,
  onAddDeal,
  onEditStage,
  onDeleteStage,
  onEditDeal,
  onDeleteDeal,
  onMoveDeal
}) => {
  const [draggedDealId, setDraggedDealId] = useState<string | null>(null);

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, dealId: string) => {
    setDraggedDealId(dealId);
    e.dataTransfer.setData('text/plain', dealId);
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, stageId: string) => {
    e.preventDefault();

    if (draggedDealId) {
      // Get the current deal
      const deal = deals.find(d => d.id === draggedDealId);

      // Only move if the stage is different
      if (deal && deal.stage_id !== stageId) {
        onMoveDeal(draggedDealId, stageId);
      }

      setDraggedDealId(null);
    }
  };

  if (stages.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-stone-400">No stages found for this pipeline</p>
      </div>
    );
  }

  return (
    <div className="h-full flex space-x-4 overflow-x-auto overflow-y-hidden custom-scrollbar pb-2 max-h-full">
      {stages.map(stage => (
        <StageColumn
          key={stage.id}
          stage={stage}
          deals={deals}
          onAddDeal={onAddDeal}
          onEditStage={onEditStage}
          onDeleteStage={onDeleteStage}
          onEditDeal={onEditDeal}
          onDeleteDeal={onDeleteDeal}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        />
      ))}
    </div>
  );
});

export default PipelineView;
