import React, { useState, useEffect, useCallback } from 'react'
import { Client } from '../../../types'
import { getSupabase } from '../../../lib/supabase'
import { DashboardIcons } from '../../icons/DashboardIcons'
import { debounce } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { sendLoanPaymentNotification } from '../../../utils/whatsAppNotificationService'

// Import components
import FormStepper from './FormStepper'
import FormLayout from './FormLayout'
import ClientStep from './ClientStep'
import DetailsStep from './DetailsStep'
import PaymentStep from './PaymentStep'

// Import types
import { LoanFormProps, LoanFormState, FormStep } from './types'

function LoanForm({ onClose, onSuccess }: LoanFormProps): React.JSX.Element {
  const [formData, setFormData] = useState<LoanFormState>({
    client_id: '',
    license_id: '',
    start_date: new Date().toISOString().split('T')[0],
    invoice_number: '',
    weapon_cost: 0,
    initial_payment: 0,
    loan_amount: 0,
    remaining_balance: 0,
    interest_rate: 0, // Default interest rate is 0%
    payment_due_date: '',
    status: 'active',
    loan_term: 3, // Default to 3 months
    notes: '', // Added notes field
    pause_notifications: false // Default to notifications enabled
  })
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [clientSearchTerm, setClientSearchTerm] = useState('')
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [step, setStep] = useState<FormStep>('client')
  const [completedSteps, setCompletedSteps] = useState<FormStep[]>([])

  // Define form steps
  const formSteps = [
    { id: 'client', label: 'Client' },
    { id: 'details', label: 'Loan Info' },
    { id: 'payment', label: 'Payment Terms' },
  ]

  // Navigate to a step
  const goToStep = (stepId: FormStep) => {
    // Only allow navigation to completed steps or the current step + 1
    const currentStepIndex = formSteps.findIndex(s => s.id === step)
    const targetStepIndex = formSteps.findIndex(s => s.id === stepId)

    if (completedSteps.includes(stepId) || targetStepIndex <= currentStepIndex + 1) {
      setStep(stepId)
    }
  }

  // Mark current step as completed and move to next step
  const completeStep = (nextStep: FormStep) => {
    if (!completedSteps.includes(step)) {
      setCompletedSteps([...completedSteps, step])
    }
    setStep(nextStep)
  }

  // Handle next step navigation with validation
  const handleNextStep = () => {
    const currentStepIndex = formSteps.findIndex(s => s.id === step)

    // Validate current step
    if (step === 'client' && !formData.client_id) {
      setErrors({ client_id: 'Please select a client' })
      return
    }

    if (step === 'details') {
      if (!formData.invoice_number) {
        setErrors({ invoice_number: 'Quote number is required' })
        return
      }
      if (!formData.weapon_cost || formData.weapon_cost <= 0) {
        setErrors({ weapon_cost: 'Valid Firearm Cost is required' })
        return
      }
    }

    if (step === 'payment') {
      if (!formData.payment_due_date) {
        setErrors({ payment_due_date: 'Payment due date is required' })
        return
      }
    }

    // Move to next step
    if (currentStepIndex < formSteps.length - 1) {
      const nextStep = formSteps[currentStepIndex + 1].id as FormStep
      completeStep(nextStep)
    }
  }

  // Handle previous step navigation
  const handlePrevStep = () => {
    const currentStepIndex = formSteps.findIndex(s => s.id === step)
    if (currentStepIndex > 0) {
      setStep(formSteps[currentStepIndex - 1].id as FormStep)
    }
  }

  // Handle form field changes
  const handleFormFieldChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    const inputElement = e.target as HTMLInputElement

    // Handle numeric inputs (only for input elements)
    if (e.target instanceof HTMLInputElement && inputElement.type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }))
    } else {
      // Convert text inputs to uppercase
      const uppercaseValue = typeof value === 'string' ? value.toUpperCase() : value;

      setFormData(prev => ({
        ...prev,
        [name]: uppercaseValue
      }))
    }

    // Auto-calculate remaining balance when loan amount changes
    if (name === 'loan_amount') {
      setFormData(prev => ({
        ...prev,
        remaining_balance: parseFloat(value) || 0
      }))
    }

    // Clear error when field is updated
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }))
  }

  // Fetch clients with debounce for search
  const fetchClients = useCallback(
    debounce(async (searchTerm: string) => {
      try {
        let query = getSupabase().from('clients').select('*')

        if (searchTerm) {
          const trimmedTerm = searchTerm.trim();
          const terms = trimmedTerm.split(' ').filter(t => t.length > 0);

          // Check if it's an ID number (only digits)
          if (/^\d+$/.test(trimmedTerm)) {
            query = query.ilike('id_number', `%${trimmedTerm}%`);
          }
          // If multiple terms (likely a full name)
          else if (terms.length > 1) {
            // Try different combinations for first name and last name
            const possibleFirstName = terms[0];
            const possibleLastName = terms[terms.length - 1];

            // First name + last name pattern (most common case)
            query = query.or(
              `and(first_name.ilike.%${possibleFirstName}%,last_name.ilike.%${possibleLastName}%),` +
              // Also try the reverse in case names are entered in reverse order
              `and(first_name.ilike.%${possibleLastName}%,last_name.ilike.%${possibleFirstName}%),` +
              // Also check if the full search term is in either field (for compound names)
              `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%`
            );
          }
          // Single term (could be first name, last name, or partial ID)
          else {
            query = query.or(
              `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%,id_number.ilike.%${trimmedTerm}%`
            );
          }
        }

        const { data, error } = await query.limit(10)

        if (error) throw error
        setClients(data || [])
      } catch (error) {
        console.error('Error fetching clients:', error)
      }
    }, 300),
    []
  )

  // Handle client selection
  const handleClientSelect = (client: Client) => {
    setSelectedClient(client)
    setFormData(prev => ({
      ...prev,
      client_id: client.id,
    }))
    fetchClients(client.first_name) // Clear search term
    completeStep('details') // Move to details step after selecting client
  }

  // Handle client search change
  const handleClientSearchChange = (searchTerm: string) => {
    setClientSearchTerm(searchTerm)
    fetchClients(searchTerm)
  }

  // Handle client removal
  const handleClientRemove = () => {
    setSelectedClient(null)
    setFormData(prev => ({ ...prev, client_id: '' }))
  }

  // Calculate payment due date (always 28th of the month)
  const getNextMonthPaymentDate = (dateString: string): string => {
    const startDate = new Date(dateString)
    const nextMonth = new Date(startDate)

    // Always set to 28th of next month
    nextMonth.setMonth(startDate.getMonth() + 1)
    nextMonth.setDate(28)

    // Ensure we're returning the date in YYYY-MM-DD format
    return nextMonth.toISOString().split('T')[0]
  }

  // Update due date when start date changes
  useEffect(() => {
    if (formData.start_date) {
      // Use the function to get the 28th of the next month (after grace period)
      const dueDate = getNextMonthPaymentDate(formData.start_date)
      setFormData(prev => ({
        ...prev,
        payment_due_date: dueDate,
        next_payment_date: dueDate // Set the next_payment_date to match payment_due_date
      }))
    }
  }, [formData.start_date])

  // Auto-calculate loan amount and initial payment as 40% of Firearm Cost
  useEffect(() => {
    if (formData.weapon_cost !== undefined && formData.weapon_cost > 0) {
      // Calculate 40% of the Firearm Cost for initial payment
      const fortyPercentOfCost = Math.round((formData.weapon_cost * 0.4) * 100) / 100
      // Calculate 60% of the Firearm Cost for loan amount (remaining balance)
      const sixtyPercentOfCost = Math.round((formData.weapon_cost * 0.6) * 100) / 100

      setFormData(prev => ({
        ...prev,
        initial_payment: fortyPercentOfCost,
        loan_amount: sixtyPercentOfCost,
        remaining_balance: sixtyPercentOfCost,
        _isAutoUpdate: true // Add a flag to track auto-updates
      }))
    }
  }, [formData.weapon_cost])

  // Update loan amount and remaining balance when initial payment changes manually
  useEffect(() => {
    // Skip if weapon cost is 0 or undefined to prevent incorrect calculations
    if (!formData.weapon_cost || formData.weapon_cost <= 0) return

    // Skip if this update was triggered by the weapon cost useEffect
    if (formData._isAutoUpdate) {
      // Clear the auto-update flag for future updates
      setFormData(prev => ({
        ...prev,
        _isAutoUpdate: undefined
      }))
      return
    }

    // Only run this when initial_payment is explicitly changed by the user
    if (formData.initial_payment !== undefined) {
      // Calculate new loan amount based on weapon cost minus initial payment
      const newLoanAmount = Math.max(0, Math.round((formData.weapon_cost - formData.initial_payment) * 100) / 100)

      // Only update loan_amount and remaining_balance if they're different from current values
      if (newLoanAmount !== formData.loan_amount) {
        setFormData(prev => ({
          ...prev,
          loan_amount: newLoanAmount,
          remaining_balance: newLoanAmount
        }))
      }
    }
  }, [formData.initial_payment])

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.client_id) newErrors.client_id = 'Client is required'
    if (!formData.start_date) newErrors.start_date = 'Start date is required'
    if (!formData.invoice_number) newErrors.invoice_number = 'Quote number is required'
    if (!formData.weapon_cost || formData.weapon_cost <= 0) {
      newErrors.weapon_cost = 'Valid Firearm Cost is required'
    }
    if (!formData.loan_amount || formData.loan_amount <= 0) {
      newErrors.loan_amount = 'Valid loan amount is required'
    }
    if (formData.interest_rate === undefined || formData.interest_rate === null || formData.interest_rate < 0) {
      newErrors.interest_rate = 'Valid interest rate is required'
    }
    if (!formData.payment_due_date) newErrors.payment_due_date = 'Payment due date is required'
    if (!formData.loan_term || formData.loan_term <= 0) {
      newErrors.loan_term = 'Valid loan term is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Send WhatsApp notification
  const sendWhatsAppNotification = async (loanData: any, client: Client | null) => {
    try {
      // Skip notification if notifications are paused or client data is missing
      if (loanData.pause_notifications || !client || !client.phone) return

      console.log('Preparing to send WhatsApp notification for loan:', loanData.invoice_number)

      const clientName = `${client.first_name} ${client.last_name}`
      const paymentAmount = (loanData.loan_amount / loanData.loan_term).toFixed(2)
      const paymentDate = new Date(loanData.payment_due_date).toLocaleDateString()

      // Use the shared WhatsApp notification service
      const result = await sendLoanPaymentNotification(
        clientName,
        client.phone,
        loanData.invoice_number,
        parseFloat(paymentAmount),
        paymentDate,
        loanData.remaining_balance
      )

      if (result.success) {
        console.log('Successfully sent WhatsApp notification')
      } else {
        console.error('Failed to send WhatsApp notification:', result.error)
      }
    } catch (error) {
      console.error('Error sending WhatsApp notification:', error)
      // Don't throw error - we don't want to fail loan creation if WhatsApp fails
    }
  }

  // Create loan
  const handleCreateLoan = () => {
    if (!validateForm()) return

    setLoading(true)
    const submitLoan = async () => {
      try {
        // Make sure loan_amount and remaining_balance have valid values
        const finalLoanAmount = formData.loan_amount || 0
        const finalRemainingBalance = formData.remaining_balance || finalLoanAmount

        // Only include properties that exist in the database schema (exclude _isAutoUpdate)
        const loanData: any = {
          id: uuidv4(),
          client_id: formData.client_id,
          start_date: formData.start_date,
          invoice_number: formData.invoice_number,
          weapon_cost: formData.weapon_cost,
          initial_payment: formData.initial_payment,
          loan_amount: finalLoanAmount,
          remaining_balance: finalRemainingBalance,
          interest_rate: formData.interest_rate,
          payment_due_date: formData.payment_due_date,
          next_payment_date: formData.payment_due_date, // Set next_payment_date to match payment_due_date
          status: formData.status,
          loan_term: formData.loan_term,
          notes: formData.notes,
          penalties: 0, // Initialize penalties to 0
          pause_notifications: formData.pause_notifications
        }

        // Only add license_id if it's not empty
        if (formData.license_id && formData.license_id.trim() !== '') {
          loanData.license_id = formData.license_id
        }

        console.log('Creating loan with data:', loanData)

        // Create new loan
        const { error } = await getSupabase()
          .from('loans')
          .insert(loanData)

        if (error) throw error

        // Send WhatsApp notification after successful loan creation
        await sendWhatsAppNotification(loanData, selectedClient)

        onSuccess()
      } catch (error: any) {
        console.error('Error saving loan:', error)
        alert(`Failed to save loan: ${error.message}`)
      } finally {
        setLoading(false)
      }
    }

    submitLoan()
  }

  // Render current step content
  const renderStepContent = () => {
    switch (step) {
      case 'client':
        return (
          <ClientStep
            selectedClient={selectedClient}
            clientSearchTerm={clientSearchTerm}
            clients={clients}
            onClientSearchChange={handleClientSearchChange}
            onClientSelect={handleClientSelect}
            onClientRemove={handleClientRemove}
            error={errors.client_id}
          />
        )
      case 'details':
        return (
          <DetailsStep
            formData={formData}
            onChange={handleFormFieldChange}
            errors={errors}
          />
        )
      case 'payment':
        return (
          <PaymentStep
            formData={formData}
            onChange={handleFormFieldChange}
            onCheckboxChange={handleCheckboxChange}
            errors={errors}
          />
        )
      default:
        return null
    }
  }

  // Navigation buttons
  const renderNavButtons = () => {
    const isFirstStep = step === 'client'
    const isLastStep = step === 'payment'

    return (
      <div className="flex justify-between mt-6">
        {!isFirstStep ? (
          <button
            type="button"
            onClick={handlePrevStep}
            className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors"
          >
            Back
          </button>
        ) : (
          <div></div> // Empty div to maintain layout
        )}

        <div className="flex gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-stone-700 hover:bg-stone-600 text-white rounded-lg transition-colors"
          >
            Cancel
          </button>

          {isLastStep ? (
            <button
              type="button"
              onClick={handleCreateLoan}
              disabled={loading}
              className="px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                text-white rounded-lg shadow-lg shadow-orange-900/20 transition-all flex items-center gap-2 disabled:opacity-70"
            >
              {loading ? (
                <>
                  <DashboardIcons.Spinner className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <DashboardIcons.Save className="w-4 h-4" />
                  Create Loan
                </>
              )}
            </button>
          ) : (
            <button
              type="button"
              onClick={handleNextStep}
              className="px-4 py-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
                text-white rounded-lg shadow-lg shadow-orange-900/20 transition-all"
            >
              Continue
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
    <FormLayout
      title="Create New Loan"
      onClose={onClose}
      className="max-w-4xl"
    >
      <FormStepper
        steps={formSteps}
        currentStep={step}
        onStepClick={goToStep as (id: string) => void}
      />

      {renderStepContent()}
      {renderNavButtons()}
    </FormLayout>
  )
}

export default LoanForm
