import { NewLicenceData } from '../../../../types/NewLicenceData'

/**
 * Common props for all section components
 */
export interface SectionProps {
  formData: NewLicenceData
  updateFormData: (updatedData: Partial<NewLicenceData>) => void
  className?: string
}

/**
 * Props for sections that use address autocomplete
 */
export interface SectionWithAddressProps extends SectionProps {
  handleAddressChange: (
    address: string,
    postalCode?: string,
    houseNumber?: string,
    isWorkAddress?: boolean
  ) => void
  handleHouseNumberChange: (houseNumber: string, isWorkAddress?: boolean) => void
}

/**
 * Validation status type
 */
export type ValidationStatus = 'idle' | 'processing' | 'success' | 'error'

/**
 * Template status type
 */
export type TemplateStatus = 'loading' | 'ready' | 'error'
