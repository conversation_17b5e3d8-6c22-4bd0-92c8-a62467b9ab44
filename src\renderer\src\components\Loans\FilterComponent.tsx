import React from 'react'
import { FilterCounts, LoanFilterType } from './types'

interface FilterComponentProps {
  filter: LoanFilterType
  setFilter: (filter: LoanFilterType) => void
  filterCounts: FilterCounts
  totalRemainingAmount: number
}

const FilterComponent: React.FC<FilterComponentProps> = ({
  filter,
  setFilter,
  filterCounts,
  totalRemainingAmount
}) => {
  return (
    <div className="bg-stone-800/70 p-4 rounded-lg shadow-lg flex flex-col h-full overflow-hidden">
      <div className="flex flex-col gap-3 h-full">
        {/* Total Outstanding - Fixed height container */}
        <div className="flex items-center justify-between mb-2 bg-stone-700/30 p-2 rounded-lg border border-stone-600/30">
          <div className="text-white text-sm font-medium">Total Outstanding:</div>
          <div className="text-white font-medium bg-orange-500/10 px-2 py-1 rounded-md border border-orange-500/30 text-sm w-auto max-w-[60%] overflow-hidden text-ellipsis whitespace-nowrap">
            R{' '}
            {totalRemainingAmount.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            })}
          </div>
        </div>

        {/* Filter Label */}
        <div className="flex items-center">
          <div className="text-stone-300 font-medium text-base">Filter Loans:</div>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between ${
              filter === 'all'
                ? 'bg-gradient-to-r from-stone-500 to-stone-600 text-white shadow-md shadow-stone-500/20'
                : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
            }`}
          >
            <span className="flex items-center">
              <span className="w-1.5 h-1.5 rounded-full bg-stone-400 mr-1" />
              <span>All</span>
            </span>
            <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
              {filterCounts.all}
            </span>
          </button>
          <button
            onClick={() => setFilter('active')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between ${
              filter === 'active'
                ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-md shadow-green-500/20'
                : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
            }`}
          >
            <span className="flex items-center">
              <span className="w-1.5 h-1.5 rounded-full bg-green-400 mr-1" />
              <span>Active</span>
            </span>
            <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
              {filterCounts.active}
            </span>
          </button>
          <button
            onClick={() => setFilter('paid')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between ${
              filter === 'paid'
                ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md shadow-blue-500/20'
                : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
            }`}
          >
            <span className="flex items-center">
              <span className="w-1.5 h-1.5 rounded-full bg-blue-400 mr-1" />
              <span>Paid</span>
            </span>
            <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
              {filterCounts.paid}
            </span>
          </button>
          <button
            onClick={() => setFilter('overdue')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between ${
              filter === 'overdue'
                ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-md shadow-red-500/20'
                : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
            }`}
          >
            <span className="flex items-center">
              <span className="w-1.5 h-1.5 rounded-full bg-red-400 mr-1" />
              <span>Overdue</span>
            </span>
            <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
              {filterCounts.overdue}
            </span>
          </button>
          <button
            onClick={() => setFilter('pending')}
            className={`px-4 py-2 rounded-lg text-sm transition-all duration-200 flex items-center justify-between ${
              filter === 'pending'
                ? 'bg-gradient-to-r from-amber-500 to-amber-600 text-white shadow-md shadow-amber-500/20'
                : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
            }`}
            title="Shows loans with current month due amount above R0.01"
          >
            <span className="flex items-center">
              <span className="w-1.5 h-1.5 rounded-full bg-amber-400 mr-1" />
              <span>Current Due</span>
            </span>
            <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
              {filterCounts.pending}
            </span>
          </button>
        </div>

        {/* Days Active Filters */}
        <div className="mt-2 pt-3 border-t border-stone-700 flex flex-col flex-1 overflow-hidden">
          <div className="text-stone-300 font-medium text-base mb-2">Filter by Days Active:</div>
          <div className="grid grid-cols-2 gap-2 overflow-y-auto custom-scrollbar pb-1 pr-1">
            <button
              onClick={() => setFilter('days-0-30')}
              className={`px-2 py-1.5 rounded-lg text-sm transition-all duration-200 flex items-center justify-between w-full ${
                filter === 'days-0-30'
                  ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-md shadow-purple-500/20'
                  : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
              }`}
            >
              <span className="flex items-center">
                <span className="w-1.5 h-1.5 rounded-full bg-purple-400 mr-1" />
                <span>0-30 Days</span>
              </span>
              <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
                {filterCounts['days-0-30']}
              </span>
            </button>
            <button
              onClick={() => setFilter('days-31-60')}
              className={`px-2 py-1.5 rounded-lg text-sm transition-all duration-200 flex items-center justify-between w-full ${
                filter === 'days-31-60'
                  ? 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white shadow-md shadow-indigo-500/20'
                  : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
              }`}
            >
              <span className="flex items-center">
                <span className="w-1.5 h-1.5 rounded-full bg-indigo-400 mr-1" />
                <span>31-60 Days</span>
              </span>
              <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
                {filterCounts['days-31-60']}
              </span>
            </button>
            <button
              onClick={() => setFilter('days-61-90')}
              className={`px-2 py-1.5 rounded-lg text-sm transition-all duration-200 flex items-center justify-between w-full ${
                filter === 'days-61-90'
                  ? 'bg-gradient-to-r from-teal-500 to-teal-600 text-white shadow-md shadow-teal-500/20'
                  : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
              }`}
            >
              <span className="flex items-center">
                <span className="w-1.5 h-1.5 rounded-full bg-teal-400 mr-1" />
                <span>61-90 Days</span>
              </span>
              <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
                {filterCounts['days-61-90']}
              </span>
            </button>
            <button
              onClick={() => setFilter('days-90-plus')}
              className={`px-2 py-1.5 rounded-lg text-sm transition-all duration-200 flex items-center justify-between w-full ${
                filter === 'days-90-plus'
                  ? 'bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-md shadow-pink-500/20'
                  : 'bg-stone-700 text-stone-300 hover:bg-stone-600'
              }`}
            >
              <span className="flex items-center">
                <span className="w-1.5 h-1.5 rounded-full bg-pink-400 mr-1" />
                <span>90+ Days</span>
              </span>
              <span className="ml-1 px-1.5 py-0.5 bg-stone-800/40 rounded text-xs">
                {filterCounts['days-90-plus']}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FilterComponent

