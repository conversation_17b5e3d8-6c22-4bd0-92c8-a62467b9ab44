export interface ClientCreditWallet {
  id: string;
  client_id: string;
  balance: number;
  created_at: string;
  updated_at: string;
}

export interface CreditTransaction {
  id: string;
  wallet_id: string;
  amount: number;
  transaction_type: 'payment' | 'charge' | 'adjustment' | 'transfer';
  reference_type: 'firearm' | 'manual' | 'system';
  reference_id?: string; // Can be firearm_id or null for manual transactions
  description?: string;
  transaction_date: string;
  created_at: string;
  updated_at: string;
}

export interface FirearmStorageCharge {
  id: string;
  firearm_id: string;
  client_id: string;
  wallet_id: string;
  transaction_id: string;
  charge_date: string;
  days_charged: number;
  daily_rate: number;
  total_amount: number;
  created_at: string;
  updated_at: string;
}

// Extended client interface with wallet information
export interface ClientWithWallet {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  wallet?: ClientCreditWallet;
}

// Transaction with related information
export interface TransactionWithDetails extends CreditTransaction {
  client?: {
    id: string;
    first_name: string;
    last_name: string;
  };
  firearm?: {
    id: string;
    make: string;
    model: string;
    serial: string;
  };
}
