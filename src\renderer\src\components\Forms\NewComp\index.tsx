import React, { useState, useEffect, useMemo } from 'react'
import { FormData, initialFormData } from '../../../types/FormData'
import {
  FormLayout,
  FormSectionType
} from '../../FormComponents'

// Import section components
import PersonalInfo from './sections/PersonalInfo'
import SpouseInfo from './sections/SpouseInfo'
import ProfessionalInfo from './sections/ProfessionalInfo'
import FirearmTypes from './sections/FirearmTypes'
import TrainingInstitution from './sections/TrainingInstitution'
import CriminalHistory from './sections/CriminalHistory'
import TemplateStatus from './sections/TemplateStatus'

// Import types
import { ValidationStatus, TemplateStatus as TemplateStatusType } from './utils/types'

// Constants
const TEMPLATE_URL =
  'https://app.gunlicence.co.za/storage/v1/object/public/templates/Competency/517_SAPS_Form.docx'
const TEMPLATE_NAME = '517_SAPS_Form.docx'

// Types
interface NewCompFormProps {
  formType: 'competency' | 'licence' | 'deceased'
  onSubmit: (data: FormData) => void
}

/**
 * NewComp - New Competency Application Form Component
 *
 * A multi-step form for processing new competency applications
 * that passes data to DocScript for document generation.
 */
export default function NewCompForm({ formType, onSubmit }: NewCompFormProps): JSX.Element {
  // Form state
  const [formData, setFormData] = useState<FormData>(initialFormData)
  const [currentStep, setCurrentStep] = useState<number>(1)

  // Template state
  const [templateStatus, setTemplateStatus] = useState<TemplateStatusType>('loading')
  const [templateError, setTemplateError] = useState<string | null>(null)

  // Submission state
  const [submissionStatus, setSubmissionStatus] = useState<ValidationStatus>('idle')
  const [submissionMessage, setSubmissionMessage] = useState<string | null>(null)

  // Check if the template is accessible on component mount
  useEffect(() => {
    let isMounted = true

    async function checkTemplate() {
      if (!isMounted) return

      setTemplateStatus('loading')
      setTemplateError(null)

      try {
        // Check if the template URL is accessible
        const response = await fetch(TEMPLATE_URL, { method: 'HEAD' })

        if (!isMounted) return

        if (!response.ok) {
          throw new Error(`Template not accessible: ${response.statusText}`)
        }

        setTemplateStatus('ready')
      } catch (err) {
        if (!isMounted) return

        console.error('Error checking template:', err)
        setTemplateStatus('error')
        setTemplateError('Could not access the template file. Please try again later.')
      }
    }

    checkTemplate()

    return () => {
      isMounted = false
    }
  }, [])

  // Form sections definition
  const sections: FormSectionType[] = useMemo(
    () => {
      if (formType === 'competency') {
        return [
          { id: 'personal', title: 'Personal Information' },
          { id: 'professional', title: 'Professional Information' },
          { id: 'firearms', title: 'Firearm Types' },
          { id: 'training', title: 'Training Institution' },
          { id: 'criminal', title: 'Criminal History' }
        ]
      } else if (formType === 'licence') {
        return [
          { id: 'personal', title: 'Personal Information' },
          { id: 'firearms', title: 'Firearm Details' },
          { id: 'supporting', title: 'Supporting Documents' },
          { id: 'declaration', title: 'Declaration' }
        ]
      } else {
        // deceased
        return [
          { id: 'deceased', title: 'Deceased Details' },
          { id: 'executor', title: 'Executor Details' },
          { id: 'firearms', title: 'Firearm Details' }
        ]
      }
    },
    [formType]
  )

  // Update form data
  const updateFormData = (updatedData: Partial<FormData>) => {
    setFormData((prevData) => ({
      ...prevData,
      ...updatedData
    }))
  }

  // Handle address change
  // @ts-ignore - houseNumber parameter is unused but kept for compatibility with SectionWithAddressProps
  const handleAddressChange = (
    address: string,
    postalCode?: string,
    _houseNumber?: string,
    isWorkAddress = false
  ) => {
    if (isWorkAddress) {
      updateFormData({
        workAddress: address,
        workPostalCode: postalCode || formData.workPostalCode || ''
      })
    } else {
      updateFormData({
        physicalAddress: address,
        postalCode: postalCode || formData.postalCode || ''
      })
    }
  }

  // Deprecated function stub for backward compatibility
  // Will be removed in a future version
  const handleHouseNumberChange = () => {}

  // Validate the current step

  // Update submission state
  const updateSubmissionState = (status: ValidationStatus, message: string) => {
    setSubmissionStatus(status)
    setSubmissionMessage(message)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Skip validation since all fields are now optional

    // Update submission status
    updateSubmissionState('processing', 'Processing your application...')

    try {
      // Create a copy of the form data
      const submissionData = { ...formData }

      // Add the template information
      submissionData.templateUrl = TEMPLATE_URL
      submissionData.templateName = TEMPLATE_NAME

      // Ensure ID is properly mapped for template processing
      if (submissionData.idNumber) {
        // Make sure the ID is also available in the npid field for backward compatibility
        (submissionData as any).npid = submissionData.idNumber
      }

      // Process the document directly here instead of sending to parent component
      updateSubmissionState('processing', 'Downloading template...')

      try {
        // Fetch the template file
        const templateResponse = await fetch(TEMPLATE_URL)
        if (!templateResponse.ok) {
          throw new Error(`Failed to fetch template: ${templateResponse.statusText}`)
        }

        const templateBlob = await templateResponse.blob()
        const templateFile = new File([templateBlob], TEMPLATE_NAME, {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })

        // Process the document
        updateSubmissionState('processing', 'Processing document...')
        const { processDocument, saveProcessedDocument } = await import('../../DocScript/DocumentProcessor')
        const { blob, filename } = await processDocument(templateFile, submissionData)

        // Auto-download the document (no save dialog for forms)
        updateSubmissionState('processing', 'Downloading document...')
        await saveProcessedDocument(blob, filename, false) // false = don't show save dialog

        // Update submission status
        updateSubmissionState('success', 'Document processed and saved successfully!')
      } catch (error) {
        console.error('Error processing template:', error)
        throw new Error(`Error processing template: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }

      // Also submit the data to the parent component for any additional processing
      onSubmit(submissionData)
    } catch (error) {
      console.error('Error processing document:', error)
      updateSubmissionState('error', `Error processing document: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Render the current step based on the currentStep state
  const renderCurrentStep = () => {
    const sectionId = sections[currentStep - 1].id

    switch (sectionId) {
      case 'personal':
        return (
          <>
            <PersonalInfo
              formData={formData}
              updateFormData={updateFormData}
              handleAddressChange={handleAddressChange}
            />
            <SpouseInfo
              formData={formData}
              updateFormData={updateFormData}
            />
          </>
        )
      case 'professional':
        return (
          <ProfessionalInfo
            formData={formData}
            updateFormData={updateFormData}
            handleAddressChange={handleAddressChange}
          />
        )
      case 'firearms':
        return (
          <FirearmTypes
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'training':
        return (
          <TrainingInstitution
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      case 'criminal':
        return (
          <CriminalHistory
            formData={formData}
            updateFormData={updateFormData}
          />
        )
      default:
        return null
    }
  }

  // Handle cancel submission
  const onCancelSubmission = () => {
    setSubmissionStatus('idle')
  }

  // Main component render
  return (
    <FormLayout
      title="New Competency Application"
      sections={sections}
      currentStep={currentStep}
      setCurrentStep={setCurrentStep}
      onSubmit={handleSubmit}
      submissionStatus={submissionStatus}
      submissionMessage={submissionMessage}
      onCancelSubmission={onCancelSubmission}
      isDocScriptForm={true}
    >
      {templateStatus === 'error' && (
        <TemplateStatus
          status={templateStatus}
          templateName={TEMPLATE_NAME}
          error={templateError}
        />
      )}
      {renderCurrentStep()}
    </FormLayout>
  )
}
