import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
// @ts-ignore
const { app } = require('electron');

// Encryption settings - using AES-256-CBC
const ALGORITHM = 'aes-256-cbc';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits

/**
 * Encrypts a string (typically .env file contents)
 * @param text The text to encrypt (usually the content of .env file)
 * @param key The encryption key
 * @returns The encrypted text as a Buffer
 */
export function encrypt(text: string, key: Buffer): Buffer {
  // Create a random initialization vector
  const iv = crypto.randomBytes(IV_LENGTH);

  // Create the cipher using the key and iv
  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

  // Encrypt the text
  let encrypted = cipher.update(text, 'utf8');
  encrypted = Buffer.concat([encrypted, cipher.final()]);

  // Return iv concatenated with the encrypted data
  return Buffer.concat([iv, encrypted]);
}

/**
 * Decrypts an encrypted buffer
 * @param data The encrypted data
 * @param key The decryption key
 * @returns The decrypted text
 */
export function decrypt(data: Buffer, key: Buffer): string {
  // Extract the IV from the first 16 bytes
  const iv = data.subarray(0, IV_LENGTH);

  // Get the encrypted text
  const encryptedText = data.subarray(IV_LENGTH);

  // Create the decipher
  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

  // Decrypt the data
  let decrypted = decipher.update(encryptedText);
  decrypted = Buffer.concat([decrypted, decipher.final()]);

  // Return the decrypted text
  return decrypted.toString('utf8');
}

/**
 * Derives an encryption key from a password
 * @param password The password to derive the key from
 * @returns A Buffer containing the derived key
 */
export function deriveKey(password: string): Buffer {
  // Use a hardcoded salt - in production you might want to use a more secure method
  const salt = 'firearmstudio-salt-value';

  // Derive a key using PBKDF2
  const key = crypto.pbkdf2Sync(password, salt, 10000, KEY_LENGTH, 'sha256');
  return key;
}

/**
 * Generates a random password for encryption
 * @returns A random password string
 */
export function generatePassword(): string {
  return crypto.randomBytes(24).toString('hex');
}

/**
 * Encrypts a .env file using a generated password
 * @param sourcePath Path to the source .env file
 * @param destinationPath Path to save the encrypted .env file
 * @returns The password used for encryption (to be used later for decryption)
 */
export function encryptEnvFile(sourcePath: string, destinationPath: string): string {
  // Read the source file
  const content = fs.readFileSync(sourcePath, 'utf8');

  // Generate a password and derive a key
  const password = generatePassword();
  const key = deriveKey(password);

  // Encrypt the content
  const encrypted = encrypt(content, key);

  // Write the encrypted content to the destination file
  fs.writeFileSync(destinationPath, encrypted);

  // Return the password that was used
  return password;
}

/**
 * Decrypts a .env file using a password
 * @param sourcePath Path to the encrypted .env file
 * @param password The password used for encryption
 * @returns The decrypted content of the .env file
 */
export function decryptEnvFile(sourcePath: string, password: string): string {
  // Read the encrypted file
  const encryptedData = fs.readFileSync(sourcePath);

  // Derive the key from the password
  const key = deriveKey(password);

  // Decrypt the content
  try {
    return decrypt(encryptedData, key);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to decrypt .env file: ${errorMessage}`);
  }
}

// When in production, we need to access process.resourcesPath
declare global {
  namespace NodeJS {
    interface Process {
      readonly resourcesPath: string;
    }
  }
}

/**
 * Gets the path to the environment file based on whether the app is packaged
 * @param encrypted Whether to get the path to the encrypted .env file
 * @returns Path to the environment file
 */
export function getEnvFilePath(encrypted = false): string {
  if (app.isPackaged) {
    // In production, we need to check multiple possible locations for the .env.encrypted file
    // This is because Electron Builder may place extraResources in different locations
    const resourcesPath = process.resourcesPath;

    if (encrypted) {
      // For encrypted file, try multiple possible paths
      const possiblePaths = [
        path.join(resourcesPath, '.env.encrypted'),           // Direct in resources
        path.join(path.dirname(resourcesPath), '.env.encrypted'), // Parent of resources
        path.join(resourcesPath, '..', '.env.encrypted'),     // Parent directory
        path.join(app.getAppPath(), '.env.encrypted'),        // App directory
        path.join(app.getPath('userData'), '.env.encrypted')  // User data directory
      ];

      // Return the first path that exists
      for (const possiblePath of possiblePaths) {
        if (fs.existsSync(possiblePath)) {
          console.log('Found encrypted env file at:', possiblePath);
          return possiblePath;
        }
      }

      // If no file found, log paths we checked and return the default path
      console.error('Could not find .env.encrypted in any of these locations:', possiblePaths);
      return path.join(resourcesPath, '.env.encrypted');
    } else {
      // For non-encrypted, plaintext .env is not included in production
      return path.join(resourcesPath, '.env');
    }
  } else {
    // In development, we have both files in the project root
    return path.join(process.cwd(), encrypted ? '.env.encrypted' : '.env');
  }
}

/**
 * Loads environment variables from a decrypted .env file into process.env
 * @param envContent The decrypted content of the .env file
 */
export function loadEnvFromString(envContent: string): void {
  // Split the content by lines
  const lines = envContent.split('\n');

  // Process each line
  for (const line of lines) {
    // Skip comments and empty lines
    if (line.trim().startsWith('#') || !line.trim()) {
      continue;
    }

    // Split by the first equals sign
    const equalIndex = line.indexOf('=');
    if (equalIndex > 0) {
      const key = line.substring(0, equalIndex).trim();
      const value = line.substring(equalIndex + 1).trim();

      // Remove quotes if present
      let processedValue = value;
      if ((value.startsWith('"') && value.endsWith('"')) ||
        (value.startsWith("'") && value.endsWith("'"))) {
        processedValue = value.substring(1, value.length - 1);
      }

      // Set the environment variable
      process.env[key] = processedValue;
    }
  }
}