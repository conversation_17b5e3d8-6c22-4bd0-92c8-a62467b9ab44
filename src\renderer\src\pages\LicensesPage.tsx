import React, { Suspense, lazy, useEffect } from 'react'
import { DashboardIcons } from '../components/icons/DashboardIcons'
import { Dialog } from '../components/Dialog'
import SearchContainer from '../components/SearchContainer'
import Toast from '../components/Toast'
import {
  FilterComponent,
  Pagination,
  EmptyState,
  useLicensesService,
  LicenseCard,
  LicenseCardList
} from '../components/Licenses'
import { SkeletonLicenses } from '../components/SkeletonLoading'
import '../styles/focus-mode.css'

// Lazy load components
const LicenseForm = lazy(() => import('../components/Forms/AddLicence'))

function LicensesPage(): React.JSX.Element {
  const {
    // State
    loading,
    searchQuery,
    page,
    perPage,
    activeFilter,
    formState,
    deleteDialog,
    toast,
    focusedLicenseId,
    focusedLicense,
    visibleLicenses,
    total,
    licenseSearchTips,

    // Actions
    setPage,
    setActiveFilter,
    handleSearch,
    fetchLicenses,
    handleDeleteLicense,
    onEditLicense,
    resetFormState,
    setDeleteDialog,
    setToast,
    handleLicenseFocusToggle,
    handleAddLicense
  } = useLicensesService()

  // Add a useEffect to disable scrolling on the main content area and body
  useEffect(() => {
    // Get the main content area
    const mainContent = document.querySelector(
      '#root > div > div:nth-child(2)'
    ) as HTMLElement | null

    // Save original styles
    const originalBodyOverflow = document.body.style.overflow
    const originalHtmlOverflow = document.documentElement.style.overflow

    // Disable scrolling on body and html
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    if (mainContent) {
      // Save the original style
      const originalOverflow = mainContent.style.overflowY

      // Disable scrolling
      mainContent.style.overflowY = 'hidden'

      // Restore original styles when component unmounts
      return () => {
        mainContent.style.overflowY = originalOverflow
        document.body.style.overflow = originalBodyOverflow
        document.documentElement.style.overflow = originalHtmlOverflow
      }
    }

    // Restore original styles when component unmounts (if mainContent not found)
    return () => {
      document.body.style.overflow = originalBodyOverflow
      document.documentElement.style.overflow = originalHtmlOverflow
    }
  }, [])

  // Apply body overflow class when focus mode is active
  useEffect(() => {
    if (focusedLicenseId) {
      document.body.classList.add('focus-mode-active')
    } else {
      document.body.classList.remove('focus-mode-active')
    }

    return () => {
      document.body.classList.remove('focus-mode-active')
    }
  }, [focusedLicenseId])

  return (
    <div className="min-h-screen bg-gradient-to-b from-stone-900 to-stone-800 p-6">
      {/* Toast notifications */}
      {toast && <Toast message={toast.message} type={toast.type} onClose={() => setToast(null)} />}

      {/* Page title with pagination */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white flex items-center gap-3">
          License Management
          <div className="px-3 py-1 bg-stone-700/50 rounded-full text-sm text-stone-300">
            Total Records: {total}
          </div>
        </h1>

        {/* Pagination controls next to page title */}
        {visibleLicenses.length > 0 && (
          <Pagination
            page={page}
            setPage={setPage}
            totalItems={total}
            itemsPerPage={perPage}
          />
        )}
      </div>

      {/* Two-column layout */}
      <div className="flex flex-row h-[calc(100vh-180px)]">
        {/* Left column: Page tools */}
        <div className="w-80 bg-stone-800/60 rounded-lg shadow-lg p-4 flex-shrink-0">
          {/* Action Buttons */}
          <div className="flex items-center justify-between mb-4">
            {/* Refresh Button */}
            <button
              onClick={fetchLicenses}
              disabled={loading}
              className="bg-stone-700 hover:bg-stone-600 text-white px-3 py-2 rounded-lg
                transition-colors flex items-center gap-1 disabled:opacity-50 h-10"
            >
              <DashboardIcons.Refresh className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
            </button>
          </div>

          {/* Search Container */}
          <div className="mb-6">
            <SearchContainer
              onSearch={handleSearch}
              isLoading={loading}
              placeholder="Search licenses..."
              searchHintsLoader={async () => []}
              showRefreshButton={false}
              showCreateButton={false}
              searchTipsContent={
                <>
                  <p>{licenseSearchTips.title}</p>
                  <ul className="list-disc pl-4 mt-1 space-y-1">
                    {licenseSearchTips.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </>
              }
              debounceTime={400}
              className="w-full"
              initialValue={searchQuery}
            />
          </div>

          {/* Filter Component */}
          <FilterComponent activeFilter={activeFilter} setActiveFilter={setActiveFilter} />
        </div>

        {/* Right column: License cards */}
        <div className="flex-1 ml-6 flex flex-col">
          <div className="bg-stone-800/30 rounded-lg p-4 flex flex-col h-full">
            {loading ? (
              <div className="h-full">
                <SkeletonLicenses count={9} />
              </div>
            ) : focusedLicenseId && focusedLicense ? (
              <div className="focus-mode-container">
                <div className="focus-mode-card">
                  <LicenseCard
                    key={focusedLicense.id}
                    license={focusedLicense}
                    onEditLicense={(license) => {
                      // Extract client ID from the license object
                      const clientId = license.client && typeof license.client === 'object' ? license.client.id : ''
                      onEditLicense(license, clientId)
                    }}
                    onDeleteLicense={(licenseId) => setDeleteDialog({ isOpen: true, licenseId })}
                    onFocusToggle={handleLicenseFocusToggle}
                    isFocused={true}
                    isOtherCardFocused={false}
                  />
                </div>
              </div>
            ) : visibleLicenses.length > 0 ? (
              <LicenseCardList
                licenses={visibleLicenses}
                onEditLicense={(license) => {
                  // Extract client ID from the license object
                  const clientId = license.client && typeof license.client === 'object' ? license.client.id : ''
                  onEditLicense(license, clientId)
                }}
                onDeleteLicense={(licenseId) => setDeleteDialog({ isOpen: true, licenseId })}
                onFocusToggle={handleLicenseFocusToggle}
                focusedLicenseId={focusedLicenseId}
              />
            ) : (
              <EmptyState
                searchQuery={searchQuery}
                activeFilter={activeFilter}
                onAddLicense={handleAddLicense}
              />
            )}
          </div>
        </div>
      </div>

      {/* License Form */}
      <Suspense fallback={null}>
        {formState.isOpen && formState.type === 'license' && (
          <LicenseForm
            license={formState.selectedLicense}
            clientId={formState.selectedClientId!}
            onClose={resetFormState}
            onSuccess={() => {
              fetchLicenses()
              resetFormState()
            }}
          />
        )}
      </Suspense>

      <Dialog
        isOpen={deleteDialog.isOpen}
        title="Confirm Delete"
        message="Are you sure you want to delete this license? This action cannot be undone."
        onConfirm={() => deleteDialog.licenseId && handleDeleteLicense(deleteDialog.licenseId)}
        onCancel={() => setDeleteDialog({ isOpen: false, licenseId: null })}
      />
    </div>
  )
}

export default LicensesPage


