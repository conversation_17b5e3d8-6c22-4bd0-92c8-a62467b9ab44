import React from 'react'
import { FormSection, FormField } from '../../../FormComponents'
import { SectionProps } from '../utils/types'

/**
 * Firearm Types section component
 */
const FirearmTypes: React.FC<SectionProps> = ({
  formData,
  updateFormData,
  className = ''
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement

    updateFormData({
      [name]: type === 'checkbox' ? checked : value
    })
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-2">Firearm License Information</h3>

      <FormSection title="Competency Type" subtitle="Select the type of competency">
        <div className="flex flex-wrap gap-3 p-3 bg-stone-800 rounded-lg">
          <label className="flex items-center">
            <input
              type="radio"
              name="competencyType"
              value="trade"
              checked={formData.competencyType === 'trade'}
              onChange={handleChange}
              required={false}
              className="mr-2 h-4 w-4 text-orange-500 border-stone-600 rounded focus:ring-orange-500/50"
            />
            <span className="text-stone-300 text-sm">Trade in Firearms</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="competencyType"
              value="possess"
              checked={formData.competencyType === 'possess'}
              onChange={handleChange}
              required={false}
              className="mr-2 h-4 w-4 text-orange-500 border-stone-600 rounded focus:ring-orange-500/50"
            />
            <span className="text-stone-300 text-sm">To Possess Firearm</span>
          </label>
        </div>
      </FormSection>

      <FormSection title="Firearm Types" subtitle="Select the types of firearms">
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
          <label className="flex items-center p-2 bg-stone-800 rounded-lg">
            <input
              type="checkbox"
              name="pistol"
              checked={formData.pistol}
              onChange={handleChange}
              className="mr-2 h-4 w-4 text-orange-500 border-stone-600 rounded focus:ring-orange-500/50"
            />
            <span className="text-stone-300 text-sm">Pistol</span>
          </label>

          <label className="flex items-center p-2 bg-stone-800 rounded-lg">
            <input
              type="checkbox"
              name="rifle"
              checked={formData.rifle}
              onChange={handleChange}
              className="mr-2 h-4 w-4 text-orange-500 border-stone-600 rounded focus:ring-orange-500/50"
            />
            <span className="text-stone-300 text-sm">Rifle</span>
          </label>

          <label className="flex items-center p-2 bg-stone-800 rounded-lg">
            <input
              type="checkbox"
              name="shotgun"
              checked={formData.shotgun}
              onChange={handleChange}
              className="mr-2 h-4 w-4 text-orange-500 border-stone-600 rounded focus:ring-orange-500/50"
            />
            <span className="text-stone-300 text-sm">Shotgun</span>
          </label>

          <label className="flex items-center p-2 bg-stone-800 rounded-lg">
            <input
              type="checkbox"
              name="revolver"
              checked={formData.revolver}
              onChange={handleChange}
              className="mr-2 h-4 w-4 text-orange-500 border-stone-600 rounded focus:ring-orange-500/50"
            />
            <span className="text-stone-300 text-sm">Revolver</span>
          </label>

          <label className="flex items-center p-2 bg-stone-800 rounded-lg">
            <input
              type="checkbox"
              name="selfLoading"
              checked={formData.selfLoading}
              onChange={handleChange}
              className="mr-2 h-4 w-4 text-orange-500 border-stone-600 rounded focus:ring-orange-500/50"
            />
            <span className="text-stone-300 text-sm">Self Loading</span>
          </label>
        </div>
      </FormSection>

      {/* Under Age of 21 Section */}
      {formData.age && parseInt(formData.age) < 21 && (
        <FormSection
          title="Under Age of 21 - Additional Requirements"
          subtitle="Required for applicants under 21 years of age"
          variant="highlight"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
            <label className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700 cursor-pointer transition">
              <input
                type="checkbox"
                name="conductBusiness"
                checked={formData.conductBusiness || false}
                onChange={handleChange}
                className="mr-2 text-orange-500 border-stone-600 rounded h-4 w-4"
              />
              <span className="text-stone-300 text-sm">Conduct Business</span>
            </label>

            <label className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700 cursor-pointer transition">
              <input
                type="checkbox"
                name="gainfullyEmployed"
                checked={formData.gainfullyEmployed || false}
                onChange={handleChange}
                className="mr-2 text-orange-500 border-stone-600 rounded h-4 w-4"
              />
              <span className="text-stone-300 text-sm">Gainfully Employed</span>
            </label>

            <label className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700 cursor-pointer transition">
              <input
                type="checkbox"
                name="dedicatedHunter"
                checked={formData.dedicatedHunter || false}
                onChange={handleChange}
                className="mr-2 text-orange-500 border-stone-600 rounded h-4 w-4"
              />
              <span className="text-stone-300 text-sm">Dedicated Hunter</span>
            </label>

            <label className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700 cursor-pointer transition">
              <input
                type="checkbox"
                name="dedicatedSportPersonal"
                checked={formData.dedicatedSportPersonal || false}
                onChange={handleChange}
                className="mr-2 text-orange-500 border-stone-600 rounded h-4 w-4"
              />
              <span className="text-stone-300 text-sm">Dedicated Sport-Personal</span>
            </label>

            <label className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700 cursor-pointer transition">
              <input
                type="checkbox"
                name="privateCollector"
                checked={formData.privateCollector || false}
                onChange={handleChange}
                className="mr-2 text-orange-500 border-stone-600 rounded h-4 w-4"
              />
              <span className="text-stone-300 text-sm">Private Collector</span>
            </label>

            <label className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700 cursor-pointer transition">
              <input
                type="checkbox"
                name="publicCollector"
                checked={formData.publicCollector || false}
                onChange={handleChange}
                className="mr-2 text-orange-500 border-stone-600 rounded h-4 w-4"
              />
              <span className="text-stone-300 text-sm">Public Collector</span>
            </label>

            <label className="flex items-center p-2 bg-stone-800 rounded-lg hover:bg-stone-700 cursor-pointer transition">
              <input
                type="checkbox"
                name="otherUnderAge21"
                checked={formData.otherUnderAge21 || false}
                onChange={handleChange}
                className="mr-2 text-orange-500 border-stone-600 rounded h-4 w-4"
              />
              <span className="text-stone-300 text-sm">Other</span>
            </label>
          </div>

          {formData.otherUnderAge21 && (
            <div className="mt-2">
              <FormField
                label="Other (Please specify)"
                name="otherDetails"
                value={formData.otherDetails || ''}
                onChange={handleChange}
                placeholder="Specify other reason"
              />
            </div>
          )}

          <div className="mt-3">
            <FormField
              label="Submit Full Details"
              name="specifyDetails"
              value={formData.specifyDetails || ''}
              onChange={handleChange}
              type="textarea"
              rows={3}
              placeholder="Provide full details to support your application (required)"
              required={false}
            />
          </div>
        </FormSection>
      )}
    </div>
  )
}

export default FirearmTypes
