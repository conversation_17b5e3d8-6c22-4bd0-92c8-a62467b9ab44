import React, { Suspense, memo, useMemo } from 'react'
import { Client, License } from '../../types'
import { SkeletonDashboard } from '../SkeletonLoading'
import { useVirtualization } from '../../hooks/useVirtualization'
import { CardListErrorBoundary } from '../ErrorBoundary'

const ClientCard = React.lazy(() => import('./ClientCard'))

interface ClientCardListProps {
  clients: Client[]
  onEditClient: (client: Client) => void
  onDeleteClient: (clientId: string) => void
  onAddLicense: (clientId: string) => void
  onEditLicense: (license: License, clientId: string) => void
  onDeleteLicense: (licenseId: string) => void
  onFocusToggle: (clientId: string | null) => void
  focusedClientId: string | null
}

// Number of items to load initially and in each batch
const INITIAL_BATCH_SIZE = 5
const BATCH_INCREMENT = 5

const ClientCardList: React.FC<ClientCardListProps> = memo(({
  clients,
  onEditClient,
  onDeleteClient,
  onAddLicense,
  onEditLicense,
  onDeleteLicense,
  onFocusToggle,
  focusedClientId
}) => {
  // Use the virtualization hook
  const {
    items: visibleClients,
    total,
    isLoading: isLoadingMore,
    handleScroll,
    handleLoadMore
  } = useVirtualization<Client>(clients, INITIAL_BATCH_SIZE, BATCH_INCREMENT)

  // Memoize client-specific handlers to prevent unnecessary re-renders
  const clientHandlers = useMemo(() => {
    return visibleClients.map(client => ({
      handleEditClient: () => onEditClient(client),
      handleDeleteClient: () => onDeleteClient(client.id),
      handleAddLicense: () => onAddLicense(client.id),
      isFocused: focusedClientId === client.id,
      isOtherCardFocused: focusedClientId !== null && focusedClientId !== client.id
    }))
  }, [visibleClients, onEditClient, onDeleteClient, onAddLicense, focusedClientId])

  // Handle error in the client list
  const handleCardListError = (error: Error) => {
    console.error('Error in client card list:', error);
    // You could also report this error to a monitoring service
  };

  // Reset function for the error boundary
  const handleReset = () => {
    // Attempt to reload the data or reset the component state
    handleLoadMore();
  };

  return (
    <div className="flex flex-col h-full overflow-y-auto" onScroll={handleScroll}>
      <CardListErrorBoundary
        listName="clients"
        onError={handleCardListError}
        onReset={handleReset}
        resetKeys={[visibleClients.length]}
      >
        <div className="grid grid-cols-1 gap-4">
          {visibleClients.map((client, index) => (
            <Suspense
              key={client.id}
              fallback={<div className="h-32 bg-stone-800/30 rounded-lg animate-pulse" />}
            >
              <ClientCard
                client={client}
                onEditClient={clientHandlers[index].handleEditClient}
                onDeleteClient={clientHandlers[index].handleDeleteClient}
                onAddLicense={clientHandlers[index].handleAddLicense}
                onEditLicense={(license) => onEditLicense(license, client.id)}
                onDeleteLicense={onDeleteLicense}
                onFocusToggle={onFocusToggle}
                isFocused={clientHandlers[index].isFocused}
                isOtherCardFocused={clientHandlers[index].isOtherCardFocused}
              />
            </Suspense>
          ))}
        </div>

        {isLoadingMore && (
          <div className="py-2">
            <SkeletonDashboard cardCount={1} />
          </div>
        )}

        {total > visibleClients.length && !isLoadingMore && (
          <div className="text-center py-2">
            <button
              className="text-orange-500 hover:text-orange-400 text-sm"
              onClick={handleLoadMore}
            >
              Load more clients ({total - visibleClients.length} remaining)
            </button>
          </div>
        )}
      </CardListErrorBoundary>
    </div>
  )
})

export default ClientCardList
