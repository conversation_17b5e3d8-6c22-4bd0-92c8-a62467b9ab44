import { useState, useEffect, useRef } from 'react'

/**
 * A custom hook that returns a debounced value
 * 
 * @param value The value to debounce
 * @param delay The delay in milliseconds
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  
  useEffect(() => {
    // Set up the timeout
    const timer = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    
    // Clean up the timeout if the value changes or the component unmounts
    return () => {
      clearTimeout(timer)
    }
  }, [value, delay])
  
  return debouncedValue
}

/**
 * A custom hook that returns a debounced function
 * 
 * @param callback The function to debounce
 * @param delay The delay in milliseconds
 * @returns The debounced function
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): (...args: Parameters<T>) => void {
  const callbackRef = useRef<T>(callback)
  
  // Update the callback ref when the callback changes
  useEffect(() => {
    callbackRef.current = callback
  }, [callback])
  
  // Create a memoized debounced function
  const debouncedFunction = useRef<(...args: Parameters<T>) => void>()
  
  if (!debouncedFunction.current) {
    debouncedFunction.current = function(...args: Parameters<T>) {
      const timeoutId = debouncedFunction.current.timeoutId
      
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      debouncedFunction.current.timeoutId = setTimeout(() => {
        callbackRef.current(...args)
      }, delay) as unknown as number
    }
    
    debouncedFunction.current.timeoutId = undefined as unknown as number
  }
  
  // Update the delay if it changes
  useEffect(() => {
    const originalDebounced = debouncedFunction.current
    
    return () => {
      if (originalDebounced && originalDebounced.timeoutId) {
        clearTimeout(originalDebounced.timeoutId)
      }
    }
  }, [delay])
  
  return debouncedFunction.current
}
