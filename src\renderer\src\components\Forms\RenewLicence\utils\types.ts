import { RenewLicenceData } from '../../../../types/RenewLicenceData'

/**
 * Common props for all section components
 */
export interface SectionProps {
  formData: RenewLicenceData
  updateFormData: (updatedData: Partial<RenewLicenceData>) => void
  className?: string
}

/**
 * Props for sections that require address handling
 */
export interface AddressSectionProps extends SectionProps {
  handleAddressChange: (
    address: string,
    postalCode?: string,
    houseNumber?: string,
    isWorkAddress?: boolean,
    isResponsiblePerson?: boolean
  ) => void
  handleHouseNumberChange: (
    houseNumber: string, 
    isWorkAddress?: boolean, 
    isResponsiblePerson?: boolean
  ) => void
}

/**
 * Validation status type
 */
export type ValidationStatus = 'idle' | 'processing' | 'success' | 'error'

/**
 * Template status type
 */
export type TemplateStatus = 'loading' | 'ready' | 'error'

/**
 * Define a type for the submission data that includes all placeholders
 */
export type SubmissionData = Omit<RenewLicenceData, 's13' | 's15' | 's16' | 's20' | 's20a' | 's20b'> & {
  [key: string]: string | boolean | number | undefined | null | any[] | object
}
