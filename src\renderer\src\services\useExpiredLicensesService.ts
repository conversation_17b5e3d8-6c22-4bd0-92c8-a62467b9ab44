import { useState, useEffect, useCallback, useMemo } from 'react'
import { debounce } from 'lodash'
import { getOrInitSupabase } from '../lib/supabase'
import { GunLicense, RenewalFormData, NotificationStatus } from '../components/ExpiredLicenses/types'

export const useExpiredLicensesService = () => {
  // State variables
  const [expiredLicenses, setExpiredLicenses] = useState<GunLicense[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(5)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [notificationStatus, setNotificationStatus] = useState<NotificationStatus>({
    loading: false
  })
  const [showRenewalForm, setShowRenewalForm] = useState(false)
  const [selectedLicenseId, setSelectedLicenseId] = useState<string | null>(null)
  const [renewalFormData, setRenewalFormData] = useState<RenewalFormData>({
    issue_date: '',
    expiry_date: '',
    lic_number: '',
    section: ''
  })
  const [totalLicenses, setTotalLicenses] = useState<number>(0)
  const [, setCachedTotalLicenses] = useState<number>(0)
  const [notificationFilter, setNotificationFilter] = useState<'all' | 'active' | 'paused'>('all')
  const [focusedLicenseId, setFocusedLicenseId] = useState<string | null>(null)

  // Create a debounced fetch function
  const debouncedFetch = useMemo(
    () =>
      debounce(async (term: string) => {
        try {
          setLoading(true)
          const today = new Date()
          const futureDate = new Date(today)
          futureDate.setDate(today.getDate() + 130)

          const supabase = await getOrInitSupabase()

          // Store the current search term to compare later
          const currentSearchTerm = term

          // Create a base query
          let query = supabase
            .from('gun_licences')
            .select(
              `
              *,
              client:clients(*)
            `,
              { count: 'exact' }
            )
            .lte('expiry_date', futureDate.toISOString().split('T')[0])
            .order('expiry_date', { ascending: true })

          // Add search conditions if there's a search term
          if (term && term.trim()) {
            const trimmedTerm = term.trim().toLowerCase()

            // Try to find clients matching the search term
            try {
              // Start building a client search query
              let clientQuery = supabase.from('clients').select('id')

              const terms = trimmedTerm.split(' ').filter((t) => t.length > 0)

              if (terms.length > 1) {
                // Multi-term search (likely a name)
                const firstName = terms[0]
                const lastName = terms[terms.length - 1]

                // Try various name combinations
                clientQuery = clientQuery.or(
                  `first_name.ilike.%${firstName}%,last_name.ilike.%${lastName}%,` +
                    `first_name.ilike.%${lastName}%,last_name.ilike.%${firstName}%`
                )
              } else {
                // Single term
                clientQuery = clientQuery.or(
                  `first_name.ilike.%${trimmedTerm}%,last_name.ilike.%${trimmedTerm}%,` +
                    `email.ilike.%${trimmedTerm}%,phone.ilike.%${trimmedTerm}%`
                )
              }

              // Execute the client search
              const { data: clientIds, error: clientError } = await clientQuery

              if (clientError) throw clientError

              // If clients found, filter licenses by those clients
              if (clientIds && clientIds.length > 0) {
                query = query.in(
                  'client_id',
                  clientIds.map((c) => c.id)
                )
              } else {
                // No matching clients found, try searching license fields
                query = query.or(
                  `make.ilike.%${trimmedTerm}%,` +
                    `serial_number.ilike.%${trimmedTerm}%,` +
                    `type.ilike.%${trimmedTerm}%,` +
                    `caliber.ilike.%${trimmedTerm}%,` +
                    `lic_number.ilike.%${trimmedTerm}%`
                )
              }
            } catch (searchError) {
              console.error('Error searching for clients:', searchError)
              // In case of error, try a simple search on license fields
              query = query.or(
                `make.ilike.%${trimmedTerm}%,` +
                  `serial_number.ilike.%${trimmedTerm}%,` +
                  `type.ilike.%${trimmedTerm}%,` +
                  `caliber.ilike.%${trimmedTerm}%,` +
                  `lic_number.ilike.%${trimmedTerm}%`
              )
            }
          }

          // Execute the query
          const { data, error, count } = await query

          if (error) throw error

          // Only update state if this search is still relevant
          if (currentSearchTerm === searchQuery) {
            if (data) {
              setExpiredLicenses(data)
              setTotalLicenses(count || data.length)

              // Only update the cached total for full listings (no search term)
              if (!term) {
                setCachedTotalLicenses(count || data.length)
              }
            }
          }
        } catch (error) {
          console.error('Error fetching licenses:', error)
          setExpiredLicenses([])
          setTotalLicenses(0)
        } finally {
          setLoading(false)
        }
      }, 400),
    [searchQuery] // Add searchQuery as a dependency to compare against
  )

  // Keep the original fetchExpiredLicenses for compatibility with existing code
  const fetchExpiredLicenses = useCallback(async (): Promise<void> => {
    debouncedFetch('')
  }, [debouncedFetch])

  useEffect(() => {
    // Initial fetch when component mounts
    debouncedFetch('')

    // Clean up when unmounting
    return (): void => debouncedFetch.cancel()
  }, [debouncedFetch])

  // Effect to handle filter changes separately from search
  useEffect(() => {
    console.log(`Filter changed to: ${notificationFilter}, current search: "${searchQuery}"`)
    // When filter changes but we have a search query, maintain the search
    if (notificationFilter !== 'all' || searchQuery) {
      debouncedFetch(searchQuery)
    }
  }, [notificationFilter, debouncedFetch, searchQuery])

  // Fetch search hints (client names, emails, etc.)
  const fetchSearchHints = useCallback(async (): Promise<string[]> => {
    try {
      const supabase = await getOrInitSupabase()

      // Get client names for search hints
      const { data: clientsData } = await supabase
        .from('clients')
        .select('first_name, last_name, email, id_number')
        .limit(50)

      // Get license data for search hints
      const { data: licensesData } = await supabase
        .from('gun_licences')
        .select('make, serial_number, lic_number')
        .limit(20)

      if (clientsData) {
        const hints: string[] = []

        // Add full names
        clientsData.forEach((client) => {
          if (client.first_name && client.last_name) {
            hints.push(`${client.first_name} ${client.last_name}`)
          }
        })

        // Add emails
        clientsData.forEach((client) => {
          if (client.email) {
            hints.push(client.email)
          }
        })

        // Add ID numbers
        clientsData.forEach((client) => {
          if (client.id_number) {
            hints.push(client.id_number)
          }
        })

        // Add license data
        licensesData?.forEach((license) => {
          if (license.make) hints.push(license.make)
          if (license.serial_number) hints.push(license.serial_number)
          if (license.lic_number) hints.push(license.lic_number)
        })

        // Remove duplicates and return hints
        return [...new Set(hints)]
      }

      return []
    } catch (error) {
      console.error('Error fetching search hints:', error)
      return []
    }
  }, [])

  // Handle search functionality
  const handleSearch = (query: string): void => {
    const trimmedQuery = query.trim()

    console.log(`Search requested: "${trimmedQuery}"`)

    // Clear search and show all records
    if (!trimmedQuery) {
      console.log('Empty search term - clearing search and showing all licenses')
      setSearchQuery('')
      debouncedFetch('') // Explicitly fetch all licenses
      return
    }

    // Don't search if query is too short
    if (trimmedQuery.length < 2) {
      console.log('Search term too short - ignoring')
      return
    }

    // Don't search if query hasn't changed
    if (trimmedQuery === searchQuery) {
      console.log('Search term unchanged - ignoring duplicate search')
      return
    }

    // Set page back to 1 when searching
    setCurrentPage(1)

    // Update search query and explicitly trigger search
    console.log(`Setting search query to: "${trimmedQuery}" and executing search`)
    setSearchQuery(trimmedQuery)
    debouncedFetch(trimmedQuery)
  }

  // Handle refresh
  const handleRefresh = async (): Promise<void> => {
    setIsRefreshing(true)
    await debouncedFetch(searchQuery)
    setIsRefreshing(false)
  }

  // Copy to clipboard helper function with notification
  const copyToClipboard = (text: string): void => {
    navigator.clipboard.writeText(text)

    // Show notification
    setNotificationStatus({
      loading: false,
      success: 'Copied to clipboard!'
    })

    // Clear notification after 2 seconds
    setTimeout(() => {
      setNotificationStatus({ loading: false })
    }, 2000)
  }

  const markAsNotified = async (licenseId: string): Promise<void> => {
    try {
      setNotificationStatus({ loading: true })
      const license = expiredLicenses.find((l) => l.id === licenseId)
      if (!license) throw new Error('License not found')

      // Toggle the notifications state
      const newNotificationState = !license.toggle_notifications

      const supabase = await getOrInitSupabase()
      const { error: dbError } = await supabase
        .from('gun_licences')
        .update({ toggle_notifications: newNotificationState })
        .eq('id', licenseId)

      if (dbError) throw dbError

      setNotificationStatus({
        loading: false,
        success: newNotificationState ? 'Notifications enabled' : 'Notifications paused'
      })

      // Clear success message after 3 seconds
      setTimeout(() => {
        setNotificationStatus({ loading: false })
      }, 3000)

      // Refresh the licenses list
      await fetchExpiredLicenses()
    } catch (error) {
      console.error('Error updating notifications:', error)
      setNotificationStatus({
        loading: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to update notifications. Please try again.'
      })

      // Clear error message after 3 seconds
      setTimeout(() => {
        setNotificationStatus({ loading: false })
      }, 3000)
    }
  }

  const handleRenewalSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()
    if (!selectedLicenseId || !renewalFormData.expiry_date) return

    try {
      const supabase = await getOrInitSupabase()
      const { error } = await supabase
        .from('gun_licences')
        .update({
          expiry_date: renewalFormData.expiry_date,
          lic_number: renewalFormData.lic_number,
          last_notification_date: null,
          toggle_notifications: true
        })
        .eq('id', selectedLicenseId)

      if (error) throw error

      // Only update local state after successful Supabase update
      setExpiredLicenses((prevLicenses) =>
        prevLicenses.map((license) =>
          license.id === selectedLicenseId
            ? {
                ...license,
                expiry_date: renewalFormData.expiry_date,
                lic_number: renewalFormData.lic_number,
                last_notification_date: null,
                toggle_notifications: true
              }
            : license
        )
      )

      clearAndCloseForm()
    } catch (error) {
      console.error('Error renewing license:', error)
    }
  }

  // Add new function to handle clearing and closing
  const clearAndCloseForm = (): void => {
    setShowRenewalForm(false)
    setSelectedLicenseId(null)
    setRenewalFormData({
      issue_date: '',
      expiry_date: '',
      lic_number: '',
      section: ''
    })
  }

  // Add filtered licenses logic
  const filteredLicenses = useMemo(() => {
    return expiredLicenses.filter((license) => {
      if (notificationFilter === 'all') return true
      if (notificationFilter === 'active') return license.toggle_notifications
      if (notificationFilter === 'paused') return !license.toggle_notifications
      return true
    })
  }, [expiredLicenses, notificationFilter])

  // Use debounced value for filtering
  const paginatedLicenses = useMemo(() => {
    const lastIndex = currentPage * itemsPerPage
    const firstIndex = lastIndex - itemsPerPage

    return {
      licenses: filteredLicenses.slice(firstIndex, lastIndex),
      totalPages: Math.ceil(filteredLicenses.length / itemsPerPage),
      totalLicenses: filteredLicenses.length
    }
  }, [filteredLicenses, currentPage, itemsPerPage])

  // Custom search tips for licenses - we need to return a React element from a hook
  const licenseSearchTips = {
    title: 'Search tips:',
    items: [
      'Enter a client\'s name (e.g., "John Smith")',
      'Search by client ID number',
      'Search by license number',
      'Search by make or model',
      'Search by serial number',
      'Use filters to narrow down results'
    ]
  }

  // Get focused license
  const focusedLicense = useMemo(() => {
    if (!focusedLicenseId) return null
    return expiredLicenses.find((license) => license.id === focusedLicenseId) || null
  }, [expiredLicenses, focusedLicenseId])

  // Handle license focus toggle
  const handleLicenseFocusToggle = (licenseId: string | null) => {
    setFocusedLicenseId(licenseId)
  }

  // Apply body overflow class when focus mode is active
  useEffect(() => {
    if (focusedLicenseId) {
      document.body.classList.add('focus-mode-active')
    } else {
      document.body.classList.remove('focus-mode-active')
    }

    return () => {
      document.body.classList.remove('focus-mode-active')
    }
  }, [focusedLicenseId])

  return {
    // State
    expiredLicenses,
    loading,
    isRefreshing,
    searchQuery,
    currentPage,
    itemsPerPage,
    notificationStatus,
    showRenewalForm,
    selectedLicenseId,
    renewalFormData,
    totalLicenses,
    notificationFilter,
    filteredLicenses,
    paginatedLicenses,
    focusedLicenseId,
    focusedLicense,

    // Actions
    setCurrentPage,
    setNotificationFilter,
    handleSearch,
    handleRefresh,
    copyToClipboard,
    markAsNotified,
    handleRenewalSubmit,
    clearAndCloseForm,
    setShowRenewalForm,
    setSelectedLicenseId,
    setRenewalFormData,
    handleLicenseFocusToggle,

    // Helpers
    fetchSearchHints,
    licenseSearchTips
  }
}
