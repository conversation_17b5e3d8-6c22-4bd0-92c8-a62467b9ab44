import React, { memo } from 'react'
import { SkeletonLicenses } from '../SkeletonLoading'
import { LicenseCard } from './LicenseCard'
import { GunLicense, NotificationStatus } from './types'
import { useVirtualization } from '../../hooks/useVirtualization'

interface LicenseCardListProps {
  licenses: GunLicense[]
  onRenew: (licenseId: string) => void
  onToggleNotifications: (licenseId: string) => void
  notificationStatus: NotificationStatus
  copyToClipboard: (text: string) => void
  onFocusToggle: (licenseId: string | null) => void
  focusedLicenseId: string | null
}

// Number of items to load initially and in each batch
const INITIAL_BATCH_SIZE = 6
const BATCH_INCREMENT = 6

const LicenseCardList: React.FC<LicenseCardListProps> = memo(({
  licenses,
  onRenew,
  onToggleNotifications,
  notificationStatus,
  copyToClipboard,
  onFocusToggle,
  focusedLicenseId
}) => {
  // Use the virtualization hook
  const {
    items: visibleLicenses,
    total,
    isLoading: isLoadingMore,
    handleScroll,
    handleLoadMore
  } = useVirtualization<GunLicense>(licenses, INITIAL_BATCH_SIZE, BATCH_INCREMENT)

  return (
    <div className="overflow-y-auto h-full" onScroll={handleScroll}>
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {visibleLicenses.map((license) => (
          <LicenseCard
            key={license.id}
            license={license}
            onRenew={(licenseId) => onRenew(licenseId)}
            onToggleNotifications={onToggleNotifications}
            notificationStatus={notificationStatus}
            copyToClipboard={copyToClipboard}
            isFocused={focusedLicenseId === license.id}
            isOtherCardFocused={focusedLicenseId !== null && focusedLicenseId !== license.id}
            onFocusToggle={onFocusToggle}
          />
        ))}
      </div>

      {isLoadingMore && (
        <div className="py-2">
          <SkeletonLicenses count={3} />
        </div>
      )}

      {total > visibleLicenses.length && !isLoadingMore && (
        <div className="text-center py-4">
          <button
            className="text-orange-500 hover:text-orange-400 text-sm"
            onClick={handleLoadMore}
          >
            Load more licenses ({total - visibleLicenses.length} remaining)
          </button>
        </div>
      )}
    </div>
  )
})

export default LicenseCardList
