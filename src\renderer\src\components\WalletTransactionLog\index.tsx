import { useState, useEffect } from 'react'
import { WalletTransaction } from '../../types'
import { DashboardIcons } from '../icons/DashboardIcons'
import { getSupabase } from '../../lib/supabase'
import { useTableVirtualization } from '../../hooks/useTableVirtualization'

interface WalletTransactionLogProps {
  walletId: string
  onClose: () => void
}

export const WalletTransactionLog = ({
  walletId,
  onClose
}: WalletTransactionLogProps): JSX.Element => {
  const [transactions, setTransactions] = useState<WalletTransaction[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get wallet transactions
  const getWalletTransactions = async (walletId: string): Promise<WalletTransaction[]> => {
    setLoading(true)
    setError(null)

    try {
      const supabase = getSupabase()
      const { data, error } = await supabase
        .from('credit_transactions')
        .select('*')
        .eq('wallet_id', walletId)
        .order('transaction_date', { ascending: false })
        .limit(100) // Increased limit for virtualization

      if (error) {
        setError('Failed to fetch transaction history')
        return []
      }

      return data as WalletTransaction[]
    } catch (err) {
      setError('An unexpected error occurred')
      return []
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const fetchTransactions = async () => {
      if (walletId) {
        const data = await getWalletTransactions(walletId)
        setTransactions(data)
      }
    }

    fetchTransactions()
  }, [walletId])

  // Use the table virtualization hook
  const virtualizedTransactions = useTableVirtualization(transactions, 15, 10)

  const formatTransactionType = (type: string): string => {
    switch (type) {
      case 'payment':
        return 'Payment'
      case 'charge':
        return 'Charge'
      case 'adjustment':
        return 'Adjustment'
      case 'transfer':
        return 'Transfer'
      default:
        return type.charAt(0).toUpperCase() + type.slice(1)
    }
  }

  const formatReferenceType = (type: string): string => {
    switch (type) {
      case 'firearm':
        return 'Firearm Storage'
      case 'manual':
        return 'Manual Entry'
      case 'system':
        return 'System'
      default:
        return type.charAt(0).toUpperCase() + type.slice(1)
    }
  }

  return (
    <div className="bg-stone-900 rounded-lg p-6 shadow-lg max-w-5xl w-full mx-auto max-h-[90vh] overflow-hidden flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-white">Wallet Transaction History</h2>
        <button
          onClick={onClose}
          className="text-stone-400 hover:text-white transition-colors"
          aria-label="Close"
        >
          <DashboardIcons.Close className="w-5 h-5" />
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-md text-white">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      ) : transactions.length === 0 ? (
        <div className="text-center py-8 text-stone-400">
          No transaction history available
        </div>
      ) : (
        <div className="overflow-auto flex-1 max-h-[calc(100vh-250px)]" onScroll={(e) => virtualizedTransactions.handleScroll(e)}>
          <table className="min-w-full divide-y divide-stone-700">
            <thead className="bg-stone-800 sticky top-0 z-10">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                >
                  Date
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                >
                  Reference
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                >
                  Amount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-stone-300 uppercase tracking-wider"
                >
                  Description
                </th>
              </tr>
            </thead>
            <tbody className="bg-stone-800/30 divide-y divide-stone-700">
              {virtualizedTransactions.items.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-stone-800/50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-stone-300">
                    {new Date(transaction.transaction_date).toLocaleString('en-ZA', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-stone-300">
                    {formatTransactionType(transaction.transaction_type)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-stone-300">
                    {formatReferenceType(transaction.reference_type)}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                    transaction.amount >= 0 ? 'text-green-500' : 'text-red-500'
                  }`}>
                    R {Math.abs(transaction.amount).toFixed(2)}
                    {transaction.amount >= 0 ? ' (+)' : ' (-)'}
                  </td>
                  <td className="px-6 py-4 text-sm text-stone-300">
                    {transaction.description || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Loading indicator at the bottom of the table */}
          {virtualizedTransactions.isLoading && (
            <div className="py-2 text-center">
              <div className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500"></div>
              <span className="ml-2 text-sm text-stone-400">Loading more transactions...</span>
            </div>
          )}

          {/* Load more button */}
          {virtualizedTransactions.total > virtualizedTransactions.items.length && !virtualizedTransactions.isLoading && (
            <div className="text-center py-2">
              <button
                className="text-orange-500 hover:text-orange-400 text-sm"
                onClick={virtualizedTransactions.handleLoadMore}
              >
                Load more transactions ({virtualizedTransactions.total - virtualizedTransactions.items.length} remaining)
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default WalletTransactionLog
