import React from 'react'
import { useWindowSize } from '../../contexts/WindowSizeContext'
import New<PERSON>om<PERSON> from '../Forms/NewComp'
import FurtherComp from '../Forms/FurtherComp'
import RenewComp from '../Forms/RenewComp'
import NewLicence from '../Forms/NewLicence'
import RenewLicence from '../Forms/RenewLicence'
import AnnexureA from '../Forms/AnnexureA'
import E350Information from '../Forms/E350Information'
import SAPSInspectionReport from '../Forms/SAPSInspectionReport'
import { FormData, FurtherCompetencyData } from '../../types/FormData'

interface FormModalProps {
  formType: 'select' | 'competency' | 'licence' | 'competency-select' | 'licence-select' | 'miscellaneous-select' | 'miscellaneous' | null
  competencyType: 'new' | 'further' | 'renew' | null
  licenceType: 'new' | 'renew' | null
  miscellaneousType: 'annexure_a_381a' | 'e350_information' | 'saps_inspection_report' | null
  submittedFormType: string | null
  setFormType: React.Dispatch<
    React.SetStateAction<'select' | 'competency' | 'licence' | 'competency-select' | 'licence-select' | 'miscellaneous-select' | 'miscellaneous' | null>
  >
  setCompetencyType: React.Dispatch<React.SetStateAction<'new' | 'further' | 'renew' | null>>
  setLicenceType: React.Dispatch<React.SetStateAction<'new' | 'renew' | null>>
  setMiscellaneousType: React.Dispatch<React.SetStateAction<'annexure_a_381a' | 'e350_information' | 'saps_inspection_report' | null>>
  handleFormSubmit: (data: FormData | FurtherCompetencyData) => void
}

const FormModal: React.FC<FormModalProps> = ({
  formType,
  competencyType,
  licenceType,
  miscellaneousType,
  submittedFormType,
  setFormType,
  setCompetencyType,
  setLicenceType,
  setMiscellaneousType,
  handleFormSubmit
}) => {
  const { calculateResponsiveHeight } = useWindowSize()

  // Competency form modal
  if (formType === 'competency' && !submittedFormType) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 app-form-modal">
        <div
          className="bg-stone-800/90 backdrop-blur-sm rounded-2xl p-4 border border-stone-700/30 shadow-2xl w-full max-w-screen-90 relative overflow-hidden"
          style={{ height: `${calculateResponsiveHeight(85)}px` }}
        >
          <button
            onClick={() => {
              setFormType('competency-select')
              setCompetencyType(null)
            }}
            className="absolute top-2 right-2 text-stone-300 hover:text-white transition-colors z-50 bg-stone-800/90 p-1 rounded-full border border-stone-700/50 hover:bg-stone-700"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
          <div className="h-full">
            {competencyType === 'new' ? (
              <NewComp formType="competency" onSubmit={handleFormSubmit} />
            ) : competencyType === 'further' ? (
              <FurtherComp onSubmit={handleFormSubmit} />
            ) : competencyType === 'renew' ? (
              <RenewComp formType="competency" onSubmit={handleFormSubmit} />
            ) : null}
          </div>
        </div>
      </div>
    )
  }

  // Licence Form Modal
  if (formType === 'licence' && !submittedFormType) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 app-form-modal">
        <div
          className="bg-stone-800/90 backdrop-blur-sm rounded-2xl p-4 border border-stone-700/30 shadow-2xl w-full max-w-screen-90 relative overflow-hidden"
          style={{ height: `${calculateResponsiveHeight(85)}px` }}
        >
          <button
            onClick={() => {
              setFormType('licence-select')
              setLicenceType(null)
            }}
            className="absolute top-2 right-2 text-stone-300 hover:text-white transition-colors z-50 bg-stone-800/90 p-1 rounded-full border border-stone-700/50 hover:bg-stone-700"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
          <div className="h-full">
            {licenceType === 'new' ? (
              <NewLicence onSubmit={handleFormSubmit} />
            ) : licenceType === 'renew' ? (
              <RenewLicence onSubmit={handleFormSubmit} />
            ) : null}
          </div>
        </div>
      </div>
    )
  }

  // Miscellaneous Form Modal
  if (formType === 'miscellaneous' && !submittedFormType) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 app-form-modal">
        <div
          className="bg-stone-800/90 backdrop-blur-sm rounded-2xl p-4 border border-stone-700/30 shadow-2xl w-full max-w-screen-90 relative overflow-hidden"
          style={{ height: `${calculateResponsiveHeight(85)}px` }}
        >
          <button
            onClick={() => {
              setFormType('miscellaneous-select')
              setMiscellaneousType(null)
            }}
            className="absolute top-2 right-2 text-stone-300 hover:text-white transition-colors z-50 bg-stone-800/90 p-1 rounded-full border border-stone-700/50 hover:bg-stone-700"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
          <div className="h-full">
            {miscellaneousType === 'annexure_a_381a' ? (
              <AnnexureA onSubmit={handleFormSubmit} />
            ) : miscellaneousType === 'e350_information' ? (
              <E350Information onSubmit={handleFormSubmit} />
            ) : miscellaneousType === 'saps_inspection_report' ? (
              <SAPSInspectionReport onSubmit={handleFormSubmit} />
            ) : null}
          </div>
        </div>
      </div>
    )
  }

  return null
}

export default FormModal
