import { useState, useEffect, useCallback } from 'react'
import { getSupabase } from '../lib/supabase'
import { debounce } from 'lodash'
import { Firearm } from '../types/firearm'
import { v4 as uuidv4 } from 'uuid'

// Define form state type
interface FormState {
  isOpen: boolean
  type: 'firearm' | 'assignment' | null
  selectedFirearm: Firearm | null
}

// Define delete dialog state type
interface DeleteDialogState {
  isOpen: boolean
  firearmId: string | null
  hasAssignment?: boolean
  assignmentInfo?: any
}

// Define toast notification type
interface Toast {
  message: string
  type: 'success' | 'error' | 'info'
}

// Define filter options
type FilterOption = 'all' | 'in-storage' | 'assigned' | 'signed-out' | 'owner' | 'private' | 'dealer'

// Helper function to check if a date is valid
const isValidDate = (date: Date): boolean => {
  return !isNaN(date.getTime())
}

// Helper function to safely convert a date to ISO string
const safeToISOString = (date: Date): string | null => {
  try {
    if (isValidDate(date)) {
      return date.toISOString()
    }
    return null
  } catch (error) {
    console.error('Invalid date conversion:', error)
    return null
  }
}

// Helper function to calculate charges and payment status based on storage type, assignment date, and wallet balance
const calculateChargesAndPaymentStatus = async (firearm: Firearm, assignment: any) => {
  try {
    if (!assignment || !firearm.is_assigned) {
      firearm.payment_status = undefined
      firearm.next_payment_date = undefined
      firearm.credit_balance = undefined
      firearm.wallet_balance = undefined
      return
    }

    const now = new Date()
    const assignedDate = new Date(assignment.assigned_date)
    const DAILY_RATE = 7.5 // R7.50 per day

    // Validate assigned date
    if (!isValidDate(assignedDate)) {
      console.error('Invalid assigned date:', assignment.assigned_date)
      firearm.payment_status = undefined
      firearm.next_payment_date = undefined
      return
    }

    // For Dealer type, check if within free storage period
    if (firearm.storage_type === 'Dealer' && assignment.free_storage_until) {
      const freeUntil = new Date(assignment.free_storage_until)

      // Validate free storage until date
      if (isValidDate(freeUntil)) {
        if (now < freeUntil) {
          // Still in free period
          firearm.payment_status = 'free'
          firearm.next_payment_date = assignment.free_storage_until
          return
        }
      }
    }

    // Determine the start date for charging
    // If last_charge_date exists, use that, otherwise use assigned_date
    const lastChargeDate = assignment.last_charge_date
      ? new Date(assignment.last_charge_date)
      : assignedDate

    // Calculate days since last charge
    const daysSinceLastCharge = Math.floor(
      (now.getTime() - lastChargeDate.getTime()) / (1000 * 60 * 60 * 24)
    )

    // Initialize credit balance from wallet if available
    let creditBalance = 0
    if (firearm.wallet_balance !== undefined) {
      creditBalance = firearm.wallet_balance
    }

    // If at least one day has passed, calculate charges
    if (daysSinceLastCharge > 0) {
      const totalCharge = daysSinceLastCharge * DAILY_RATE

      // Update credit balance for backward compatibility
      creditBalance = creditBalance - totalCharge

      // Update the assignment with new last charge date
      const supabase = getSupabase()
      const { error: updateError } = await supabase
        .from('firearm_assignments')
        .update({
          last_charge_date: now.toISOString(),
          updated_at: now.toISOString()
        })
        .eq('id', assignment.id)

      if (updateError) {
        console.error('Error updating assignment:', updateError)
      }

      // If we have a wallet ID, create a transaction and update the wallet
      if (firearm.wallet_id && firearm.client_id) {
        try {
          // Create a transaction record
          const transactionId = uuidv4()
          const { error: transactionError } = await supabase
            .from('credit_transactions')
            .insert({
              id: transactionId,
              wallet_id: firearm.wallet_id,
              amount: -totalCharge, // Negative amount for a charge
              transaction_type: 'charge',
              reference_type: 'firearm',
              reference_id: firearm.id,
              description: `Automatic daily charge for ${daysSinceLastCharge} days at R${DAILY_RATE} per day`,
              transaction_date: now.toISOString(),
              created_at: now.toISOString(),
              updated_at: now.toISOString()
            })

          if (transactionError) {
            console.error('Error creating transaction:', transactionError)
          } else {
            // Create a charge record
            const { error: chargeError } = await supabase
              .from('firearm_storage_charges')
              .insert({
                id: uuidv4(),
                firearm_id: firearm.id,
                client_id: firearm.client_id,
                wallet_id: firearm.wallet_id,
                transaction_id: transactionId,
                charge_date: now.toISOString(),
                days_charged: daysSinceLastCharge,
                daily_rate: DAILY_RATE,
                total_amount: totalCharge,
                created_at: now.toISOString(),
                updated_at: now.toISOString()
              })

            if (chargeError) {
              console.error('Error creating charge record:', chargeError)
            }
          }

          // Fetch the updated wallet balance
          const { data: updatedWallet, error: walletError } = await supabase
            .from('client_credit_wallets')
            .select('*')
            .eq('id', firearm.wallet_id)
            .single()

          if (walletError) {
            console.error('Error fetching updated wallet:', walletError)
          } else if (updatedWallet) {
            firearm.wallet_balance = updatedWallet.balance
          }
        } catch (err) {
          console.error('Error processing wallet transaction:', err)
        }
      }
    }

    // Update the firearm's credit balance for backward compatibility
    firearm.credit_balance = creditBalance

    // Set payment status based on wallet balance if available, otherwise use credit balance
    const balanceToCheck = firearm.wallet_balance !== undefined ? firearm.wallet_balance : creditBalance

    if (balanceToCheck >= 0) {
      firearm.payment_status = 'paid'
    } else {
      firearm.payment_status = 'due'
    }

    // Calculate next payment date (28 days from last charge)
    const nextPaymentDate = new Date(lastChargeDate)
    nextPaymentDate.setDate(lastChargeDate.getDate() + 28)

    // Validate next payment date
    if (!isValidDate(nextPaymentDate)) {
      console.error('Invalid next payment date calculated')
      firearm.next_payment_date = undefined
    } else {
      // Set next payment date
      const isoString = safeToISOString(nextPaymentDate)
      if (isoString) {
        firearm.next_payment_date = isoString
      } else {
        firearm.next_payment_date = undefined
      }
    }
  } catch (error) {
    console.error('Error in calculateChargesAndPaymentStatus:', error)
    firearm.payment_status = undefined
    firearm.next_payment_date = undefined
    firearm.credit_balance = undefined
    firearm.wallet_balance = undefined
  }
}

export const useFirearmService = () => {
  // State
  const [loading, setLoading] = useState(true)
  const [, setFirearms] = useState<Firearm[]>([])
  const [visibleFirearms, setVisibleFirearms] = useState<Firearm[]>([])
  const [totalFirearms, setTotalFirearms] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const [page, setPage] = useState(1)
  const [perPage] = useState(10)
  const [activeFilter, setActiveFilter] = useState<FilterOption>('all')
  const [formState, setFormState] = useState<FormState>({
    isOpen: false,
    type: null,
    selectedFirearm: null
  })
  const [deleteDialog, setDeleteDialog] = useState<DeleteDialogState>({
    isOpen: false,
    firearmId: null
  })
  const [toast, setToast] = useState<Toast | null>(null)
  const [focusedFirearmId, setFocusedFirearmId] = useState<string | null>(null)

  // Fetch firearms from the database
  const fetchFirearms = useCallback(async () => {
    setLoading(true)
    try {
      const supabase = await getSupabase()

      // Build the query based on the active filter
      let query = supabase.from('firearms').select('*')

      if (activeFilter === 'in-storage') {
        query = query.is('date_signed_out', null)
      } else if (activeFilter === 'signed-out') {
        query = query.not('date_signed_out', 'is', null)
      } else if (activeFilter === 'assigned') {
        // For assigned firearms, we need to join with the firearm_assignments table
        // This is a simplified approach - in a real implementation, you'd need to handle this differently
        const { data: assignedFirearmIds } = await supabase
          .from('firearm_assignments')
          .select('firearm_id')
          .is('return_date', null)

        if (assignedFirearmIds && assignedFirearmIds.length > 0) {
          const ids = assignedFirearmIds.map(item => item.firearm_id)
          query = query.in('id', ids)
        } else {
          // If no firearms are assigned, return an empty array
          setFirearms([])
          setVisibleFirearms([])
          setTotalFirearms(0)
          setLoading(false)
          return
        }
      } else if (activeFilter === 'owner') {
        // Filter by Owner storage type
        query = query.eq('storage_type', 'Owner')
      } else if (activeFilter === 'private') {
        // Filter by Private storage type
        query = query.eq('storage_type', 'Private')
      } else if (activeFilter === 'dealer') {
        // Filter by Dealer storage type
        query = query.eq('storage_type', 'Dealer')
      }

      // Apply search query if provided
      if (searchQuery) {
        if (searchQuery.startsWith('SN:')) {
          const stockNumber = searchQuery.substring(3).trim()
          query = query.ilike('stock_number', `%${stockNumber}%`)
        } else if (searchQuery.startsWith('SER:')) {
          const serial = searchQuery.substring(4).trim()
          query = query.ilike('serial', `%${serial}%`)
        } else if (searchQuery.startsWith('MAKE:')) {
          const make = searchQuery.substring(5).trim()
          query = query.ilike('make', `%${make}%`)
        } else if (searchQuery.startsWith('MODEL:')) {
          const model = searchQuery.substring(6).trim()
          query = query.ilike('model', `%${model}%`)
        } else if (searchQuery.startsWith('OWNER:')) {
          const owner = searchQuery.substring(6).trim()
          query = query.ilike('full_name', `%${owner}%`)
        } else {
          // General search across multiple columns
          query = query.or(
            `stock_number.ilike.%${searchQuery}%,serial.ilike.%${searchQuery}%,make.ilike.%${searchQuery}%,model.ilike.%${searchQuery}%,full_name.ilike.%${searchQuery}%`
          )
        }
      }

      // Get the total count - first clone the query
      const countQuery = supabase
        .from('firearms')
        .select('id', { count: 'exact' })

      // Apply the same filters as the main query
      if (activeFilter === 'in-storage') {
        countQuery.is('date_signed_out', null)
      } else if (activeFilter === 'signed-out') {
        countQuery.not('date_signed_out', 'is', null)
      } else if (activeFilter === 'assigned') {
        // For assigned filter, we need to handle this differently
        // We'll use the same logic as in the main query
      } else if (activeFilter === 'owner') {
        // Filter by Owner storage type
        countQuery.eq('storage_type', 'Owner')
      } else if (activeFilter === 'private') {
        // Filter by Private storage type
        countQuery.eq('storage_type', 'Private')
      } else if (activeFilter === 'dealer') {
        // Filter by Dealer storage type
        countQuery.eq('storage_type', 'Dealer')
      }

      // Apply the same search filters
      if (searchQuery) {
        if (searchQuery.startsWith('SN:')) {
          const stockNumber = searchQuery.substring(3).trim()
          countQuery.ilike('stock_number', `%${stockNumber}%`)
        } else if (searchQuery.startsWith('SER:')) {
          const serial = searchQuery.substring(4).trim()
          countQuery.ilike('serial', `%${serial}%`)
        } else if (searchQuery.startsWith('MAKE:')) {
          const make = searchQuery.substring(5).trim()
          countQuery.ilike('make', `%${make}%`)
        } else if (searchQuery.startsWith('MODEL:')) {
          const model = searchQuery.substring(6).trim()
          countQuery.ilike('model', `%${model}%`)
        } else if (searchQuery.startsWith('OWNER:')) {
          const owner = searchQuery.substring(6).trim()
          countQuery.ilike('full_name', `%${owner}%`)
        } else {
          // General search across multiple columns
          countQuery.or(
            `stock_number.ilike.%${searchQuery}%,serial.ilike.%${searchQuery}%,make.ilike.%${searchQuery}%,model.ilike.%${searchQuery}%,full_name.ilike.%${searchQuery}%`
          )
        }
      }

      const { count, error: countError } = await countQuery

      if (countError) {
        console.error('Error getting count:', countError)
        setTotalFirearms(0)
      } else {
        setTotalFirearms(count || 0)
      }

      // Apply pagination to the main query
      const { data, error } = await query
        .select('*')
        .order('created_at', { ascending: false })
        .range((page - 1) * perPage, page * perPage - 1)

      // If we have data, check for assignments
      if (data && data.length > 0) {
        // Get all firearm IDs
        const firearmIds = data.map(firearm => firearm.id)

        // Fetch active assignments for these firearms with detailed client information
        const { data: assignments, error: assignmentError } = await supabase
          .from('firearm_assignments')
          .select(`
            id,
            firearm_id,
            client_id,
            notes,
            assigned_date,
            free_storage_until,
            last_charge_date,
            clients:client_id(id, first_name, last_name, email, phone, id_number)
          `)
          .in('firearm_id', firearmIds)
          .is('return_date', null)

        // Fetch wallet information for all clients involved
        let clientIds: string[] = []
        if (assignments && assignments.length > 0) {
          clientIds = assignments.map(assignment => assignment.client_id).filter(Boolean)
        }

        // Add owner client IDs if available
        const ownerClientIds = data
          .filter(firearm => firearm.storage_type === 'Owner' && firearm.client_id)
          .map(firearm => firearm.client_id as string)

        clientIds = [...new Set([...clientIds, ...ownerClientIds])]

        // Fetch wallets for these clients
        const wallets: Record<string, any> = {}
        if (clientIds.length > 0) {
          const { data: walletData } = await supabase
            .from('client_credit_wallets')
            .select('*')
            .in('client_id', clientIds)

          if (walletData) {
            walletData.forEach(wallet => {
              wallets[wallet.client_id] = wallet
            })
          }
        }

        if (assignmentError) {
          console.error('Error fetching assignments:', assignmentError)
        } else if (assignments && assignments.length > 0) {
          // Process each firearm with its assignment
          for (const firearm of data) {
            const assignment = assignments.find(a => a.firearm_id === firearm.id)

            if (assignment && assignment.clients) {
              // The clients field is an object, not an array
              const client = assignment.clients as any

              // Update firearm with assignment information
              firearm.is_assigned = true
              firearm.assigned_to = `${client.first_name} ${client.last_name}`
              firearm.client_id = client.id
              firearm.assignment_id = assignment.id // Store assignment ID for payment form
              firearm.assignment_notes = assignment.notes
              firearm.free_storage_until = assignment.free_storage_until

              // Add wallet information if available
              if (wallets[client.id]) {
                firearm.wallet_id = wallets[client.id].id
                firearm.wallet_balance = wallets[client.id].balance
                // Set credit_balance to wallet_balance for backward compatibility
                firearm.credit_balance = wallets[client.id].balance
              } else {
                firearm.wallet_balance = 0
                firearm.credit_balance = 0
              }

              // Add detailed client information
              firearm.assigned_client_details = {
                id: client.id,
                first_name: client.first_name,
                last_name: client.last_name,
                email: client.email,
                phone: client.phone,
                id_number: client.id_number
              }

              // Calculate charges and payment status
              await calculateChargesAndPaymentStatus(firearm, assignment)
            } else if (firearm.is_assigned && firearm.assigned_to) {
              // This is a case where the firearm is marked as assigned but we couldn't find the assignment
              // This can happen with older data or if the assignment was created differently
              console.log(`Firearm ${firearm.id} is marked as assigned but no assignment record was found`)

              // We'll still keep it marked as assigned but without the assignment_id
              // The payment form will handle finding the assignment when needed
              firearm.credit_balance = 0 // Default to 0 credit balance
              firearm.payment_status = 'due' // Default to payment due

              // If we have assigned_client_details but no assignment_id, we can still use it
              if (firearm.assigned_client_details) {
                console.log(`Using existing assigned_client_details for firearm ${firearm.id}`)
              }
            } else if (firearm.storage_type === 'Owner') {
              // For Owner type firearms, we want to show a credit balance even if not assigned
              // This allows the Pay button to work for Owner type firearms
              firearm.is_assigned = false
              firearm.assigned_to = undefined

              // Try to find the client ID from the full_name
              const nameParts = firearm.full_name.split(' ')
              const lastName = nameParts.length > 1 ? nameParts[nameParts.length - 1] : nameParts[0]

              // Look for a matching client
              const { data: matchingClients } = await supabase
                .from('clients')
                .select('id, first_name, last_name')
                .ilike('last_name', `%${lastName}%`)
                .limit(1)

              if (matchingClients && matchingClients.length > 0) {
                firearm.client_id = matchingClients[0].id

                // Check if this client has a wallet
                if (wallets[matchingClients[0].id]) {
                  firearm.wallet_id = wallets[matchingClients[0].id].id
                  firearm.wallet_balance = wallets[matchingClients[0].id].balance
                } else {
                  firearm.wallet_balance = 0
                }
              } else {
                firearm.client_id = undefined // Will be determined when payment is made
                firearm.wallet_balance = 0
              }

              firearm.assignment_id = undefined
              firearm.assignment_notes = undefined
              firearm.assigned_client_details = undefined
              firearm.free_storage_until = undefined
              firearm.credit_balance = 0 // Default to 0 credit balance for backward compatibility
              firearm.payment_status = 'paid' // Default to paid status
              firearm.next_payment_date = undefined
            } else {
              // Reset fields if not assigned
              firearm.is_assigned = false
              firearm.assigned_to = undefined
              firearm.client_id = undefined
              firearm.assignment_id = undefined
              firearm.assignment_notes = undefined
              firearm.assigned_client_details = undefined
              firearm.free_storage_until = undefined
              firearm.credit_balance = undefined
              firearm.wallet_id = undefined
              firearm.wallet_balance = undefined
              firearm.payment_status = undefined
              firearm.next_payment_date = undefined
            }
          }
        }
      }

      if (error) {
        console.error('Error fetching firearms:', error)
        setToast({
          message: 'Failed to load firearms. Please try again.',
          type: 'error'
        })
        return
      }

      setFirearms(data || [])
      setVisibleFirearms(data || [])
    } catch (error) {
      console.error('Error in fetchFirearms:', error)
      setToast({
        message: 'An unexpected error occurred. Please try again.',
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }, [searchQuery, page, perPage, activeFilter])

  // Debounced search handler
  const debouncedFetch = useCallback(
    debounce((query: string) => {
      setSearchQuery(query)
      setPage(1) // Reset to first page on new search
    }, 300),
    []
  )

  // Handle search input
  const handleSearch = useCallback(
    (query: string) => {
      debouncedFetch(query)
    },
    [debouncedFetch]
  )

  // Handle firearm deletion
  const handleDeleteFirearm = useCallback(async (
    firearmId: string,
    forceDelete = false,
    assignmentInfo: { id: string } | null = null
  ) => {
    try {
      const supabase = getSupabase()

      // If forceDelete is true, we're coming from the confirmation dialog and should delete both the assignment and firearm
      if (forceDelete && assignmentInfo) {
        // Delete the assignment first
        const { error: deleteAssignmentError } = await supabase
          .from('firearm_assignments')
          .delete()
          .eq('id', assignmentInfo.id)

        if (deleteAssignmentError) {
          console.error('Error deleting firearm assignment:', deleteAssignmentError)
          setToast({
            message: 'Failed to delete firearm assignment. Please try again.',
            type: 'error'
          })
          return
        }
      } else if (!forceDelete) {
        // First, check if the firearm is assigned to any client (active assignments)
        const { data: activeAssignments, error: assignmentError } = await supabase
          .from('firearm_assignments')
          .select('*, clients:client_id(first_name, last_name)')
          .eq('firearm_id', firearmId)
          .is('return_date', null)

        if (assignmentError) {
          console.error('Error checking firearm assignments:', assignmentError)
          setToast({
            message: 'Failed to check firearm assignments. Please try again.',
            type: 'error'
          })
          return
        }

        if (activeAssignments && activeAssignments.length > 0) {
          // Show the confirmation dialog with assignment info
          setDeleteDialog({
            isOpen: true,
            firearmId,
            hasAssignment: true,
            assignmentInfo: activeAssignments[0]
          })
          return
        }
      }

      // Check if there are any historical assignments (completed assignments)
      const { data: historicalAssignments } = await supabase
        .from('firearm_assignments')
        .select('*')
        .eq('firearm_id', firearmId)
        .not('return_date', 'is', null)

      // If there are historical assignments, delete them first
      if (historicalAssignments && historicalAssignments.length > 0) {
        const { error: deleteAssignmentsError } = await supabase
          .from('firearm_assignments')
          .delete()
          .eq('firearm_id', firearmId)

        if (deleteAssignmentsError) {
          console.error('Error deleting firearm assignments:', deleteAssignmentsError)
          setToast({
            message: 'Failed to delete firearm assignments. Please try again.',
            type: 'error'
          })
          return
        }
      }

      // Now delete the firearm
      const { error } = await supabase
        .from('firearms')
        .delete()
        .eq('id', firearmId)

      if (error) {
        console.error('Error deleting firearm:', error)
        setToast({
          message: 'Failed to delete firearm. Please try again.',
          type: 'error'
        })
        return
      }

      // Refresh the firearms list
      fetchFirearms()

      setToast({
        message: 'Firearm deleted successfully.',
        type: 'success'
      })
    } catch (error) {
      console.error('Error in handleDeleteFirearm:', error)
      setToast({
        message: 'An unexpected error occurred. Please try again.',
        type: 'error'
      })
    }
  }, [fetchFirearms, setDeleteDialog])

  // Handle edit firearm
  const onEditFirearm = useCallback((firearm: Firearm) => {
    setFormState({
      isOpen: true,
      type: 'firearm',
      selectedFirearm: firearm
    })
  }, [])

  // Handle add firearm
  const handleAddFirearm = useCallback(() => {
    setFormState({
      isOpen: true,
      type: 'firearm',
      selectedFirearm: null
    })
  }, [])

  // Handle assign firearm
  const handleAssignFirearm = useCallback((firearm: Firearm) => {
    setFormState({
      isOpen: true,
      type: 'assignment',
      selectedFirearm: firearm
    })
  }, [])

  // Reset form state
  const resetFormState = useCallback(() => {
    setFormState({
      isOpen: false,
      type: null,
      selectedFirearm: null
    })
  }, [])

  // Handle firearm focus toggle
  const handleFirearmFocusToggle = useCallback((firearmId: string | null) => {
    if (firearmId === null) {
      setFocusedFirearmId(null)
    } else {
      setFocusedFirearmId(prevId => (prevId === firearmId ? null : firearmId))
    }
  }, [])

  // Handle booking out a firearm
  const handleSignOutFirearm = useCallback(async (firearmId: string) => {
    try {
      const supabase = getSupabase()

      // First, check if the firearm is assigned to any client
      const { data: assignments } = await supabase
        .from('firearm_assignments')
        .select('*')
        .eq('firearm_id', firearmId)
        .is('return_date', null)

      if (assignments && assignments.length > 0) {
        // If assigned, we need to end the assignment first
        const now = new Date().toISOString()
        const { error: assignmentError } = await supabase
          .from('firearm_assignments')
          .update({ return_date: now })
          .eq('firearm_id', firearmId)
          .is('return_date', null)

        if (assignmentError) {
          console.error('Error ending firearm assignment:', assignmentError)
          setToast({
            message: 'Failed to end firearm assignment. Please try again.',
            type: 'error'
          })
          return
        }
      }

      // Update the firearm to be booked out by setting date_signed_out
      const now = new Date().toISOString()
      const { error } = await supabase
        .from('firearms')
        .update({ date_signed_out: now })
        .eq('id', firearmId)

      if (error) {
        console.error('Error booking out firearm:', error)
        setToast({
          message: 'Failed to book out firearm. Please try again.',
          type: 'error'
        })
        return
      }

      // Refresh the firearms list
      fetchFirearms()

      setToast({
        message: 'Firearm booked out successfully.',
        type: 'success'
      })
    } catch (error) {
      console.error('Error in booking out firearm:', error)
      setToast({
        message: 'An unexpected error occurred. Please try again.',
        type: 'error'
      })
    }
  }, [fetchFirearms])

  // Fetch firearms when component mounts or dependencies change
  useEffect(() => {
    fetchFirearms()
  }, [fetchFirearms])

  return {
    // State
    loading,
    searchQuery,
    page,
    perPage,
    activeFilter,
    formState,
    deleteDialog,
    toast,
    focusedFirearmId,
    visibleFirearms,
    totalFirearms,

    // Actions
    setPage,
    setActiveFilter,
    handleSearch,
    fetchFirearms,
    handleDeleteFirearm,
    onEditFirearm,
    resetFormState,
    setDeleteDialog,
    setToast,
    handleFirearmFocusToggle,
    handleAddFirearm,
    handleAssignFirearm,
    handleSignOutFirearm
  }
}
