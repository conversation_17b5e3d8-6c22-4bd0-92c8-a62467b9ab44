#!/usr/bin/env pwsh
# clean.ps1 - A robust script to clean up before building

Write-Host "Starting cleanup process..." -ForegroundColor Cyan

# Kill any running Electron processes
Write-Host "Terminating Electron processes..." -ForegroundColor Yellow
taskkill /F /IM electron.exe /T 2>$null
taskkill /F /IM Firearm-Studio.exe /T 2>$null

# Wait for processes to fully terminate
Write-Host "Waiting for processes to terminate..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

# Try to unlock any locked files
Write-Host "Checking for locked files..." -ForegroundColor Yellow
$lockedFile = "C:\Users\<USER>\Documents\FLM-System-Client\dist\win-unpacked\resources\app.asar"
if (Test-Path $lockedFile) {
    Write-Host "Found locked file: $lockedFile" -ForegroundColor Red
    
    # Get processes that might be locking the file
    $processes = Get-Process | Where-Object { $_.Modules.FileName -contains $lockedFile }
    
    if ($processes) {
        Write-Host "Found processes locking the file:" -ForegroundColor Red
        $processes | ForEach-Object {
            Write-Host "  - $($_.ProcessName) (PID: $($_.Id))" -ForegroundColor Red
            Stop-Process -Id $_.Id -Force
        }
    }
}

# Clean up dist and out directories
Write-Host "Removing dist and out directories..." -ForegroundColor Yellow
if (Test-Path "dist") {
    try {
        Remove-Item -Path "dist" -Recurse -Force -ErrorAction Stop
        Write-Host "Successfully removed dist directory" -ForegroundColor Green
    } catch {
        Write-Host "Error removing dist directory: $_" -ForegroundColor Red
        
        # Try to remove files one by one
        Write-Host "Trying to remove files individually..." -ForegroundColor Yellow
        Get-ChildItem -Path "dist" -Recurse | ForEach-Object {
            try {
                Remove-Item -Path $_.FullName -Force -ErrorAction SilentlyContinue
            } catch {
                Write-Host "Could not remove: $($_.FullName)" -ForegroundColor Red
            }
        }
    }
}

if (Test-Path "out") {
    try {
        Remove-Item -Path "out" -Recurse -Force
        Write-Host "Successfully removed out directory" -ForegroundColor Green
    } catch {
        Write-Host "Error removing out directory: $_" -ForegroundColor Red
    }
}

Write-Host "Cleanup process completed!" -ForegroundColor Cyan
